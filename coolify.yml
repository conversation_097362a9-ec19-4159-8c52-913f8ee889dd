# Coolify Configuration for Avian Email
# This file helps Coolify understand how to build and deploy your application

# Build configuration
build:
  dockerfile: Dockerfile
  context: .
  
# Application configuration
app:
  port: 3002
  healthcheck:
    path: /api/health-check
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 60s
  
# Environment variables that should be set in Coolify
environment:
  # Required variables
  - NODE_ENV=production
  - PORT=3002
  
  # Database (set these in Coolify)
  - DATABASE_URL
  - DIRECT_URL
  
  # Redis (set these in Coolify)
  - REDIS_URL
  
  # Security (set these in Coolify)
  - NEXTAUTH_URL
  - NEXTAUTH_SECRET
  - ENCRYPTION_KEY
  - CRON_SECRET
  
  # AI Provider (choose one and set in Coolify)
  - AI_PROVIDER
  - OPENAI_API_KEY
  - OLLAMA_BASE_URL
  - OLLAMA_MODEL
  
  # Optional: Monitoring
  - NEXT_PUBLIC_SENTRY_DSN
  - SENTRY_AUTH_TOKEN
  
# Resource requirements
resources:
  memory: 2GB
  cpu: 1
  
# Volumes (if needed)
volumes:
  - logs:/app/logs
