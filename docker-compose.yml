version: '3.8'

services:
  app:
    image: zainsyed1234/avian-email:v1.0.1-9
    container_name: avian-email-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      # Database
      - DATABASE_URL=${DATABASE_URL}
      - DIRECT_URL=${DIRECT_URL}

      # Redis
      - REDIS_URL=${REDIS_URL}

      # Security
      - NEXTAUTH_URL=https://avian-mail.wattlesol.com
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - CRON_SECRET=${CRON_SECRET}

      # Sentry
      - NEXT_PUBLIC_SENTRY_DSN=${NEXT_PUBLIC_SENTRY_DSN}
      - SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}
      - SENTRY_ORG=${SENTRY_ORG}
      - SENTRY_PROJECT=${SENTRY_PROJECT}

      # OAuth Credentials
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - OUTLOOK_CLIENT_ID=${OUTLOOK_CLIENT_ID}
      - OUTLOOK_CLIENT_SECRET=${OUTLOOK_CLIENT_SECRET}

      # Stripe
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - STRIPE_BASIC_PRICE_ID=${STRIPE_BASIC_PRICE_ID}
      - STRIPE_PRO_PRICE_ID=${STRIPE_PRO_PRICE_ID}
      - STRIPE_ENTERPRISE_PRICE_ID=${STRIPE_ENTERPRISE_PRICE_ID}

      # SMTP Configuration
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_SECURE=${SMTP_SECURE}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_FROM=${SMTP_FROM}

      # Email Reputation APIs
      - GOOGLE_POSTMASTER_API_KEY=${GOOGLE_POSTMASTER_API_KEY}
      - MXTOOLBOX_API_KEY=${MXTOOLBOX_API_KEY}
      - SPAMHUS_API_KEY=${SPAMHUS_API_KEY}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - CLOUD_FLARE_API_KEY=${CLOUD_FLARE_API_KEY}

      # Default tracking domains
      - DEFAULT_OPEN_TRACKING_DOMAIN=${DEFAULT_OPEN_TRACKING_DOMAIN}
      - DEFAULT_CLICK_TRACKING_DOMAIN=${DEFAULT_CLICK_TRACKING_DOMAIN}
      - DEFAULT_BOUNCE_TRACKING_DOMAIN=${DEFAULT_BOUNCE_TRACKING_DOMAIN}

      # AI Provider Configuration
      - AI_PROVIDER=${AI_PROVIDER}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL}
      - OLLAMA_MODEL=${OLLAMA_MODEL}
      - OLLAMA_EMBEDDING_MODEL=${OLLAMA_EMBEDDING_MODEL}

      # LangChain Configuration
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY}
      - LANGCHAIN_PROJECT=${LANGCHAIN_PROJECT}
      - LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2}

      # Vector Database
      - CHROMA_API_URL=${CHROMA_API_URL}

      # R1 Optimization Settings
      - R1_OPTIMIZATION_LEVEL=${R1_OPTIMIZATION_LEVEL}
      - R1_MAX_TOKENS=${R1_MAX_TOKENS}
      - R1_TEMPERATURE=${R1_TEMPERATURE}

      # Additional AI APIs
      - SERPER_API_KEY=${SERPER_API_KEY}
      - ENABLE_QUEUE_WORKER=${ENABLE_QUEUE_WORKER}

      # Application settings
      - SEQUENCE_PROCESSOR_INTERVAL=5000
      - SEQUENCE_PROCESSOR_BATCH_SIZE=20
      - CAMPAIGN_CHECK_INTERVAL=60000
      - PORT=3002
    networks:
      - traefik_web
    labels:
      - "traefik.enable=true"
      # Main application service and router
      - "traefik.http.services.avian-app.loadbalancer.server.port=3002"
      - "traefik.http.routers.avian.rule=Host(`avian-mail.wattlesol.com`)"
      - "traefik.http.routers.avian.entrypoints=websecure"
      - "traefik.http.routers.avian.tls=true"
      - "traefik.http.routers.avian.tls.certresolver=myresolver"
      - "traefik.http.routers.avian.service=avian-app"

networks:
  traefik_web:
    external: true