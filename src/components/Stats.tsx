interface Campaign {
  id: string;
  status: 'draft' | 'scheduled' | 'sending' | 'completed' | 'failed';
}

interface StatsProps {
  campaigns: Campaign[];
}

export function Stats({ campaigns }: StatsProps) {
  // Ensure campaigns is an array
  const campaignsArray = Array.isArray(campaigns) ? campaigns : [];

  const stats = {
    total: campaignsArray.length,
    completed: campaignsArray.filter(c => c.status === 'completed').length,
    sending: campaignsArray.filter(c => c.status === 'sending').length,
    scheduled: campaignsArray.filter(c => c.status === 'scheduled').length,
    draft: campaignsArray.filter(c => c.status === 'draft').length,
  };

  return (
    <div className="mb-8">
      <dl className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
        <div className="px-4 py-5 bg-white dark:bg-dark-card shadow rounded-lg overflow-hidden sm:p-6 transition-colors duration-200">
          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
            Total Campaigns
          </dt>
          <dd className="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
            {stats.total}
          </dd>
        </div>

        <div className="px-4 py-5 bg-white dark:bg-dark-card shadow rounded-lg overflow-hidden sm:p-6 transition-colors duration-200">
          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
            Completed
          </dt>
          <dd className="mt-1 text-3xl font-semibold text-green-600 dark:text-green-400">
            {stats.completed}
          </dd>
        </div>

        <div className="px-4 py-5 bg-white dark:bg-dark-card shadow rounded-lg overflow-hidden sm:p-6 transition-colors duration-200">
          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
            Sending
          </dt>
          <dd className="mt-1 text-3xl font-semibold text-blue-600 dark:text-blue-400">
            {stats.sending}
          </dd>
        </div>

        <div className="px-4 py-5 bg-white dark:bg-dark-card shadow rounded-lg overflow-hidden sm:p-6 transition-colors duration-200">
          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
            Scheduled
          </dt>
          <dd className="mt-1 text-3xl font-semibold text-yellow-600 dark:text-yellow-400">
            {stats.scheduled}
          </dd>
        </div>

        <div className="px-4 py-5 bg-white dark:bg-dark-card shadow rounded-lg overflow-hidden sm:p-6 transition-colors duration-200">
          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
            Drafts
          </dt>
          <dd className="mt-1 text-3xl font-semibold text-gray-600 dark:text-gray-300">
            {stats.draft}
          </dd>
        </div>
      </dl>
    </div>
  );
}