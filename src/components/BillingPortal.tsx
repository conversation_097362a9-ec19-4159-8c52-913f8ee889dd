import { useState } from 'react';
import { useSession } from 'next-auth/react';

export function BillingPortal() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);

  const handleManageBilling = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/billing/portal', {
        method: 'POST',
      });
      const data = await response.json();
      if (data.url) {
        window.location.href = data.url;
      }
    } catch (error) {
      console.error('Failed to access billing portal:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white shadow sm:rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-base font-semibold leading-6 text-gray-900">
          Manage Subscription
        </h3>
        <div className="mt-2 max-w-xl text-sm text-gray-500">
          <p>
            Manage your subscription plan, billing cycle, and subscription status.
          </p>
        </div>
        <div className="mt-5">
          <button
            type="button"
            onClick={handleManageBilling}
            disabled={loading}
            className="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            {loading ? 'Loading...' : 'Manage Subscription'}
          </button>
        </div>
      </div>
    </div>
  );
}