import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { PLANS } from '@/config/stripe';
import { CheckIcon } from '@heroicons/react/24/outline';

export function PricingTable() {
  const { data: session } = useSession();
  const [isAnnual, setIsAnnual] = useState(false);

  const handleSubscribe = async (priceId: string) => {
    if (!session) {
      // Redirect to sign in
      window.location.href = '/auth/signin';
      return;
    }

    try {
      const response = await fetch('/api/subscriptions/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
          organizationId: session.user.currentOrganization?.id,
        }),
      });

      const data = await response.json();

      // Redirect to Stripe Checkout
      if (data.url) {
        window.location.href = data.url;
      }
    } catch (error) {
      console.error('Subscription error:', error);
    }
  };

  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-base font-semibold leading-7 text-indigo-600">Pricing</h2>
          <p className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            Choose the right plan for you
          </p>
        </div>

        <div className="mt-6 flex justify-center">
          <div className="relative flex rounded-full bg-gray-100 p-1">
            <button
              onClick={() => setIsAnnual(false)}
              className={`${
                !isAnnual ? 'bg-white shadow-sm' : ''
              } relative rounded-full px-4 py-1 text-sm font-semibold transition-all`}
            >
              Monthly
            </button>
            <button
              onClick={() => setIsAnnual(true)}
              className={`${
                isAnnual ? 'bg-white shadow-sm' : ''
              } relative rounded-full px-4 py-1 text-sm font-semibold transition-all`}
            >
              Annual
            </button>
          </div>
        </div>

        <div className="mx-auto mt-16 grid max-w-lg grid-cols-1 gap-y-6 sm:grid-cols-3 sm:gap-x-8">
          {Object.values(PLANS).map((plan) => (
            <div
              key={plan.id}
              className="flex flex-col rounded-3xl bg-white p-8 shadow-xl ring-1 ring-gray-200"
            >
              <h3 className="text-lg font-semibold leading-8 text-gray-900">{plan.name}</h3>
              <p className="mt-4 text-sm leading-6 text-gray-600">{plan.features[0]}</p>
              <p className="mt-6 flex items-baseline gap-x-1">
                <span className="text-4xl font-bold tracking-tight text-gray-900">
                  ${isAnnual ? (plan.priceMonthly * 10).toFixed(2) : plan.priceMonthly.toFixed(2)}
                </span>
                <span className="text-sm font-semibold leading-6 text-gray-600">/month</span>
              </p>
              <button
                onClick={() => handleSubscribe(plan.id)}
                className="mt-6 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Subscribe
              </button>
              <ul role="list" className="mt-8 space-y-3 text-sm leading-6 text-gray-600">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex gap-x-3">
                    <CheckIcon className="h-6 w-5 flex-none text-indigo-600" aria-hidden="true" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}