import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Select, Button, Table, InputNumber, Switch, Tooltip, Tag, Space } from 'antd';
import { PlusOutlined, DeleteOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { EmailAccount } from '@prisma/client';

interface EmailAccountWithWeight {
  id: string;
  weight: number;
}

interface MultipleEmailAccountsSelectorProps {
  value?: EmailAccountWithWeight[];
  onChange?: (value: EmailAccountWithWeight[]) => void;
  useMultipleSenders: boolean;
  onUseMultipleSendersChange: (value: boolean) => void;
  primaryEmailAccountId?: string;
  onPrimaryEmailAccountChange?: (value: string) => void;
}

export const MultipleEmailAccountsSelector: React.FC<MultipleEmailAccountsSelectorProps> = ({
  value = [],
  onChange,
  useMultipleSenders,
  onUseMultipleSendersChange,
  primaryEmailAccountId,
  onPrimaryEmailAccountChange,
}) => {
  const [selectedAccounts, setSelectedAccounts] = useState<EmailAccountWithWeight[]>(value);

  // Fetch email accounts
  const { data: emailAccounts, isLoading } = useQuery({
    queryKey: ['emailAccounts'],
    queryFn: () => fetch('/api/email-accounts').then(res => res.json()),
  });

  // Update the parent component when selectedAccounts changes
  useEffect(() => {
    if (onChange) {
      onChange(selectedAccounts);
    }
  }, [selectedAccounts, onChange]);

  // Add a new email account
  const handleAddAccount = (accountId: string) => {
    if (!accountId || selectedAccounts.some(a => a.id === accountId)) {
      return;
    }

    setSelectedAccounts([...selectedAccounts, { id: accountId, weight: 1 }]);
  };

  // Remove an email account
  const handleRemoveAccount = (accountId: string) => {
    setSelectedAccounts(selectedAccounts.filter(a => a.id !== accountId));
  };

  // Update the weight of an email account
  const handleWeightChange = (accountId: string, weight: number) => {
    setSelectedAccounts(
      selectedAccounts.map(a => (a.id === accountId ? { ...a, weight } : a))
    );
  };

  // Get email account details by ID
  const getEmailAccountById = (id: string) => {
    return emailAccounts?.find((account: EmailAccount) => account.id === id);
  };

  // Render reputation tag
  const renderReputationTag = (score: number | null) => {
    if (score === null) return <Tag color="default">Unknown</Tag>;
    if (score >= 80) return <Tag color="success">{score}</Tag>;
    if (score >= 60) return <Tag color="processing">{score}</Tag>;
    if (score >= 40) return <Tag color="warning">{score}</Tag>;
    return <Tag color="error">{score}</Tag>;
  };

  // Table columns
  const columns = [
    {
      title: 'Email',
      dataIndex: 'id',
      key: 'email',
      render: (id: string) => {
        const account = getEmailAccountById(id);
        return account ? (
          <div>
            <div>{account.email}</div>
            <div className="text-xs text-gray-500">{account.name}</div>
          </div>
        ) : id;
      },
    },
    {
      title: 'Status',
      dataIndex: 'id',
      key: 'status',
      render: (id: string) => {
        const account = getEmailAccountById(id);
        if (!account) return null;

        return (
          <Tag color={account.status === 'active' ? 'success' : account.status === 'pending' ? 'warning' : 'error'}>
            {account.status}
          </Tag>
        );
      },
    },
    {
      title: 'Reputation ℹ️',
      dataIndex: 'id',
      key: 'reputation',
      render: (id: string) => {
        const account = getEmailAccountById(id);
        if (!account) return null;

        return renderReputationTag(account.reputationScore);
      },
    },
    {
      title: 'Weight ℹ️',
      dataIndex: 'weight',
      key: 'weight',
      render: (weight: number, record: EmailAccountWithWeight) => (
        <InputNumber
          min={1}
          max={10}
          value={weight}
          onChange={(value) => handleWeightChange(record.id, value || 1)}
        />
      ),
    },
    {
      title: 'Action',
      key: 'action',
      render: (_: any, record: EmailAccountWithWeight) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveAccount(record.id)}
        />
      ),
    },
  ];

  // Filter out already selected accounts and suspended accounts
  const availableAccounts = emailAccounts?.filter(
    (account: EmailAccount) =>
      !selectedAccounts.some(a => a.id === account.id) &&
      account.status !== 'suspended'
  ) || [];

  return (
    <div className="space-y-4">
      <div className="flex items-center mb-4">
        <Switch
          checked={useMultipleSenders}
          onChange={onUseMultipleSendersChange}
        />
        <span className="ml-2">Use multiple senders</span>
        <InfoCircleOutlined
          className="ml-2 text-gray-400"
          title="When enabled, emails will be distributed among multiple accounts based on their reputation"
        />
      </div>

      {!useMultipleSenders ? (
        <div>
          <div className="mb-2">Primary Email Account</div>
          <Select
            style={{ width: '100%' }}
            placeholder="Select an email account"
            loading={isLoading}
            value={primaryEmailAccountId}
            onChange={onPrimaryEmailAccountChange}
            optionFilterProp="label"
          >
            {emailAccounts?.map((account: EmailAccount) => (
              <Select.Option
                key={account.id}
                value={account.id}
                label={account.email}
                disabled={account.status === 'suspended'}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <div>{account.email}</div>
                    <div className="text-xs text-gray-500">{account.name}</div>
                  </div>
                  <Space>
                    <Tag color={account.status === 'active' ? 'success' : account.status === 'pending' ? 'warning' : 'error'}>
                      {account.status}
                    </Tag>
                    {account.reputationScore !== null && renderReputationTag(account.reputationScore)}
                  </Space>
                </div>
              </Select.Option>
            ))}
          </Select>
        </div>
      ) : (
        <>
          <div className="flex justify-between items-center mb-2">
            <div>Email Accounts</div>
            <Select
              style={{ width: 300 }}
              placeholder="Add an email account"
              optionFilterProp="label"
              onChange={handleAddAccount}
              value={null}
              disabled={availableAccounts.length === 0}
            >
              {availableAccounts.map((account: EmailAccount) => (
                <Select.Option
                  key={account.id}
                  value={account.id}
                  label={account.email}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <div>{account.email}</div>
                      <div className="text-xs text-gray-500">{account.name}</div>
                    </div>
                    <Space>
                      <Tag color={account.status === 'active' ? 'success' : 'warning'}>
                        {account.status}
                      </Tag>
                      {account.reputationScore !== null && renderReputationTag(account.reputationScore)}
                    </Space>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </div>

          <Table
            dataSource={selectedAccounts}
            columns={columns}
            rowKey="id"
            pagination={false}
            size="small"
            locale={{ emptyText: 'No email accounts selected' }}
          />

          {selectedAccounts.length === 0 && (
            <div className="text-center text-gray-500 mt-4">
              Add at least one email account to send from
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default MultipleEmailAccountsSelector;
