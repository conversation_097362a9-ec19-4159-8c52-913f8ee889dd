import React from 'react';
import { Card, Tag, Tooltip } from 'antd';
import {
  MailOutlined,
  ClockCircleOutlined,
  BranchesOutlined,
  RobotOutlined,
  ArrowRightOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';

interface Step {
  id: string;
  name: string;
  type: string;
  position: number;
  subject?: string;
  content?: string;
  emailAccountId?: string;
  emailAccount?: {
    id: string;
    name: string;
    email: string;
  };
  waitDuration?: number;
  waitUntil?: string;
  conditionType?: string;
  conditionStepId?: string;
  conditionTimeframe?: number;
  actionType?: string;
  actionValue?: string;
  // Added properties for real-time data
  conditionStep?: Step;
  conditionedSteps?: Step[];
  activities?: Array<{
    id: string;
    status: string;
    sentAt?: string;
    openedAt?: string;
    clickedAt?: string;
    repliedAt?: string;
    bouncedAt?: string;
    error?: string;
  }>;
}

interface StepCompletion {
  [key: string]: {
    total: number;
    completed: number;
    pending: number;
    failed: number;
  };
}

interface SequenceFlowDiagramProps {
  steps: Step[];
  stepCompletion: StepCompletion;
}

const SequenceFlowDiagram: React.FC<SequenceFlowDiagramProps> = ({ steps, stepCompletion }) => {
  // Sort steps by position
  const sortedSteps = [...steps].sort((a, b) => a.position - b.position);

  // Get step icon based on type
  const getStepIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <MailOutlined className="text-blue-500" />;
      case 'wait':
        return <ClockCircleOutlined className="text-orange-500" />;
      case 'condition':
        return <BranchesOutlined className="text-green-500" />;
      case 'action':
        return <RobotOutlined className="text-purple-500" />;
      default:
        return <QuestionCircleOutlined />;
    }
  };

  // Get step color based on type
  const getStepColor = (type: string) => {
    switch (type) {
      case 'email':
        return 'border-blue-500 bg-blue-50 dark:bg-blue-900/20';
      case 'wait':
        return 'border-orange-500 bg-orange-50 dark:bg-orange-900/20';
      case 'condition':
        return 'border-green-500 bg-green-50 dark:bg-green-900/20';
      case 'action':
        return 'border-purple-500 bg-purple-50 dark:bg-purple-900/20';
      default:
        return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  // Get step details based on type
  const getStepDetails = (step: Step) => {
    switch (step.type) {
      case 'email':
        return (
          <>
            <div className="font-medium">{step.subject}</div>
            <div className="text-xs text-gray-500">
              {step.emailAccount ? `${step.emailAccount.name} (${step.emailAccount.email})` : 'No sender specified'}
            </div>
          </>
        );
      case 'wait':
        if (step.waitDuration) {
          return <div>Wait for {step.waitDuration} hours</div>;
        } else if (step.waitUntil) {
          return <div>Wait until {new Date(step.waitUntil).toLocaleString()}</div>;
        }
        return <div>Wait step</div>;
      case 'condition':
        return (
          <>
            <div>{step.conditionType?.replace(/_/g, ' ')}</div>
            <div className="text-xs text-gray-500">
              {step.conditionTimeframe} hours timeframe
            </div>
          </>
        );
      case 'action':
        return (
          <>
            <div>{step.actionType?.replace(/_/g, ' ')}</div>
            {step.actionValue && (
              <div className="text-xs text-gray-500">{step.actionValue}</div>
            )}
          </>
        );
      default:
        return <div>Unknown step type</div>;
    }
  };

  // Get step completion stats
  const getStepStats = (stepId: string) => {
    // Make sure stepCompletion exists and has the stepId key
    if (!stepCompletion || !stepId) {
      return (
        <div className="text-xs mt-2">
          <div className="flex items-center">
            <span>No data available</span>
          </div>
        </div>
      );
    }

    const stats = stepCompletion[stepId] || {
      total: 0,
      completed: 0,
      pending: 0,
      failed: 0
    };

    const completionRate = stats.total > 0
      ? Math.round((stats.completed / stats.total) * 100)
      : 0;

    return (
      <div className="text-xs mt-2">
        <div className="flex items-center">
          <CheckCircleOutlined className="text-green-500 mr-1" />
          <span>{stats.completed} completed</span>
          <span className="mx-1">|</span>
          <span>{completionRate}%</span>
        </div>
        {stats.failed > 0 && (
          <div className="flex items-center text-red-500">
            <CloseCircleOutlined className="mr-1" />
            <span>{stats.failed} failed</span>
          </div>
        )}
      </div>
    );
  };

  // Find the step that a condition is referencing
  const findReferencedStep = (conditionStepId: string | undefined) => {
    if (!conditionStepId) return null;

    // First try to find by direct ID reference
    const directStep = steps.find(s => s.id === conditionStepId);
    if (directStep) return directStep;

    // If not found, try to find by position (for backward compatibility)
    const positionMatch = steps.find(s => s.position.toString() === conditionStepId);
    if (positionMatch) return positionMatch;

    // If still not found, check if the step has a conditionStep property
    const stepWithCondition = steps.find(s =>
      s.type === 'condition' &&
      s.conditionStep &&
      s.conditionStep.id === conditionStepId
    );
    if (stepWithCondition?.conditionStep) return stepWithCondition.conditionStep;

    return null;
  };

  return (
    <div className="sequence-flow-diagram">
      <div className="flex flex-col items-center">
        {sortedSteps.map((step, index) => (
          <div key={step.id} className="w-full mb-8">
            <div className="flex justify-center">
              <div className={`relative p-4 border-l-4 rounded-lg shadow-sm w-full max-w-md ${getStepColor(step.type)}`}>
                <div className="flex items-center mb-2">
                  <div className="mr-2">
                    {getStepIcon(step.type)}
                  </div>
                  <div className="font-bold">
                    {step.name || `Step ${step.position + 1}`}
                  </div>
                  <div className="ml-auto">
                    <Tag color={
                      step.type === 'email' ? 'blue' :
                      step.type === 'wait' ? 'orange' :
                      step.type === 'condition' ? 'green' :
                      step.type === 'action' ? 'purple' : 'default'
                    }>
                      {step.type}
                    </Tag>
                  </div>
                </div>
                <div className="ml-6">
                  {getStepDetails(step)}
                  {getStepStats(step.id)}
                </div>

                {/* Show condition branches */}
                {step.type === 'condition' && (
                  <div className="mt-4 ml-6 border-t pt-2">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <div className="font-medium text-green-600">Yes</div>
                        <div className="text-xs">Continue to next step</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-red-600">No</div>
                        <div className="text-xs">
                          {step.conditionStepId ? (
                            <>
                              {findReferencedStep(step.conditionStepId)?.name ||
                                `Step ${findReferencedStep(step.conditionStepId)?.position || '?'}`}
                            </>
                          ) : (
                            'End sequence'
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Connector arrow */}
            {index < sortedSteps.length - 1 && (
              <div className="flex justify-center my-2">
                <ArrowRightOutlined className="transform rotate-90 text-gray-400" />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SequenceFlowDiagram;
