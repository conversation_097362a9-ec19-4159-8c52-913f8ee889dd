import { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  InputNumber,
  Radio,
  DatePicker,
  Divider,
  Typography,
  Tabs
} from 'antd';
import dynamic from 'next/dynamic';
import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import EmailAccountsSelector from './EmailAccountsSelector';

// Step types
const STEP_TYPES = {
  EMAIL: 'email',
  WAIT: 'wait',
  CONDITION: 'condition',
  ACTION: 'action'
};

const { Title, Text } = Typography;
const { Option } = Select;

// Import the rich text editor dynamically to avoid SSR issues
const RichTextEditor = dynamic(() => import('../RichTextEditor'), { ssr: false });

// Condition types
const CONDITION_TYPES = [
  { value: 'opened', label: 'Email was opened' },
  { value: 'not_opened', label: 'Email was not opened' },
  { value: 'clicked', label: 'Link was clicked' },
  { value: 'not_clicked', label: '<PERSON> was not clicked' },
  { value: 'replied', label: '<PERSON><PERSON> was replied to' },
  { value: 'not_replied', label: '<PERSON><PERSON> was not replied to' },
];

// Action types
const ACTION_TYPES = [
  { value: 'update_lead', label: 'Update lead status' },
  { value: 'add_tag', label: 'Add tag to lead' },
  { value: 'remove_tag', label: 'Remove tag from lead' },
  { value: 'exit_campaign', label: 'Exit campaign' },
];

interface CampaignStepEditorProps {
  step: any;
  onChange: (data: any) => void;
  emailAccounts: any[];
  steps: any[];
  currentStepIndex: number;
}

export default function CampaignStepEditor({
  step,
  onChange,
  emailAccounts,
  steps,
  currentStepIndex
}: CampaignStepEditorProps) {
  const [localStep, setLocalStep] = useState(step);
  const [waitType, setWaitType] = useState<'duration' | 'date'>(
    step.waitUntil ? 'date' : 'duration'
  );
  const [contentType, setContentType] = useState<'write' | 'template'>(
    step.templateId ? 'template' : 'write'
  );

  // Fetch templates
  const { data: templates = [] } = useQuery({
    queryKey: ['templates'],
    queryFn: () => fetch('/api/templates').then(res => res.json()),
  });

  // Update local state when props change
  useEffect(() => {
    console.log('===== CAMPAIGN STEP EDITOR - STEP CHANGED =====');
    console.log('Step received:', JSON.stringify(step));
    console.log('Step type:', step.type);
    console.log('Current step index:', currentStepIndex);

    // Create a deep copy of the step to avoid reference issues
    const stepCopy = JSON.parse(JSON.stringify(step));
    console.log('Step copy created:', JSON.stringify(stepCopy));

    // Set the local step with the deep copy
    setLocalStep(stepCopy);
    setWaitType(stepCopy.waitUntil ? 'date' : 'duration');
    setContentType(stepCopy.templateId ? 'template' : 'write');

    // Log after state update
    setTimeout(() => {
      console.log('After state update - localStep:', JSON.stringify(localStep));
      console.log('===== CAMPAIGN STEP EDITOR - UPDATE COMPLETE =====');
    }, 100);
  }, [step]);

  const handleChange = (field: string, value: any) => {
    const updatedStep = { ...localStep, [field]: value };
    setLocalStep(updatedStep);
    onChange(updatedStep);
  };

  const handleContentChange = (content: string) => {
    handleChange('content', content);
  };

  const handleWaitTypeChange = (type: 'duration' | 'date') => {
    console.log('Changing wait type to:', type);
    setWaitType(type);

    if (type === 'duration') {
      // When switching to duration, clear the date
      handleChange('waitUntil', null);
      // Set a default duration if none exists
      if (!localStep.waitDuration) {
        handleChange('waitDuration', 24); // Default 24 hours
      }
    } else {
      // When switching to date, clear the duration
      handleChange('waitDuration', null);
      // Set a default date if none exists (tomorrow)
      if (!localStep.waitUntil) {
        const tomorrow = dayjs().add(1, 'day');
        handleChange('waitUntil', tomorrow.toISOString());
      }
    }
  };

  const handleContentTypeChange = (type: 'write' | 'template') => {
    setContentType(type);
    if (type === 'write') {
      handleChange('templateId', null);
    } else {
      handleChange('content', '');
    }
  };

  // Render the email step editor
  const renderEmailStep = () => (
    <div>
      <Form.Item label="Email Sender Configuration">
        <Radio.Group
          value={localStep.useMultipleSenders ? true : false}
          onChange={(e) => handleChange('useMultipleSenders', e.target.value)}
        >
          <Radio value={false}>Use single sender</Radio>
          <Radio value={true}>Use multiple senders</Radio>
        </Radio.Group>
      </Form.Item>

      <Form.Item label="Primary Email Account">
        <Select
          value={localStep.emailAccountId}
          onChange={(value) => handleChange('emailAccountId', value)}
          placeholder="Select an email account"
          className="w-full dark:bg-dark-card dark:text-white"
        >
          {emailAccounts.map((account: any) => (
            <Option key={account.id} value={account.id}>
              {account.name} ({account.email})
            </Option>
          ))}
        </Select>
      </Form.Item>

      {localStep.useMultipleSenders && (
        <Form.Item label="Additional Email Accounts">
          <EmailAccountsSelector
            emailAccounts={emailAccounts}
            value={localStep.additionalEmailAccountIds || []}
            onChange={(accounts) => handleChange('additionalEmailAccountIds', accounts)}
          />
        </Form.Item>
      )}

      <Form.Item label="Subject">
        <Input
          value={localStep.subject}
          onChange={(e) => handleChange('subject', e.target.value)}
          placeholder="Email subject"
        />
      </Form.Item>

      <Form.Item label="Content Source">
        <Radio.Group
          value={contentType}
          onChange={(e) => handleContentTypeChange(e.target.value)}
          optionType="button"
          buttonStyle="solid"
          className="dark:bg-dark-card dark:text-white"
        >
          <Radio.Button value="write">Write Content</Radio.Button>
          <Radio.Button value="template">Use Template</Radio.Button>
        </Radio.Group>
      </Form.Item>

      {contentType === 'template' ? (
        <Form.Item label="Select Template">
          <Select
            value={localStep.templateId}
            onChange={(value) => handleChange('templateId', value)}
            placeholder="Select a template"
            className="w-full dark:bg-dark-card dark:text-white"
          >
            {templates.map((template: any) => (
              <Option key={template.id} value={template.id}>
                {template.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
      ) : (
        <Form.Item label="Content">
          <RichTextEditor
            value={localStep.content}
            onChange={handleContentChange}
          />
        </Form.Item>
      )}
    </div>
  );

  // Render the wait step editor
  const renderWaitStep = () => (
    <div>
      <Form.Item label="Wait Type">
        <Radio.Group
          value={waitType}
          onChange={(e) => handleWaitTypeChange(e.target.value)}
          optionType="button"
          buttonStyle="solid"
          className="dark:bg-dark-card dark:text-white w-full"
        >
          <Radio.Button value="duration" className="w-1/2 text-center">Wait Duration (hours)</Radio.Button>
          <Radio.Button value="date" className="w-1/2 text-center">Wait Until Specific Date</Radio.Button>
        </Radio.Group>
      </Form.Item>

      {waitType === 'duration' ? (
        <Form.Item label="Wait Duration (hours)">
          <InputNumber
            min={1}
            max={720} // 30 days
            value={localStep.waitDuration}
            onChange={(value) => handleChange('waitDuration', value)}
            className="w-full dark:bg-dark-card dark:text-white"
          />
        </Form.Item>
      ) : (
        <Form.Item label="Wait Until Date">
          <DatePicker
            showTime
            value={localStep.waitUntil ? dayjs(localStep.waitUntil) : null}
            onChange={(date) => {
              console.log('Date selected:', date);
              handleChange('waitUntil', date ? date.toISOString() : null);
            }}
            className="w-full dark:bg-dark-card dark:text-white"
            format="YYYY-MM-DD HH:mm:ss"
            disabledDate={(current) => current && current < dayjs().startOf('day')}
          />
        </Form.Item>
      )}
    </div>
  );

  // Render the condition step editor
  const renderConditionStep = () => (
    <div>
      <Form.Item label="Condition Type">
        <Select
          value={localStep.conditionType}
          onChange={(value) => handleChange('conditionType', value)}
          className="w-full dark:bg-dark-card dark:text-white"
        >
          {CONDITION_TYPES.map((type) => (
            <Option key={type.value} value={type.value}>
              {type.label}
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item label="Email Step to Check">
        <Select
          value={localStep.conditionStepId}
          onChange={(value) => handleChange('conditionStepId', value)}
          className="w-full dark:bg-dark-card dark:text-white"
        >
          {steps
            .filter((s: any) => s.type === STEP_TYPES.EMAIL && s.position < localStep.position)
            .map((step: any) => (
              <Option key={step.id} value={step.id}>
                {step.name || `Step ${step.position + 1}`}
              </Option>
            ))}
        </Select>
      </Form.Item>

      <Form.Item label="Timeframe (hours)">
        <InputNumber
          min={1}
          max={720} // 30 days
          value={localStep.conditionTimeframe}
          onChange={(value) => handleChange('conditionTimeframe', value)}
          className="w-full dark:bg-dark-card dark:text-white"
        />
      </Form.Item>
    </div>
  );

  // Render the action step editor
  const renderActionStep = () => (
    <div>
      <Form.Item label="Action Type">
        <Select
          value={localStep.actionType}
          onChange={(value) => handleChange('actionType', value)}
          className="w-full dark:bg-dark-card dark:text-white"
        >
          {ACTION_TYPES.map((type) => (
            <Option key={type.value} value={type.value}>
              {type.label}
            </Option>
          ))}
        </Select>
      </Form.Item>

      {localStep.actionType === 'add_tag' || localStep.actionType === 'remove_tag' ? (
        <Form.Item label="Tag Name">
          <Input
            value={localStep.actionValue}
            onChange={(e) => handleChange('actionValue', e.target.value)}
            placeholder="Enter tag name"
          />
        </Form.Item>
      ) : null}
    </div>
  );

  return (
    <div>
      <Form.Item label="Step Name">
        <Input
          value={localStep.name}
          onChange={(e) => handleChange('name', e.target.value)}
          placeholder="Enter a name for this step"
        />
      </Form.Item>

      <Form.Item label="Description">
        <Input.TextArea
          value={localStep.description}
          onChange={(e) => handleChange('description', e.target.value)}
          placeholder="Optional description"
          rows={2}
        />
      </Form.Item>

      <Divider />

      {/* Hidden debug info */}
      <div style={{ display: 'none' }}>
        <div>DEBUG INFO (hidden):</div>
        <div>Step type: {localStep.type}</div>
        <div>EMAIL type constant: {STEP_TYPES.EMAIL}</div>
        <div>WAIT type constant: {STEP_TYPES.WAIT}</div>
        <div>CONDITION type constant: {STEP_TYPES.CONDITION}</div>
        <div>ACTION type constant: {STEP_TYPES.ACTION}</div>
        <div>Type comparison (email): {String(localStep.type === STEP_TYPES.EMAIL)}</div>
        <div>Type comparison (wait): {String(localStep.type === STEP_TYPES.WAIT)}</div>
        <div>Type comparison (condition): {String(localStep.type === STEP_TYPES.CONDITION)}</div>
        <div>Type comparison (action): {String(localStep.type === STEP_TYPES.ACTION)}</div>
      </div>

      {(() => {
        console.log('===== RENDERING STEP EDITOR =====');
        console.log('localStep.type:', localStep.type);
        console.log('STEP_TYPES.EMAIL:', STEP_TYPES.EMAIL);
        console.log('STEP_TYPES.WAIT:', STEP_TYPES.WAIT);
        console.log('STEP_TYPES.CONDITION:', STEP_TYPES.CONDITION);
        console.log('STEP_TYPES.ACTION:', STEP_TYPES.ACTION);
        console.log('Type comparison (email):', localStep.type === STEP_TYPES.EMAIL);
        console.log('Type comparison (wait):', localStep.type === STEP_TYPES.WAIT);
        console.log('Type comparison (condition):', localStep.type === STEP_TYPES.CONDITION);
        console.log('Type comparison (action):', localStep.type === STEP_TYPES.ACTION);

        if (localStep.type === STEP_TYPES.EMAIL) {
          console.log('Rendering EMAIL step');
          return renderEmailStep();
        } else if (localStep.type === STEP_TYPES.WAIT) {
          console.log('Rendering WAIT step');
          return renderWaitStep();
        } else if (localStep.type === STEP_TYPES.CONDITION) {
          console.log('Rendering CONDITION step');
          return renderConditionStep();
        } else if (localStep.type === STEP_TYPES.ACTION) {
          console.log('Rendering ACTION step');
          return renderActionStep();
        } else {
          console.log('Unknown step type:', localStep.type);
          return (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
              <p>Unknown step type: {localStep.type}</p>
            </div>
          );
        }
      })()}
    </div>
  );
}
