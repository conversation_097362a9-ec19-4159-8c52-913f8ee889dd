import React from 'react';
import { Card, Row, Col, Statistic, Progress, Divider, Tooltip } from 'antd';
import {
  MailOutlined,
  EyeOutlined,
  LinkOutlined,
  MessageOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  StopOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

interface StepCompletion {
  [key: string]: {
    total: number;
    completed: number;
    pending: number;
    failed: number;
  };
}

interface CampaignAnalytics {
  totalLeads: number;
  sentCount: number;
  openCount: number;
  clickCount: number;
  replyCount: number;
  bounceCount: number;
  unsubscribeCount: number;
  openRate: number;
  clickRate: number;
  replyRate: number;
  bounceRate: number;
  unsubscribeRate: number;
  leadsByStatus: {
    active: number;
    completed: number;
    unsubscribed: number;
    bounced: number;
  };
  stepCompletion: StepCompletion;
}

interface SequencePerformanceMetricsProps {
  analytics: CampaignAnalytics;
  status: string;
}

// Default empty analytics object for safety
const defaultAnalytics: CampaignAnalytics = {
  totalLeads: 0,
  sentCount: 0,
  openCount: 0,
  clickCount: 0,
  replyCount: 0,
  bounceCount: 0,
  unsubscribeCount: 0,
  openRate: 0,
  clickRate: 0,
  replyRate: 0,
  bounceRate: 0,
  unsubscribeRate: 0,
  leadsByStatus: {
    active: 0,
    completed: 0,
    unsubscribed: 0,
    bounced: 0,
  },
  stepCompletion: {},
};

const SequencePerformanceMetrics: React.FC<SequencePerformanceMetricsProps> = ({ analytics: rawAnalytics, status }) => {
  // Use default analytics if not provided or merge with defaults for safety
  const analytics = rawAnalytics ? { ...defaultAnalytics, ...rawAnalytics } : defaultAnalytics;

  // Calculate overall campaign progress
  const totalRecipients = analytics.totalLeads || 0;
  const completedRecipients = analytics.leadsByStatus?.completed || 0;
  const campaignProgress = totalRecipients > 0
    ? Math.round((analytics.sentCount / totalRecipients) * 100)
    : 0;

  // Calculate completion rate
  const completionRate = totalRecipients > 0
    ? Math.round((completedRecipients / totalRecipients) * 100)
    : 0;

  // Calculate engagement metrics
  const engagementScore = analytics.openRate > 0
    ? (analytics.openRate + analytics.clickRate * 2 + analytics.replyRate * 3) / 6
    : 0;

  return (
    <div className="sequence-performance-metrics">
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="Campaign Progress" className="h-full">
            <div className="mb-6">
              <div className="flex justify-between mb-1">
                <span>Overall Progress</span>
                <span>{campaignProgress}%</span>
              </div>
              <Progress
                percent={campaignProgress}
                status={status === 'completed' ? 'success' : 'active'}
                strokeWidth={10}
              />
              <div className="text-xs text-gray-500 mt-1">
                {analytics.sentCount} emails sent out of {totalRecipients} total recipients
              </div>
            </div>

            <div className="mb-6">
              <div className="flex justify-between mb-1">
                <span>Completion Rate</span>
                <span>{completionRate}%</span>
              </div>
              <Progress
                percent={completionRate}
                status="success"
                strokeWidth={10}
                strokeColor="#52c41a"
              />
              <div className="text-xs text-gray-500 mt-1">
                {completedRecipients} recipients completed the sequence
              </div>
            </div>

            <Divider />

            <div className="grid grid-cols-2 gap-4">
              <Statistic
                title={
                  <Tooltip title="Recipients currently active in the sequence">
                    <span>
                      Active Recipients <InfoCircleOutlined className="text-gray-400" />
                    </span>
                  </Tooltip>
                }
                value={analytics.leadsByStatus.active}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
              <Statistic
                title={
                  <Tooltip title="Recipients who have completed the sequence">
                    <span>
                      Completed <InfoCircleOutlined className="text-gray-400" />
                    </span>
                  </Tooltip>
                }
                value={analytics.leadsByStatus.completed}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
              <Statistic
                title={
                  <Tooltip title="Recipients who have unsubscribed">
                    <span>
                      Unsubscribed <InfoCircleOutlined className="text-gray-400" />
                    </span>
                  </Tooltip>
                }
                value={analytics.leadsByStatus.unsubscribed}
                prefix={<StopOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
              <Statistic
                title={
                  <Tooltip title="Recipients whose emails bounced">
                    <span>
                      Bounced <InfoCircleOutlined className="text-gray-400" />
                    </span>
                  </Tooltip>
                }
                value={analytics.leadsByStatus.bounced}
                prefix={<WarningOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="Engagement Metrics" className="h-full">
            <div className="mb-6">
              <div className="flex justify-between mb-1">
                <span>Overall Engagement</span>
                <span>{engagementScore.toFixed(1)}%</span>
              </div>
              <Progress
                percent={engagementScore}
                strokeWidth={10}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <div className="text-xs text-gray-500 mt-1">
                Based on opens, clicks, and replies
              </div>
            </div>

            <Divider />

            <div className="grid grid-cols-2 gap-4">
              <Statistic
                title="Emails Sent"
                value={analytics.sentCount}
                prefix={<MailOutlined />}
              />
              <Statistic
                title="Opens"
                value={analytics.openCount}
                prefix={<EyeOutlined />}
                suffix={analytics.openRate > 0 ? `(${analytics.openRate.toFixed(1)}%)` : ''}
              />
              <Statistic
                title="Clicks"
                value={analytics.clickCount}
                prefix={<LinkOutlined />}
                suffix={analytics.clickRate > 0 ? `(${analytics.clickRate.toFixed(1)}%)` : ''}
              />
              <Statistic
                title="Replies"
                value={analytics.replyCount}
                prefix={<MessageOutlined />}
                suffix={analytics.replyRate > 0 ? `(${analytics.replyRate.toFixed(1)}%)` : ''}
                valueStyle={{ color: '#52c41a' }}
              />
            </div>

            <Divider />

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Tooltip title="Percentage of emails that were opened">
                  <div className="text-center">
                    <Progress
                      type="circle"
                      percent={Math.round(analytics.openRate)}
                      width={80}
                    />
                    <div className="mt-2">Open Rate</div>
                  </div>
                </Tooltip>
              </div>
              <div>
                <Tooltip title="Percentage of opened emails that were clicked">
                  <div className="text-center">
                    <Progress
                      type="circle"
                      percent={analytics.openCount > 0 ? Math.round((analytics.clickCount / analytics.openCount) * 100) : 0}
                      width={80}
                      strokeColor="#1890ff"
                    />
                    <div className="mt-2">Click-to-Open</div>
                  </div>
                </Tooltip>
              </div>
              <div>
                <Tooltip title="Percentage of opened emails that received replies">
                  <div className="text-center">
                    <Progress
                      type="circle"
                      percent={analytics.openCount > 0 ? Math.round((analytics.replyCount / analytics.openCount) * 100) : 0}
                      width={80}
                      strokeColor="#52c41a"
                    />
                    <div className="mt-2">Reply Rate</div>
                  </div>
                </Tooltip>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SequencePerformanceMetrics;
