import React, { useState, useEffect } from 'react';
import { Select, Button, Table, InputNumber, Tag, Space } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';

interface EmailAccount {
  id: string;
  email: string;
  name?: string;
  status: string;
  reputationScore?: number | null;
}

interface EmailAccountWithWeight {
  id: string;
  weight: number;
}

interface EmailAccountsSelectorProps {
  value?: EmailAccountWithWeight[];
  onChange?: (value: EmailAccountWithWeight[]) => void;
  emailAccounts: EmailAccount[];
  selectedEmailAccounts?: EmailAccountWithWeight[];
}

const EmailAccountsSelector: React.FC<EmailAccountsSelectorProps> = ({
  value = [],
  onChange,
  emailAccounts,
  selectedEmailAccounts = [],
}) => {
  const [selectedAccounts, setSelectedAccounts] = useState<EmailAccountWithWeight[]>(selectedEmailAccounts.length > 0 ? selectedEmailAccounts : value);

  // Update the parent component when selectedAccounts changes
  const triggerChange = (newAccounts: EmailAccountWithWeight[]) => {
    setSelectedAccounts(newAccounts);
    onChange?.(newAccounts);
  };

  // Update selectedAccounts when selectedEmailAccounts changes
  useEffect(() => {
    if (selectedEmailAccounts.length > 0) {
      setSelectedAccounts(selectedEmailAccounts);
    }
  }, [selectedEmailAccounts]);

  // Add a new email account
  const handleAddAccount = (accountId: string) => {
    if (!accountId || selectedAccounts.some(a => a.id === accountId)) {
      return;
    }

    const newAccounts = [...selectedAccounts, { id: accountId, weight: 1 }];
    triggerChange(newAccounts);
  };

  // Remove an email account
  const handleRemoveAccount = (accountId: string) => {
    const newAccounts = selectedAccounts.filter(a => a.id !== accountId);
    triggerChange(newAccounts);
  };

  // Update the weight of an email account
  const handleWeightChange = (accountId: string, weight: number) => {
    const newAccounts = selectedAccounts.map(a =>
      a.id === accountId ? { ...a, weight } : a
    );
    triggerChange(newAccounts);
  };

  // Get email account details by ID
  const getEmailAccountById = (id: string) => {
    return emailAccounts.find(account => account.id === id);
  };

  // Render reputation tag
  const renderReputationTag = (score: number | null) => {
    if (score === null) return <Tag color="default">Unknown</Tag>;
    if (score >= 80) return <Tag color="success">{score}</Tag>;
    if (score >= 60) return <Tag color="processing">{score}</Tag>;
    if (score >= 40) return <Tag color="warning">{score}</Tag>;
    return <Tag color="error">{score}</Tag>;
  };

  // Table columns
  const columns = [
    {
      title: 'Email',
      dataIndex: 'id',
      key: 'email',
      render: (id: string) => {
        const account = getEmailAccountById(id);
        return account ? (
          <div>
            <div>{account.email}</div>
            <div className="text-xs text-gray-500">{account.name}</div>
          </div>
        ) : id;
      },
    },
    {
      title: 'Status',
      dataIndex: 'id',
      key: 'status',
      render: (id: string) => {
        const account = getEmailAccountById(id);
        if (!account) return null;

        return (
          <Tag color={account.status === 'active' ? 'success' : account.status === 'pending' ? 'warning' : 'error'}>
            {account.status}
          </Tag>
        );
      },
    },
    {
      title: 'Reputation',
      dataIndex: 'id',
      key: 'reputation',
      render: (id: string) => {
        const account = getEmailAccountById(id);
        if (!account) return null;

        return renderReputationTag(account.reputationScore || null);
      },
    },
    {
      title: 'Weight',
      dataIndex: 'weight',
      key: 'weight',
      render: (weight: number, record: EmailAccountWithWeight) => (
        <InputNumber
          min={1}
          max={10}
          value={weight}
          onChange={(value) => handleWeightChange(record.id, value || 1)}
        />
      ),
    },
    {
      title: 'Action',
      key: 'action',
      render: (_: any, record: EmailAccountWithWeight) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveAccount(record.id)}
        />
      ),
    },
  ];

  // Filter out already selected accounts
  const availableAccounts = emailAccounts.filter(
    account => !selectedAccounts.some(a => a.id === account.id)
  );

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-2">
        <div>Email Accounts</div>
        <Select
          style={{ width: 300 }}
          placeholder="Add an email account"
          onChange={handleAddAccount}
          value={null}
          disabled={availableAccounts.length === 0}
        >
          {availableAccounts.map((account) => (
            <Select.Option
              key={account.id}
              value={account.id}
            >
              <div className="flex justify-between items-center">
                <div>
                  <div>{account.email}</div>
                  <div className="text-xs text-gray-500">{account.name}</div>
                </div>
                <Space>
                  <Tag color={account.status === 'active' ? 'success' : 'warning'}>
                    {account.status}
                  </Tag>
                  {account.reputationScore !== null && renderReputationTag(account.reputationScore || null)}
                </Space>
              </div>
            </Select.Option>
          ))}
        </Select>
      </div>

      <Table
        dataSource={selectedAccounts}
        columns={columns}
        rowKey="id"
        pagination={false}
        size="small"
        locale={{ emptyText: 'No email accounts selected' }}
      />

      {selectedAccounts.length === 0 && (
        <div className="text-center text-gray-500 mt-4">
          Add at least one email account to send from
        </div>
      )}
    </div>
  );
};

export default EmailAccountsSelector;
