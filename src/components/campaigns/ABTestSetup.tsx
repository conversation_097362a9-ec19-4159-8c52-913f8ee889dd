import { useState } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Slider, 
  Radio, 
  Button, 
  Tabs, 
  Divider, 
  Typography,
  Space
} from 'antd';
import { ExperimentOutlined, BarChartOutlined, RocketOutlined } from '@ant-design/icons';
import dynamic from 'next/dynamic';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// Import the rich text editor dynamically to avoid SSR issues
const RichTextEditor = dynamic(() => import('../RichTextEditor'), { ssr: false });

interface ABTestSetupProps {
  templates: any[];
  emailAccounts: any[];
  onSubmit: (values: any) => void;
  loading: boolean;
}

export default function ABTestSetup({ templates, emailAccounts, onSubmit, loading }: ABTestSetupProps) {
  const [form] = Form.useForm();
  const [testType, setTestType] = useState('subject');
  const [variantAContent, setVariantAContent] = useState('');
  const [variantBContent, setVariantBContent] = useState('');

  const handleSubmit = (values: any) => {
    // Prepare the data for submission
    const testData = {
      ...values,
      variants: [
        {
          name: 'Variant A',
          subject: testType === 'subject' ? values.variantASubject : values.subject,
          content: testType === 'content' ? variantAContent : undefined,
          templateId: testType === 'template' ? values.variantATemplateId : undefined,
          emailAccountId: testType === 'sender' ? values.variantAEmailAccountId : undefined,
        },
        {
          name: 'Variant B',
          subject: testType === 'subject' ? values.variantBSubject : values.subject,
          content: testType === 'content' ? variantBContent : undefined,
          templateId: testType === 'template' ? values.variantBTemplateId : undefined,
          emailAccountId: testType === 'sender' ? values.variantBEmailAccountId : undefined,
        }
      ]
    };

    onSubmit(testData);
  };

  return (
    <Card className="mt-4">
      <div className="flex items-center mb-4">
        <ExperimentOutlined className="text-2xl mr-2 text-blue-500" />
        <Title level={4} className="m-0">A/B Test Setup</Title>
      </div>
      
      <Text className="block mb-4">
        Create an A/B test to compare different versions of your campaign and find out what works best.
      </Text>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          name: 'A/B Test',
          testType: 'subject',
          splitPercent: 50,
          winnerMetric: 'openRate',
        }}
      >
        <Form.Item
          name="name"
          label="Test Name"
          rules={[{ required: true, message: 'Please enter a test name' }]}
        >
          <Input placeholder="Enter a name for this test" />
        </Form.Item>

        <Form.Item
          name="testType"
          label="What do you want to test?"
          rules={[{ required: true }]}
        >
          <Radio.Group 
            onChange={(e) => setTestType(e.target.value)}
            value={testType}
          >
            <Radio.Button value="subject">Subject Line</Radio.Button>
            <Radio.Button value="content">Email Content</Radio.Button>
            <Radio.Button value="template">Email Template</Radio.Button>
            <Radio.Button value="sender">Sender Account</Radio.Button>
          </Radio.Group>
        </Form.Item>

        <Divider />

        {testType === 'subject' && (
          <div>
            <Form.Item
              name="variantASubject"
              label="Variant A Subject Line"
              rules={[{ required: true, message: 'Please enter subject for Variant A' }]}
            >
              <Input placeholder="Enter subject line for Variant A" />
            </Form.Item>

            <Form.Item
              name="variantBSubject"
              label="Variant B Subject Line"
              rules={[{ required: true, message: 'Please enter subject for Variant B' }]}
            >
              <Input placeholder="Enter subject line for Variant B" />
            </Form.Item>
          </div>
        )}

        {testType === 'content' && (
          <div>
            <Form.Item
              name="subject"
              label="Email Subject"
              rules={[{ required: true, message: 'Please enter an email subject' }]}
            >
              <Input placeholder="Enter email subject" />
            </Form.Item>

            <Tabs defaultActiveKey="variantA">
              <TabPane tab="Variant A Content" key="variantA">
                <div className="mb-4">
                  <RichTextEditor
                    value={variantAContent}
                    onChange={setVariantAContent}
                    placeholder="Compose content for Variant A..."
                  />
                </div>
              </TabPane>
              <TabPane tab="Variant B Content" key="variantB">
                <div className="mb-4">
                  <RichTextEditor
                    value={variantBContent}
                    onChange={setVariantBContent}
                    placeholder="Compose content for Variant B..."
                  />
                </div>
              </TabPane>
            </Tabs>
          </div>
        )}

        {testType === 'template' && (
          <div>
            <Form.Item
              name="subject"
              label="Email Subject"
              rules={[{ required: true, message: 'Please enter an email subject' }]}
            >
              <Input placeholder="Enter email subject" />
            </Form.Item>

            <Form.Item
              name="variantATemplateId"
              label="Variant A Template"
              rules={[{ required: true, message: 'Please select a template for Variant A' }]}
            >
              <Select placeholder="Select template for Variant A">
                {templates.map((template) => (
                  <Option key={template.id} value={template.id}>
                    {template.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="variantBTemplateId"
              label="Variant B Template"
              rules={[{ required: true, message: 'Please select a template for Variant B' }]}
            >
              <Select placeholder="Select template for Variant B">
                {templates.map((template) => (
                  <Option key={template.id} value={template.id}>
                    {template.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>
        )}

        {testType === 'sender' && (
          <div>
            <Form.Item
              name="subject"
              label="Email Subject"
              rules={[{ required: true, message: 'Please enter an email subject' }]}
            >
              <Input placeholder="Enter email subject" />
            </Form.Item>

            <Form.Item
              name="content"
              label="Email Content"
              rules={[{ required: true, message: 'Please enter email content' }]}
            >
              <RichTextEditor
                value=""
                onChange={(value) => form.setFieldsValue({ content: value })}
                placeholder="Compose your email..."
              />
            </Form.Item>

            <Form.Item
              name="variantAEmailAccountId"
              label="Variant A Sender"
              rules={[{ required: true, message: 'Please select a sender for Variant A' }]}
            >
              <Select placeholder="Select sender for Variant A">
                {emailAccounts.map((account) => (
                  <Option key={account.id} value={account.id}>
                    {account.name || account.email}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="variantBEmailAccountId"
              label="Variant B Sender"
              rules={[{ required: true, message: 'Please select a sender for Variant B' }]}
            >
              <Select placeholder="Select sender for Variant B">
                {emailAccounts.map((account) => (
                  <Option key={account.id} value={account.id}>
                    {account.name || account.email}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>
        )}

        <Divider />

        <Form.Item
          name="splitPercent"
          label="Traffic Split"
          help="Percentage of recipients who will receive Variant A. The rest will receive Variant B."
        >
          <Slider
            marks={{
              0: '0%',
              25: '25%',
              50: '50%',
              75: '75%',
              100: '100%'
            }}
            defaultValue={50}
          />
        </Form.Item>

        <Form.Item
          name="winnerMetric"
          label="Winner Metric"
          help="How will you determine which variant performed better?"
          rules={[{ required: true }]}
        >
          <Select>
            <Option value="openRate">Open Rate</Option>
            <Option value="clickRate">Click Rate</Option>
            <Option value="replyRate">Reply Rate</Option>
            <Option value="conversionRate">Conversion Rate</Option>
          </Select>
        </Form.Item>

        <div className="flex justify-end mt-4">
          <Button
            type="primary"
            htmlType="submit"
            icon={<RocketOutlined />}
            loading={loading}
          >
            Create A/B Test
          </Button>
        </div>
      </Form>
    </Card>
  );
}
