import { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Progress, 
  Button, 
  Statistic, 
  Row, 
  Col, 
  Divider, 
  Typography,
  Tag,
  Tooltip,
  Empty,
  Spin
} from 'antd';
import { 
  CheckCircleOutlined, 
  TrophyOutlined, 
  ExperimentOutlined,
  Bar<PERSON>hartOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';

const { Title, Text } = Typography;

interface ABTestResultsProps {
  campaignId: string;
  testId: string;
}

export default function ABTestResults({ campaignId, testId }: ABTestResultsProps) {
  // Fetch A/B test data
  const { 
    data: abTest, 
    isLoading,
    refetch
  } = useQuery({
    queryKey: ['abTest', testId],
    queryFn: () => fetch(`/api/campaigns/${campaignId}/ab-test/${testId}`).then(res => res.json()),
  });

  const [winner, setWinner] = useState<any>(null);

  useEffect(() => {
    if (abTest?.variants && abTest.variants.length >= 2) {
      // Determine the winner based on the winnerMetric
      const metric = abTest.winnerMetric || 'openRate';
      
      let bestVariant = abTest.variants[0];
      let bestValue = getMetricValue(bestVariant, metric);
      
      abTest.variants.forEach((variant: any) => {
        const value = getMetricValue(variant, metric);
        if (value > bestValue) {
          bestValue = value;
          bestVariant = variant;
        }
      });
      
      setWinner(bestVariant);
    }
  }, [abTest]);

  const getMetricValue = (variant: any, metric: string) => {
    switch (metric) {
      case 'openRate':
        return variant.recipientCount > 0 ? (variant.opens / variant.recipientCount) * 100 : 0;
      case 'clickRate':
        return variant.recipientCount > 0 ? (variant.clicks / variant.recipientCount) * 100 : 0;
      case 'replyRate':
        return variant.recipientCount > 0 ? (variant.replies / variant.recipientCount) * 100 : 0;
      case 'conversionRate':
        return variant.recipientCount > 0 ? (variant.conversions / variant.recipientCount) * 100 : 0;
      default:
        return 0;
    }
  };

  const getMetricDifference = (variant: any, otherVariant: any, metric: string) => {
    const value1 = getMetricValue(variant, metric);
    const value2 = getMetricValue(otherVariant, metric);
    return value1 - value2;
  };

  const getMetricLabel = (metric: string) => {
    switch (metric) {
      case 'openRate':
        return 'Open Rate';
      case 'clickRate':
        return 'Click Rate';
      case 'replyRate':
        return 'Reply Rate';
      case 'conversionRate':
        return 'Conversion Rate';
      default:
        return metric;
    }
  };

  const columns = [
    {
      title: 'Variant',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <div className="flex items-center">
          {winner && winner.id === record.id && (
            <Tooltip title="Winner">
              <TrophyOutlined className="mr-2 text-yellow-500" />
            </Tooltip>
          )}
          <span>{text}</span>
        </div>
      ),
    },
    {
      title: 'Recipients',
      dataIndex: 'recipientCount',
      key: 'recipientCount',
    },
    {
      title: 'Opens',
      key: 'opens',
      render: (text: string, record: any) => (
        <div>
          <div>{record.opens}</div>
          <div className="text-xs text-gray-500">
            {(record.recipientCount > 0 ? (record.opens / record.recipientCount) * 100 : 0).toFixed(1)}%
          </div>
        </div>
      ),
    },
    {
      title: 'Clicks',
      key: 'clicks',
      render: (text: string, record: any) => (
        <div>
          <div>{record.clicks}</div>
          <div className="text-xs text-gray-500">
            {(record.recipientCount > 0 ? (record.clicks / record.recipientCount) * 100 : 0).toFixed(1)}%
          </div>
        </div>
      ),
    },
    {
      title: 'Replies',
      key: 'replies',
      render: (text: string, record: any) => (
        <div>
          <div>{record.replies}</div>
          <div className="text-xs text-gray-500">
            {(record.recipientCount > 0 ? (record.replies / record.recipientCount) * 100 : 0).toFixed(1)}%
          </div>
        </div>
      ),
    },
    {
      title: 'Conversions',
      key: 'conversions',
      render: (text: string, record: any) => (
        <div>
          <div>{record.conversions}</div>
          <div className="text-xs text-gray-500">
            {(record.recipientCount > 0 ? (record.conversions / record.recipientCount) * 100 : 0).toFixed(1)}%
          </div>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Spin size="large" />
      </div>
    );
  }

  if (!abTest) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="No A/B test data available"
      />
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <ExperimentOutlined className="text-2xl mr-2 text-blue-500" />
            <Title level={4} className="m-0">{abTest.name}</Title>
          </div>
          <Tag color={abTest.status === 'active' ? 'blue' : abTest.status === 'completed' ? 'green' : 'orange'}>
            {abTest.status.charAt(0).toUpperCase() + abTest.status.slice(1)}
          </Tag>
        </div>

        <div className="mb-4">
          <Text type="secondary">
            Testing: <Tag>{getMetricLabel(abTest.testType)}</Tag>
            Winner Metric: <Tag>{getMetricLabel(abTest.winnerMetric)}</Tag>
            Split: <Tag>{abTest.splitPercent}% / {100 - abTest.splitPercent}%</Tag>
          </Text>
        </div>

        {winner && (
          <Card className="mb-4 bg-blue-50 border-blue-200">
            <Row gutter={16} align="middle">
              <Col span={8}>
                <div className="flex items-center">
                  <TrophyOutlined className="text-4xl mr-4 text-yellow-500" />
                  <div>
                    <Title level={5} className="m-0">Winner: {winner.name}</Title>
                    <Text type="secondary">
                      Outperformed by {Math.abs(getMetricDifference(
                        winner, 
                        abTest.variants.find((v: any) => v.id !== winner.id), 
                        abTest.winnerMetric
                      )).toFixed(1)}%
                    </Text>
                  </div>
                </div>
              </Col>
              <Col span={16}>
                <Row gutter={16}>
                  <Col span={6}>
                    <Statistic
                      title="Open Rate"
                      value={getMetricValue(winner, 'openRate')}
                      precision={1}
                      suffix="%"
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Click Rate"
                      value={getMetricValue(winner, 'clickRate')}
                      precision={1}
                      suffix="%"
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Reply Rate"
                      value={getMetricValue(winner, 'replyRate')}
                      precision={1}
                      suffix="%"
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Conversion Rate"
                      value={getMetricValue(winner, 'conversionRate')}
                      precision={1}
                      suffix="%"
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                </Row>
              </Col>
            </Row>
          </Card>
        )}

        <Table
          dataSource={abTest.variants}
          columns={columns}
          rowKey="id"
          pagination={false}
        />

        {abTest.status === 'active' && (
          <div className="flex justify-end mt-4">
            <Button 
              type="primary"
              onClick={() => refetch()}
              icon={<BarChartOutlined />}
            >
              Refresh Results
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
}
