import { useEffect, useCallback } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Select,
  Typography,
  Radio,
  Switch,
} from 'antd';
import { EyeOutlined, MailOutlined } from '@ant-design/icons';
import dynamic from 'next/dynamic';
import EmailAccountsSelector from '@/components/campaigns/EmailAccountsSelector';

const { Text } = Typography;
const { Option } = Select;

// Import the rich text editor dynamically to avoid SSR issues
const RichTextEditor = dynamic(() => import('@/components/RichTextEditor'), { ssr: false });

interface CampaignEditFormProps {
  form: any;
  campaign: any;
  emailAccounts: any[];
  templates: any[];
  useMultipleSenders: boolean;
  setUseMultipleSenders: (value: boolean) => void;
  selectedEmailAccounts: { id: string; weight: number }[];
  setSelectedEmailAccounts: (value: { id: string; weight: number }[]) => void;
  emailContent: string;
  setEmailContent: (value: string) => void;
  contentType: string;
  setContentType: (value: string) => void;
  recipientType: string;
  setRecipientType: (value: string) => void;
  leads: any[];
  leadLists: any[];
  listLeads: any[];
  fetchListLeads: (listId: string) => void;
  onTestEmail: () => void;
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

// Helper function to analyze campaign leads and determine if they're from a list
function analyzeCampaignLeads(campaign: any) {
  if (!campaign || !campaign.leads || campaign.leads.length === 0) {
    return {
      leadIds: [],
      listId: null,
      useList: false
    };
  }

  // Extract all lead IDs
  const leadIds = campaign.leads.map((cl: any) => cl.leadId);

  // Check if campaign has a direct listId property
  if (campaign.listId) {
    console.log('Campaign has direct listId property:', campaign.listId);
    return {
      leadIds,
      listId: campaign.listId,
      useList: true
    };
  }

  // Check if campaign has a recipientType property
  if (campaign.recipientType) {
    console.log('Campaign has recipientType property:', campaign.recipientType);
    return {
      leadIds,
      listId: campaign.recipientType === 'list' ? campaign.leads[0]?.lead?.listId : null,
      useList: campaign.recipientType === 'list'
    };
  }

  // Check if campaign has only one lead and it's not from a list
  if (campaign.leads.length === 1 && !campaign.leads[0].lead?.listId) {
    console.log('Campaign has only one lead and it\'s not from a list');
    return {
      leadIds,
      listId: null,
      useList: false
    };
  }

  // Check if campaign has a specific property indicating it's using individual leads
  if (campaign.useIndividualLeads) {
    console.log('Campaign has useIndividualLeads property');
    return {
      leadIds,
      listId: null,
      useList: false
    };
  }

  // Check if all leads have the same listId
  let allLeadsFromSameList = true;
  let previousListId = null;
  let leadWithoutListCount = 0;

  for (const campaignLead of campaign.leads) {
    if (campaignLead.lead?.listId) {
      if (previousListId === null) {
        previousListId = campaignLead.lead.listId;
      } else if (previousListId !== campaignLead.lead.listId) {
        // Found leads from different lists
        allLeadsFromSameList = false;
        break;
      }
    } else {
      // Found a lead without a listId
      leadWithoutListCount++;
    }
  }

  // If all leads have no listId, use individual leads
  if (leadWithoutListCount === campaign.leads.length) {
    console.log('All leads have no listId, using individual leads');
    return {
      leadIds,
      listId: null,
      useList: false
    };
  }

  // If all leads with a listId have the same listId, use that list
  if (allLeadsFromSameList && previousListId && leadWithoutListCount < campaign.leads.length / 2) {
    console.log('All leads with a listId have the same listId:', previousListId);
    return {
      leadIds,
      listId: previousListId,
      useList: true
    };
  }

  // Check if most leads have the same listId
  const listIdCounts: Record<string, number> = {};
  let maxCount = 0;
  let mostCommonListId = null;

  for (const campaignLead of campaign.leads) {
    if (campaignLead.lead?.listId) {
      const listId = campaignLead.lead.listId;
      listIdCounts[listId] = (listIdCounts[listId] || 0) + 1;

      if (listIdCounts[listId] > maxCount) {
        maxCount = listIdCounts[listId];
        mostCommonListId = listId;
      }
    }
  }

  // If most leads have the same listId, use that
  if (mostCommonListId && maxCount > campaign.leads.length / 2) {
    console.log('Most leads have the same listId:', mostCommonListId);
    return {
      leadIds,
      listId: mostCommonListId,
      useList: true
    };
  }

  // Otherwise, use individual leads
  console.log('Using individual leads as fallback');
  return {
    leadIds,
    listId: null,
    useList: false
  };
}

export default function CampaignEditForm({
  form,
  campaign,
  emailAccounts,
  templates,
  useMultipleSenders,
  setUseMultipleSenders,
  selectedEmailAccounts,
  setSelectedEmailAccounts,
  emailContent,
  setEmailContent,
  contentType,
  setContentType,
  recipientType,
  setRecipientType,
  leads,
  leadLists,
  listLeads,
  fetchListLeads,
  onTestEmail,
  onSubmit,
  onCancel,
}: CampaignEditFormProps) {
  // Set form values when campaign data changes
  useEffect(() => {
    if (campaign) {
      console.log('Setting form values from campaign data:', campaign);

      // Determine if this is a standard or sequence campaign
      const isStandardCampaign = !campaign.steps || campaign.steps.length === 0;

      // Set basic form values
      const formValues: any = {
        name: campaign.name,
        description: campaign.description || '',
      };

      // Set email-specific values based on campaign type
      if (isStandardCampaign) {
        formValues.emailAccountId = campaign.emailAccountId || '';
        formValues.subject = campaign.subject || '';
      } else if (campaign.steps && campaign.steps.length > 0) {
        formValues.emailAccountId = campaign.steps[0].emailAccountId || '';
        formValues.subject = campaign.steps[0].subject || '';
      }

      // Set recipient data
      if (campaign.leads && campaign.leads.length > 0) {
        console.log('Setting recipient data from campaign leads:', campaign.leads.length);
        const leadIds = campaign.leads.map((cl: any) => cl.leadId);

        // Check if leads have a list
        let foundListId = null;
        for (const campaignLead of campaign.leads) {
          if (campaignLead.lead?.listId) {
            foundListId = campaignLead.lead.listId;
            break;
          }
        }

        if (foundListId) {
          console.log('Using list:', foundListId);
          formValues.listId = foundListId;
          fetchListLeads(foundListId);
        } else {
          console.log('Using individual leads:', leadIds.length);
          formValues.leadIds = leadIds;
        }
      }

      // Set form values
      console.log('Setting form values:', formValues);
      form.setFieldsValue(formValues);
    }
  }, [campaign, form, fetchListLeads]);

  // Set email content when campaign data changes
  useEffect(() => {
    if (campaign) {
      // Set content based on campaign type
      const isStandardCampaign = !campaign.steps || campaign.steps.length === 0;

      if (isStandardCampaign && campaign.content) {
        console.log('Setting email content from standard campaign');
        setEmailContent(campaign.content);
      } else if (campaign.steps && campaign.steps.length > 0 && campaign.steps[0].content) {
        console.log('Setting email content from sequence campaign step');
        setEmailContent(campaign.steps[0].content);
      }

      // Set content type based on whether a template is used
      if ((isStandardCampaign && campaign.templateId) ||
          (!isStandardCampaign && campaign.steps && campaign.steps.length > 0 && campaign.steps[0].templateId)) {
        console.log('Setting content type to template');
        setContentType('template');
      } else {
        console.log('Setting content type to write');
        setContentType('write');
      }

      // Set multiple senders if applicable
      const usesMultipleSenders = isStandardCampaign ?
        campaign.useMultipleSenders :
        (campaign.steps && campaign.steps.length > 0 ? campaign.steps[0].useMultipleSenders : false);

      console.log('Setting useMultipleSenders:', usesMultipleSenders);
      setUseMultipleSenders(usesMultipleSenders || false);
    }
  }, [campaign, setEmailContent, setContentType, setUseMultipleSenders]);

  // Set recipient type and leads when campaign data changes
  useEffect(() => {
    if (campaign) {
      console.log('Processing campaign leads');
      console.log('Campaign name:', campaign.name);
      console.log('Campaign leads:', campaign.leads);
      console.log('Campaign recipientType:', campaign.recipientType);
      console.log('Campaign listId:', campaign.listId);

      if (campaign.leads && campaign.leads.length > 0) {
        console.log('First lead:', campaign.leads[0]);
        console.log('Lead has listId?', !!campaign.leads[0].lead?.listId);
        console.log('Lead has campaignId?', !!campaign.leads[0].campaignId);
      }

      // If the campaign has a recipientType, use that
      if (campaign.recipientType) {
        console.log('Using campaign recipientType:', campaign.recipientType);

        if (campaign.recipientType === 'list' && campaign.listId) {
          console.log('Setting recipient type to list with listId:', campaign.listId);
          setRecipientType('list');
          form.setFieldsValue({ listId: campaign.listId });
          fetchListLeads(campaign.listId);
          return;
        } else if (campaign.recipientType === 'individual') {
          console.log('Setting recipient type to individual');
          const leadIds = campaign.leads.map((cl: any) => cl.leadId);
          setRecipientType('individual');
          form.setFieldsValue({ leadIds });
          return;
        }
      }

      // Fallback to analyzing campaign leads if recipientType is not set
      const { leadIds, listId, useList } = analyzeCampaignLeads(campaign);

      console.log('Lead analysis result:', { leadIds, listId, useList });

      // Check if we need to update the form
      const currentRecipientType = recipientType;
      const currentListId = form.getFieldValue('listId');
      const currentLeadIds = form.getFieldValue('leadIds');

      const needToUpdateRecipientType =
        (useList && currentRecipientType !== 'list') ||
        (!useList && leadIds.length > 0 && currentRecipientType !== 'individual');

      const needToUpdateListId =
        useList && listId && currentListId !== listId;

      const needToUpdateLeadIds =
        !useList && leadIds.length > 0 &&
        (!currentLeadIds || currentLeadIds.length !== leadIds.length);

      console.log('Update needed?', {
        needToUpdateRecipientType,
        needToUpdateListId,
        needToUpdateLeadIds
      });

      // Only update if needed to prevent infinite loops
      if (needToUpdateRecipientType) {
        if (useList && listId) {
          console.log('Setting recipient type to list');
          setRecipientType('list');
        } else if (leadIds.length > 0) {
          console.log('Setting recipient type to individual');
          setRecipientType('individual');
        }
      }

      if (needToUpdateListId) {
        console.log('Setting listId:', listId);
        form.setFieldsValue({ listId });
        fetchListLeads(listId);
      }

      if (needToUpdateLeadIds) {
        console.log('Setting leadIds:', leadIds);
        form.setFieldsValue({ leadIds });
      }
    }
  }, [campaign, recipientType, setRecipientType, form, fetchListLeads]);

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onSubmit}
    >
      <div className="space-y-6">
        <Card className="shadow-sm dark:bg-dark-card">
          <div className="space-y-4">
            <Form.Item
              name="name"
              label="Campaign Name"
              rules={[{ required: true, message: 'Please enter a campaign name' }]}
            >
              <Input placeholder="Enter campaign name" />
            </Form.Item>
            <div className="text-xs text-gray-500 mb-2">
              Debug - Campaign Name: {campaign?.name || 'Not set'}
            </div>

            <Form.Item
              name="description"
              label="Description (optional)"
            >
              <Input.TextArea
                placeholder="Enter campaign description"
                rows={3}
              />
            </Form.Item>
            <div className="text-xs text-gray-500 mb-2">
              Debug - Description: {campaign?.description || 'Not set'}
            </div>
          </div>
        </Card>

        <Card title="Email Content" className="shadow-sm dark:bg-dark-card">
          <div className="space-y-4">
            <div className="mb-4">
              <Switch
                checked={useMultipleSenders}
                onChange={setUseMultipleSenders}
                className="mr-2"
              />
              <Text>Use multiple senders (for better deliverability)</Text>
            </div>

            {useMultipleSenders ? (
              <EmailAccountsSelector
                emailAccounts={emailAccounts}
                selectedEmailAccounts={selectedEmailAccounts}
                onChange={setSelectedEmailAccounts}
              />
            ) : (
              <Form.Item
                name="emailAccountId"
                label="Email Account"
                rules={[{ required: !useMultipleSenders, message: 'Please select an email account' }]}
              >
                <Select placeholder="Select email account">
                  {emailAccounts.map((account: any) => (
                    <Option key={account.id} value={account.id}>
                      {account.email} ({account.name})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )}
            <div className="text-xs text-gray-500 mb-2">
              Debug - Email Account ID: {campaign?.emailAccountId || campaign?.steps?.[0]?.emailAccountId || 'Not set'}
            </div>

            <div className="mb-4">
              <Radio.Group
                value={contentType}
                onChange={(e) => setContentType(e.target.value)}
                className="mb-4"
              >
                <Radio.Button value="write">Write Email</Radio.Button>
                <Radio.Button value="template">Use Template</Radio.Button>
              </Radio.Group>
            </div>

            {contentType === 'template' ? (
              <Form.Item
                name="templateId"
                label="Select Template"
                rules={[{ required: contentType === 'template', message: 'Please select a template' }]}
              >
                <Select
                  placeholder="Select a template"
                  onChange={(value) => {
                    const template = templates.find((t: any) => t.id === value);
                    if (template) {
                      setEmailContent(template.content);
                      form.setFieldsValue({ subject: template.subject });
                    }
                  }}
                >
                  {templates.map((template: any) => (
                    <Option key={template.id} value={template.id}>
                      {template.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            ) : null}

            <Form.Item
              name="subject"
              label="Email Subject"
              rules={[{ required: true, message: 'Please enter an email subject' }]}
            >
              <Input placeholder="Enter email subject" />
            </Form.Item>
            <div className="text-xs text-gray-500 mb-2">
              Debug - Subject: {campaign?.subject || campaign?.steps?.[0]?.subject || 'Not set'}
            </div>

            {contentType === 'write' && (
              <div className="mb-4">
                <Text strong>Email Content</Text>
                <div className="mt-2">
                  <RichTextEditor
                    value={emailContent || campaign?.content || campaign?.steps?.[0]?.content || ''}
                    onChange={setEmailContent}
                  />
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  Debug - Content Length: {(campaign?.content || campaign?.steps?.[0]?.content || '').length} characters
                </div>
              </div>
            )}

            <div className="flex justify-end">
              <Button
                type="default"
                onClick={onTestEmail}
                icon={<MailOutlined />}
              >
                Send Test Email
              </Button>
            </div>
          </div>
        </Card>

        <Card title="Recipients" className="shadow-sm dark:bg-dark-card">
          <div className="space-y-4">
            <Radio.Group
              value={recipientType}
              onChange={(e) => setRecipientType(e.target.value)}
              className="mb-4"
            >
              <Radio.Button value="list">Lead List</Radio.Button>
              <Radio.Button value="individual">Individual Leads</Radio.Button>
            </Radio.Group>

            {recipientType === 'list' ? (
              <>
                <Form.Item
                  name="listId"
                  label="Select Lead List"
                  rules={[{ required: recipientType === 'list', message: 'Please select a lead list' }]}
                >
                  <Select
                    placeholder="Select a lead list"
                    onChange={(value) => fetchListLeads(value)}
                  >
                    {leadLists.map((list: any) => (
                      <Option key={list.id} value={list.id}>
                        {list.name} ({list.leadCount || 0} leads)
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <div className="text-xs text-gray-500 mb-2">
                  Debug - List ID: {form.getFieldValue('listId') || campaign?.listId || campaign?.leads?.[0]?.lead?.listId || 'Not set'}
                  <br />
                  Debug - Lead Count from List: {listLeads.length || 0}
                </div>
              </>
            ) : (
              <>
                <Form.Item
                  name="leadIds"
                  label="Select Leads"
                  rules={[{ required: recipientType === 'individual', message: 'Please select at least one lead' }]}
                >
                  <Select
                    mode="multiple"
                    placeholder="Select leads"
                    optionFilterProp="label"
                    className="w-full"
                    filterOption={(input, option) => {
                      if (typeof option?.label === 'string') {
                        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }
                      return false;
                    }}
                  >
                    {leads.map((lead: any) => {
                      const label = `${lead.email} ${lead.firstName ? `(${lead.firstName} ${lead.lastName || ''})` : ''}`;
                      return (
                        <Option key={lead.id} value={lead.id} label={label}>
                          {label}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
                <div className="text-xs text-gray-500 mb-2">
                  Debug - Lead IDs: {(form.getFieldValue('leadIds') || []).length || campaign?.leads?.length || 0} leads selected
                  <br />
                  Debug - First few lead IDs: {JSON.stringify((form.getFieldValue('leadIds') || []).slice(0, 3))}
                </div>
              </>
            )}
          </div>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button
            type="default"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            icon={<EyeOutlined />}
          >
            Preview Campaign
          </Button>
        </div>
      </div>
    </Form>
  );
}
