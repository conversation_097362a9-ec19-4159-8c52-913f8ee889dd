import React, { useState } from 'react';
import { Table, Tag, Progress, Button, Input, Space } from 'antd';
import { SearchOutlined, FilterOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

interface Step {
  id: string;
  name: string;
  type: string;
  position: number;
}

interface StepActivity {
  id: string;
  campaignLeadId: string;
  stepId: string;
  status: string;
  sentAt?: string;
  openedAt?: string;
  clickedAt?: string;
  repliedAt?: string;
  bouncedAt?: string;
  errorMessage?: string;
  step: Step;
}

interface Lead {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
}

interface CampaignLead {
  id: string;
  campaignId: string;
  leadId: string;
  status: string;
  currentStepId: string;
  lead: Lead;
  stepActivities: StepActivity[];
}

interface LeadProgressTrackerProps {
  leads: CampaignLead[];
  steps: Step[];
}

const LeadProgressTracker: React.FC<LeadProgressTrackerProps> = ({ leads = [], steps = [] }) => {
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string[]>([]);

  // Get step type tag
  const getStepTypeTag = (type: string) => {
    switch (type) {
      case 'email':
        return <Tag color="blue">Email</Tag>;
      case 'wait':
        return <Tag color="orange">Wait</Tag>;
      case 'condition':
        return <Tag color="green">Condition</Tag>;
      case 'action':
        return <Tag color="purple">Action</Tag>;
      default:
        return <Tag color="default">{type}</Tag>;
    }
  };

  // Get status tag
  const getStatusTag = (status: string) => {
    switch (status) {
      case 'active':
        return <Tag color="blue">Active</Tag>;
      case 'completed':
        return <Tag color="green">Completed</Tag>;
      case 'unsubscribed':
        return <Tag color="orange">Unsubscribed</Tag>;
      case 'bounced':
        return <Tag color="red">Bounced</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  // Get activity status tag
  const getActivityStatusTag = (status: string) => {
    switch (status) {
      case 'pending':
        return <Tag color="blue">Pending</Tag>;
      case 'completed':
        return <Tag color="green">Completed</Tag>;
      case 'failed':
        return <Tag color="red">Failed</Tag>;
      case 'skipped':
        return <Tag color="orange">Skipped</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  // Filter leads based on search text and status filter
  const filteredLeads = leads.filter(lead => {
    const matchesSearch = searchText === '' ||
      lead.lead.email.toLowerCase().includes(searchText.toLowerCase()) ||
      (lead.lead.firstName && lead.lead.firstName.toLowerCase().includes(searchText.toLowerCase())) ||
      (lead.lead.lastName && lead.lead.lastName.toLowerCase().includes(searchText.toLowerCase()));

    const matchesStatus = statusFilter.length === 0 || statusFilter.includes(lead.status);

    return matchesSearch && matchesStatus;
  });

  // Define table columns
  const columns: ColumnsType<CampaignLead> = [
    {
      title: 'Recipient',
      dataIndex: ['lead', 'email'],
      key: 'email',
      render: (_, record) => (
        <div>
          <div>{record.lead.email}</div>
          {(record.lead.firstName || record.lead.lastName) && (
            <div className="text-xs text-gray-500">
              {[record.lead.firstName, record.lead.lastName].filter(Boolean).join(' ')}
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: 'Active', value: 'active' },
        { text: 'Completed', value: 'completed' },
        { text: 'Unsubscribed', value: 'unsubscribed' },
        { text: 'Bounced', value: 'bounced' },
      ],
      onFilter: (value, record) => record.status === value,
      render: (status) => getStatusTag(status),
    },
    {
      title: 'Current Step',
      key: 'currentStep',
      render: (_, record) => {
        const currentStep = steps.find(step => step.id === record.currentStepId);
        return currentStep ? (
          <div>
            <div>{currentStep.name || `Step ${currentStep.position + 1}`}</div>
            <div className="text-xs text-gray-500">{getStepTypeTag(currentStep.type)}</div>
          </div>
        ) : 'N/A';
      },
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (_, record) => {
        const completedSteps = record.stepActivities.filter(
          activity => activity.status === 'completed'
        ).length;

        const percent = steps.length > 0
          ? Math.round((completedSteps / steps.length) * 100)
          : 0;

        return (
          <div>
            <Progress percent={percent} size="small" />
            <div className="text-xs mt-1">
              {completedSteps} / {steps.length} steps
            </div>
          </div>
        );
      },
    },
    {
      title: 'Last Activity',
      key: 'lastActivity',
      render: (_, record) => {
        const lastActivity = record.stepActivities[0];
        if (!lastActivity) return 'No activity';

        let activityTime = null;
        let activityType = '';

        if (lastActivity.repliedAt) {
          activityTime = new Date(lastActivity.repliedAt);
          activityType = 'Replied';
        } else if (lastActivity.clickedAt) {
          activityTime = new Date(lastActivity.clickedAt);
          activityType = 'Clicked';
        } else if (lastActivity.openedAt) {
          activityTime = new Date(lastActivity.openedAt);
          activityType = 'Opened';
        } else if (lastActivity.sentAt) {
          activityTime = new Date(lastActivity.sentAt);
          activityType = 'Sent';
        } else if (lastActivity.bouncedAt) {
          activityTime = new Date(lastActivity.bouncedAt);
          activityType = 'Bounced';
        }

        return activityTime ? (
          <div>
            <div>{activityType}</div>
            <div className="text-xs text-gray-500">
              {activityTime.toLocaleString()}
            </div>
          </div>
        ) : 'Pending';
      },
    },
  ];

  // Define expandable row render function
  const expandedRowRender = (record: CampaignLead) => (
    <div className="p-4">
      <h4 className="text-lg font-medium mb-4">Activity History</h4>
      <Table
        dataSource={record.stepActivities}
        rowKey="id"
        pagination={false}
        size="small"
        columns={[
          {
            title: 'Step',
            dataIndex: ['step', 'name'],
            key: 'step',
            render: (_, activity) => (
              <div>
                <div>{activity.step.name || `Step ${activity.step.position + 1}`}</div>
                <div className="text-xs text-gray-500">{getStepTypeTag(activity.step.type)}</div>
              </div>
            ),
          },
          {
            title: 'Status',
            dataIndex: 'status',
            key: 'status',
            render: (status) => getActivityStatusTag(status),
          },
          {
            title: 'Sent',
            key: 'sent',
            render: (_, activity) => activity.sentAt ? new Date(activity.sentAt).toLocaleString() : 'N/A',
          },
          {
            title: 'Opened',
            key: 'opened',
            render: (_, activity) => activity.openedAt ? new Date(activity.openedAt).toLocaleString() : 'N/A',
          },
          {
            title: 'Clicked',
            key: 'clicked',
            render: (_, activity) => activity.clickedAt ? new Date(activity.clickedAt).toLocaleString() : 'N/A',
          },
          {
            title: 'Replied',
            key: 'replied',
            render: (_, activity) => activity.repliedAt ? new Date(activity.repliedAt).toLocaleString() : 'N/A',
          },
          {
            title: 'Error',
            key: 'error',
            render: (_, activity) => activity.errorMessage || 'None',
          },
        ]}
      />
    </div>
  );

  return (
    <div>
      <div className="mb-4 flex justify-between">
        <Input
          placeholder="Search by email or name"
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          style={{ width: 300 }}
        />
        <Space>
          <span>Filter by status:</span>
          <Button.Group>
            <Button
              type={statusFilter.includes('active') ? 'primary' : 'default'}
              onClick={() => {
                if (statusFilter.includes('active')) {
                  setStatusFilter(statusFilter.filter(s => s !== 'active'));
                } else {
                  setStatusFilter([...statusFilter, 'active']);
                }
              }}
            >
              Active
            </Button>
            <Button
              type={statusFilter.includes('completed') ? 'primary' : 'default'}
              onClick={() => {
                if (statusFilter.includes('completed')) {
                  setStatusFilter(statusFilter.filter(s => s !== 'completed'));
                } else {
                  setStatusFilter([...statusFilter, 'completed']);
                }
              }}
            >
              Completed
            </Button>
            <Button
              type={statusFilter.includes('unsubscribed') ? 'primary' : 'default'}
              onClick={() => {
                if (statusFilter.includes('unsubscribed')) {
                  setStatusFilter(statusFilter.filter(s => s !== 'unsubscribed'));
                } else {
                  setStatusFilter([...statusFilter, 'unsubscribed']);
                }
              }}
            >
              Unsubscribed
            </Button>
            <Button
              type={statusFilter.includes('bounced') ? 'primary' : 'default'}
              onClick={() => {
                if (statusFilter.includes('bounced')) {
                  setStatusFilter(statusFilter.filter(s => s !== 'bounced'));
                } else {
                  setStatusFilter([...statusFilter, 'bounced']);
                }
              }}
            >
              Bounced
            </Button>
          </Button.Group>
          <Button
            icon={<FilterOutlined />}
            onClick={() => setStatusFilter([])}
            disabled={statusFilter.length === 0}
          >
            Clear Filters
          </Button>
        </Space>
      </div>
      <Table
        dataSource={filteredLeads}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 10 }}
        expandable={{
          expandedRowRender,
        }}
      />
    </div>
  );
};

export default LeadProgressTracker;
