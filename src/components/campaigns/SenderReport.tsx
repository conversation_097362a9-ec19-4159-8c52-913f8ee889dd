import React, { useState, useEffect } from 'react';
import { Table, Card, Statistic, Row, Col, Tag, Spin, Alert, Typography, Divider, Select, Input, Space } from 'antd';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip as RechartsTooltip } from 'recharts';
import { SearchOutlined, FilterOutlined } from '@ant-design/icons';
import api from '@/lib/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface SenderReportProps {
  campaignId: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#8DD1E1'];

const SenderReport: React.FC<SenderReportProps> = ({ campaignId }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<any>(null);
  const [filteredData, setFilteredData] = useState<any[]>([]);
  const [senderFilter, setSenderFilter] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');

  // Define columns for the table
  const columns = [
    {
      title: 'Lead',
      dataIndex: 'leadEmail',
      key: 'leadEmail',
      render: (text: string, record: any) => (
        <div>
          <div>{text}</div>
          {record.leadName && <div className="text-xs text-gray-500">{record.leadName}</div>}
        </div>
      ),
    },
    {
      title: 'Step',
      dataIndex: 'stepName',
      key: 'stepName',
    },
    {
      title: 'Sender',
      dataIndex: 'senderEmail',
      key: 'senderEmail',
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'Sent At',
      dataIndex: 'sentAt',
      key: 'sentAt',
      render: (date: string | null) => date ? new Date(date).toLocaleString() : 'N/A',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = 'default';
        if (status === 'sent') color = 'blue';
        if (status === 'opened') color = 'green';
        if (status === 'clicked') color = 'purple';
        if (status === 'replied') color = 'gold';
        if (status === 'bounced') color = 'red';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: 'Opened',
      dataIndex: 'openedAt',
      key: 'openedAt',
      render: (date: string | null) => date ? new Date(date).toLocaleString() : 'No',
    },
    {
      title: 'Clicked',
      dataIndex: 'clickedAt',
      key: 'clickedAt',
      render: (date: string | null) => date ? new Date(date).toLocaleString() : 'No',
    },
    {
      title: 'Replied',
      dataIndex: 'repliedAt',
      key: 'repliedAt',
      render: (date: string | null) => date ? new Date(date).toLocaleString() : 'No',
    },
  ];

  useEffect(() => {
    const fetchSenderReport = async () => {
      try {
        setLoading(true);
        const response = await api.get(`/campaigns/${campaignId}/sender-report`);
        setReportData(response.data);
        setFilteredData(response.data.senderReport || []);
      } catch (err) {
        console.error('Error fetching sender report:', err);
        setError('Failed to load sender report data');
      } finally {
        setLoading(false);
      }
    };

    if (campaignId) {
      fetchSenderReport();
    }
  }, [campaignId]);

  useEffect(() => {
    if (!reportData) return;

    let filtered = [...(reportData.senderReport || [])];

    // Apply sender filter
    if (senderFilter) {
      filtered = filtered.filter(item => item.senderEmail === senderFilter);
    }

    // Apply search filter
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(
        item =>
          item.leadEmail.toLowerCase().includes(searchLower) ||
          item.leadName.toLowerCase().includes(searchLower) ||
          item.stepName.toLowerCase().includes(searchLower)
      );
    }

    setFilteredData(filtered);
  }, [reportData, senderFilter, searchText]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return <Alert message="Error" description={error} type="error" showIcon />;
  }

  if (!reportData || !reportData.hasData) {
    // Show sample data for demonstration purposes
    return (
      <div className="space-y-6">
        <Alert
          message="No sender data available yet"
          description={
            <div>
              <p>This campaign hasn't sent any emails yet, or the sender information hasn't been recorded.</p>
              <p>Once emails are sent, you'll see detailed information about which leads received emails from which senders.</p>
              <p>Below is a sample of what the report will look like when data is available.</p>
            </div>
          }
          type="info"
          showIcon
          className="mb-6"
        />

        <Card className="dark:bg-dark-card dark:border-dark-border">
          <Title level={4} className="dark:text-white">Sample Sender Distribution</Title>
          <Text className="dark:text-gray-300">
            This is a sample visualization of how emails might be distributed among different senders.
          </Text>

          <Row gutter={24} className="mt-6">
            <Col span={12}>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={[
                      { name: '<EMAIL>', value: 45 },
                      { name: '<EMAIL>', value: 35 },
                      { name: '<EMAIL>', value: 20 },
                    ]}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {[
                      { name: '<EMAIL>', value: 45 },
                      { name: '<EMAIL>', value: 35 },
                      { name: '<EMAIL>', value: 20 },
                    ].map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip formatter={(value, name) => [`${value} emails`, name]} />
                </PieChart>
              </ResponsiveContainer>
            </Col>
            <Col span={12}>
              <div className="grid grid-cols-2 gap-4">
                {[
                  { email: '<EMAIL>', totalSent: 45, opened: 30, clicked: 20, replied: 10 },
                  { email: '<EMAIL>', totalSent: 35, opened: 25, clicked: 15, replied: 8 },
                  { email: '<EMAIL>', totalSent: 20, opened: 15, clicked: 10, replied: 5 },
                ].map((stats: any) => (
                  <Card key={stats.email} size="small" className="dark:bg-dark-content">
                    <Statistic
                      title={<span className="dark:text-gray-300">{stats.email}</span>}
                      value={stats.totalSent}
                      suffix={<span className="dark:text-gray-300">emails</span>}
                      className="dark:text-white"
                    />
                    <div className="mt-2 text-xs dark:text-gray-300">
                      <div>Opened: {stats.opened} ({Math.round((stats.opened / stats.totalSent) * 100)}%)</div>
                      <div>Clicked: {stats.clicked} ({Math.round((stats.clicked / stats.totalSent) * 100)}%)</div>
                      <div>Replied: {stats.replied} ({Math.round((stats.replied / stats.totalSent) * 100)}%)</div>
                    </div>
                  </Card>
                ))}
              </div>
            </Col>
          </Row>
        </Card>

        <Card className="dark:bg-dark-card dark:border-dark-border">
          <Title level={4} className="dark:text-white">Sample Detailed Sender Report</Title>
          <Text className="dark:text-gray-300">
            This table will show which leads received emails from which senders, along with tracking information.
          </Text>

          <Table
            dataSource={[
              {
                leadEmail: '<EMAIL>',
                leadName: 'John Doe',
                stepName: 'Initial Email',
                senderEmail: '<EMAIL>',
                sentAt: new Date().toISOString(),
                status: 'sent',
                openedAt: new Date().toISOString(),
                clickedAt: null,
                repliedAt: null,
              },
              {
                leadEmail: '<EMAIL>',
                leadName: 'Jane Smith',
                stepName: 'Initial Email',
                senderEmail: '<EMAIL>',
                sentAt: new Date().toISOString(),
                status: 'opened',
                openedAt: new Date().toISOString(),
                clickedAt: new Date().toISOString(),
                repliedAt: null,
              },
              {
                leadEmail: '<EMAIL>',
                leadName: 'Bob Johnson',
                stepName: 'Initial Email',
                senderEmail: '<EMAIL>',
                sentAt: new Date().toISOString(),
                status: 'replied',
                openedAt: new Date().toISOString(),
                clickedAt: new Date().toISOString(),
                repliedAt: new Date().toISOString(),
              },
            ]}
            columns={columns}
            rowKey={(record) => `${record.leadEmail}-${record.senderEmail}`}
            pagination={{ pageSize: 10 }}
            className="dark:bg-dark-card dark:text-white"
          />
        </Card>
      </div>
    );
  }

  // Prepare data for pie chart
  const pieData = Object.entries(reportData.senderStats || {}).map(([email, stats]: [string, any]) => ({
    name: email,
    value: stats.totalSent,
  }));

  // Get unique senders
  const uniqueSenders: string[] = Array.from(new Set((reportData.senderReport || []).map((item: any) => item.senderEmail)));

  return (
    <div className="space-y-6">
      <Card className="dark:bg-dark-card dark:border-dark-border">
        <Title level={4} className="dark:text-white">Sender Distribution</Title>
        <Text className="dark:text-gray-300">
          This report shows how emails were distributed among different senders for this campaign.
        </Text>

        <Row gutter={24} className="mt-6">
          <Col span={12}>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  nameKey="name"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {pieData.map((entry: any, index: number) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <RechartsTooltip formatter={(value, name) => [`${value} emails`, name]} />
              </PieChart>
            </ResponsiveContainer>
          </Col>
          <Col span={12}>
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(reportData.senderStats || {}).map(([email, stats]: [string, any]) => (
                <Card key={email} size="small" className="dark:bg-dark-content">
                  <Statistic
                    title={<span className="dark:text-gray-300">{email}</span>}
                    value={stats.totalSent}
                    suffix={<span className="dark:text-gray-300">emails</span>}
                    className="dark:text-white"
                  />
                  <div className="mt-2 text-xs dark:text-gray-300">
                    <div>Opened: {stats.opened} ({Math.round((stats.opened / stats.totalSent) * 100)}%)</div>
                    <div>Clicked: {stats.clicked} ({Math.round((stats.clicked / stats.totalSent) * 100)}%)</div>
                    <div>Replied: {stats.replied} ({Math.round((stats.replied / stats.totalSent) * 100)}%)</div>
                  </div>
                </Card>
              ))}
            </div>
          </Col>
        </Row>
      </Card>

      <Card className="dark:bg-dark-card dark:border-dark-border">
        <div className="flex justify-between items-center mb-4">
          <Title level={4} className="dark:text-white m-0">Detailed Sender Report</Title>
          <Space>
            <Input
              placeholder="Search leads or steps"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className="dark:bg-dark-content dark:text-white dark:border-gray-600"
            />
            <Select
              placeholder="Filter by sender"
              allowClear
              style={{ width: 200 }}
              onChange={(value) => setSenderFilter(value)}
              className="dark:bg-dark-content dark:text-white"
            >
              {uniqueSenders.map((sender: string) => (
                <Option key={sender} value={sender}>{sender}</Option>
              ))}
            </Select>
          </Space>
        </div>

        <Table
          dataSource={filteredData}
          columns={columns}
          rowKey={(record) => `${record.leadId}-${record.stepId}-${record.sentAt}`}
          pagination={{ pageSize: 10 }}
          className="dark:bg-dark-card dark:text-white"
        />

        <div className="mt-4 text-right dark:text-gray-300">
          <Text>
            Showing {filteredData.length} of {reportData.senderReport?.length || 0} emails sent
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default SenderReport;
