import { useState } from 'react';
import Link from 'next/link';

interface Campaign {
  id: string;
  name: string;
  status: 'draft' | 'scheduled' | 'sending' | 'completed';
  sentCount?: number;
  totalRecipients?: number;
  createdAt: string;
}

interface CampaignListProps {
  campaigns: Campaign[];
}

export function CampaignList({ campaigns }: CampaignListProps) {
  // Ensure campaigns is an array
  const campaignsArray = Array.isArray(campaigns) ? campaigns : [];

  if (!campaignsArray.length) {
    return <div className="text-center py-4 dark:text-gray-300">No campaigns found</div>;
  }

  return (
    <div className="space-y-4">
      {campaignsArray.map((campaign) => (
        <div
          key={campaign.id}
          className="bg-white dark:bg-dark-card shadow rounded-lg p-4 transition-colors duration-200"
        >
          <h3 className="font-medium dark:text-white">{campaign.name}</h3>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Status: {campaign.status}
          </div>
          {campaign.sentCount !== undefined && campaign.totalRecipients !== undefined && (
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Progress: {campaign.sentCount}/{campaign.totalRecipients}
            </div>
          )}
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Created: {new Date(campaign.createdAt).toLocaleDateString()}
          </div>
        </div>
      ))}
    </div>
  );
}
