import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';
import Sidebar from './Sidebar';
import ThemeToggle from './ThemeToggle';
import { UpgradeButton } from './UpgradeButton';
import { SubscriptionBanner } from './SubscriptionBanner';
import { useTheme } from '@/contexts/ThemeContext';
import { SubscriptionTier } from '@/config/subscription-plans';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
}

export default function Layout({ children, title }: LayoutProps) {
  const { data: session } = useSession();
  const { isDarkMode } = useTheme();
  const [subscription, setSubscription] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showUpgradeButton, setShowUpgradeButton] = useState(true);
  const [bannerDismissed, setBannerDismissed] = useState(true);

  // Fetch subscription data
  useEffect(() => {
    if (session) {
      fetchSubscriptionData();
    }
  }, [session]);

  // Check if banner is dismissed and set up event listener
  useEffect(() => {
    const checkBannerDismissed = () => {
      const isDismissed = sessionStorage.getItem('subscription_banner_dismissed') === 'true';
      console.log('Layout: Banner dismissed state:', isDismissed);
      setBannerDismissed(isDismissed);

      // Update upgrade button visibility based on subscription and banner state
      if (subscription?.tier === SubscriptionTier.FREE && !isDismissed) {
        setShowUpgradeButton(false);
      } else {
        setShowUpgradeButton(true);
      }
    };

    // Initial check
    checkBannerDismissed();

    // Set up event listener for storage changes
    window.addEventListener('storage', checkBannerDismissed);

    // Custom event for direct communication
    const handleBannerDismiss = () => {
      console.log('Layout: Banner dismiss event received');
      checkBannerDismissed();
    };
    window.addEventListener('subscription-banner-dismissed', handleBannerDismiss);

    return () => {
      window.removeEventListener('storage', checkBannerDismissed);
      window.removeEventListener('subscription-banner-dismissed', handleBannerDismiss);
    };
  }, [subscription]);

  const fetchSubscriptionData = async () => {
    try {
      console.log('Layout: Fetching subscription data...');
      const response = await fetch('/api/subscriptions');

      if (!response.ok) {
        console.error('Layout: Subscription API error:', response.status, response.statusText);
        // For testing purposes, assume FREE tier
        setSubscription({
          tier: SubscriptionTier.FREE
        });

        // Check if banner is dismissed
        if (!bannerDismissed) {
          setShowUpgradeButton(false);
        } else {
          setShowUpgradeButton(true);
        }
        return;
      }

      const data = await response.json();
      console.log('Layout: Subscription data:', data);
      setSubscription(data.subscription);

      // Hide upgrade button for free tier users when banner is not dismissed
      if (data.subscription?.tier === SubscriptionTier.FREE && !bannerDismissed) {
        console.log('Layout: Hiding upgrade button - Free tier with banner not dismissed');
        setShowUpgradeButton(false);
      } else {
        console.log('Layout: Showing upgrade button');
        setShowUpgradeButton(true);
      }
    } catch (error) {
      console.error('Layout: Failed to fetch subscription data:', error);
      // For testing purposes, assume FREE tier
      setSubscription({
        tier: SubscriptionTier.FREE
      });

      // Check if banner is dismissed
      if (!bannerDismissed) {
        setShowUpgradeButton(false);
      } else {
        setShowUpgradeButton(true);
      }
    } finally {
      setLoading(false);
    }
  };

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-light-bg dark:bg-dark-bg transition-colors duration-200">
      <Sidebar />

      <div className="pl-64">
        {/* Subscription Banner */}
        <SubscriptionBanner />

        {/* Header */}
        <header className="bg-white dark:bg-dark-header shadow-sm dark:shadow-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  {title}
                </h1>
              </div>
              <div className="flex items-center space-x-4">
                {showUpgradeButton && <UpgradeButton variant="primary" size="sm" />}
                <ThemeToggle showLabel={true} />
              </div>
            </div>
          </div>
        </header>

        {/* Main content */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {children}
        </main>
      </div>
    </div>
  );
}
