import Link from 'next/link';
import { useSession } from 'next-auth/react';

export default function Navigation() {
  const { data: session } = useSession();

  return (
    <nav className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex space-x-8">
            <Link href="/dashboard" className="flex items-center px-3 py-2">
              Dashboard
            </Link>
            <Link href="/campaigns" className="flex items-center px-3 py-2">
              Campaigns
            </Link>
            <Link href="/leads" className="flex items-center px-3 py-2">
              Leads
            </Link>
            <Link href="/email-accounts" className="flex items-center px-3 py-2">
              Email Accounts
            </Link>
            {session?.user?.role === 'ADMIN' && (
              <Link href="/admin/dashboard" className="flex items-center px-3 py-2">
                Admin
              </Link>
            )}
          </div>
          <div className="flex items-center">
            {session?.user?.email}
          </div>
        </div>
      </div>
    </nav>
  );
}
