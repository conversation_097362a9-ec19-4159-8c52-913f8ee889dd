import { useState } from 'react';
import Link from 'next/link';
import ResendVerificationButton from './email-accounts/ResendVerificationButton';

interface EmailAccount {
  id: string;
  email: string;
  name?: string;
  provider: string;
  status: 'active' | 'inactive' | 'pending';
  lastUsed?: string;
}

interface EmailAccountsListProps {
  accounts?: EmailAccount[];
  isLoading?: boolean;
}

export function EmailAccountsList({ accounts = [], isLoading = false }: EmailAccountsListProps) {
  console.log('EmailAccountsList received accounts:', accounts);
  if (isLoading) {
    return (
      <div className="bg-white dark:bg-dark-card shadow rounded-lg transition-colors duration-200">
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          Loading...
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-dark-card shadow rounded-lg transition-colors duration-200">
      <div className="px-4 py-5 sm:px-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
          Email Accounts
        </h3>
      </div>

      <div className="border-t border-gray-200 dark:border-gray-700">
        {!accounts || accounts.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            No email accounts connected. Add your first email account to get started!
          </div>
        ) : (
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {accounts.map((account) => (
              <li key={account.id} className="px-4 py-4 sm:px-6 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center">
                      <p className="text-sm font-medium text-indigo-600 dark:text-indigo-400 truncate">
                        {account.email}
                      </p>
                      <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        ${account.status === 'active' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' :
                          account.status === 'pending' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400' :
                          'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400'}`}>
                        {account.status.charAt(0).toUpperCase() + account.status.slice(1)}
                      </span>
                    </div>
                    {account.name && (
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        {account.name}
                      </p>
                    )}
                    <div className="mt-1 text-sm text-gray-500 dark:text-gray-400 flex items-center">
                      <span className="capitalize">{account.provider}</span>
                      {account.lastUsed && (
                        <>
                          <span className="mx-1">•</span>
                          <span>Last used: {new Date(account.lastUsed).toLocaleDateString()}</span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Link
                      href={`/email-accounts/${account.id}`}
                      className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 text-sm font-medium"
                    >
                      View
                    </Link>
                    <Link
                      href={`/email-accounts/${account.id}?tab=reputation`}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium"
                    >
                      Reputation
                    </Link>
                    {account.status === 'pending' && (
                      <ResendVerificationButton emailAccountId={account.id} />
                    )}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
