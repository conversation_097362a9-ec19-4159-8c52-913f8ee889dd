interface SwitchProps {
  checked: boolean
  onChange: (checked: boolean) => void
  disabled?: boolean
}

export function Switch({ checked, onChange, disabled = false }: SwitchProps) {
  return (
    <button
      type="button"
      role="switch"
      aria-checked={checked}
      disabled={disabled}
      className={`${checked ? 'bg-indigo-600' : 'bg-gray-200'} 
        relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full 
        cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 
        focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed`}
      onClick={() => !disabled && onChange(!checked)}
    >
      <span className="sr-only">Toggle</span>
      <span
        aria-hidden="true"
        className={`${checked ? 'translate-x-5' : 'translate-x-0'}
          pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform 
          ring-0 transition ease-in-out duration-200`}
      />
    </button>
  )
}