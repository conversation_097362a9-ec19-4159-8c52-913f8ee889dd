import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { CreditCardIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';

interface PaymentMethod {
  id: string;
  brand: string;
  last4: string;
  expMonth: number;
  expYear: number;
  isDefault: boolean;
}

export function PaymentMethods() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [showAddCard, setShowAddCard] = useState(false);

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/payment-methods');
      if (response.ok) {
        const data = await response.json();
        setPaymentMethods(data);
      }
    } catch (error) {
      console.error('Failed to fetch payment methods:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddPaymentMethod = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/payment-methods/setup', {
        method: 'POST',
      });
      
      if (response.ok) {
        const { url } = await response.json();
        if (url) {
          window.location.href = url;
        }
      }
    } catch (error) {
      console.error('Failed to setup payment method:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSetDefault = async (id: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/payment-methods/${id}/default`, {
        method: 'POST',
      });
      
      if (response.ok) {
        // Update the local state to reflect the change
        setPaymentMethods(methods => 
          methods.map(method => ({
            ...method,
            isDefault: method.id === id
          }))
        );
      }
    } catch (error) {
      console.error('Failed to set default payment method:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemovePaymentMethod = async (id: string) => {
    if (!confirm('Are you sure you want to remove this payment method?')) {
      return;
    }
    
    setLoading(true);
    try {
      const response = await fetch(`/api/payment-methods/${id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        // Remove the payment method from the local state
        setPaymentMethods(methods => methods.filter(method => method.id !== id));
      }
    } catch (error) {
      console.error('Failed to remove payment method:', error);
    } finally {
      setLoading(false);
    }
  };

  const getBrandIcon = (brand: string) => {
    // In a real app, you might want to use different icons for different card brands
    return <CreditCardIcon className="h-6 w-6 text-gray-500" />;
  };

  return (
    <div className="bg-white shadow sm:rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-base font-semibold leading-6 text-gray-900">
          Payment Methods
        </h3>
        <div className="mt-2 max-w-xl text-sm text-gray-500">
          <p>
            Manage your credit cards and other payment methods.
          </p>
        </div>
        
        <div className="mt-5 space-y-4">
          {loading && <p className="text-sm text-gray-500">Loading payment methods...</p>}
          
          {!loading && paymentMethods.length === 0 && (
            <p className="text-sm text-gray-500">No payment methods found.</p>
          )}
          
          {!loading && paymentMethods.map((method) => (
            <div 
              key={method.id} 
              className={`flex items-center justify-between p-4 border rounded-md ${method.isDefault ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200'}`}
            >
              <div className="flex items-center space-x-3">
                {getBrandIcon(method.brand)}
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {method.brand.charAt(0).toUpperCase() + method.brand.slice(1)} •••• {method.last4}
                  </p>
                  <p className="text-xs text-gray-500">
                    Expires {method.expMonth}/{method.expYear}
                    {method.isDefault && <span className="ml-2 text-indigo-600 font-medium">Default</span>}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {!method.isDefault && (
                  <button
                    type="button"
                    onClick={() => handleSetDefault(method.id)}
                    disabled={loading}
                    className="text-sm text-indigo-600 hover:text-indigo-900"
                  >
                    Set as default
                  </button>
                )}
                
                <button
                  type="button"
                  onClick={() => handleRemovePaymentMethod(method.id)}
                  disabled={loading}
                  className="text-sm text-red-600 hover:text-red-900"
                >
                  <TrashIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          ))}
          
          <button
            type="button"
            onClick={handleAddPaymentMethod}
            disabled={loading}
            className="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
          >
            <PlusIcon className="-ml-0.5 mr-1.5 h-5 w-5 text-gray-400" />
            Add Payment Method
          </button>
        </div>
      </div>
    </div>
  );
}
