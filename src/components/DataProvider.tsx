import React, { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useQuery } from '@tanstack/react-query';
import { useAppStore } from '@/store';

// Fetch common data (organization, lead lists, email accounts)
async function fetchCommonData() {
  const response = await fetch('/api/common-data');
  if (!response.ok) {
    throw new Error('Failed to fetch common data');
  }
  return response.json();
}

// Note: We're now fetching lead lists and email accounts as part of the common data

export function DataProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const {
    setUser,
    setOrganization,
    setLeadLists,
    setEmailAccounts,
    setIsLoadingOrganization,
    setIsLoadingLeadLists,
    setIsLoadingEmailAccounts,
    resetState,
  } = useAppStore();

  // Reset state when session changes
  useEffect(() => {
    if (status === 'unauthenticated') {
      resetState();
    }
  }, [status, resetState]);

  // Set user from session
  useEffect(() => {
    if (session?.user) {
      setUser(session.user);
    }
  }, [session, setUser]);

  // Fetch common data (organization, lead lists, email accounts)
  const commonDataQuery = useQuery({
    queryKey: ['commonData'],
    queryFn: fetchCommonData,
    enabled: !!session,
  });

  const { isLoading: isLoadingCommonData } = commonDataQuery;

  // Handle success and error separately
  useEffect(() => {
    if (commonDataQuery.data) {
      console.log('Common data fetched successfully:', commonDataQuery.data);
      setOrganization(commonDataQuery.data.organization);
      setLeadLists(commonDataQuery.data.leadLists);
      setEmailAccounts(commonDataQuery.data.emailAccounts);
    }
    if (commonDataQuery.error) {
      console.error('Error fetching common data:', commonDataQuery.error);
    }
  }, [commonDataQuery.data, commonDataQuery.error, setOrganization, setLeadLists, setEmailAccounts]);

  // Update loading state
  useEffect(() => {
    setIsLoadingOrganization(isLoadingCommonData);
    setIsLoadingLeadLists(isLoadingCommonData);
    setIsLoadingEmailAccounts(isLoadingCommonData);
  }, [isLoadingCommonData, setIsLoadingOrganization, setIsLoadingLeadLists, setIsLoadingEmailAccounts]);

  // Note: We're now fetching lead lists and email accounts as part of the common data

  return <>{children}</>;
}
