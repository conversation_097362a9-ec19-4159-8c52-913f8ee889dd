import React from 'react';
import { Switch, Tooltip, Space } from 'antd';
import { BulbOutlined, BulbFilled } from '@ant-design/icons';
import { useTheme } from '@/contexts/ThemeContext';

interface ThemeToggleProps {
  showLabel?: boolean;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ showLabel = false }) => {
  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <Tooltip title={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
      <Space align="center">
        {showLabel && <span className="mr-2">{isDarkMode ? 'Dark' : 'Light'} Mode</span>}
        <Switch
          checked={isDarkMode}
          onChange={toggleTheme}
          checkedChildren={<BulbFilled />}
          unCheckedChildren={<BulbOutlined />}
          className="bg-gray-300 dark:bg-gray-700"
        />
      </Space>
    </Tooltip>
  );
};

export default ThemeToggle;
