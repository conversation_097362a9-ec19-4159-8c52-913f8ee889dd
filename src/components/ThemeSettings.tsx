import React from 'react';
import { Radio, Card, Space, Typography } from 'antd';
import { BulbOutlined, BulbFilled, SettingOutlined } from '@ant-design/icons';
import { useTheme } from '@/contexts/ThemeContext';

const { Title, Text } = Typography;

const ThemeSettings: React.FC = () => {
  const { mode, setMode } = useTheme();

  return (
    <Card 
      title={
        <Space>
          <SettingOutlined />
          <span>Theme Settings</span>
        </Space>
      }
      className="dark:bg-dark-card dark:border-dark-border"
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <div>
          <Title level={5} className="dark:text-gray-200">Theme Mode</Title>
          <Text type="secondary" className="dark:text-gray-400">
            Choose how you want the application to appear
          </Text>
        </div>
        
        <Radio.Group 
          value={mode} 
          onChange={(e) => setMode(e.target.value)}
          className="w-full"
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Radio value="light" className="dark:text-gray-200">
              <Space>
                <BulbOutlined />
                <span>Light</span>
              </Space>
              <div className="text-xs text-gray-500 dark:text-gray-400 ml-6">
                Always use light theme
              </div>
            </Radio>
            
            <Radio value="dark" className="dark:text-gray-200">
              <Space>
                <BulbFilled />
                <span>Dark</span>
              </Space>
              <div className="text-xs text-gray-500 dark:text-gray-400 ml-6">
                Always use dark theme
              </div>
            </Radio>
            
            <Radio value="system" className="dark:text-gray-200">
              <Space>
                <SettingOutlined />
                <span>System</span>
              </Space>
              <div className="text-xs text-gray-500 dark:text-gray-400 ml-6">
                Follow system theme settings
              </div>
            </Radio>
          </Space>
        </Radio.Group>
      </Space>
    </Card>
  );
};

export default ThemeSettings;
