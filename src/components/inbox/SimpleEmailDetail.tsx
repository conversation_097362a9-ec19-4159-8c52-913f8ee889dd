import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>, Button, Tooltip, Tag, Tabs, Spin } from 'antd';
import { RollbackOutlined, MessageOutlined, MailOutlined } from '@ant-design/icons';
import { ReplyBox } from './ReplyBox';
import { InlineReplyBox } from './InlineReplyBox';
import { EmailThread } from './EmailThread';
import axios from 'axios';

const { Title, Text } = Typography;

interface SimpleEmailDetailProps {
  email: any;
}

export const SimpleEmailDetail: React.FC<SimpleEmailDetailProps> = ({ email }) => {
  const [replyVisible, setReplyVisible] = useState(false);
  const [inlineReplyVisible, setInlineReplyVisible] = useState(false);
  const [threadData, setThreadData] = useState<any>(null);
  const [loadingThread, setLoadingThread] = useState(false);
  const htmlIframeRef = useRef<HTMLIFrameElement>(null);
  const textIframeRef = useRef<HTMLIFrameElement>(null);

  // Fetch the email thread when the email changes
  useEffect(() => {
    if (email) {
      fetchEmailThread();
    }
  }, [email]);

  const fetchEmailThread = async () => {
    if (!email || !email.id) return;

    try {
      setLoadingThread(true);
      const response = await axios.get(`/api/inbox/thread/${email.id}`);
      setThreadData(response.data);
    } catch (error) {
      console.error('Error fetching email thread:', error);
    } finally {
      setLoadingThread(false);
    }
  };

  // Function to resize iframe height based on content
  const resizeIframe = (iframe: HTMLIFrameElement | null) => {
    if (!iframe) return;

    try {
      // Wait for iframe to load
      iframe.onload = () => {
        if (iframe.contentWindow) {
          // Get the document height and add some padding
          const height = iframe.contentWindow.document.body.scrollHeight + 30;
          iframe.style.height = `${height}px`;
        }
      };
    } catch (error) {
      console.error('Error resizing iframe:', error);
    }
  };

  // Set up iframe resizing when email changes
  useEffect(() => {
    if (email) {
      if (email.htmlBody) {
        resizeIframe(htmlIframeRef.current);
      } else if (email.textBody) {
        resizeIframe(textIframeRef.current);
      }
    }
  }, [email]);

  try {
    if (!email) {
      return <div className="p-6">No email data available</div>;
    }

    return (
      <div className="p-6 h-full overflow-y-auto relative">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Title level={4} className="mb-0">{email.subject || '(No subject)'}</Title>
            {email.isRepliedTo && (
              <Tooltip title="You have replied to this email">
                <Tag color="green" className="ml-3">
                  <RollbackOutlined className="mr-1" />
                  Replied
                </Tag>
              </Tooltip>
            )}
          </div>
          <Button
            type="primary"
            icon={<RollbackOutlined />}
            onClick={() => {
              setInlineReplyVisible(!inlineReplyVisible);
              if (!inlineReplyVisible) {
                setTimeout(() => {
                  window.scrollTo({
                    top: document.body.scrollHeight,
                    behavior: 'smooth'
                  });
                }, 100);
              }
            }}
          >
            Reply
          </Button>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-6">
          <div className="flex items-center mb-2">
            <Text strong className="w-16">From: </Text>
            <Text>{email.from}</Text>
          </div>
          <div className="flex items-center mb-2">
            <Text strong className="w-16">To: </Text>
            <Text>{email.to}</Text>
          </div>
          <div className="flex items-center">
            <Text strong className="w-16">Date: </Text>
            <Text>{new Date(email.receivedAt).toLocaleString()}</Text>
          </div>
        </div>

        <div className="mb-6">
          {loadingThread ? (
            <div className="flex justify-center items-center py-12">
              <Spin size="large" />
            </div>
          ) : threadData && threadData.thread ? (
            <EmailThread
              thread={threadData.thread}
              originalEmailId={email.id}
              currentUserEmail={email.emailAccount?.email || ''}
            />
          ) : (
            <div className="my-4 border-t pt-4">
              {email.htmlBody ? (
                <div className="p-4 rounded-lg shadow-sm overflow-hidden">
                  {/* Email content iframe to isolate styles and prevent dark mode from affecting it */}
                  <div className="bg-white rounded-lg p-4" style={{ isolation: 'isolate' }}>
                    <iframe
                      ref={htmlIframeRef}
                      srcDoc={`
                        <!DOCTYPE html>
                        <html>
                          <head>
                            <meta charset="utf-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1">
                            <style>
                              /* Reset all styles to ensure email displays as intended */
                              html, body {
                                margin: 0;
                                padding: 0;
                                font-family: Arial, sans-serif;
                                background-color: white;
                                color: #333;
                              }
                              img { max-width: 100%; height: auto; }
                              table { max-width: 100%; }
                              /* Ensure links are visible */
                              a { color: #0066cc; text-decoration: underline; }
                              /* Basic spacing */
                              p { margin: 0.5em 0; }
                              h1, h2, h3, h4, h5, h6 { margin: 0.5em 0; }
                            </style>
                          </head>
                          <body>
                            ${email.htmlBody
                              // Only fix responsive issues but preserve original styling
                              .replace(/width="[^"]*"/g, '') // Remove fixed width attributes
                              .replace(/height="[^"]*"/g, '') // Remove fixed height attributes
                              .replace(/<table/g, '<table style="max-width:100%;border-collapse:collapse;margin:0 auto;"') // Make tables responsive
                              .replace(/<img/g, '<img style="max-width:100%;height:auto;"') // Make images responsive
                            }
                          </body>
                        </html>
                      `}
                      style={{ border: 'none', width: '100%', minHeight: '200px', height: 'auto', backgroundColor: 'white' }}
                      title="Email Content"
                    />
                  </div>
                </div>
              ) : (
                <div className="p-4 rounded-lg shadow-sm overflow-hidden">
                  <div className="bg-white rounded-lg p-4" style={{ isolation: 'isolate' }}>
                    <iframe
                      ref={textIframeRef}
                      srcDoc={`
                        <!DOCTYPE html>
                        <html>
                          <head>
                            <meta charset="utf-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1">
                            <style>
                              /* Reset all styles to ensure email displays as intended */
                              html, body {
                                margin: 0;
                                padding: 0;
                                font-family: monospace;
                                white-space: pre-wrap;
                                color: #333;
                                background-color: white;
                                line-height: 1.5;
                              }
                              /* Ensure links are visible */
                              a { color: #0066cc; text-decoration: underline; }
                            </style>
                          </head>
                          <body>
                            ${email.textBody || 'No content'}
                          </body>
                        </html>
                      `}
                      style={{ border: 'none', width: '100%', minHeight: '200px', height: 'auto', backgroundColor: 'white' }}
                      title="Email Text Content"
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Inline Reply Box */}
        {inlineReplyVisible && (
          <InlineReplyBox
            email={email}
            onClose={() => setInlineReplyVisible(false)}
            onReplySent={() => {
              // Refresh the thread data to show the updated conversation
              fetchEmailThread();
            }}
          />
        )}

        {/* Floating Reply Button */}
        <div className="fixed bottom-8 right-8">
          <Tooltip title="Reply">
            <Button
              type="primary"
              shape="circle"
              size="large"
              icon={<RollbackOutlined />}
              onClick={() => {
                // Toggle inline reply box
                setInlineReplyVisible(!inlineReplyVisible);
                // Scroll to the reply box if it's being opened
                if (!inlineReplyVisible) {
                  setTimeout(() => {
                    window.scrollTo({
                      top: document.body.scrollHeight,
                      behavior: 'smooth'
                    });
                  }, 100);
                }
              }}
            />
          </Tooltip>
        </div>

        {/* Legacy Reply Box Modal - keeping for now as fallback */}
        <ReplyBox
          visible={replyVisible}
          onClose={() => setReplyVisible(false)}
          email={email}
        />
      </div>
    );
  } catch (error) {
    console.error('Error rendering email detail:', error);
    return (
      <div className="p-6">
        <Text type="danger">Error displaying email. Please try again.</Text>
      </div>
    );
  }
};
