import React, { useState } from 'react';
import { Button, Input, Form, message } from 'antd';
import { SendOutlined, CloseOutlined } from '@ant-design/icons';
import axios from 'axios';

const { TextArea } = Input;

interface InlineReplyBoxProps {
  email: any;
  onClose: () => void;
  onReplySent: () => void;
}

export const InlineReplyBox: React.FC<InlineReplyBoxProps> = ({
  email,
  onClose,
  onReplySent
}) => {
  const [form] = Form.useForm();
  const [sending, setSending] = useState(false);

  const handleSubmit = async (values: any) => {
    try {
      setSending(true);

      // Validate email data
      if (!email || !email.emailAccountId) {
        message.error('Missing email account information');
        return;
      }

      if (!email.from) {
        message.error('Missing recipient information');
        return;
      }

      // Prepare the reply data
      const replyData = {
        emailAccountId: email.emailAccountId,
        to: email.from,
        subject: email.subject ? (email.subject.startsWith('Re:') ? email.subject : `Re: ${email.subject}`) : 'Re: (No subject)',
        textContent: values.content,
        inReplyTo: email.messageId || '',
        references: email.references ? `${email.references} ${email.messageId || ''}` : (email.messageId || ''),
      };

      // Send the reply
      const response = await axios.post('/api/inbox/reply', replyData);

      message.success('Reply sent successfully');
      form.resetFields();

      // Wait a moment for the database to update before refreshing the thread view
      setTimeout(() => {
        onReplySent();
        onClose();
      }, 500);
    } catch (error: any) {
      console.error('Error sending reply:', error);
      message.error(error.response?.data?.error || 'Failed to send reply');
    } finally {
      setSending(false);
    }
  };

  return (
    <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mt-4">
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          content: `\n\n\n------ Original Message ------\nFrom: ${email?.from || 'Unknown'}\nTo: ${email?.to || 'Unknown'}\nDate: ${email?.receivedAt ? new Date(email.receivedAt).toLocaleString() : 'Unknown'}\nSubject: ${email?.subject || '(No subject)'}\n\n${email?.textBody ? email.textBody.substring(0, 300) + (email.textBody.length > 300 ? '...' : '') : (email?.htmlBody ? 'HTML content (not displayed)' : 'No content')}`
        }}
      >
        <Form.Item
          name="content"
          rules={[{ required: true, message: 'Please enter your reply' }]}
        >
          <TextArea
            rows={8}
            placeholder="Type your reply here..."
            autoFocus
            className="dark:bg-gray-700 dark:text-gray-200"
          />
        </Form.Item>

        <div className="flex justify-end space-x-2">
          <Button
            onClick={onClose}
            icon={<CloseOutlined />}
            className="dark:text-gray-300 dark:border-gray-600 dark:hover:text-white dark:hover:border-gray-500"
          >
            Cancel
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={sending}
            icon={<SendOutlined />}
          >
            Send
          </Button>
        </div>
      </Form>
    </div>
  );
};
