import React, { useState } from 'react';
import { List, Badge, Tooltip, Typo<PERSON>, Button } from 'antd';
import { MailOutlined, ExclamationCircleOutlined, CheckCircleOutlined, SettingOutlined } from '@ant-design/icons';
import { EnableImapModal } from './EnableImapModal';
import { formatDistanceToNow } from 'date-fns';

const { Text } = Typography;

interface EmailAccount {
  id: string;
  email: string;
  name: string;
  status: string;
  imapEnabled: boolean;
  lastImapSync: string | null;
  _count: {
    receivedEmails: number;
  };
}

interface EmailAccountSidebarProps {
  accounts: EmailAccount[];
  selectedAccountId: string | null;
  onSelectAccount: (accountId: string) => void;
}

export const EmailAccountSidebar: React.FC<EmailAccountSidebarProps> = ({
  accounts,
  selectedAccountId,
  onSelectAccount,
}) => {
  const [imapModalVisible, setImapModalVisible] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<EmailAccount | null>(null);
  const handleOpenImapModal = (account: EmailAccount) => {
    setSelectedAccount(account);
    setImapModalVisible(true);
  };

  const handleImapSuccess = () => {
    // Reload the page to refresh the accounts
    window.location.reload();
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <h2 className="text-lg font-semibold">Email Accounts</h2>
      </div>

      <List
        className="overflow-y-auto flex-1"
        dataSource={accounts}
        renderItem={(account) => {
          const isActive = account.id === selectedAccountId;
          const isVerified = account.status === 'active' || account.status === 'verified';

          return (
            <List.Item
              className={`cursor-pointer transition-colors duration-150 border-b border-gray-100 dark:border-gray-700 ${
                isActive
                  ? 'bg-blue-50 dark:bg-blue-900/20'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700/30'
              } ${!account.imapEnabled ? 'opacity-50' : ''}`}
              onClick={() => onSelectAccount(account.id)}
            >
              <div className="flex items-center w-full px-4 py-2">
                <div className="mr-3">
                  {isVerified ? (
                    <CheckCircleOutlined className="text-green-500" />
                  ) : (
                    <ExclamationCircleOutlined className="text-yellow-500" />
                  )}
                </div>

                <Tooltip title="Configure IMAP">
                  <Button
                    type="text"
                    size="small"
                    icon={<SettingOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenImapModal(account);
                    }}
                    className="absolute right-2 top-2"
                  />
                </Tooltip>

                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-center">
                    <Text
                      strong
                      className={`${isActive ? 'text-blue-600 dark:text-blue-400' : ''}`}
                      ellipsis
                    >
                      {account.name || account.email.split('@')[0]}
                    </Text>

                    <Badge count={account._count.receivedEmails} overflowCount={99} />
                  </div>

                  <Text type="secondary" className="text-xs block" ellipsis>
                    {account.email}
                  </Text>

                  {account.imapEnabled ? (
                    <Text type="secondary" className="text-xs block">
                      {account.lastImapSync
                        ? `Synced ${formatDistanceToNow(new Date(account.lastImapSync))} ago`
                        : 'Never synced'}
                    </Text>
                  ) : (
                    <Text type="secondary" className="text-xs block text-yellow-500">
                      IMAP not enabled
                    </Text>
                  )}
                </div>
              </div>
            </List.Item>
          );
        }}
      />

      <div className="p-4 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400">
        <p>
          <MailOutlined className="mr-1" />
          {accounts.filter(a => a.imapEnabled).length} of {accounts.length} accounts with IMAP enabled
        </p>
      </div>

      {/* IMAP Configuration Modal */}
      {selectedAccount && (
        <EnableImapModal
          visible={imapModalVisible}
          onClose={() => setImapModalVisible(false)}
          emailAccount={selectedAccount}
          onSuccess={handleImapSuccess}
        />
      )}
    </div>
  );
};
