import React from 'react';
import { Typography, Divider, Tag, Avatar, Tooltip } from 'antd';
import { UserOutlined, MailOutlined, CalendarOutlined, RobotOutlined } from '@ant-design/icons';
import { format } from 'date-fns';

const { Text, Title } = Typography;

interface EmailThreadProps {
  thread: any[];
  originalEmailId: string;
  currentUserEmail: string;
}

export const EmailThread: React.FC<EmailThreadProps> = ({
  thread,
  originalEmailId,
  currentUserEmail
}) => {
  if (!thread || thread.length === 0) {
    return <div className="p-4">No emails found in this thread.</div>;
  }

  // Function to safely render HTML content
  const renderEmailContent = (email: any) => {
    if (email.htmlBody) {
      return (
        <div className="bg-white rounded-lg p-4 mt-2" style={{ isolation: 'isolate' }}>
          <iframe
            srcDoc={`
              <!DOCTYPE html>
              <html>
                <head>
                  <meta charset="utf-8">
                  <meta name="viewport" content="width=device-width, initial-scale=1">
                  <style>
                    /* Reset all styles to ensure email displays as intended */
                    html, body {
                      margin: 0;
                      padding: 0;
                      font-family: Arial, sans-serif;
                      background-color: white;
                      color: #333;
                    }
                    img { max-width: 100%; height: auto; }
                    table { max-width: 100%; }
                    /* Ensure links are visible */
                    a { color: #0066cc; text-decoration: underline; }
                    /* Basic spacing */
                    p { margin: 0.5em 0; }
                    h1, h2, h3, h4, h5, h6 { margin: 0.5em 0; }
                  </style>
                </head>
                <body>
                  ${email.htmlBody
                    // Only fix responsive issues but preserve original styling
                    .replace(/width="[^"]*"/g, '') // Remove fixed width attributes
                    .replace(/height="[^"]*"/g, '') // Remove fixed height attributes
                    .replace(/<table/g, '<table style="max-width:100%;border-collapse:collapse;margin:0 auto;"') // Make tables responsive
                    .replace(/<img/g, '<img style="max-width:100%;height:auto;"') // Make images responsive
                  }
                </body>
              </html>
            `}
            style={{ border: 'none', width: '100%', minHeight: '100px', maxHeight: '300px', height: 'auto', backgroundColor: 'white' }}
            title="Email Content"
          />
        </div>
      );
    }

    if (email.textBody) {
      return (
        <div className="bg-white rounded-lg p-4 mt-2" style={{ isolation: 'isolate' }}>
          <iframe
            srcDoc={`
              <!DOCTYPE html>
              <html>
                <head>
                  <meta charset="utf-8">
                  <meta name="viewport" content="width=device-width, initial-scale=1">
                  <style>
                    /* Reset all styles to ensure email displays as intended */
                    html, body {
                      margin: 0;
                      padding: 0;
                      font-family: monospace;
                      white-space: pre-wrap;
                      color: #333;
                      background-color: white;
                      line-height: 1.5;
                    }
                    /* Ensure links are visible */
                    a { color: #0066cc; text-decoration: underline; }
                  </style>
                </head>
                <body>
                  ${email.textBody || 'No content'}
                </body>
              </html>
            `}
            style={{ border: 'none', width: '100%', minHeight: '100px', maxHeight: '300px', height: 'auto', backgroundColor: 'white' }}
            title="Email Text Content"
          />
        </div>
      );
    }

    return <Text type="secondary">No content</Text>;
  };

  // Check if an email is from the current user
  const isFromCurrentUser = (email: any) => {
    // For sent emails, they're always from the current user
    if (email.type === 'sent') return true;

    // For received emails, check if the account matches or the from field contains the email
    return email.emailAccount?.email === currentUserEmail ||
           email.from?.includes(currentUserEmail);
  };

  // Check if this is an agent reply
  const isAgentReply = (email: any) => {
    return email.messageType === 'agent-reply' || email.agent;
  };

  // Get the sender's name from the email
  const getSenderName = (email: any) => {
    // For agent replies, use agent name
    if (isAgentReply(email)) {
      return email.agent?.name || 'AI Agent';
    }

    // Try to extract name from "Name <<EMAIL>>" format
    const fromMatch = email.from?.match(/^"?([^"<]+)"?\s*(?:<[^>]+>)?$/);
    if (fromMatch) {
      return fromMatch[1].trim();
    }

    // If no name found, use the email address
    const emailMatch = email.from?.match(/<([^>]+)>/) || email.from?.match(/([^\s]+@[^\s]+)/);
    return emailMatch ? emailMatch[1] : email.from || 'Unknown';
  };

  return (
    <div className="space-y-6">
      <Title level={4} className="mb-4">Conversation Thread</Title>

      {thread.map((email, index) => {
        const isOriginal = email.id === originalEmailId;
        const fromCurrentUser = isFromCurrentUser(email);
        const isAgent = isAgentReply(email);
        const senderName = getSenderName(email);

        // Determine background color and avatar
        let bgColor = 'bg-gray-50 dark:bg-gray-800';
        let avatarColor = 'bg-gray-400';
        let avatarIcon = <UserOutlined />;

        if (isAgent) {
          bgColor = 'bg-green-50 dark:bg-green-900/20';
          avatarColor = 'bg-green-500';
          avatarIcon = <RobotOutlined />;
        } else if (fromCurrentUser) {
          bgColor = 'bg-blue-50 dark:bg-blue-900/20';
          avatarColor = 'bg-blue-500';
        }

        return (
          <div
            key={email.id}
            className={`p-4 rounded-lg ${isOriginal ? 'border-2 border-blue-500 dark:border-blue-400' : 'border border-gray-200 dark:border-gray-700'} ${bgColor}`}
          >
            <div className="flex items-start">
              <Avatar
                className={`mr-3 ${avatarColor}`}
                icon={avatarIcon}
              />

              <div className="flex-1">
                <div className="flex justify-between items-center mb-1">
                  <div>
                    <Text strong>
                      {isAgent ? senderName : (fromCurrentUser ? 'You' : senderName)}
                    </Text>
                    {isOriginal && (
                      <Tag color="blue" className="ml-2">Current Email</Tag>
                    )}
                    {isAgent && (
                      <Tag color="green" className="ml-2">AI Agent</Tag>
                    )}
                    {email.isRepliedTo && (
                      <Tag color="orange" className="ml-2">Replied</Tag>
                    )}
                    {email.agentReply?.status === 'SENT' && (
                      <Tag color="green" className="ml-2">Sent</Tag>
                    )}
                  </div>
                  <Tooltip title={format(new Date(email.receivedAt), 'PPpp')}>
                    <Text type="secondary" className="text-xs">
                      {format(new Date(email.receivedAt), 'MMM d, yyyy h:mm a')}
                    </Text>
                  </Tooltip>
                </div>

                <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-2">
                  <MailOutlined className="mr-1" />
                  <Text type="secondary" className="mr-3">
                    {isAgent
                      ? `AI Reply to: ${email.to}`
                      : (fromCurrentUser ? `To: ${email.to}` : `From: ${email.from}`)
                    }
                  </Text>
                </div>

                <Text strong className="block mb-2">{email.subject}</Text>

                {renderEmailContent(email)}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};
