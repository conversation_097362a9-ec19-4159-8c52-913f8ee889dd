import React, { useState } from 'react';
import { Modal, Form, Input, Button, Alert, Switch, InputNumber, Typography } from 'antd';
import axios from 'axios';

const { Text } = Typography;

interface EnableImapModalProps {
  visible: boolean;
  onClose: () => void;
  emailAccount: {
    id: string;
    email: string;
    name?: string;
    imapEnabled: boolean;
    imapHost?: string;
    imapPort?: number;
    imapUsername?: string;
  };
  onSuccess: () => void;
}

export const EnableImapModal: React.FC<EnableImapModalProps> = ({
  visible,
  onClose,
  emailAccount,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      setError(null);

      await axios.put(`/api/email-accounts/${emailAccount.id}/imap`, {
        imapEnabled: values.imapEnabled,
        imapHost: values.imapHost,
        imapPort: values.imapPort,
        imapUsername: values.imapUsername,
        imapPassword: values.imapPassword,
      });

      setLoading(false);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error updating IMAP settings:', error);
      setError('Failed to update IMAP settings. Please check your credentials and try again.');
      setLoading(false);
    }
  };

  return (
    <Modal
      title={`IMAP Settings for ${emailAccount.name || emailAccount.email}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          imapEnabled: emailAccount.imapEnabled,
          imapHost: emailAccount.imapHost || '',
          imapPort: emailAccount.imapPort || 993,
          imapUsername: emailAccount.imapUsername || emailAccount.email,
          imapPassword: '',
        }}
        onFinish={handleSubmit}
      >
        <Form.Item
          name="imapEnabled"
          label="Enable IMAP"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name="imapHost"
          label="IMAP Host"
          rules={[{ required: true, message: 'Please enter the IMAP host' }]}
        >
          <Input placeholder="e.g., imap.gmail.com" />
        </Form.Item>

        <Form.Item
          name="imapPort"
          label="IMAP Port"
          rules={[{ required: true, message: 'Please enter the IMAP port' }]}
        >
          <InputNumber min={1} max={65535} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="imapUsername"
          label="IMAP Username"
          rules={[{ required: true, message: 'Please enter the IMAP username' }]}
        >
          <Input placeholder="Usually your email address" />
        </Form.Item>

        <Form.Item
          name="imapPassword"
          label="IMAP Password"
          rules={[{ required: true, message: 'Please enter the IMAP password' }]}
          extra="Your password will be encrypted before being stored."
        >
          <Input.Password placeholder="Enter your IMAP password" />
        </Form.Item>

        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-4">
          <Text type="secondary">
            <p className="text-sm">
              <strong>Note:</strong> IMAP access allows the application to read emails from your inbox. 
              This is required for the inbox feature to work.
            </p>
            <p className="text-sm mt-2">
              For Gmail accounts, you may need to enable "Less secure app access" or create an app password.
            </p>
            <p className="text-sm mt-2">
              Common IMAP settings:
              <br />- Gmail: imap.gmail.com (Port 993)
              <br />- Outlook/Hotmail: outlook.office365.com (Port 993)
              <br />- Yahoo: imap.mail.yahoo.com (Port 993)
            </p>
          </Text>
        </div>

        {error && (
          <Alert
            message="Error"
            description={error}
            type="error"
            showIcon
            className="mb-4"
          />
        )}

        <div className="flex justify-end">
          <Button onClick={onClose} className="mr-2">
            Cancel
          </Button>
          <Button type="primary" htmlType="submit" loading={loading}>
            Save Settings
          </Button>
        </div>
      </Form>
    </Modal>
  );
};
