import React from 'react';
import { Typo<PERSON>, Divider, Tag, <PERSON>, Button, Tooltip } from 'antd';
import { format } from 'date-fns';
import { UserOutlined, MailOutlined, CalendarOutlined, TagOutlined, RollbackOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface EmailDetailProps {
  email: {
    id: string;
    subject: string;
    from: string;
    to: string;
    cc?: string;
    receivedAt: string;
    textBody?: string;
    htmlBody?: string;
    isRepliedTo: boolean;
    emailAccount: {
      id: string;
      email: string;
      name?: string;
    };
    campaign?: {
      id: string;
      name: string;
      status: string;
    };
    lead?: {
      id: string;
      email: string;
      firstName?: string;
      lastName?: string;
    };
    agentReply?: {
      id: string;
      subject: string;
      textContent: string;
      htmlContent?: string;
      status: string;
      sentAt?: string;
    };
  };
}

export const EmailDetail: React.FC<EmailDetailProps> = ({ email }) => {
  if (!email) {
    return <div className="p-6">No email data available</div>;
  }
  // Function to safely render HTML content
  const renderHtmlContent = () => {
    if (email.htmlBody) {
      return (
        <div
          className="email-html-content"
          dangerouslySetInnerHTML={{ __html: email.htmlBody }}
        />
      );
    }

    if (email.textBody) {
      return (
        <div className="email-text-content whitespace-pre-wrap">
          {email.textBody}
        </div>
      );
    }

    return <Text type="secondary">No content</Text>;
  };

  return (
    <div className="p-6 h-full overflow-y-auto">
      <div className="flex justify-between items-start mb-4">
        <Title level={4} className="m-0">
          {email.subject || '(No subject)'}
        </Title>

        <div>
          {email.campaign && (
            <Tag color="blue" className="mr-2">
              <TagOutlined className="mr-1" />
              {email.campaign.name}
            </Tag>
          )}

          {email.isRepliedTo ? (
            <Tag color="green">
              <RollbackOutlined className="mr-1" />
              Replied
            </Tag>
          ) : (
            <Button type="primary" icon={<RollbackOutlined />}>
              Reply
            </Button>
          )}
        </div>
      </div>

      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-6">
        <div className="flex items-center mb-2">
          <UserOutlined className="mr-2 text-gray-500" />
          <Text strong>From:</Text>
          <Text className="ml-2">{email.from}</Text>
        </div>

        <div className="flex items-center mb-2">
          <MailOutlined className="mr-2 text-gray-500" />
          <Text strong>To:</Text>
          <Text className="ml-2">{email.to}</Text>
        </div>

        {email.cc && (
          <div className="flex items-center mb-2">
            <MailOutlined className="mr-2 text-gray-500" />
            <Text strong>CC:</Text>
            <Text className="ml-2">{email.cc}</Text>
          </div>
        )}

        <div className="flex items-center">
          <CalendarOutlined className="mr-2 text-gray-500" />
          <Text strong>Date:</Text>
          <Text className="ml-2">
            {format(new Date(email.receivedAt), 'PPpp')}
          </Text>
        </div>
      </div>

      <div className="email-content mb-6">
        {renderHtmlContent()}
      </div>

      {email.lead && (
        <div className="mb-6">
          <Divider orientation="left">Lead Information</Divider>
          <Card size="small">
            <div className="flex items-center mb-2">
              <UserOutlined className="mr-2 text-gray-500" />
              <Text strong>Name:</Text>
              <Text className="ml-2">
                {email.lead.firstName || email.lead.lastName
                  ? `${email.lead.firstName || ''} ${email.lead.lastName || ''}`
                  : 'Unknown'}
              </Text>
            </div>

            <div className="flex items-center">
              <MailOutlined className="mr-2 text-gray-500" />
              <Text strong>Email:</Text>
              <Text className="ml-2">{email.lead.email}</Text>
            </div>
          </Card>
        </div>
      )}

      {email.agentReply && (
        <div>
          <Divider orientation="left">AI Reply</Divider>
          <Card
            size="small"
            title={email.agentReply.subject}
            extra={
              <Tag color={email.agentReply.status === 'SENT' ? 'green' : 'orange'}>
                {email.agentReply.status}
              </Tag>
            }
          >
            <div className="mb-2">
              {email.agentReply.sentAt && (
                <Text type="secondary">
                  Sent: {format(new Date(email.agentReply.sentAt), 'PPpp')}
                </Text>
              )}
            </div>

            {email.agentReply.htmlContent ? (
              <div
                dangerouslySetInnerHTML={{ __html: email.agentReply.htmlContent }}
              />
            ) : (
              <div className="whitespace-pre-wrap">
                {email.agentReply.textContent}
              </div>
            )}
          </Card>
        </div>
      )}
    </div>
  );
};
