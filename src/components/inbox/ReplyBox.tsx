import React, { useState } from 'react';
import { Button, Input, Form, message, Modal } from 'antd';
import { SendOutlined, CloseOutlined } from '@ant-design/icons';
import axios from 'axios';

const { TextArea } = Input;

interface ReplyBoxProps {
  visible: boolean;
  onClose: () => void;
  email: any;
}

export const ReplyBox: React.FC<ReplyBoxProps> = ({ visible, onClose, email }) => {
  const [form] = Form.useForm();
  const [sending, setSending] = useState(false);

  const handleSubmit = async (values: any) => {
    try {
      setSending(true);

      // Validate email data
      if (!email || !email.emailAccountId) {
        message.error('Missing email account information');
        return;
      }

      if (!email.from) {
        message.error('Missing recipient information');
        return;
      }

      // Prepare the reply data
      const replyData = {
        emailAccountId: email.emailAccountId,
        to: email.from,
        subject: email.subject ? (email.subject.startsWith('Re:') ? email.subject : `Re: ${email.subject}`) : 'Re: (No subject)',
        textContent: values.content,
        inReplyTo: email.messageId || '',
        references: email.references ? `${email.references} ${email.messageId || ''}` : (email.messageId || ''),
      };

      console.log('Sending reply with data:', replyData);

      // Send the reply
      const response = await axios.post('/api/inbox/reply', replyData);

      console.log('Reply sent successfully:', response.data);
      message.success('Reply sent successfully');
      form.resetFields();
      onClose();
    } catch (error: any) {
      console.error('Error sending reply:', error);
      message.error(error.response?.data?.error || 'Failed to send reply');
    } finally {
      setSending(false);
    }
  };

  return (
    <Modal
      title="Reply"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          content: `\n\n\n------ Original Message ------\nFrom: ${email?.from || 'Unknown'}\nTo: ${email?.to || 'Unknown'}\nDate: ${email?.receivedAt ? new Date(email.receivedAt).toLocaleString() : 'Unknown'}\nSubject: ${email?.subject || '(No subject)'}\n\n${email?.textBody || (email?.htmlBody ? 'HTML content (not displayed)' : 'No content')}`
        }}
      >
        <Form.Item
          name="content"
          rules={[{ required: true, message: 'Please enter your reply' }]}
        >
          <TextArea
            rows={12}
            placeholder="Type your reply here..."
            autoFocus
          />
        </Form.Item>

        <div className="flex justify-end space-x-2">
          <Button onClick={onClose} icon={<CloseOutlined />}>
            Cancel
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={sending}
            icon={<SendOutlined />}
          >
            Send
          </Button>
        </div>
      </Form>
    </Modal>
  );
};
