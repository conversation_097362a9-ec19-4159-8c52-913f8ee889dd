import React from 'react';
import { List, Typography, Tag, Pagination, Empty } from 'antd';
import { format, formatDistanceToNow } from 'date-fns';

const { Text, Title } = Typography;

interface Email {
  id: string;
  subject: string;
  from: string;
  receivedAt: string;
  isRepliedTo: boolean;
  isRead: boolean;
  readAt?: string;
  campaign?: {
    id: string;
    name: string;
  };
  lead?: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
  };
}

interface EmailListProps {
  emails: Email[];
  selectedEmailId: string | null;
  onSelectEmail: (emailId: string) => void;
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
  };
  onPageChange: (page: number) => void;
}

export const EmailList: React.FC<EmailListProps> = ({
  emails,
  selectedEmailId,
  onSelectEmail,
  pagination,
  onPageChange,
}) => {
  // Extract sender name from email
  const getSenderName = (from: string) => {
    const match = from.match(/^"?([^"<]+)"?\s*(?:<[^>]+>)?$/);
    return match ? match[1].trim() : from;
  };

  return (
    <div className="h-full flex flex-col">
      {emails.length === 0 ? (
        <Empty
          description="No emails found"
          className="my-8"
        />
      ) : (
        <List
          className="overflow-y-auto flex-1"
          dataSource={emails}
          renderItem={(email) => {
            const isActive = email.id === selectedEmailId;
            const senderName = getSenderName(email.from);

            return (
              <List.Item
                className={`cursor-pointer transition-colors duration-150 border-b border-gray-100 dark:border-gray-700 ${
                  isActive
                    ? 'bg-blue-50 dark:bg-blue-900/20'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-700/30'
                } ${!email.isRead ? 'border-l-4 border-l-blue-500' : ''}`}
                onClick={() => onSelectEmail(email.id)}
              >
                <div className="flex flex-col w-full px-4 py-3">
                  <div className="flex justify-between items-start mb-1">
                    <div className="flex items-center">
                      {!email.isRead && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 flex-shrink-0"></div>
                      )}
                      <Text
                        strong={!email.isRead}
                        className={`${isActive ? 'text-blue-600 dark:text-blue-400' : ''} ${
                          !email.isRead ? 'font-semibold' : ''
                        }`}
                        ellipsis
                      >
                        {senderName}
                      </Text>
                    </div>
                    <Text type="secondary" className="text-xs whitespace-nowrap ml-2">
                      {formatDistanceToNow(new Date(email.receivedAt), { addSuffix: true })}
                    </Text>
                  </div>

                  <Text
                    ellipsis
                    className={`mb-1 ${!email.isRead ? 'font-medium' : ''}`}
                  >
                    {email.subject || '(No subject)'}
                  </Text>

                  <div className="flex items-center">
                    {email.campaign && (
                      <Tag color="blue" className="mr-2">
                        {email.campaign.name}
                      </Tag>
                    )}

                    {email.isRepliedTo && (
                      <Tag color="green">Replied</Tag>
                    )}
                  </div>
                </div>
              </List.Item>
            );
          }}
        />
      )}

      {pagination.totalPages > 1 && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-center">
          <Pagination
            current={pagination.page}
            total={pagination.totalCount}
            pageSize={pagination.limit}
            onChange={onPageChange}
            size="small"
            showSizeChanger={false}
          />
        </div>
      )}
    </div>
  );
};
