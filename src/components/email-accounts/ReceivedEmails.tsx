import { useState } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Typo<PERSON>,
  Space,
  Drawer,
  Tabs,
  Empty,
  Spin,
  Badge,
  Tooltip,
  Select,
  Divider
} from 'antd';
import {
  MailOutlined,
  ReloadOutlined,
  EyeOutlined,
  RobotOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/router';
import { useQuery, useQueryClient } from '@tanstack/react-query';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

interface ReceivedEmailsProps {
  emailAccountId: string;
}

export default function ReceivedEmails({ emailAccountId }: ReceivedEmailsProps) {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedEmail, setSelectedEmail] = useState<any>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [filter, setFilter] = useState<any>({});

  const queryClient = useQueryClient();

  // Fetch campaigns for filtering
  const { data: campaigns = [] } = useQuery({
    queryKey: ['campaigns'],
    queryFn: () => fetch('/api/campaigns').then(res => res.json()),
  });

  // Fetch received emails
  const {
    data: emailsData,
    isLoading,
    refetch
  } = useQuery({
    queryKey: ['receivedEmails', emailAccountId, page, pageSize, filter],
    queryFn: () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pageSize.toString(),
      });

      if (filter.isRepliedTo !== undefined) {
        params.append('isRepliedTo', filter.isRepliedTo.toString());
      }

      if (filter.campaignId) {
        params.append('campaignId', filter.campaignId);
      }

      return fetch(`/api/email-accounts/${emailAccountId}/emails?${params.toString()}`).then(res => res.json());
    },
  });

  const handleViewEmail = (email: any) => {
    setSelectedEmail(email);
    setDrawerVisible(true);
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilter({ ...filter, [key]: value });
    setPage(1); // Reset to first page when filter changes
  };

  const columns = [
    {
      title: 'From',
      dataIndex: 'from',
      key: 'from',
      render: (text: string, record: any) => (
        <div>
          <div>{text}</div>
          {record.lead && (
            <Tag color="blue">
              {record.lead.firstName} {record.lead.lastName}
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: 'Subject',
      dataIndex: 'subject',
      key: 'subject',
    },
    {
      title: 'Campaign',
      dataIndex: ['campaign', 'name'],
      key: 'campaign',
      render: (text: string, record: any) => (
        record.campaign ? <Tag color="purple">{text}</Tag> : '-'
      ),
    },
    {
      title: 'Received',
      dataIndex: 'receivedAt',
      key: 'receivedAt',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: 'Status',
      key: 'status',
      render: (text: string, record: any) => (
        <Space>
          {record.isRepliedTo && (
            <Tag color="green" icon={<CheckCircleOutlined />}>
              Replied
            </Tag>
          )}
          {record.agentReply && (
            <Tag color="blue" icon={<RobotOutlined />}>
              AI Reply
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (text: string, record: any) => (
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => handleViewEmail(record)}
        >
          View
        </Button>
      ),
    },
  ];

  return (
    <div className="space-y-4">
      <Card>
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <MailOutlined className="text-2xl mr-2 text-blue-500" />
            <Title level={4} className="m-0">Received Emails</Title>
          </div>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
          >
            Refresh
          </Button>
        </div>

        <div className="mb-4 flex flex-wrap gap-4">
          <Select
            placeholder="Filter by campaign"
            style={{ width: 200 }}
            allowClear
            onChange={(value) => handleFilterChange('campaignId', value)}
            value={filter.campaignId}
          >
            {campaigns.map((campaign: any) => (
              <Option key={campaign.id} value={campaign.id}>
                {campaign.name}
              </Option>
            ))}
          </Select>

          <Select
            placeholder="Filter by reply status"
            style={{ width: 200 }}
            allowClear
            onChange={(value) => handleFilterChange('isRepliedTo', value)}
            value={filter.isRepliedTo}
          >
            <Option value="true">Replied</Option>
            <Option value="false">Not Replied</Option>
          </Select>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <Spin size="large" />
          </div>
        ) : emailsData?.emails.length > 0 ? (
          <Table
            dataSource={emailsData.emails}
            columns={columns}
            rowKey="id"
            pagination={{
              current: page,
              pageSize,
              total: emailsData.pagination.totalCount,
              onChange: (page, pageSize) => {
                setPage(page);
                setPageSize(pageSize);
              },
              showSizeChanger: true,
              showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
            }}
          />
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No emails found"
          />
        )}
      </Card>

      <Drawer
        title={selectedEmail?.subject}
        placement="right"
        width={600}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {selectedEmail && (
          <Tabs defaultActiveKey="email">
            <TabPane tab="Email" key="email">
              <div className="space-y-4">
                <div>
                  <Text type="secondary">From:</Text>
                  <div>{selectedEmail.from}</div>
                </div>
                <div>
                  <Text type="secondary">To:</Text>
                  <div>{selectedEmail.to}</div>
                </div>
                {selectedEmail.cc && (
                  <div>
                    <Text type="secondary">CC:</Text>
                    <div>{selectedEmail.cc}</div>
                  </div>
                )}
                <div>
                  <Text type="secondary">Received:</Text>
                  <div>{new Date(selectedEmail.receivedAt).toLocaleString()}</div>
                </div>
                <Divider />
                <div>
                  <Text type="secondary">Subject:</Text>
                  <div className="text-lg font-medium">{selectedEmail.subject}</div>
                </div>
                <div>
                  <Text type="secondary">Message:</Text>
                  {selectedEmail.htmlBody ? (
                    <div
                      className="border p-4 rounded mt-2 bg-white"
                      dangerouslySetInnerHTML={{ __html: selectedEmail.htmlBody }}
                    />
                  ) : (
                    <div className="border p-4 rounded mt-2 bg-white whitespace-pre-wrap">
                      {selectedEmail.textBody}
                    </div>
                  )}
                </div>
              </div>
            </TabPane>

            {selectedEmail.agentReply && (
              <TabPane
                tab={
                  <span>
                    <RobotOutlined /> AI Reply
                  </span>
                }
                key="reply"
              >
                <div className="space-y-4">
                  <div>
                    <Text type="secondary">Status:</Text>
                    <div>
                      {selectedEmail.agentReply.status === 'SENT' ? (
                        <Tag color="green" icon={<CheckCircleOutlined />}>Sent</Tag>
                      ) : selectedEmail.agentReply.status === 'FAILED' ? (
                        <Tag color="red" icon={<CloseCircleOutlined />}>Failed</Tag>
                      ) : (
                        <Tag color="blue" icon={<Spin size="small" />}>Pending</Tag>
                      )}
                    </div>
                  </div>
                  {selectedEmail.agentReply.sentAt && (
                    <div>
                      <Text type="secondary">Sent At:</Text>
                      <div>{new Date(selectedEmail.agentReply.sentAt).toLocaleString()}</div>
                    </div>
                  )}
                  <div>
                    <Text type="secondary">Subject:</Text>
                    <div className="text-lg font-medium">{selectedEmail.agentReply.subject}</div>
                  </div>
                  <div>
                    <Text type="secondary">Message:</Text>
                    <div className="border p-4 rounded mt-2 bg-white whitespace-pre-wrap">
                      {selectedEmail.agentReply.textContent}
                    </div>
                  </div>
                </div>
              </TabPane>
            )}

            {selectedEmail.lead && (
              <TabPane
                tab={
                  <span>
                    <UserOutlined /> Lead
                  </span>
                }
                key="lead"
              >
                <div className="space-y-4">
                  <div>
                    <Text type="secondary">Email:</Text>
                    <div>{selectedEmail.lead.email}</div>
                  </div>
                  <div>
                    <Text type="secondary">Name:</Text>
                    <div>
                      {selectedEmail.lead.firstName} {selectedEmail.lead.lastName}
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button
                      type="primary"
                      onClick={() => router.push(`/leads/${selectedEmail.lead.id}`)}
                    >
                      View Lead Details
                    </Button>
                  </div>
                </div>
              </TabPane>
            )}
          </Tabs>
        )}
      </Drawer>
    </div>
  );
}
