import { useState } from 'react';
import { Form, Input, Button, message, Card, Typography, Alert } from 'antd';
import { SendOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface TestEmailSectionProps {
  emailAccountId: string;
}

const TestEmailSection: React.FC<TestEmailSectionProps> = ({ emailAccountId }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const handleSendTestEmail = async (values: any) => {
    setLoading(true);
    setTestResult(null);

    try {
      const response = await fetch('/api/email-accounts/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailAccountId,
          testEmail: values.testEmail,
          subject: values.subject || 'Test Email',
          content: values.content || 'This is a test email to verify your SMTP settings are working correctly.',
          overridePassword: values.overridePassword,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send test email');
      }

      setTestResult({
        success: true,
        message: 'Test email sent successfully! Please check your inbox.',
      });

      message.success('Test email sent successfully!');
      form.resetFields(['subject', 'content']);
    } catch (error: any) {
      setTestResult({
        success: false,
        message: error.message || 'Failed to send test email',
      });
      message.error(error.message || 'Failed to send test email');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title={<Title level={4}>Test Email Connection</Title>} className="mb-6">
      <Text>
        Send a test email to verify your SMTP settings are working correctly.
      </Text>

      {testResult && (
        <Alert
          message={testResult.success ? 'Success' : 'Error'}
          description={testResult.message}
          type={testResult.success ? 'success' : 'error'}
          showIcon
          className="my-4"
        />
      )}

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSendTestEmail}
        className="mt-4"
        initialValues={{
          subject: 'Test Email',
          content: 'This is a test email to verify your SMTP settings are working correctly.',
        }}
      >
        <Form.Item
          name="testEmail"
          label="Recipient Email"
          rules={[
            { required: true, message: 'Please enter a recipient email address' },
            { type: 'email', message: 'Please enter a valid email address' }
          ]}
        >
          <Input placeholder="Enter recipient email address" />
        </Form.Item>

        <Form.Item
          name="overridePassword"
          label="SMTP Password (Optional)"
          help="If your stored password is not working, you can provide it here directly"
        >
          <Input.Password placeholder="Enter SMTP password" />
        </Form.Item>

        <Form.Item
          name="subject"
          label="Subject"
          rules={[{ required: true, message: 'Please enter a subject' }]}
        >
          <Input placeholder="Enter email subject" />
        </Form.Item>

        <Form.Item
          name="content"
          label="Content"
          rules={[{ required: true, message: 'Please enter email content' }]}
        >
          <Input.TextArea
            rows={4}
            placeholder="Enter email content"
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SendOutlined />}
            loading={loading}
          >
            Send Test Email
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default TestEmailSection;
