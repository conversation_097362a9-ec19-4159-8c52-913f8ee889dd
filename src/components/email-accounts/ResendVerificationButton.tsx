import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button, Tooltip, message } from 'antd';
import { MailOutlined, SyncOutlined } from '@ant-design/icons';

interface ResendVerificationButtonProps {
  emailAccountId: string;
}

export default function ResendVerificationButton({ emailAccountId }: ResendVerificationButtonProps) {
  const queryClient = useQueryClient();

  // Resend verification email mutation
  const resendMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/email-accounts/${emailAccountId}/verify`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to resend verification email');
      }

      return response.json();
    },
    onSuccess: () => {
      message.success('Verification email sent successfully');
      queryClient.invalidateQueries({ queryKey: ['emailAccounts'] });
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to resend verification email');
    },
  });

  return (
    <Tooltip title="Resend verification email">
      <Button
        type="text"
        size="small"
        icon={resendMutation.isPending ? <SyncOutlined spin /> : <MailOutlined />}
        onClick={() => resendMutation.mutate()}
        loading={resendMutation.isPending}
        className="text-orange-600 hover:text-orange-900"
      >
        Verify
      </Button>
    </Tooltip>
  );
}
