import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Switch,
  Button,
  message,
  Typography,
  Space,
  Divider,
  Alert,
  Row,
  Col,
} from 'antd';
import { SaveOutlined, EyeOutlined } from '@ant-design/icons';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface SignatureSettingsProps {
  emailAccountId: string;
}

interface SignatureData {
  signatureEnabled: boolean;
  signatureName?: string;
  signatureTitle?: string;
  signatureCompany?: string;
  signaturePhone?: string;
  signatureEmail?: string;
  signatureWebsite?: string;
  signatureCustom?: string;
}

const SignatureSettings: React.FC<SignatureSettingsProps> = ({ emailAccountId }) => {
  const [form] = Form.useForm();
  const [previewMode, setPreviewMode] = useState(false);
  const queryClient = useQueryClient();

  // Fetch current signature settings
  const { data: emailAccount, isLoading } = useQuery({
    queryKey: ['emailAccount', emailAccountId],
    queryFn: async () => {
      const response = await fetch(`/api/email-accounts/${emailAccountId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch email account');
      }
      return response.json();
    },
  });

  // Set form values when data is loaded
  useEffect(() => {
    if (emailAccount) {
      form.setFieldsValue({
        signatureEnabled: emailAccount.signatureEnabled || false,
        signatureName: emailAccount.signatureName || '',
        signatureTitle: emailAccount.signatureTitle || '',
        signatureCompany: emailAccount.signatureCompany || '',
        signaturePhone: emailAccount.signaturePhone || '',
        signatureEmail: emailAccount.signatureEmail || emailAccount.email,
        signatureWebsite: emailAccount.signatureWebsite || '',
        signatureCustom: emailAccount.signatureCustom || '',
      });
    }
  }, [emailAccount, form]);

  // Update signature mutation
  const updateSignatureMutation = useMutation({
    mutationFn: async (values: SignatureData) => {
      const response = await fetch(`/api/email-accounts/${emailAccountId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        throw new Error('Failed to update signature settings');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['emailAccount', emailAccountId] });
      message.success('Signature settings updated successfully');
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to update signature settings');
    },
  });

  const handleSubmit = (values: SignatureData) => {
    updateSignatureMutation.mutate(values);
  };

  // Generate signature preview
  const generateSignaturePreview = (values: any) => {
    if (values.signatureCustom) {
      return values.signatureCustom;
    }

    const parts = [];
    
    if (values.signatureName) {
      parts.push(values.signatureName);
    }
    
    if (values.signatureTitle) {
      parts.push(values.signatureTitle);
    }
    
    if (values.signatureCompany) {
      parts.push(values.signatureCompany);
    }
    
    if (values.signaturePhone) {
      parts.push(`Phone: ${values.signaturePhone}`);
    }
    
    if (values.signatureEmail) {
      parts.push(`Email: ${values.signatureEmail}`);
    }
    
    if (values.signatureWebsite) {
      parts.push(`Website: ${values.signatureWebsite}`);
    }

    return parts.join('\n');
  };

  const watchedValues = Form.useWatch([], form);

  return (
    <div className="space-y-6">
      <Card>
        <Title level={4}>Email Signature Settings</Title>
        <Paragraph type="secondary">
          Configure a custom email signature for this account. When enabled, this signature will be used 
          in AI agent replies instead of the default generated signatures.
        </Paragraph>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          disabled={isLoading}
        >
          <Form.Item
            name="signatureEnabled"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="Enabled"
              unCheckedChildren="Disabled"
            />
            <Text className="ml-2">Enable custom signature</Text>
          </Form.Item>

          {watchedValues?.signatureEnabled && (
            <>
              <Divider />
              
              <Alert
                message="Signature Configuration"
                description="You can either fill in the individual fields below to auto-generate a signature, or use the custom signature field for complete control."
                type="info"
                showIcon
                className="mb-4"
              />

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="signatureName"
                    label="Name"
                    extra="e.g., 'John Smith' or 'Sales Team'"
                  >
                    <Input placeholder="Enter name or team" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="signatureTitle"
                    label="Title/Position"
                    extra="e.g., 'Sales Manager' or 'Customer Success'"
                  >
                    <Input placeholder="Enter job title" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="signatureCompany"
                    label="Company Name"
                    extra="Override default company name"
                  >
                    <Input placeholder="Enter company name" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="signaturePhone"
                    label="Phone Number"
                  >
                    <Input placeholder="Enter phone number" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="signatureEmail"
                    label="Email Address"
                    extra="Defaults to account email"
                  >
                    <Input placeholder="Enter email address" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="signatureWebsite"
                    label="Website"
                  >
                    <Input placeholder="Enter website URL" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="signatureCustom"
                label="Custom Signature"
                extra="Use this field for complete control over the signature format. This will override the auto-generated signature above."
              >
                <TextArea
                  rows={4}
                  placeholder="Enter custom signature text..."
                />
              </Form.Item>

              <Divider />

              <div className="flex justify-between items-center">
                <Button
                  icon={<EyeOutlined />}
                  onClick={() => setPreviewMode(!previewMode)}
                >
                  {previewMode ? 'Hide Preview' : 'Show Preview'}
                </Button>

                <Space>
                  <Button onClick={() => form.resetFields()}>
                    Reset
                  </Button>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    htmlType="submit"
                    loading={updateSignatureMutation.isPending}
                  >
                    Save Signature
                  </Button>
                </Space>
              </div>

              {previewMode && watchedValues && (
                <>
                  <Divider />
                  <Card size="small" title="Signature Preview">
                    <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit' }}>
                      {generateSignaturePreview(watchedValues) || 'No signature content'}
                    </pre>
                  </Card>
                </>
              )}
            </>
          )}
        </Form>
      </Card>
    </div>
  );
};

export default SignatureSettings;
