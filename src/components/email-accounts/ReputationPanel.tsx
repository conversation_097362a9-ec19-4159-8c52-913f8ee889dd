import { useState } from 'react';
import {
  Card,
  Button,
  Progress,
  Descriptions,
  Tag,
  Tooltip,
  Alert,
  Divider,
  Typography,
  Spin,
  Empty,
  Modal,
  Form,
  InputNumber,
  Select,
  Switch
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  QuestionCircleOutlined,
  RocketOutlined,
  Bar<PERSON><PERSON>Outlined,
  HistoryOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useMutation, useQuery } from '@tanstack/react-query';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface ReputationPanelProps {
  emailAccountId: string;
  emailAddress: string;
}

export default function ReputationPanel({ emailAccountId, emailAddress }: ReputationPanelProps) {
  const [isWarmupModalVisible, setIsWarmupModalVisible] = useState(false);
  const [form] = Form.useForm();

  // Fetch reputation data
  const {
    data: reputationData,
    isLoading: loadingReputation,
    refetch: refetchReputation
  } = useQuery({
    queryKey: ['emailReputation', emailAccountId],
    queryFn: () => fetch(`/api/email-accounts/${emailAccountId}/reputation`).then(res => res.json()),
  });

  // Get recommendations from reputation data
  const recommendations = reputationData?.recommendations || [];

  // Fetch warmup status
  const {
    data: warmupData,
    isLoading: loadingWarmup,
    refetch: refetchWarmup
  } = useQuery({
    queryKey: ['emailWarmup', emailAccountId],
    queryFn: () => fetch(`/api/email-accounts/${emailAccountId}/warmup`).then(res => res.json()),
  });

  // Check reputation mutation
  const checkReputationMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/email-accounts/${emailAccountId}/reputation/check`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to check reputation');
      }

      return response.json();
    },
    onSuccess: () => {
      refetchReputation();
    },
  });

  // Start/stop warmup mutation
  const warmupMutation = useMutation({
    mutationFn: async (values: any) => {
      const response = await fetch(`/api/email-accounts/${emailAccountId}/warmup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        throw new Error('Failed to update warmup settings');
      }

      return response.json();
    },
    onSuccess: () => {
      setIsWarmupModalVisible(false);
      refetchWarmup();
    },
  });

  const getReputationColor = (score: number) => {
    if (score >= 80) return 'green';
    if (score >= 60) return 'blue';
    if (score >= 40) return 'orange';
    return 'red';
  };

  const getWarmupStatusTag = (status: string) => {
    switch (status) {
      case 'IN_PROGRESS':
        return <Tag color="blue">In Progress</Tag>;
      case 'COMPLETED':
        return <Tag color="green">Completed</Tag>;
      case 'FAILED':
        return <Tag color="red">Failed</Tag>;
      default:
        return <Tag color="default">Not Started</Tag>;
    }
  };

  const handleWarmupSubmit = (values: any) => {
    warmupMutation.mutate({
      ...values,
      enabled: true,
    });
  };

  const handleStopWarmup = () => {
    warmupMutation.mutate({
      enabled: false,
    });
  };

  return (
    <div className="space-y-6">
      {/* Reputation Card */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <BarChartOutlined className="text-2xl mr-2 text-blue-500" />
            <Title level={4} className="m-0">Email Reputation</Title>
          </div>
          <Button
            type="primary"
            size="large"
            onClick={() => checkReputationMutation.mutate()}
            loading={checkReputationMutation.isPending}
            icon={<HistoryOutlined />}
          >
            Check Now
          </Button>
        </div>

        <div className="mb-4 text-gray-500 dark:text-gray-400 text-sm">
          <p>Reputation is checked automatically every week. Click "Check Now" for an immediate assessment.</p>
        </div>

        {loadingReputation ? (
          <div className="flex justify-center py-8">
            <Spin size="large" />
          </div>
        ) : reputationData?.score ? (
          <div>
            <div className="flex justify-center mb-4">
              <Progress
                type="dashboard"
                percent={reputationData.score}
                format={(percent) => `${percent?.toFixed(1)}`}
                strokeColor={getReputationColor(reputationData.score)}
                width={120}
              />
            </div>

            <Descriptions bordered size="small" column={1}>
              <Descriptions.Item label="Last Checked">
                {reputationData.lastChecked ? new Date(reputationData.lastChecked).toLocaleString() : 'Never'}
              </Descriptions.Item>
              <Descriptions.Item label="Provider">
                {reputationData.provider || 'Unknown'}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                {reputationData.score >= 80 ? (
                  <Tag color="green">Excellent</Tag>
                ) : reputationData.score >= 60 ? (
                  <Tag color="blue">Good</Tag>
                ) : reputationData.score >= 40 ? (
                  <Tag color="orange">Fair</Tag>
                ) : (
                  <Tag color="red">Poor</Tag>
                )}
              </Descriptions.Item>
            </Descriptions>

            {reputationData.score < 60 && (
              <Alert
                message="Reputation Needs Improvement"
                description="Your email reputation is below recommended levels. Consider using the email warmup feature to improve deliverability."
                type="warning"
                showIcon
                className="mt-4"
                action={
                  <Button
                    size="small"
                    type="primary"
                    onClick={() => setIsWarmupModalVisible(true)}
                  >
                    Start Warmup
                  </Button>
                }
              />
            )}

            {/* Recommendations Section */}
            {recommendations.length > 0 && (
              <div className="mt-4">
                <Divider orientation="left">Recommendations</Divider>
                <ul className="space-y-2">
                  {recommendations.map((rec, index) => (
                    <li key={index} className="flex items-start">
                      {rec.priority === 'HIGH' ? (
                        <Tag color="red" className="mt-1 mr-2">High</Tag>
                      ) : rec.priority === 'MEDIUM' ? (
                        <Tag color="orange" className="mt-1 mr-2">Medium</Tag>
                      ) : (
                        <Tag color="blue" className="mt-1 mr-2">Low</Tag>
                      )}
                      <div>
                        <div className="font-medium">{rec.message}</div>
                        {rec.actionUrl && (
                          <a href={rec.actionUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 text-sm">
                            {rec.action === 'DELIST' ? 'Check blacklist status' :
                             rec.action === 'CONFIGURE_SPF' ? 'Configure SPF record' :
                             rec.action === 'CONFIGURE_DKIM' ? 'Configure DKIM signing' :
                             rec.action === 'CONFIGURE_DMARC' ? 'Configure DMARC policy' :
                             rec.action === 'CLEAN_LIST' ? 'Clean your email list' :
                             rec.action === 'IMPROVE_CONTENT' ? 'Improve email content' :
                             rec.action === 'START_WARMUP' ? 'Start email warmup' :
                             'Learn more'}
                          </a>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <span>
                No reputation data available. Click "Check Now" to analyze your email reputation.
              </span>
            }
          />
        )}
      </Card>

      {/* Warmup Card */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <RocketOutlined className="text-2xl mr-2 text-blue-500" />
            <Title level={4} className="m-0">Email Warmup</Title>
          </div>
          <div>
            {warmupData?.status === 'IN_PROGRESS' ? (
              <Button
                danger
                onClick={handleStopWarmup}
                loading={warmupMutation.isPending}
              >
                Stop Warmup
              </Button>
            ) : (
              <Button
                type="primary"
                icon={<RocketOutlined />}
                onClick={() => setIsWarmupModalVisible(true)}
              >
                Start Warmup
              </Button>
            )}
          </div>
        </div>

        {loadingWarmup ? (
          <div className="flex justify-center py-8">
            <Spin size="large" />
          </div>
        ) : warmupData?.status ? (
          <div>
            <Descriptions bordered size="small" column={1}>
              <Descriptions.Item label="Status">
                {getWarmupStatusTag(warmupData.status)}
              </Descriptions.Item>
              {warmupData.startedAt && (
                <Descriptions.Item label="Started">
                  {new Date(warmupData.startedAt).toLocaleString()}
                </Descriptions.Item>
              )}
              {warmupData.completedAt && (
                <Descriptions.Item label="Completed">
                  {new Date(warmupData.completedAt).toLocaleString()}
                </Descriptions.Item>
              )}
              {warmupData.emailsSent > 0 && (
                <Descriptions.Item label="Emails Sent">
                  {warmupData.emailsSent}
                </Descriptions.Item>
              )}
              {warmupData.emailsOpened > 0 && (
                <Descriptions.Item label="Emails Opened">
                  {warmupData.emailsOpened}
                </Descriptions.Item>
              )}
              {warmupData.emailsReplied > 0 && (
                <Descriptions.Item label="Emails Replied">
                  {warmupData.emailsReplied}
                </Descriptions.Item>
              )}
            </Descriptions>

            {warmupData.status === 'IN_PROGRESS' && (
              <div className="mt-4">
                <Progress
                  percent={warmupData.progress || 0}
                  status="active"
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
                <Text type="secondary" className="block mt-2">
                  Warmup is in progress. This process typically takes 2-4 weeks for optimal results.
                </Text>
              </div>
            )}

            {warmupData.status === 'COMPLETED' && (
              <Alert
                message="Warmup Completed"
                description="Your email account has been successfully warmed up. Your deliverability should now be improved."
                type="success"
                showIcon
                className="mt-4"
              />
            )}

            {warmupData.status === 'FAILED' && (
              <Alert
                message="Warmup Failed"
                description={warmupData.error || "There was an issue with the warmup process. Please try again."}
                type="error"
                showIcon
                className="mt-4"
                action={
                  <Button
                    size="small"
                    type="primary"
                    onClick={() => setIsWarmupModalVisible(true)}
                  >
                    Retry
                  </Button>
                }
              />
            )}
          </div>
        ) : (
          <div>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <span>
                  No warmup activity. Start the warmup process to improve your email deliverability.
                </span>
              }
            />
            <Divider />
            <Paragraph>
              <Title level={5}>What is email warmup?</Title>
              Email warmup is the process of gradually increasing the volume of emails sent from a new email account to establish a positive sending reputation. This helps:
              <ul className="list-disc pl-5 mt-2">
                <li>Improve deliverability rates</li>
                <li>Reduce the chance of emails going to spam</li>
                <li>Build sender reputation with email providers</li>
                <li>Increase open and click-through rates</li>
              </ul>
            </Paragraph>
          </div>
        )}
      </Card>

      {/* Warmup Modal */}
      <Modal
        title="Email Warmup Settings"
        open={isWarmupModalVisible}
        onCancel={() => setIsWarmupModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleWarmupSubmit}
          initialValues={{
            dailyIncrement: 5,
            maxDailyEmails: 40,
            warmupDuration: 14,
            includeWeekends: true,
          }}
        >
          <Form.Item
            name="dailyIncrement"
            label="Daily Increment"
            help="Number of additional emails to send each day"
            rules={[{ required: true, message: 'Please enter daily increment' }]}
          >
            <InputNumber min={1} max={10} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="maxDailyEmails"
            label="Maximum Daily Emails"
            help="Maximum number of emails to send per day at peak"
            rules={[{ required: true, message: 'Please enter maximum daily emails' }]}
          >
            <InputNumber min={10} max={100} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="warmupDuration"
            label="Warmup Duration (days)"
            help="Total duration of the warmup process"
            rules={[{ required: true, message: 'Please enter warmup duration' }]}
          >
            <InputNumber min={7} max={30} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="includeWeekends"
            label="Include Weekends"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <div className="flex justify-end mt-4">
            <Button
              onClick={() => setIsWarmupModalVisible(false)}
              className="mr-2"
            >
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={warmupMutation.isPending}
              icon={<RocketOutlined />}
            >
              Start Warmup
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
}
