import { useState } from 'react';
import {
  Card,
  Form,
  Select,
  Switch,
  InputNumber,
  Input,
  Button,
  Alert,
  Typography,
  Divider,
  Space,
  Badge,
  Spin,
  Tooltip,
} from 'antd';
import {
  RobotOutlined,
  SaveOutlined,
  InfoCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface AgentSettingsProps {
  emailAccountId: string;
}

export default function AgentSettings({ emailAccountId }: AgentSettingsProps) {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);

  // Fetch available agents
  const { data: agents } = useQuery({
    queryKey: ['agents'],
    queryFn: () => fetch('/api/agents').then(res => res.json()),
  });

  // Fetch current agent configuration
  const {
    data: agentConfig,
    isLoading,
    refetch
  } = useQuery({
    queryKey: ['emailAccountAgent', emailAccountId],
    queryFn: () => fetch(`/api/email-accounts/${emailAccountId}/agent`).then(res => res.json()),
  });

  // Enable/configure agent mutation
  const enableMutation = useMutation({
    mutationFn: async (values: any) => {
      const response = await fetch(`/api/email-accounts/${emailAccountId}/agent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to configure AI agent');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['emailAccountAgent', emailAccountId] });
      setIsEditing(false);
    },
  });

  // Update agent configuration mutation
  const updateMutation = useMutation({
    mutationFn: async (values: any) => {
      const response = await fetch(`/api/email-accounts/${emailAccountId}/agent`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update AI agent configuration');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['emailAccountAgent', emailAccountId] });
      setIsEditing(false);
    },
  });

  // Disable agent mutation
  const disableMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/email-accounts/${emailAccountId}/agent`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to disable AI agent');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['emailAccountAgent', emailAccountId] });
    },
  });

  const handleSubmit = (values: any) => {
    if (agentConfig?.aiAgentEnabled) {
      updateMutation.mutate(values);
    } else {
      enableMutation.mutate(values);
    }
  };

  const handleDisable = () => {
    if (confirm('Are you sure you want to disable the AI agent? This will stop automatic email replies.')) {
      disableMutation.mutate();
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Card>
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <RobotOutlined className="text-2xl mr-2 text-blue-500" />
          <Title level={4} className="m-0">AI Agent Settings</Title>
        </div>
        {agentConfig?.aiAgentEnabled ? (
          <Badge status="success" text="Enabled" />
        ) : (
          <Badge status="default" text="Disabled" />
        )}
      </div>

      {(enableMutation.isError || updateMutation.isError) && (
        <Alert
          message="Failed to configure AI agent"
          description={
            (enableMutation.error instanceof Error ? enableMutation.error.message : '') ||
            (updateMutation.error instanceof Error ? updateMutation.error.message : 'Unknown error')
          }
          type="error"
          showIcon
          className="mb-4"
        />
      )}

      {!isEditing && !agentConfig?.aiAgentEnabled ? (
        <div>
          <Paragraph>
            Enable an AI agent to automatically respond to incoming emails. The AI agent can use your knowledge base
            to provide accurate and contextual responses to customer inquiries.
          </Paragraph>
          <Alert
            message="Prerequisites & Features"
            description={
              <div>
                <p>• IMAP must be enabled for this email account</p>
                <p>• At least one AI agent must be created</p>
                <p>• Knowledge base content is recommended for better responses</p>
                <p>• Calendar integration enables automatic appointment scheduling</p>
                <p>• AI agents can automatically update lead status based on email interactions</p>
              </div>
            }
            type="info"
            showIcon
            className="mb-4"
          />
          <Button
            type="primary"
            onClick={() => setIsEditing(true)}
            disabled={!agents?.length}
          >
            {agents?.length ? 'Configure AI Agent' : 'No Agents Available'}
          </Button>
        </div>
      ) : !isEditing ? (
        <div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <Text type="secondary">AI Agent</Text>
              <div className="text-lg">{agentConfig?.agent?.name || 'None'}</div>
            </div>
            <div>
              <Text type="secondary">Auto Reply</Text>
              <div className="text-lg">
                {agentConfig?.aiReplyEnabled ? 'Enabled' : 'Disabled'}
              </div>
            </div>
            <div>
              <Text type="secondary">Reply Delay</Text>
              <div className="text-lg">
                {agentConfig?.aiReplyDelay || 0} minutes
              </div>
            </div>
            <div>
              <Text type="secondary">Knowledge Bases</Text>
              <div className="text-lg">
                {agentConfig?.agent?.knowledgeBases?.length || 0} connected
              </div>
            </div>
            <div>
              <Text type="secondary">Calendar Integration</Text>
              <div className="text-lg">
                {agentConfig?.agent?.calendarEnabled ?
                  `${agentConfig.agent.calendarProvider} (Enabled)` :
                  'Disabled'
                }
              </div>
            </div>
            <div>
              <Text type="secondary">Appointment Scheduling</Text>
              <div className="text-lg">
                {agentConfig?.agent?.capabilities?.includes('appointment_scheduling') ?
                  'Enabled' :
                  'Disabled'
                }
              </div>
            </div>
          </div>

          {agentConfig?.aiReplyPrompt && (
            <div className="mb-4">
              <Text type="secondary">Custom Prompt</Text>
              <div className="mt-1 p-3 bg-gray-50 rounded border">
                {agentConfig.aiReplyPrompt}
              </div>
            </div>
          )}

          <Space>
            <Button
              onClick={() => setIsEditing(true)}
              icon={<SettingOutlined />}
            >
              Edit Configuration
            </Button>
            <Button
              danger
              onClick={handleDisable}
              loading={disableMutation.isPending}
            >
              Disable AI Agent
            </Button>
          </Space>
        </div>
      ) : (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            agentId: agentConfig?.agent?.id || '',
            aiReplyEnabled: agentConfig?.aiReplyEnabled ?? true,
            aiReplyDelay: agentConfig?.aiReplyDelay || 0,
            aiReplyPrompt: agentConfig?.aiReplyPrompt || '',
          }}
        >
          <Form.Item
            name="agentId"
            label="Select AI Agent"
            rules={[{ required: true, message: 'Please select an AI agent' }]}
            tooltip="Choose which AI agent will handle incoming emails for this account"
          >
            <Select placeholder="Select an AI agent">
              {agents?.map((agent: any) => (
                <Option key={agent.id} value={agent.id}>
                  <div>
                    <div>{agent.name}</div>
                    <div className="text-xs text-gray-500">{agent.description}</div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="aiReplyEnabled"
            label="Enable Automatic Replies"
            valuePropName="checked"
            tooltip="When enabled, the AI agent will automatically respond to incoming emails"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="aiReplyDelay"
            label="Reply Delay (minutes)"
            tooltip="How long to wait before sending an automatic reply (0 = immediate)"
          >
            <InputNumber min={0} max={1440} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="aiReplyPrompt"
            label="Custom Prompt (Optional)"
            tooltip="Override the agent's default system prompt for this inbox"
          >
            <TextArea
              rows={4}
              placeholder="Enter custom instructions for this inbox..."
            />
          </Form.Item>

          <Divider />

          <Alert
            message="AI Agent Capabilities"
            description={
              <div>
                <p>
                  <strong>Knowledge Base Integration:</strong> The AI agent will use its connected knowledge bases to provide accurate responses.
                  Make sure your knowledge base contains relevant information about your business.
                </p>
                <p className="mt-2">
                  <strong>Appointment Scheduling:</strong> If the agent has calendar integration enabled, it can automatically
                  schedule appointments when customers request meetings.
                </p>
                <p className="mt-2">
                  <strong>Lead Management:</strong> The agent automatically updates lead status and tracks interaction history
                  based on email conversations.
                </p>
                <p className="mt-2">
                  <strong>Monitoring:</strong> All AI responses and actions are logged and can be reviewed in the agent replies section.
                </p>
              </div>
            }
            type="info"
            showIcon
            className="mb-4"
          />

          <div className="flex justify-end space-x-3">
            <Button onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={enableMutation.isPending || updateMutation.isPending}
              icon={<SaveOutlined />}
            >
              Save Configuration
            </Button>
          </div>
        </Form>
      )}
    </Card>
  );
}
