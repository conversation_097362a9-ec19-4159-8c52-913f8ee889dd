import { useState } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Alert,
  Typography,
  Divider,
  Space,
  Tooltip,
  Badge,
  Spin
} from 'antd';
import {
  SyncOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  MailOutlined
} from '@ant-design/icons';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

const { Title, Text, Paragraph } = Typography;

interface ImapSettingsProps {
  emailAccountId: string;
}

export default function ImapSettings({ emailAccountId }: ImapSettingsProps) {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);

  // Fetch IMAP settings
  const {
    data: imapSettings,
    isLoading,
    refetch
  } = useQuery({
    queryKey: ['imapSettings', emailAccountId],
    queryFn: () => fetch(`/api/email-accounts/${emailAccountId}/imap`).then(res => res.json()),
  });

  // Update IMAP settings mutation
  const updateMutation = useMutation({
    mutationFn: async (values: any) => {
      const response = await fetch(`/api/email-accounts/${emailAccountId}/imap`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.details || 'Failed to update IMAP settings');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['imapSettings', emailAccountId] });
      setIsEditing(false);
    },
  });

  // Sync emails mutation
  const syncMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/email-accounts/${emailAccountId}/sync`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.details || 'Failed to sync emails');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['imapSettings', emailAccountId] });
    },
  });

  // Disable IMAP mutation
  const disableMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/email-accounts/${emailAccountId}/imap`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to disable IMAP');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['imapSettings', emailAccountId] });
    },
  });

  const handleSubmit = (values: any) => {
    updateMutation.mutate(values);
  };

  const handleSync = () => {
    syncMutation.mutate();
  };

  const handleDisable = () => {
    if (confirm('Are you sure you want to disable IMAP? This will stop receiving emails.')) {
      disableMutation.mutate();
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Card>
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <MailOutlined className="text-2xl mr-2 text-blue-500" />
          <Title level={4} className="m-0">IMAP Settings</Title>
        </div>
        {imapSettings?.imapEnabled ? (
          <Badge status="success" text="Enabled" />
        ) : (
          <Badge status="default" text="Disabled" />
        )}
      </div>

      {updateMutation.isError && (
        <Alert
          message="Failed to update IMAP settings"
          description={updateMutation.error instanceof Error ? updateMutation.error.message : 'Unknown error'}
          type="error"
          showIcon
          className="mb-4"
        />
      )}

      {syncMutation.isError && (
        <Alert
          message="Failed to sync emails"
          description={syncMutation.error instanceof Error ? syncMutation.error.message : 'Unknown error'}
          type="error"
          showIcon
          className="mb-4"
        />
      )}

      {!isEditing && !imapSettings?.imapEnabled ? (
        <div>
          <Paragraph>
            IMAP is currently disabled. Enable IMAP to receive and process incoming emails automatically.
            This allows the system to track replies and enables AI agents to respond to emails.
          </Paragraph>
          <Button
            type="primary"
            onClick={() => setIsEditing(true)}
          >
            Configure IMAP
          </Button>
        </div>
      ) : !isEditing ? (
        <div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <Text type="secondary">IMAP Host</Text>
              <div className="text-lg">{imapSettings?.imapHost}</div>
            </div>
            <div>
              <Text type="secondary">IMAP Port</Text>
              <div className="text-lg">{imapSettings?.imapPort}</div>
            </div>
            <div>
              <Text type="secondary">IMAP Username</Text>
              <div className="text-lg">{imapSettings?.imapUsername || 'Same as email'}</div>
            </div>
            <div>
              <Text type="secondary">Last Sync</Text>
              <div className="text-lg">
                {imapSettings?.lastImapSync ? new Date(imapSettings.lastImapSync).toLocaleString() : 'Never'}
              </div>
            </div>
          </div>

          <Space>
            <Button
              type="primary"
              icon={<SyncOutlined />}
              onClick={handleSync}
              loading={syncMutation.isPending}
            >
              Sync Now
            </Button>
            <Button
              onClick={() => setIsEditing(true)}
            >
              Edit Settings
            </Button>
            <Button
              danger
              onClick={handleDisable}
              loading={disableMutation.isPending}
            >
              Disable IMAP
            </Button>
          </Space>
        </div>
      ) : (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            imapHost: imapSettings?.imapHost || '',
            imapPort: imapSettings?.imapPort || 993,
            imapUsername: imapSettings?.imapUsername || '',
            imapPassword: '',
            imapEnabled: true,
          }}
        >
          <Form.Item
            name="imapHost"
            label="IMAP Host"
            rules={[{ required: true, message: 'Please enter IMAP host' }]}
            tooltip="The IMAP server hostname, e.g., imap.gmail.com"
          >
            <Input placeholder="e.g., imap.gmail.com" />
          </Form.Item>

          <Form.Item
            name="imapPort"
            label="IMAP Port"
            rules={[{ required: true, message: 'Please enter IMAP port' }]}
            tooltip="The IMAP server port, usually 993 for SSL"
          >
            <InputNumber min={1} max={65535} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="imapUsername"
            label="IMAP Username"
            tooltip="Usually your email address. Leave blank to use the email address"
          >
            <Input placeholder="Usually your email address" />
          </Form.Item>

          <Form.Item
            name="imapPassword"
            label="IMAP Password"
            tooltip="Leave blank to use the SMTP password if already configured"
          >
            <Input.Password placeholder="Enter IMAP password" />
          </Form.Item>

          <Form.Item
            name="imapEnabled"
            valuePropName="checked"
            hidden
          >
            <Switch />
          </Form.Item>

          <Divider />

          <Alert
            message="Important Information"
            description={
              <div>
                <p>
                  For Gmail accounts, you need to enable "Less secure app access" or create an app password.
                  For other providers, make sure IMAP access is enabled in your email settings.
                </p>
                <p className="mt-2">
                  The system will periodically check for new emails and process replies automatically.
                </p>
              </div>
            }
            type="info"
            showIcon
            className="mb-4"
          />

          <div className="flex justify-end space-x-3">
            <Button
              onClick={() => setIsEditing(false)}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={updateMutation.isPending}
            >
              Save Settings
            </Button>
          </div>
        </Form>
      )}
    </Card>
  );
}
