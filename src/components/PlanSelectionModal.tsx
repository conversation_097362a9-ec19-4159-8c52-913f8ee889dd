import { useState } from 'react';
import { CheckIcon } from '@heroicons/react/24/outline';
import { PLANS } from '@/config/stripe';

interface PlanSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentPlanId: string;
  onSelectPlan: (planId: string) => void;
}

export function PlanSelectionModal({
  isOpen,
  onClose,
  currentPlanId,
  onSelectPlan
}: PlanSelectionModalProps) {
  const [selectedPlanId, setSelectedPlanId] = useState(currentPlanId);
  const [isAnnual, setIsAnnual] = useState(false);
  const [loading, setLoading] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async () => {
    if (selectedPlanId === currentPlanId) {
      onClose();
      return;
    }

    setLoading(true);
    try {
      // In a real implementation, this would call an API to update the subscription
      await onSelectPlan(selectedPlanId);
      onClose();
    } catch (error) {
      console.error('Failed to update subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4 text-center">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="relative transform overflow-hidden rounded-lg bg-white dark:bg-dark-card text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-3xl">
          <div className="bg-white dark:bg-dark-card px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                <h3 className="text-xl font-semibold leading-6 text-gray-900 dark:text-white mb-6">
                  Select a Plan
                </h3>

                <div className="mb-8 flex justify-center">
                  <div className="relative flex rounded-full p-1 bg-gray-100 dark:bg-gray-700">
                    <button
                      type="button"
                      className={`relative rounded-full py-2 px-4 text-sm font-medium ${
                        !isAnnual
                          ? 'bg-white dark:bg-gray-800 shadow-sm text-gray-900 dark:text-white'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                      }`}
                      onClick={() => setIsAnnual(false)}
                    >
                      Monthly
                    </button>
                    <button
                      type="button"
                      className={`relative ml-0.5 rounded-full py-2 px-4 text-sm font-medium ${
                        isAnnual
                          ? 'bg-white dark:bg-gray-800 shadow-sm text-gray-900 dark:text-white'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                      }`}
                      onClick={() => setIsAnnual(true)}
                    >
                      Annual <span className="text-green-600 font-semibold">Save 20%</span>
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                  {Object.values(PLANS).map((plan) => {
                    const isSelected = selectedPlanId === plan.id;
                    const isCurrent = currentPlanId === plan.id;

                    return (
                      <div
                        key={plan.id}
                        className={`relative rounded-lg border p-4 shadow-sm ${
                          isSelected ? 'border-indigo-600 ring-2 ring-indigo-600' : 'border-gray-300 dark:border-gray-600'
                        } ${isCurrent ? 'bg-indigo-50 dark:bg-indigo-900/20' : 'dark:bg-gray-800'}`}
                        onClick={() => setSelectedPlanId(plan.id)}
                      >
                        {isSelected && (
                          <div className="absolute right-4 top-4 flex h-5 w-5 items-center justify-center rounded-full bg-indigo-600">
                            <CheckIcon className="h-3 w-3 text-white" />
                          </div>
                        )}

                        <div className="mb-2">
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white">{plan.name}</h3>
                          {isCurrent && <span className="text-xs text-indigo-600 font-medium">Current Plan</span>}
                        </div>

                        <div className="mb-4">
                          <span className="text-2xl font-bold dark:text-white">
                            ${isAnnual ? (plan.priceMonthly * 10).toFixed(2) : plan.priceMonthly.toFixed(2)}
                          </span>
                          <span className="text-gray-500 dark:text-gray-400">/month</span>
                          {isAnnual && <p className="text-xs text-gray-500 dark:text-gray-400">Billed annually</p>}
                        </div>

                        <ul className="space-y-2 text-sm">
                          {plan.features.map((feature, index) => (
                            <li key={index} className="flex items-start">
                              <CheckIcon className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button
              type="button"
              className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 sm:ml-3 sm:w-auto"
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading ? 'Updating...' : selectedPlanId === currentPlanId ? 'Close' : 'Update Subscription'}
            </button>
            <button
              type="button"
              className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 sm:mt-0 sm:w-auto"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
