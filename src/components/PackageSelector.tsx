import { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Typography,
  Tag,
  Space,
  Spin,
  message,
  Modal,
  List,
  Divider
} from 'antd';
import {
  CheckOutlined,
  CrownOutlined,
  StarOutlined,
  RocketOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface Package {
  id: string;
  name: string;
  description?: string;
  price: number;
  billingCycle: string;
  status: string;
  dailyEmailLimit?: number;
  monthlyEmailLimit?: number;
  aiAgentsEnabled: boolean;
  knowledgeBaseEnabled: boolean;
  emailTrackingEnabled: boolean;
  customDomainsEnabled: boolean;
  apiAccessEnabled: boolean;
  prioritySupportEnabled: boolean;
  maxAiAgents?: number;
  maxKnowledgeBases?: number;
  maxEmailAccounts?: number;
  maxLeads?: number;
  maxCampaigns?: number;
  isDefault: boolean;
}

interface CurrentSubscription {
  hasSubscription: boolean;
  package: Package | null;
  status: string;
  nextPaymentDate?: string;
  autoRenew?: boolean;
}

interface PackageSelectorProps {
  onSubscriptionChange?: () => void;
}

export default function PackageSelector({ onSubscriptionChange }: PackageSelectorProps) {
  const [loading, setLoading] = useState(true);
  const [subscribing, setSubscribing] = useState<string | null>(null);
  const [packages, setPackages] = useState<Package[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<CurrentSubscription | null>(null);

  useEffect(() => {
    fetchSubscriptionInfo();
  }, []);

  const fetchSubscriptionInfo = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/subscriptions/packages');
      if (response.ok) {
        const data = await response.json();
        setCurrentSubscription(data.currentSubscription);
        setPackages(data.availablePackages);
      } else {
        message.error('Failed to load subscription information');
      }
    } catch (error) {
      console.error('Error fetching subscription info:', error);
      message.error('Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (packageId: string) => {
    try {
      setSubscribing(packageId);
      
      // Create checkout session
      const response = await fetch('/api/subscriptions/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          packageId,
          trial: true,
          trialDays: 14
        })
      });

      if (response.ok) {
        const data = await response.json();
        
        if (data.type === 'free_package') {
          message.success(data.message);
          fetchSubscriptionInfo();
          onSubscriptionChange?.();
        } else if (data.type === 'checkout_session' && data.url) {
          // Redirect to Stripe Checkout
          window.location.href = data.url;
        }
      } else {
        const error = await response.json();
        message.error(error.error || 'Failed to create subscription');
      }
    } catch (error) {
      console.error('Error creating subscription:', error);
      message.error('Failed to create subscription');
    } finally {
      setSubscribing(null);
    }
  };

  const handleManageBilling = async () => {
    try {
      const response = await fetch('/api/subscriptions/billing-portal', {
        method: 'POST'
      });

      if (response.ok) {
        const data = await response.json();
        window.location.href = data.url;
      } else {
        message.error('Failed to open billing portal');
      }
    } catch (error) {
      console.error('Error opening billing portal:', error);
      message.error('Failed to open billing portal');
    }
  };

  const getPackageIcon = (packageName: string) => {
    const name = packageName.toLowerCase();
    if (name.includes('free')) return <StarOutlined />;
    if (name.includes('pro')) return <RocketOutlined />;
    if (name.includes('enterprise')) return <CrownOutlined />;
    return <CheckOutlined />;
  };

  const formatPrice = (price: number, billingCycle: string) => {
    if (price === 0) return 'Free';
    return `$${price}/${billingCycle.toLowerCase()}`;
  };

  const getFeatureList = (pkg: Package) => {
    const features = [];
    
    if (pkg.dailyEmailLimit) {
      features.push(`${pkg.dailyEmailLimit} emails/day`);
    } else {
      features.push('Unlimited emails');
    }
    
    if (pkg.maxEmailAccounts) {
      features.push(`${pkg.maxEmailAccounts} email account${pkg.maxEmailAccounts > 1 ? 's' : ''}`);
    } else {
      features.push('Unlimited email accounts');
    }
    
    if (pkg.aiAgentsEnabled) {
      if (pkg.maxAiAgents) {
        features.push(`${pkg.maxAiAgents} AI agent${pkg.maxAiAgents > 1 ? 's' : ''}`);
      } else {
        features.push('Unlimited AI agents');
      }
    } else {
      features.push('No AI agents');
    }
    
    if (pkg.knowledgeBaseEnabled) {
      if (pkg.maxKnowledgeBases) {
        features.push(`${pkg.maxKnowledgeBases} knowledge base${pkg.maxKnowledgeBases > 1 ? 's' : ''}`);
      } else {
        features.push('Unlimited knowledge bases');
      }
    } else {
      features.push('No knowledge base');
    }
    
    if (pkg.emailTrackingEnabled) features.push('Email tracking');
    if (pkg.customDomainsEnabled) features.push('Custom domains');
    if (pkg.apiAccessEnabled) features.push('API access');
    if (pkg.prioritySupportEnabled) features.push('Priority support');
    
    return features;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      {currentSubscription?.hasSubscription && (
        <Card style={{ marginBottom: '24px' }}>
          <Title level={4}>Current Subscription</Title>
          <Row gutter={16} align="middle">
            <Col>
              <Tag color="blue" icon={getPackageIcon(currentSubscription.package?.name || '')}>
                {currentSubscription.package?.name}
              </Tag>
            </Col>
            <Col>
              <Text strong>
                {formatPrice(currentSubscription.package?.price || 0, currentSubscription.package?.billingCycle || 'monthly')}
              </Text>
            </Col>
            <Col>
              <Text type="secondary">Status: {currentSubscription.status}</Text>
            </Col>
            <Col>
              <Button onClick={handleManageBilling}>
                Manage Billing
              </Button>
            </Col>
          </Row>
        </Card>
      )}

      <Title level={3}>Choose Your Plan</Title>
      <Paragraph>
        Select the package that best fits your needs. You can upgrade or downgrade at any time.
      </Paragraph>

      <Row gutter={[24, 24]}>
        {packages.map((pkg) => {
          const isCurrentPackage = currentSubscription?.package?.id === pkg.id;
          const features = getFeatureList(pkg);
          
          return (
            <Col xs={24} sm={12} lg={8} key={pkg.id}>
              <Card
                hoverable={!isCurrentPackage}
                style={{
                  height: '100%',
                  border: isCurrentPackage ? '2px solid #1890ff' : undefined,
                  position: 'relative'
                }}
                actions={[
                  isCurrentPackage ? (
                    <Button disabled>Current Plan</Button>
                  ) : (
                    <Button
                      type={pkg.isDefault ? 'default' : 'primary'}
                      loading={subscribing === pkg.id}
                      onClick={() => handleSubscribe(pkg.id)}
                      icon={getPackageIcon(pkg.name)}
                    >
                      {pkg.price === 0 ? 'Get Started' : 'Subscribe'}
                    </Button>
                  )
                ]}
              >
                {pkg.isDefault && (
                  <Tag color="green" style={{ position: 'absolute', top: 16, right: 16 }}>
                    Popular
                  </Tag>
                )}
                
                <div style={{ textAlign: 'center', marginBottom: '16px' }}>
                  <Title level={4} style={{ margin: 0 }}>
                    {getPackageIcon(pkg.name)} {pkg.name}
                  </Title>
                  <Title level={2} style={{ margin: '8px 0', color: '#1890ff' }}>
                    {formatPrice(pkg.price, pkg.billingCycle)}
                  </Title>
                  {pkg.description && (
                    <Text type="secondary">{pkg.description}</Text>
                  )}
                </div>

                <Divider />

                <List
                  size="small"
                  dataSource={features}
                  renderItem={(feature) => (
                    <List.Item>
                      <CheckOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
                      {feature}
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          );
        })}
      </Row>

      {packages.length === 0 && (
        <Card>
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Text type="secondary">No packages available at the moment.</Text>
          </div>
        </Card>
      )}
    </div>
  );
}
