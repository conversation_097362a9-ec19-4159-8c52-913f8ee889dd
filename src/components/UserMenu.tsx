import { Menu } from '@headlessui/react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { signOut } from 'next-auth/react'

export function UserMenu() {
  const router = useRouter()

  const handleDeleteAccount = async () => {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      try {
        const response = await fetch('/api/user/delete-account', {
          method: 'DELETE',
        })
        if (response.ok) {
          await signOut({ callbackUrl: '/auth/signin' })
        }
      } catch (error) {
        console.error('Failed to delete account:', error)
      }
    }
  }

  return (
    <Menu as="div" className="relative">
      <Menu.Button className="flex items-center space-x-2 rounded-full bg-white p-1 focus:outline-none">
        <span className="sr-only">Open user menu</span>
        <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
          <svg className="h-5 w-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
        </div>
      </Menu.Button>

      <Menu.Items className="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
        <Menu.Item>
          {({ active }) => (
            <Link
              href="/settings/privacy"
              className={`${active ? 'bg-gray-100' : ''} block px-4 py-2 text-sm text-gray-700`}
            >
              Settings
            </Link>
          )}
        </Menu.Item>

        <Menu.Item>
          {({ active }) => (
            <button
              onClick={async () => {
                const response = await fetch('/api/user/export-data')
                const blob = await response.blob()
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = 'user-data-export.json'
                a.click()
              }}
              className={`${active ? 'bg-gray-100' : ''} block w-full text-left px-4 py-2 text-sm text-gray-700`}
            >
              Export My Data
            </button>
          )}
        </Menu.Item>

        <hr className="my-1" />

        <Menu.Item>
          {({ active }) => (
            <button
              onClick={() => signOut({ callbackUrl: '/auth/signin' })}
              className={`${active ? 'bg-gray-100' : ''} block w-full text-left px-4 py-2 text-sm text-gray-700`}
            >
              Sign Out
            </button>
          )}
        </Menu.Item>

        <Menu.Item>
          {({ active }) => (
            <button
              onClick={handleDeleteAccount}
              className={`${active ? 'bg-gray-100' : ''} block w-full text-left px-4 py-2 text-sm text-red-600`}
            >
              Delete Account
            </button>
          )}
        </Menu.Item>
      </Menu.Items>
    </Menu>
  )
}
