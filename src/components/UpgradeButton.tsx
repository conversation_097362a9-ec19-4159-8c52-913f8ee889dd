import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { SubscriptionTier, SUBSCRIPTION_PLANS } from '@/config/subscription-plans';
import { getButtonClasses } from '@/styles/buttonStyles';

interface UpgradeButtonProps {
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

export function UpgradeButton({ className = '', variant = 'primary', size = 'md' }: UpgradeButtonProps) {
  const { data: session } = useSession();
  const [subscription, setSubscription] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  const fetchSubscriptionData = async () => {
    try {
      const response = await fetch('/api/subscriptions');
      const data = await response.json();
      setSubscription(data.subscription);
    } catch (error) {
      console.error('Failed to fetch subscription data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeClick = async () => {
    setLoading(true);
    await fetchSubscriptionData();
    setShowUpgradeModal(true);
    setLoading(false);
  };

  // Use the consistent button style from our utility
  const buttonClasses = getButtonClasses(variant, size, className);

  // Determine the next tier for upgrade
  const getCurrentTier = () => {
    if (!subscription) return SubscriptionTier.FREE;
    return subscription.tier;
  };

  const getNextTier = () => {
    const currentTier = getCurrentTier();
    return currentTier === SubscriptionTier.FREE
      ? SubscriptionTier.PRO
      : SubscriptionTier.ENTERPRISE;
  };

  return (
    <>
      <button
        onClick={handleUpgradeClick}
        className={buttonClasses}
      >
        Upgrade
      </button>

      {/* Upgrade Modal */}
      {showUpgradeModal && subscription && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">&#8203;</span>
            <div className="inline-block transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
              <div>
                <div className="mt-3 text-center sm:mt-5">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">Choose a Plan</h3>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      Select a plan that best fits your needs
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-5 grid grid-cols-1 gap-4 sm:grid-cols-2">
                {Object.entries(SUBSCRIPTION_PLANS).map(([tier, plan]) => (
                  <div
                    key={tier}
                    className={`rounded-lg border p-4 ${tier === getCurrentTier() ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}
                  >
                    <div className="flex justify-between">
                      <h4 className="text-lg font-medium">{plan.name}</h4>
                      {tier === getCurrentTier() && (
                        <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                          Current
                        </span>
                      )}
                    </div>
                    <p className="mt-1 text-2xl font-bold">${plan.priceMonthly}<span className="text-sm font-normal text-gray-500">/mo</span></p>
                    <ul className="mt-4 space-y-2">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-start text-sm">
                          <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          <span className="ml-2">{feature}</span>
                        </li>
                      ))}
                    </ul>
                    <div className="mt-4">
                      {tier === getCurrentTier() ? (
                        <button
                          type="button"
                          disabled
                          className="w-full rounded-md border border-gray-300 bg-gray-100 px-4 py-2 text-sm font-medium text-gray-500"
                        >
                          Current Plan
                        </button>
                      ) : (
                        <button
                          type="button"
                          className="w-full rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                          onClick={async () => {
                            try {
                              const response = await fetch('/api/subscriptions/create', {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                  tier,
                                  trial: false,
                                }),
                              });

                              const data = await response.json();

                              if (data.requiresAction === 'SETUP_PAYMENT_METHOD' && data.url) {
                                window.location.href = data.url;
                                return;
                              }

                              if (data.success) {
                                // Refresh subscription data
                                fetchSubscriptionData();
                                setShowUpgradeModal(false);
                                // Reload the page to reflect changes
                                window.location.reload();
                              }
                            } catch (error) {
                              console.error('Failed to upgrade subscription:', error);
                            }
                          }}
                        >
                          {tier === SubscriptionTier.FREE ? 'Downgrade' : 'Upgrade'}
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-5 sm:mt-6">
                <button
                  type="button"
                  className="inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:text-sm"
                  onClick={() => setShowUpgradeModal(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
