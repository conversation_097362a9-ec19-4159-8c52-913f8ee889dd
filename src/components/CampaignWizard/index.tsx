import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { TemplateBuilder } from '../TemplateBuilder';

interface CampaignWizardProps {
  onSubmit: (data: any) => Promise<void>;
  emailAccounts: any[];
}

export const CampaignWizard: React.FC<CampaignWizardProps> = ({ onSubmit, emailAccounts }) => {
  const [step, setStep] = useState(1);
  const [template, setTemplate] = useState<any>(null);
  const { register, handleSubmit, formState: { errors } } = useForm();

  const steps = {
    1: 'Campaign Details',
    2: 'Template Design',
    3: 'Recipients',
    4: 'Review & Schedule'
  };

  const handleTemplateChange = (templateData: any) => {
    setTemplate(templateData);
  };

  const onFormSubmit = async (data: any) => {
    const campaignData = {
      ...data,
      template,
      content: template.html,
    };
    await onSubmit(campaignData);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          {Object.entries(steps).map(([key, value]) => (
            <div
              key={key}
              className={`flex items-center ${
                parseInt(key) <= step ? 'text-blue-600' : 'text-gray-400'
              }`}
            >
              <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 
                ${parseInt(key) <= step ? 'border-blue-600' : 'border-gray-400'}`}
              >
                {key}
              </div>
              <span className="ml-2">{value}</span>
            </div>
          ))}
        </div>
      </div>

      <form onSubmit={handleSubmit(onFormSubmit)}>
        {step === 1 && (
          <div className="space-y-4">
            <div>
              <label className="block mb-2">Campaign Name</label>
              <input
                {...register('name', { required: true })}
                className="w-full p-2 border rounded"
                placeholder="Enter campaign name"
              />
              {errors.name && <span className="text-red-500">This field is required</span>}
            </div>

            <div>
              <label className="block mb-2">Email Account</label>
              <select
                {...register('emailAccountId', { required: true })}
                className="w-full p-2 border rounded"
              >
                <option value="">Select email account</option>
                {emailAccounts.map((account) => (
                  <option key={account.id} value={account.id}>
                    {account.email}
                  </option>
                ))}
              </select>
            </div>

            <button
              type="button"
              onClick={() => setStep(2)}
              className="px-4 py-2 bg-blue-600 text-white rounded"
            >
              Next
            </button>
          </div>
        )}

        {step === 2 && (
          <div>
            <TemplateBuilder onSave={handleTemplateChange} />
            <div className="mt-4 flex justify-between">
              <button
                type="button"
                onClick={() => setStep(1)}
                className="px-4 py-2 border rounded"
              >
                Previous
              </button>
              <button
                type="button"
                onClick={() => setStep(3)}
                className="px-4 py-2 bg-blue-600 text-white rounded"
              >
                Next
              </button>
            </div>
          </div>
        )}

        {step === 3 && (
          <div className="space-y-4">
            <div>
              <label className="block mb-2">Recipients</label>
              <textarea
                {...register('recipients', { required: true })}
                className="w-full p-2 border rounded"
                placeholder="Enter email addresses (one per line)"
                rows={10}
              />
            </div>

            <div className="flex justify-between">
              <button
                type="button"
                onClick={() => setStep(2)}
                className="px-4 py-2 border rounded"
              >
                Previous
              </button>
              <button
                type="button"
                onClick={() => setStep(4)}
                className="px-4 py-2 bg-blue-600 text-white rounded"
              >
                Next
              </button>
            </div>
          </div>
        )}

        {step === 4 && (
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-4">Campaign Summary</h3>
              {/* Add campaign summary details here */}
            </div>

            <div>
              <label className="block mb-2">Schedule Send</label>
              <input
                type="datetime-local"
                {...register('scheduledFor')}
                className="w-full p-2 border rounded"
              />
            </div>

            <div className="flex justify-between">
              <button
                type="button"
                onClick={() => setStep(3)}
                className="px-4 py-2 border rounded"
              >
                Previous
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-green-600 text-white rounded"
              >
                Launch Campaign
              </button>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};