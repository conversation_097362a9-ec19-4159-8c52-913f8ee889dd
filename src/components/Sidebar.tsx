import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  HomeIcon,
  EnvelopeIcon,
  UsersIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  InboxIcon,
  UserIcon,
  BellIcon,
  ArrowRightOnRectangleIcon,
  UserCircleIcon,
  QueueListIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  EnvelopeOpenIcon,
  CalendarIcon,
  BookOpenIcon
} from '@heroicons/react/24/outline';
import { RobotOutlined, FolderOutlined, SettingOutlined } from '@ant-design/icons';
import { Menu } from '@headlessui/react';
import { useState, useEffect } from 'react';
import ThemeToggle from './ThemeToggle';
import { useTheme } from '@/contexts/ThemeContext';
import { useCommonData } from '@/hooks/useCommonData';

interface LeadList {
  id: string;
  name: string;
  _count?: {
    leads: number;
  };
}

export default function Sidebar() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [leadsExpanded, setLeadsExpanded] = useState(false);
  const { isDarkMode } = useTheme();

  const isActive = (path: string) => router.pathname.startsWith(path);
  const isListActive = (listId: string) => {
    return router.pathname.startsWith('/leads') && router.query.listId === listId;
  };

  // Get lead lists from global state
  const { leadLists = [] } = useCommonData();

  // Auto-expand the leads section if a list is active
  useEffect(() => {
    if (router.query.listId && !leadsExpanded) {
      setLeadsExpanded(true);
    }
  }, [router.query.listId, leadsExpanded]);

  const navigationItems = [
    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
    { name: 'Campaigns', href: '/campaigns', icon: EnvelopeIcon },
    { name: 'Leads', href: '/leads', icon: UsersIcon },
    { name: 'Email Accounts', href: '/email-accounts', icon: InboxIcon },
    { name: 'Inbox', href: '/inbox', icon: EnvelopeOpenIcon },
    { name: 'AI Agents', href: '/agents', icon: RobotOutlined },
    { name: 'Knowledge Bases', href: '/knowledge-bases', icon: BookOpenIcon },
    { name: 'Appointments', href: '/appointments', icon: CalendarIcon },
    { name: 'Templates', href: '/templates', icon: DocumentTextIcon },
    { name: 'Notifications', href: '/notifications', icon: BellIcon },
  ];

  const adminItems = [
    { name: 'User Management', href: '/admin/users', icon: UsersIcon },
    { name: 'Settings', href: '/admin/settings', icon: Cog6ToothIcon },
    { name: 'Security', href: '/admin/security', icon: ShieldCheckIcon },
  ];



  return (
    <div className="h-screen w-64 bg-gray-900 dark:bg-dark-header text-white fixed left-0 top-0 flex flex-col transition-colors duration-200">
      <div className="p-4 flex-grow overflow-y-auto">
        <div className="flex items-center mb-8">
          <Link href="/dashboard" className="flex items-center space-x-2">
            <span className="text-xl font-bold">Avian Email</span>
          </Link>
        </div>

        <nav className="space-y-1">
          <div className="mb-8">
            {navigationItems.map((item) => {
              const Icon = item.icon;

              // Special handling for Leads item to add expandable list
              if (item.name === 'Leads') {
                return (
                  <div key={item.name} className="mb-1">
                    <div className="flex">
                      <Link
                        href={item.href}
                        className={`flex items-center flex-grow px-4 py-2 text-sm rounded-lg ${
                          isActive(item.href) && !router.query.listId
                            ? 'bg-gray-800 text-white'
                            : 'text-gray-300 hover:bg-gray-800'
                        }`}
                      >
                        <Icon className="h-5 w-5 mr-3" />
                        {item.name}
                      </Link>
                      <button
                        onClick={() => setLeadsExpanded(!leadsExpanded)}
                        className="px-2 py-2 text-gray-300 hover:bg-gray-800 rounded-lg"
                      >
                        {leadsExpanded ? (
                          <ChevronUpIcon className="h-4 w-4" />
                        ) : (
                          <ChevronDownIcon className="h-4 w-4" />
                        )}
                      </button>
                    </div>

                    {/* Lead Lists */}
                    {leadsExpanded && (
                      <div className="ml-7 mt-1 space-y-1">
                        <Link
                          href="/lead-lists"
                          className={`flex items-center px-4 py-2 text-sm rounded-lg ${
                            router.pathname === '/lead-lists'
                              ? 'bg-gray-800 text-white'
                              : 'text-gray-300 hover:bg-gray-800'
                          }`}
                        >
                          <QueueListIcon className="h-4 w-4 mr-3" />
                          All Lists
                        </Link>

                        {leadLists.map((list) => (
                          <Link
                            key={list.id}
                            href={`/leads?listId=${list.id}`}
                            className={`flex items-center justify-between px-4 py-2 text-sm rounded-lg ${
                              isListActive(list.id)
                                ? 'bg-gray-800 text-white'
                                : 'text-gray-300 hover:bg-gray-800'
                            }`}
                          >
                            <div className="flex items-center">
                              <FolderOutlined className="h-4 w-4 mr-3" />
                              <span className="truncate max-w-[120px]">{list.name}</span>
                            </div>
                            {list._count?.leads > 0 && (
                              <span className="text-xs bg-gray-700 px-1.5 py-0.5 rounded-full">
                                {list._count.leads}
                              </span>
                            )}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                );
              }

              // Regular navigation items
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center px-4 py-2 text-sm rounded-lg mb-1 ${
                    isActive(item.href)
                      ? 'bg-gray-800 text-white'
                      : 'text-gray-300 hover:bg-gray-800'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </div>

          {session?.user?.role === 'ADMIN' && (
            <>
              <div className="border-t border-gray-700 pt-4 mb-4">
                <p className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                  Admin
                </p>
              </div>

              <div>
                {adminItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`flex items-center px-4 py-2 text-sm rounded-lg mb-1 ${
                        isActive(item.href)
                          ? 'bg-gray-800 text-white'
                          : 'text-gray-300 hover:bg-gray-800'
                      }`}
                    >
                      <Icon className="h-5 w-5 mr-3" />
                      {item.name}
                    </Link>
                  );
                })}
              </div>
            </>
          )}
        </nav>
      </div>

      {/* User profile section at the bottom */}
      <div className="border-t border-gray-700 p-4">
        <Menu as="div" className="relative w-full">
          <Menu.Button className="flex items-center w-full px-4 py-2 text-sm rounded-lg text-gray-300 hover:bg-gray-800">
            <div className="flex items-center space-x-3">
              <div className="h-8 w-8 rounded-full bg-gray-700 flex items-center justify-center">
                {session?.user?.image ? (
                  <img
                    src={session.user.image}
                    alt={session.user.name || 'User'}
                    className="h-8 w-8 rounded-full"
                  />
                ) : (
                  <UserCircleIcon className="h-6 w-6 text-gray-300" />
                )}
              </div>
              <div className="flex-1 text-left">
                <p className="text-sm font-medium">{session?.user?.name || 'User'}</p>
                <p className="text-xs text-gray-400 truncate">{session?.user?.email}</p>
              </div>
            </div>
          </Menu.Button>

          <Menu.Items className="absolute bottom-full left-0 mb-2 w-full origin-bottom-left rounded-md bg-gray-800 py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">

            <Menu.Item>
              {({ active }) => (
                <Link
                  href="/settings"
                  className={`${active ? 'bg-gray-700' : ''} flex items-center px-4 py-2 text-sm text-gray-300`}
                >
                  <Cog6ToothIcon className="h-5 w-5 mr-3" />
                  Settings
                </Link>
              )}
            </Menu.Item>

            <hr className="my-1 border-gray-700" />

            <Menu.Item>
              {({ active }) => (
                <button
                  onClick={() => signOut({ callbackUrl: '/auth/signin' })}
                  className={`${active ? 'bg-gray-700' : ''} flex items-center w-full text-left px-4 py-2 text-sm text-gray-300`}
                >
                  <ArrowRightOnRectangleIcon className="h-5 w-5 mr-3" />
                  Sign Out
                </button>
              )}
            </Menu.Item>


          </Menu.Items>
        </Menu>
      </div>
    </div>
  );
}
