import React, { useState } from 'react';
import { Button, Modal, Alert, Space, Switch, message } from 'antd';
import { WarningOutlined } from '@ant-design/icons';
import api from '@/lib/api';

interface ForceVerifyButtonProps {
  onSuccess: (config: any) => void;
}

const ForceVerifyButton: React.FC<ForceVerifyButtonProps> = ({ onSuccess }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [confirmed, setConfirmed] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleForceVerify = async () => {
    if (!confirmed) {
      message.warning('Please confirm that your DNS records are correctly configured');
      return;
    }

    try {
      setLoading(true);
      const response = await api.post('/dns-tracking/force-verify');
      
      if (response.data.success) {
        message.success('DNS configuration verified successfully (manual override)');
        setModalVisible(false);
        onSuccess(response.data.config);
      } else {
        message.error(response.data.message || 'Force verification failed');
      }
    } catch (error) {
      console.error('Error forcing DNS verification:', error);
      message.error('Failed to force DNS verification');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button 
        type="default" 
        onClick={() => setModalVisible(true)}
        className="ml-2"
      >
        Force Verify
      </Button>

      <Modal
        title={
          <div className="flex items-center">
            <WarningOutlined className="text-yellow-500 mr-2" />
            <span>Manual DNS Verification</span>
          </div>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            Cancel
          </Button>,
          <Button
            key="override"
            type="primary"
            danger
            onClick={handleForceVerify}
            loading={loading}
            disabled={!confirmed}
          >
            Force Verification
          </Button>
        ]}
      >
        <Alert
          type="warning"
          showIcon
          className="mb-4"
          message="Manual verification requested"
          description="This will bypass automatic DNS verification checks. Only use this if you're certain your DNS records are correctly configured."
        />
        
        <p className="mb-4">
          If you're confident that your DNS records are correctly configured but automatic verification is failing, you can force the verification to proceed.
        </p>
        
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <Space direction="vertical" className="w-full">
            <div className="flex items-center justify-between">
              <span className="font-medium">I confirm my DNS records are correctly configured</span>
              <Switch 
                checked={confirmed} 
                onChange={setConfirmed} 
              />
            </div>
            {confirmed && (
              <Alert
                type="info"
                showIcon
                message="You can always re-verify later if needed"
              />
            )}
          </Space>
        </div>
      </Modal>
    </>
  );
};

export default ForceVerifyButton;
