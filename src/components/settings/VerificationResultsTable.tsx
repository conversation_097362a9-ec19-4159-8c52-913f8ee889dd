import React from 'react';
import { Table, Tag, Typography, Tooltip, Badge, Space, Alert } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface VerificationResult {
  domain: string;
  type: string;
  expected: string;
  actual: string | null;
  status: 'success' | 'failure';
}

interface VerificationResultsTableProps {
  results: VerificationResult[];
  onForceVerifySuccess?: (config: any) => void;
}

const VerificationResultsTable: React.FC<VerificationResultsTableProps> = ({ results, onForceVerifySuccess }) => {
  // Format DNS record value for better display
  const formatDnsValue = (value: string) => {
    return value.replace(/\s+/g, ' ').trim();
  };

  // Check if expected and actual values match (ignoring whitespace and case)
  const valuesMatch = (expected: string, actual: string | null) => {
    if (!expected || !actual) return false;
    return expected.replace(/\s+/g, '').toLowerCase() === actual.replace(/\s+/g, '').toLowerCase();
  };

  const columns = [
    {
      title: 'Domain',
      dataIndex: 'domain',
      key: 'domain',
      width: 180,
      render: (text: string) => (
        <Text strong className="text-sm dark:text-white whitespace-normal break-words">
          {text}
        </Text>
      ),
    },
    {
      title: 'Record Type',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (text: string) => (
        <Tag color="blue" className="text-xs font-medium px-2 py-1">
          {text}
        </Tag>
      ),
    },
    {
      title: 'Expected Value',
      dataIndex: 'expected',
      key: 'expected',
      width: 220,
      render: (text: string, record: any) => {
        const formattedValue = formatDnsValue(text);
        return (
          <Tooltip title={text} placement="topLeft">
            <Paragraph
              code
              ellipsis={{ rows: 2, tooltip: true }}
              className="m-0 text-xs dark:bg-gray-800 dark:text-gray-200 whitespace-normal break-all"
            >
              {formattedValue}
            </Paragraph>
          </Tooltip>
        );
      },
    },
    {
      title: 'Actual Value',
      dataIndex: 'actual',
      key: 'actual',
      width: 220,
      render: (text: string | null, record: any) => {
        if (!text) {
          return <Text type="secondary" className="text-xs">Not found</Text>;
        }

        const formattedValue = formatDnsValue(text);
        const matches = valuesMatch(record.expected, text);

        return (
          <div>
            <Tooltip title={text} placement="topLeft">
              <Paragraph
                code
                ellipsis={{ rows: 2, tooltip: true }}
                className={`m-0 text-xs dark:bg-gray-800 dark:text-gray-200 whitespace-normal break-all ${matches ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' : ''}`}
              >
                {formattedValue}
              </Paragraph>
            </Tooltip>
            {matches && (
              <div className="mt-1">
                <Badge
                  status="success"
                  text={<Text className="text-xs text-green-600 dark:text-green-400">Matches expected value</Text>}
                />
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center' as const,
      render: (status: string, record: any) => {
        // If expected and actual values match exactly but status is failure, show a special tag
        const matches = record.actual && valuesMatch(record.expected, record.actual);

        if (status === 'success') {
          return <Tag color="success" icon={<CheckCircleOutlined />} className="px-2 py-1">Verified</Tag>;
        } else if (matches) {
          return (
            <Tooltip title="Values match but verification failed. Try again or contact support.">
              <Tag color="warning" icon={<InfoCircleOutlined />} className="px-2 py-1">Values Match</Tag>
            </Tooltip>
          );
        } else {
          return <Tag color="error" icon={<CloseCircleOutlined />} className="px-2 py-1">Failed</Tag>;
        }
      },
    },
  ];

  // Count verification statuses
  const successCount = results.filter(r => r.status === 'success').length;
  const failureCount = results.filter(r => r.status === 'failure').length;
  const totalCount = results.length;

  // Check if values match but verification failed
  const matchingButFailed = results.filter(result => {
    if (!result.actual || result.status === 'success') return false;
    return valuesMatch(result.expected, result.actual);
  });

  // Determine overall status
  const allSuccess = successCount === totalCount;
  const allFailed = failureCount === totalCount;
  const partialSuccess = successCount > 0 && failureCount > 0;
  const hasMatchingButFailed = matchingButFailed.length > 0;

  return (
    <div className="w-full">
      {/* Status summary */}
      <div className="mb-4">
        <Alert
          type={allSuccess ? "success" : hasMatchingButFailed ? "warning" : "error"}
          showIcon
          message={
            allSuccess ? "All DNS records verified successfully" :
            partialSuccess ? `${successCount} of ${totalCount} DNS records verified` :
            hasMatchingButFailed ? "DNS records match but verification failed" :
            "DNS verification failed"
          }
          description={
            <div>
              {allSuccess && (
                <Text className="text-sm">Your DNS configuration is correct and ready to use.</Text>
              )}

              {partialSuccess && (
                <Text className="text-sm">Some DNS records are correctly configured, but others need attention.</Text>
              )}

              {hasMatchingButFailed && (
                <Text className="text-sm">
                  Your DNS records appear to match the expected values, but verification failed.
                  This could be due to DNS propagation delays (which can take up to 48 hours).
                  Try again later or check with your DNS provider.
                </Text>
              )}

              {!allSuccess && !hasMatchingButFailed && (
                <Text className="text-sm">
                  Please check your DNS configuration and make sure the records match the expected values exactly.
                </Text>
              )}
            </div>
          }
        />
      </div>

      <div className="overflow-x-auto w-full" style={{ width: '100%', maxWidth: '100%' }}>
        <Table
          dataSource={results.map((result, index) => ({ ...result, key: index }))}
          columns={columns}
          pagination={false}
          className="dark:bg-dark-card dark:text-white verification-results-table"
          size="middle"
          bordered
          scroll={{ x: true }}
          style={{
            width: '100%',
            tableLayout: 'fixed',
            maxWidth: '100%'
          }}
        />
      </div>

      {hasMatchingButFailed && (
        <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <Space direction="vertical" className="w-full">
            <div className="flex items-start">
              <InfoCircleOutlined className="text-blue-500 mr-2 mt-1" />
              <div>
                <Text strong className="text-blue-700 dark:text-blue-400">DNS Propagation Information</Text>
                <Paragraph className="text-sm text-blue-600 dark:text-blue-300 mt-1">
                  DNS changes can take up to 48 hours to fully propagate across the internet. If your records
                  appear to be correctly configured but verification is failing, please wait and try again later.
                </Paragraph>
                <Paragraph className="text-sm text-blue-600 dark:text-blue-300">
                  You can use third-party DNS lookup tools like
                  <a href="https://mxtoolbox.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-300 underline ml-1">MXToolbox</a> or
                  <a href="https://www.whatsmydns.net/" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-300 underline ml-1">WhatsMyDNS</a>
                  to check if your DNS records are visible from different locations.
                </Paragraph>
              </div>
            </div>
          </Space>
        </div>
      )}
    </div>
  );
};

export default VerificationResultsTable;
