import { useEffect, useState } from 'react';
import { <PERSON>, Typography, Button, Space, message, Tooltip, Alert } from 'antd';
import { CopyOutlined, InfoCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface DnsRecordsListProps {
  openDomain?: string;
  clickDomain?: string;
  bounceDomain?: string;
}

const DnsRecordsList: React.FC<DnsRecordsListProps> = ({
  openDomain,
  clickDomain,
  bounceDomain
}) => {
  const [hostname, setHostname] = useState<string>('');

  const [appDomain, setAppDomain] = useState<string>('');

  useEffect(() => {
    // Set hostname on client side
    const host = window.location.hostname;
    setHostname(host);

    // Set the app domain (use environment variable if available)
    const appDomainValue = process.env.NEXT_PUBLIC_APP_URL || 'avian-mail.wattlesol.com';
    setAppDomain(appDomainValue);
  }, []);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    message.success('Copied to clipboard');
  };

  // Use the app domain for DNS records (more reliable than hostname)
  const targetDomain = appDomain || hostname;

  const records = [];

  if (openDomain) {
    records.push({
      domain: openDomain,
      type: 'CNAME',
      value: targetDomain,
      description: 'Used for tracking email opens',
      instructions: 'This record allows us to track when recipients open your emails by loading a tiny invisible tracking pixel.'
    });
  }

  if (clickDomain) {
    records.push({
      domain: clickDomain,
      type: 'CNAME',
      value: targetDomain,
      description: 'Used for tracking link clicks',
      instructions: 'This record allows us to track when recipients click links in your emails by redirecting through our tracking service.'
    });
  }

  if (bounceDomain) {
    records.push({
      domain: bounceDomain,
      type: 'MX',
      value: `10 ${targetDomain}`,
      description: 'Used for receiving bounce emails',
      instructions: 'This record allows our system to receive bounce notifications when emails cannot be delivered.'
    });

    records.push({
      domain: bounceDomain,
      type: 'TXT',
      value: `v=spf1 include:${targetDomain} ~all`,
      description: 'SPF record for bounce domain',
      instructions: 'This SPF record authorizes our servers to receive emails on behalf of your domain, which is necessary for bounce processing.'
    });
  }

  return (
    <>
      {records.length > 0 && (
        <Alert
          message="DNS Records to Add"
          description={
            <div className="text-sm">
              <p>Add these DNS records to your domain registrar or DNS provider. After adding, it may take up to 24-48 hours for DNS changes to propagate.</p>
              <p className="mt-2">Common DNS providers:
                <a href="https://www.cloudflare.com/" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">Cloudflare</a>,
                <a href="https://www.godaddy.com/" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">GoDaddy</a>,
                <a href="https://www.namecheap.com/" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">Namecheap</a>,
                <a href="https://domains.google/" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">Google Domains</a>
              </p>
            </div>
          }
          type="info"
          showIcon
          className="mb-4"
        />
      )}
      <List
        itemLayout="horizontal"
        dataSource={records}
        renderItem={(item) => (
        <List.Item
          actions={[
            <Tooltip key="help" title="Add this record in your domain's DNS settings">
              <Button
                type="text"
                size="small"
                icon={<InfoCircleOutlined />}
              >
                Help
              </Button>
            </Tooltip>
          ]}
        >
          <List.Item.Meta
            title={
              <Space>
                <Text strong>{item.domain}</Text>
                <Text type="secondary">({item.type})</Text>
              </Space>
            }
            description={
              <div>
                <div className="mb-2">
                  <Text strong>Value: </Text>
                  <Text code>{item.value}</Text>
                  <Button
                    type="link"
                    icon={<CopyOutlined />}
                    onClick={() => copyToClipboard(item.value)}
                    size="small"
                    className="ml-2"
                  >
                    Copy
                  </Button>
                </div>
                <div className="mb-2">
                  <Text type="secondary">{item.description}</Text>
                </div>
                <div className="mt-2 text-xs">
                  <Tooltip title={item.instructions}>
                    <InfoCircleOutlined className="mr-1" />
                    <Text type="secondary" className="text-xs">{item.instructions}</Text>
                  </Tooltip>
                </div>
              </div>
            }
          />
        </List.Item>
      )}
    />
    </>
  );
};

export default DnsRecordsList;
