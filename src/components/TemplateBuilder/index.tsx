import React, { useState } from 'react';
import dynamic from 'next/dynamic';
import { EmailEditor } from 'react-email-editor';

const DynamicEmailEditor = dynamic(() => import('react-email-editor'), {
  ssr: false,
});

interface TemplateBuilderProps {
  onSave: (template: any) => void;
  initialDesign?: any;
}

export const TemplateBuilder: React.FC<TemplateBuilderProps> = ({ onSave, initialDesign }) => {
  const [editor, setEditor] = useState<any>(null);

  const handleReady = (editorInstance: any) => {
    setEditor(editorInstance);
    if (initialDesign) {
      editorInstance.loadDesign(initialDesign);
    }
  };

  const handleSave = () => {
    editor.exportHtml((data: any) => {
      const { design, html } = data;
      onSave({ design, html });
    });
  };

  return (
    <div className="h-screen">
      <div className="flex justify-between p-4 bg-white border-b">
        <h2 className="text-xl font-semibold">Email Template Builder</h2>
        <button
          onClick={handleSave}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Save Template
        </button>
      </div>
      <div className="h-[calc(100vh-80px)]">
        <DynamicEmailEditor
          onReady={handleReady}
          minHeight="100%"
          options={{
            customCSS: [
              'https://fonts.googleapis.com/css?family=Open+Sans',
            ],
          }}
        />
      </div>
    </div>
  );
};