import * as cron from 'node-cron';
import { prisma } from '@/lib/prisma';
import { ReputationMonitoringService } from '@/services/reputation-monitoring.service';
import { logger } from '@/lib/logger';

// Check reputation of all email accounts weekly
export const startReputationChecker = () => {
  logger.info('Starting reputation checker cron job');

  // Run weekly on Sunday at 2 AM
  cron.schedule('0 2 * * 0', async () => {
    logger.info('Running reputation checker cron job');
    await checkAllEmailAccountsReputation();
  });
};

// Check reputation of all active email accounts
async function checkAllEmailAccountsReputation() {
  try {
    // Get all active email accounts
    const emailAccounts = await prisma.emailAccount.findMany({
      where: {
        status: {
          in: ['active', 'pending']
        }
      }
    });

    logger.info(`Checking reputation for ${emailAccounts.length} email accounts`);

    // Check reputation for each account
    for (const account of emailAccounts) {
      try {
        // Add a small delay between checks to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check reputation
        await ReputationMonitoringService.checkReputation(account.id);

        logger.info(`Successfully checked reputation for ${account.email}`);
      } catch (error) {
        logger.error(`Failed to check reputation for ${account.email}`, {
          emailAccountId: account.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    logger.info('Completed reputation check for all email accounts');
  } catch (error) {
    logger.error('Error checking reputation for email accounts', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
