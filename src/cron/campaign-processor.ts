import * as cron from 'node-cron';
import { CampaignService } from '../services/campaign.service';
import { SequenceCampaignService } from '../services/sequence-campaign.service';
import { logger } from '@/lib/logger';
import { startSequenceProcessor, processSequenceEvents } from '../workers/sequence-processor.worker';

// Process active campaigns every 5 minutes
export const startCampaignProcessor = () => {
  logger.info('Starting campaign processor cron job');

  // Start the sequence processor worker
  let sequenceProcessorIntervalId: NodeJS.Timeout | null = null;
  try {
    // Start the sequence processor worker if we're in production
    if (process.env.NODE_ENV === 'production') {
      logger.info('Starting sequence processor worker in production mode');
      sequenceProcessorIntervalId = startSequenceProcessor();
    } else {
      logger.info('Sequence processor worker not started in development mode');
    }
  } catch (error) {
    logger.error('Error starting sequence processor worker', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });
  }

  // Schedule the main campaign processor
  const campaignCronJob = cron.schedule('*/5 * * * *', async () => {
    logger.info('Running campaign processor cron job');

    try {
      // Process standard campaigns
      await CampaignService.processActiveCampaigns();

      // Process sequence campaigns
      await SequenceCampaignService.processActiveSequenceCampaigns();

      // Also process any pending sequence events
      if (process.env.NODE_ENV === 'production') {
        logger.info('Processing pending sequence events');
        await processSequenceEvents().catch(error => {
          logger.error('Error processing sequence events', {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : 'No stack trace'
          });
        });
      }

      logger.info('Campaign processor completed successfully');
    } catch (error) {
      logger.error('Error in campaign processor', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
    }
  });

  // Return a function to stop both the cron job and the sequence processor
  return () => {
    campaignCronJob.stop();
    if (sequenceProcessorIntervalId) {
      clearInterval(sequenceProcessorIntervalId);
    }
    logger.info('Campaign processor and sequence processor stopped');
  };
};
