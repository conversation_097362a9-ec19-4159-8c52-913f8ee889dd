import * as cron from 'node-cron';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { AgentEnhancementService } from '@/services/agent-enhancement.service';
import { ReminderService } from '@/services/reminder.service';
import { Worker } from 'bullmq';
import { getRedisConnection } from '@/services/queue.service';

/**
 * Start the agent processor cron job
 * This job processes AI agent tasks and reminders
 */
export const startAgentProcessor = () => {
  logger.info('Starting agent processor cron job');

  // Start the reminder worker
  startReminderWorker();

  // Process agent tasks every 15 minutes
  const agentCronJob = cron.schedule('*/15 * * * *', async () => {
    logger.info('Running agent processor cron job');

    try {
      // Process campaigns with AI agents
      await processAgentCampaigns();

      logger.info('Agent processor completed successfully');
    } catch (error) {
      logger.error('Error in agent processor', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
      });
    }
  });

  // Return a function to stop the cron job
  return () => {
    agentCronJob.stop();
    logger.info('Agent processor stopped');
  };
};

/**
 * Process campaigns with AI agents
 */
async function processAgentCampaigns() {
  try {
    // Find active campaigns with AI agents
    const campaigns = await prisma.campaign.findMany({
      where: {
        status: 'active',
        agentId: {
          not: null,
        },
      },
      include: {
        agent: true,
        leads: {
          include: {
            lead: true,
            currentStep: true,
          },
        },
      },
    });

    logger.info(`Found ${campaigns.length} active campaigns with AI agents`);

    // Process each campaign
    for (const campaign of campaigns) {
      await processCampaignWithAgent(campaign);
    }
  } catch (error) {
    logger.error('Error processing agent campaigns', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

/**
 * Process a campaign with an AI agent
 * @param campaign Campaign data
 */
async function processCampaignWithAgent(campaign: any) {
  try {
    logger.info(`Processing campaign ${campaign.id} with agent ${campaign.agent.name}`);

    // Check if agent has required capabilities
    const capabilities = campaign.agent.capabilities || [];
    const hasWebScraping = capabilities.includes('web_scraping');
    const hasLinkedInResearch = capabilities.includes('linkedin_research');
    const hasPersonalization = capabilities.includes('personalization');
    const hasAppointmentScheduling = capabilities.includes('appointment_scheduling');

    // Process each lead in the campaign
    for (const campaignLead of campaign.leads) {
      // Skip leads that are not active
      if (campaignLead.status !== 'active') {
        continue;
      }

      const lead = campaignLead.lead;
      logger.info(`Processing lead ${lead.email} for campaign ${campaign.id}`);

      // Get lead's custom fields
      const customFields = lead.customFields as Record<string, any> || {};

      // Check if we have URLs to scrape
      const websiteUrl = customFields.websiteUrl || customFields.website;
      const linkedInUrl = customFields.linkedInUrl || customFields.linkedInProfile;
      const companyLinkedInUrl = customFields.companyLinkedInUrl || customFields.companyLinkedIn;

      // Gather data if we have capabilities and URLs
      let scrapedData: any = {};

      if (hasWebScraping && websiteUrl) {
        try {
          const websiteData = await AgentEnhancementService.scrapeWebsite(websiteUrl);
          scrapedData.website = websiteData;
        } catch (error) {
          logger.error(`Error scraping website for lead ${lead.id}`, {
            error: error instanceof Error ? error.message : 'Unknown error',
            websiteUrl,
          });
        }
      }

      if (hasLinkedInResearch && linkedInUrl) {
        try {
          const profileData = await AgentEnhancementService.scrapeLinkedInProfile(linkedInUrl);
          scrapedData.linkedInProfile = profileData;
        } catch (error) {
          logger.error(`Error scraping LinkedIn profile for lead ${lead.id}`, {
            error: error instanceof Error ? error.message : 'Unknown error',
            linkedInUrl,
          });
        }
      }

      if (hasLinkedInResearch && companyLinkedInUrl) {
        try {
          const companyData = await AgentEnhancementService.scrapeLinkedInProfile(companyLinkedInUrl);
          scrapedData.companyLinkedIn = companyData;
        } catch (error) {
          logger.error(`Error scraping company LinkedIn for lead ${lead.id}`, {
            error: error instanceof Error ? error.message : 'Unknown error',
            companyLinkedInUrl,
          });
        }
      }

      // Generate personalized email if we have personalization capability
      if (hasPersonalization && Object.keys(scrapedData).length > 0) {
        try {
          const emailContent = await AgentEnhancementService.generatePersonalizedEmail(
            lead.id,
            campaign.agent.id,
            scrapedData
          );

          // Store the generated email for the current step
          if (campaignLead.currentStep) {
            await prisma.campaignStep.update({
              where: { id: campaignLead.currentStepId },
              data: {
                subject: emailContent.subject,
                content: emailContent.html,
              },
            });

            logger.info(`Updated step ${campaignLead.currentStepId} with personalized content for lead ${lead.id}`);
          }
        } catch (error) {
          logger.error(`Error generating personalized email for lead ${lead.id}`, {
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    }
  } catch (error) {
    logger.error(`Error processing campaign ${campaign.id} with agent`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      campaignId: campaign.id,
      agentId: campaign.agent?.id,
    });
  }
}

/**
 * Start the reminder worker
 * This worker processes reminder jobs from the queue
 */
function startReminderWorker() {
  const reminderWorker = new Worker(
    'reminder-queue',
    async (job) => {
      await ReminderService.processReminderJob(job.data);
    },
    {
      connection: getRedisConnection(),
      concurrency: 5,
    }
  );

  reminderWorker.on('completed', (job) => {
    logger.info(`Reminder job ${job.id} completed successfully`);
  });

  reminderWorker.on('failed', (job, error) => {
    logger.error(`Reminder job ${job?.id} failed`, {
      error: error.message,
      stack: error.stack,
    });
  });

  logger.info('Reminder worker started');
}
