import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "next-auth/middleware"

// Function to handle tracking subdomains and custom tracking domains
function handleTrackingSubdomain(req: NextRequest) {
  const { pathname, search } = req.nextUrl
  const host = req.headers.get('host') || ''

  // Check for common tracking subdomains
  const trackingSubdomains = ['track', 'open', 'email', 'click', 'bounce']
  const hostParts = host.split('.')
  const subdomain = hostParts[0].toLowerCase()

  // Check if this is a tracking domain
  const isTrackingDomain = trackingSubdomains.includes(subdomain) ||
                          pathname === '/pixel' ||
                          pathname === '/link' ||
                          pathname === '/unsubscribe' ||
                          pathname === '/api/tracking/pixel' ||
                          pathname === '/api/tracking/link' ||
                          pathname === '/api/tracking/unsubscribe' ||
                          pathname === '/api/tracking/bounce'

  if (isTrackingDomain) {
    // Map the request to the appropriate tracking endpoint
    let newPathname = ''

    // Handle pixel tracking
    if (pathname === '/pixel' || subdomain === 'track' || subdomain === 'open' || subdomain === 'email') {
      newPathname = '/api/tracking/pixel'
    }
    // Handle link tracking
    else if (pathname === '/link' || subdomain === 'click') {
      newPathname = '/api/tracking/link'
    }
    // Handle unsubscribe
    else if (pathname === '/unsubscribe') {
      newPathname = '/api/tracking/unsubscribe'
    }
    // Handle bounce tracking
    else if (subdomain === 'bounce') {
      newPathname = '/api/tracking/bounce'
    }
    // If already pointing to a tracking API, use it directly
    else if (pathname.startsWith('/api/tracking/')) {
      newPathname = pathname
    }
    // If no matching endpoint, redirect to home
    else {
      return NextResponse.redirect(new URL('/', req.url))
    }

    // Create a new URL with the appropriate API endpoint
    const url = new URL(newPathname + search, req.url)

    // Log the tracking request for debugging
    console.log(`Tracking request: ${host}${pathname} -> ${url.pathname}`);

    // Rewrite the request to the appropriate API endpoint
    return NextResponse.rewrite(url)
  }

  return null
}

// Middleware function that handles tracking subdomains
function middleware(req: NextRequest) {
  // First, check if this is a tracking subdomain request
  const trackingResponse = handleTrackingSubdomain(req)
  if (trackingResponse) {
    return trackingResponse
  }

  // Check if the request is for a tracking endpoint or health check
  const { pathname } = req.nextUrl
  if (pathname.startsWith('/api/tracking/') || pathname === '/api/health-check') {
    // Skip authentication for tracking endpoints and health check
    const response = NextResponse.next()
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
    return response
  }

  // For all other routes, apply security headers
  const response = NextResponse.next()
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  return response
}

// Apply authentication middleware to protected routes
export default withAuth(middleware, {
  callbacks: {
    authorized: ({ token }) => !!token
  },
  pages: {
    signIn: '/auth/signin',
  },
})

export const config = {
  matcher: [
    // Protected routes that require authentication
    "/dashboard/:path*",
    "/campaigns/:path*",
    "/email-accounts/:path*",
    "/admin/:path*",  // This line handles admin routes
    "/api/campaigns/:path*",
    "/api/email-accounts/:path*",
    "/api/admin/:path*",
    // Removed tracking endpoints from authentication
    // "/api/tracking/:path*",
    "/leads/:path*",
    "/lead-lists/:path*",
    "/settings/:path*",
    "/templates/:path*",
    "/analytics/:path*",
    "/notifications/:path*",
    // Exclude auth pages, health check endpoint, tracking endpoints, and super admin routes
    // Modified to explicitly exclude all auth-related paths and super admin routes
    "/((?!api\\/health-check|api\\/tracking|api\\/auth|api\\/super-admin|auth\\/).*)" // This matches everything except excluded paths
  ]
}
