import { useSession } from 'next-auth/react';
import { useState } from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/Layout';
import SequenceFlowDiagram from '@/components/campaigns/SequenceFlowDiagram';
import LeadProgressTracker from '@/components/campaigns/LeadProgressTracker';
import SequencePerformanceMetrics from '@/components/campaigns/SequencePerformanceMetrics';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import {
  Button,
  Card,
  Tabs,
  Tag,
  Tooltip,
  Modal,
  Spin,
  Typography,
  Statistic,
  Progress,
  Table,
  Space,
  Divider,
  Alert,
  message
} from 'antd';
import {
  MailOutlined,
  EyeOutlined,
  MessageOutlined,
  ExclamationCircleOutlined,
  StopOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  LinkOutlined,
  WarningOutlined,
  SyncOutlined,
  ReloadOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { confirm } = Modal;

interface CampaignStep {
  id: string;
  name: string;
  type: string;
  position: number;
  subject?: string;
  content?: string;
  emailAccountId?: string;
  emailAccount?: {
    id: string;
    name: string;
    email: string;
  };
  waitDuration?: number;
  waitUntil?: string;
  conditionType?: string;
  conditionStepId?: string;
  conditionTimeframe?: number;
  actionType?: string;
  actionValue?: string;
  // Added properties for real-time data
  conditionStep?: CampaignStep;
  conditionedSteps?: CampaignStep[];
  activities?: Array<{
    id: string;
    status: string;
    sentAt?: string;
    openedAt?: string;
    clickedAt?: string;
    repliedAt?: string;
    bouncedAt?: string;
    error?: string;
    campaignLeadId?: string;
  }>;
}

interface CampaignLead {
  id: string;
  campaignId: string;
  leadId: string;
  status: string;
  currentStepId: string;
  currentStep?: CampaignStep; // Added for real-time tracking
  lead: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
  };
  stepActivities: Array<{
    id: string;
    campaignLeadId: string;
    stepId: string;
    status: string;
    sentAt?: string;
    openedAt?: string;
    clickedAt?: string;
    repliedAt?: string;
    bouncedAt?: string;
    errorMessage?: string;
    step: CampaignStep;
  }>;
}

interface SequenceCampaign {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
  errorMessage?: string;
  steps: CampaignStep[];
  leads: CampaignLead[];
  analytics: {
    totalLeads: number;
    sentCount: number;
    openCount: number;
    clickCount: number;
    replyCount: number;
    bounceCount: number;
    unsubscribeCount: number;
    openRate: number;
    clickRate: number;
    replyRate: number;
    bounceRate: number;
    unsubscribeRate: number;
    leadsByStatus: {
      active: number;
      completed: number;
      unsubscribed: number;
      bounced: number;
    };
    stepCompletion: {
      [key: string]: {
        total: number;
        completed: number;
        pending: number;
        failed: number;
      };
    };
  };
}

export default function SequenceCampaignDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const queryClient = useQueryClient();
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const [activeTab, setActiveTab] = useState('overview');
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [isCheckingConditions, setIsCheckingConditions] = useState(false);

  // Fetch campaign data with real-time updates
  const { data: campaign, isPending: isLoading } = useQuery<SequenceCampaign>({
    queryKey: ['campaign', id],
    queryFn: () => api.get(`/campaigns/sequence/${id}`).then(res => res.data),
    enabled: !!id && status === 'authenticated',
    // Use a fixed refetch interval for all campaigns
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  // Update campaign mutation
  const updateCampaignMutation = useMutation({
    mutationFn: (data: any) => api.put(`/campaigns/sequence/${id}`, data),
    onSuccess: () => {
      message.success('Campaign updated successfully');
      queryClient.invalidateQueries({ queryKey: ['campaign', id] });
      queryClient.invalidateQueries({ queryKey: ['campaigns'] });
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to update campaign');
    },
  });

  // Delete campaign mutation
  const deleteCampaignMutation = useMutation({
    mutationFn: () => api.delete(`/campaigns/sequence/${id}`),
    onSuccess: () => {
      message.success('Campaign deleted successfully');
      router.push('/campaigns');
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to delete campaign');
    },
  });

  // Check conditions mutation
  const checkConditionsMutation = useMutation({
    mutationFn: () => api.post(`/campaigns/sequence/check-conditions?campaignId=${id}`),
    onSuccess: () => {
      message.success('Sequence conditions checked successfully');
      queryClient.invalidateQueries({ queryKey: ['campaign', id] });
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to check sequence conditions');
    },
    onSettled: () => {
      setIsCheckingConditions(false);
    }
  });

  // Handle campaign actions
  const handleStartCampaign = () => {
    updateCampaignMutation.mutate({ status: 'active' });
  };

  const handlePauseCampaign = () => {
    updateCampaignMutation.mutate({ status: 'paused' });
  };

  const handleResumeCampaign = () => {
    updateCampaignMutation.mutate({ status: 'active' });
  };

  const handleStopCampaign = () => {
    confirm({
      title: 'Are you sure you want to stop this campaign?',
      icon: <ExclamationCircleOutlined />,
      content: 'This action cannot be undone. The campaign will be marked as completed and no more emails will be sent.',
      onOk() {
        updateCampaignMutation.mutate({ status: 'completed' });
      },
    });
  };

  const handleDeleteCampaign = () => {
    setIsDeleteModalVisible(true);
  };

  const handleCheckConditions = () => {
    setIsCheckingConditions(true);
    checkConditionsMutation.mutate();
  };

  const confirmDelete = () => {
    deleteCampaignMutation.mutate();
  };

  const cancelDelete = () => {
    setIsDeleteModalVisible(false);
  };

  // Helper function to get status tag
  const getStatusTag = (status: string) => {
    switch (status) {
      case 'draft':
        return <Tag color="default">Draft</Tag>;
      case 'active':
        return <Tag color="green">Active</Tag>;
      case 'paused':
        return <Tag color="orange">Paused</Tag>;
      case 'completed':
        return <Tag color="blue">Completed</Tag>;
      case 'scheduled':
        return <Tag color="purple">Scheduled</Tag>;
      case 'failed':
        return <Tag color="red">Failed</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  // Helper function to get step type tag
  const getStepTypeTag = (type: string) => {
    switch (type) {
      case 'email':
        return <Tag color="blue">Email</Tag>;
      case 'wait':
        return <Tag color="orange">Wait</Tag>;
      case 'condition':
        return <Tag color="green">Condition</Tag>;
      case 'action':
        return <Tag color="purple">Action</Tag>;
      default:
        return <Tag color="default">{type}</Tag>;
    }
  };

  if (isLoading || !campaign) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout title={`Sequence Campaign: ${campaign.name}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <div className="flex items-center">
              <Title level={2} className="mb-0 mr-2">{campaign.name}</Title>
              {getStatusTag(campaign.status)}
            </div>
            {campaign.description && (
              <Text type="secondary" className="mt-1 block">
                {campaign.description}
              </Text>
            )}
          </div>
          <div className="space-x-2">
            {campaign.status === 'draft' && (
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleStartCampaign}
                loading={updateCampaignMutation.isPending}
              >
                Start Campaign
              </Button>
            )}
            {campaign.status === 'active' && (
              <Button
                icon={<PauseCircleOutlined />}
                onClick={handlePauseCampaign}
                loading={updateCampaignMutation.isPending}
              >
                Pause Campaign
              </Button>
            )}
            {campaign.status === 'paused' && (
              <Button
                icon={<PlayCircleOutlined />}
                onClick={handleResumeCampaign}
                loading={updateCampaignMutation.isPending}
              >
                Resume Campaign
              </Button>
            )}
            {(campaign.status === 'active' || campaign.status === 'paused') && (
              <Button
                danger
                icon={<StopOutlined />}
                onClick={handleStopCampaign}
                loading={updateCampaignMutation.isPending}
              >
                Stop Campaign
              </Button>
            )}
            <Button
              icon={<EditOutlined />}
              onClick={() => router.push(`/campaigns/sequence/${id}/edit`)}
            >
              Edit Campaign
            </Button>
            {/* Make Process Sequence button available in all states */}
            <Button
              type="primary"
              icon={<SyncOutlined spin={isCheckingConditions} />}
              onClick={handleCheckConditions}
              loading={isCheckingConditions}
            >
              Process Sequence
            </Button>

            {/* Add button to reactivate completed campaigns */}
            {campaign.status === 'completed' && (
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={() => {
                  confirm({
                    title: 'Reactivate Campaign',
                    icon: <QuestionCircleOutlined />,
                    content: 'This will reactivate the campaign and allow leads to continue through the sequence. Continue?',
                    onOk() {
                      updateCampaignMutation.mutate({ status: 'active' });
                    }
                  });
                }}
              >
                Reactivate Campaign
              </Button>
            )}
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleDeleteCampaign}
            >
              Delete
            </Button>
          </div>
        </div>

        {campaign.errorMessage && (
          <Alert
            message="Campaign Error"
            description={campaign.errorMessage}
            type="error"
            showIcon
            className="mb-6"
          />
        )}

        {/* Add a helpful info alert for sequence campaigns */}
        <Alert
          message="Sequence Campaign Information"
          description={
            <div>
              <p>This is a sequence campaign that automatically progresses leads through multiple steps based on their actions.</p>
              <ul className="list-disc ml-5 mt-2">
                <li>Leads will move through the sequence when they meet the conditions (opens, clicks, etc.)</li>
                <li><strong>Important:</strong> Click the "Process Sequence" button to manually check conditions and move leads to the next step</li>
                <li>If the campaign was marked as completed prematurely, use the "Process Sequence" button to check and continue the sequence</li>
                <li>For completed campaigns, use the "Reactivate Campaign" button to restart the sequence flow</li>
              </ul>
              {campaign.status === 'completed' && (
                <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900 rounded">
                  <p className="font-semibold">⚠️ This campaign is marked as completed</p>
                  <p>If leads haven't progressed through all steps, click "Process Sequence" to check conditions or "Reactivate Campaign" to restart the flow.</p>
                </div>
              )}
            </div>
          }
          type="info"
          showIcon
          className="mb-6"
        />

        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'overview',
              label: 'Overview',
              children: (
                <>
                  <div className="mb-6">
                    <SequencePerformanceMetrics
                      analytics={campaign.analytics}
                      status={campaign.status}
                    />
                  </div>

                  <Card title="Campaign Details" className="mb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <div className="mb-4">
                          <Text strong>Campaign Type:</Text>
                          <div>Sequence</div>
                        </div>
                        <div className="mb-4">
                          <Text strong>Created At:</Text>
                          <div>{new Date(campaign.createdAt).toLocaleString()}</div>
                        </div>
                        {campaign.startDate && (
                          <div className="mb-4">
                            <Text strong>Started At:</Text>
                            <div>{new Date(campaign.startDate).toLocaleString()}</div>
                          </div>
                        )}
                        {campaign.endDate && (
                          <div className="mb-4">
                            <Text strong>Ended At:</Text>
                            <div>{new Date(campaign.endDate).toLocaleString()}</div>
                          </div>
                        )}
                      </div>
                      <div>
                        <div className="mb-4">
                          <Text strong>Status:</Text>
                          <div>{getStatusTag(campaign.status)}</div>
                        </div>
                        <div className="mb-4">
                          <Text strong>Total Steps:</Text>
                          <div>{campaign.steps.length}</div>
                        </div>
                        <div className="mb-4">
                          <Text strong>Email Steps:</Text>
                          <div>{campaign.steps.filter(step => step.type === 'email').length}</div>
                        </div>
                        <div className="mb-4">
                          <Text strong>Total Recipients:</Text>
                          <div>{campaign.leads.length}</div>
                        </div>
                      </div>
                    </div>
                  </Card>

                  <Card title="Sequence Flow" className="mb-6">
                    <SequenceFlowDiagram
                      steps={campaign.steps}
                      stepCompletion={campaign.analytics.stepCompletion}
                    />
                  </Card>
                </>
              ),
            },
            {
              key: 'steps',
              label: 'Sequence Steps',
              children: (
                <Card>
                  <Table
                    dataSource={campaign.steps}
                    rowKey="id"
                    pagination={false}
                    columns={[
                      {
                        title: 'Position',
                        dataIndex: 'position',
                        key: 'position',
                        render: (position) => position + 1,
                      },
                      {
                        title: 'Name',
                        dataIndex: 'name',
                        key: 'name',
                      },
                      {
                        title: 'Type',
                        dataIndex: 'type',
                        key: 'type',
                        render: (type) => getStepTypeTag(type),
                      },
                      {
                        title: 'Details',
                        key: 'details',
                        render: (_, step) => {
                          if (step.type === 'email') {
                            return (
                              <div>
                                <div><strong>Subject:</strong> {step.subject}</div>
                                <div><strong>Sender:</strong> {step.emailAccount?.name} ({step.emailAccount?.email})</div>
                              </div>
                            );
                          } else if (step.type === 'wait') {
                            if (step.waitDuration) {
                              return <div>Wait for {step.waitDuration} hours</div>;
                            } else if (step.waitUntil) {
                              return <div>Wait until {new Date(step.waitUntil).toLocaleString()}</div>;
                            }
                            return <div>Wait step</div>;
                          } else if (step.type === 'condition') {
                            return (
                              <div>
                                <div><strong>Condition:</strong> {step.conditionType?.replace('_', ' ')}</div>
                                <div><strong>Timeframe:</strong> {step.conditionTimeframe} hours</div>
                              </div>
                            );
                          } else if (step.type === 'action') {
                            return (
                              <div>
                                <div><strong>Action:</strong> {step.actionType?.replace('_', ' ')}</div>
                                {step.actionValue && <div><strong>Value:</strong> {step.actionValue}</div>}
                              </div>
                            );
                          }
                          return <div>-</div>;
                        },
                      },
                      {
                        title: 'Progress',
                        key: 'progress',
                        render: (_, step) => {
                          const stepStats = campaign.analytics.stepCompletion[step.id] || {
                            total: 0,
                            completed: 0,
                            pending: 0,
                            failed: 0
                          };

                          const percent = stepStats.total > 0
                            ? Math.round((stepStats.completed / stepStats.total) * 100)
                            : 0;

                          return (
                            <div>
                              <Progress percent={percent} size="small" />
                              <div className="text-xs mt-1">
                                {stepStats.completed} / {stepStats.total} completed
                                {stepStats.failed > 0 && (
                                  <span className="text-red-500 ml-2">
                                    ({stepStats.failed} failed)
                                  </span>
                                )}
                              </div>
                            </div>
                          );
                        },
                      },
                    ]}
                  />
                </Card>
              ),
            },
            {
              key: 'recipients',
              label: 'Recipients',
              children: (
                <Card>
                  <LeadProgressTracker
                    leads={campaign.leads}
                    steps={campaign.steps}
                  />
                </Card>
              ),
            },
          ]}
        />

        {/* Delete confirmation modal */}
        <Modal
          title="Delete Campaign"
          open={isDeleteModalVisible}
          onOk={confirmDelete}
          onCancel={cancelDelete}
          okText="Delete"
          okButtonProps={{ danger: true, loading: deleteCampaignMutation.isPending }}
          cancelButtonProps={{ disabled: deleteCampaignMutation.isPending }}
        >
          <p>Are you sure you want to delete this campaign? This action cannot be undone.</p>
        </Modal>
      </div>
    </Layout>
  );
}
