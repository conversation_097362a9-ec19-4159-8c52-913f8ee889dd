import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  Form,
  Input,
  Button,
  Card,
  Steps,
  message,
  Select,
  Divider,
  Typography,
  Space,
  Radio,
  DatePicker,
  Tag
} from 'antd';
import {
  MailOutlined,
  ClockCircleOutlined,
  BranchesOutlined,
  RobotOutlined,
  PlusOutlined,
  SaveOutlined,
  SendOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import Layout from '@/components/Layout';
import CampaignStepEditor from '@/components/campaigns/CampaignStepEditor';

const { Title, Text } = Typography;
const { Option } = Select;

// Step types
const STEP_TYPES = {
  EMAIL: 'email',
  WAIT: 'wait',
  CONDITION: 'condition',
  ACTION: 'action'
};

// Initial step template
const createInitialStep = (position: number, type: string = STEP_TYPES.EMAIL) => {
  console.log('Creating initial step with type:', type);

  // Force lowercase type to ensure consistency
  const normalizedType = type.toLowerCase();

  const step = {
    id: `temp_${Date.now()}_${position}`,
    position,
    type: normalizedType, // Use normalized type
    name: '',
    description: '',
    emailAccountId: '',
    subject: '',
    content: '',
    waitDuration: 24, // Default wait time: 24 hours
    conditionType: 'opened',
    conditionStepId: '',
    conditionTimeframe: 48, // Default: 48 hours
    actionType: 'update_lead',
    actionValue: '',
  };

  console.log('Step created:', step);
  return step;
};

export default function NewSequenceCampaignPage() {
  const router = useRouter();
  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const [form] = Form.useForm();
  // Preserve form values between steps
  const [formValues, setFormValues] = useState({
    name: '',
    description: '',
    agentId: '',
    leadIds: [],
    listId: undefined,
  });
  const [currentStep, setCurrentStep] = useState(0);
  const [campaignSteps, setCampaignSteps] = useState([
    createInitialStep(0, STEP_TYPES.EMAIL)
  ]);
  const [selectedStepIndex, setSelectedStepIndex] = useState(0);
  const [sendType, setSendType] = useState('now'); // 'now' or 'schedule'
  const [scheduledDate, setScheduledDate] = useState<dayjs.Dayjs | null>(dayjs().add(1, 'day'));
  const [recipientType, setRecipientType] = useState('leads'); // 'leads' or 'list'
  const [listLeads, setListLeads] = useState<any[]>([]);

  // Monitor campaignSteps changes
  useEffect(() => {
    console.log('🔍 CAMPAIGN STEPS CHANGED:', JSON.stringify(campaignSteps));
    console.log('🔍 Selected step index:', selectedStepIndex);
    if (selectedStepIndex >= 0 && selectedStepIndex < campaignSteps.length) {
      console.log('🔍 Selected step:', JSON.stringify(campaignSteps[selectedStepIndex]));
    }
  }, [campaignSteps, selectedStepIndex]);

  // Fetch email accounts
  const { data: emailAccounts = [] } = useQuery({
    queryKey: ['emailAccounts'],
    queryFn: () => fetch('/api/email-accounts').then(res => res.json()),
    enabled: !!session,
  });

  // Fetch AI agents
  const { data: agents = [] } = useQuery({
    queryKey: ['agents'],
    queryFn: () => fetch('/api/agents').then(res => res.json()),
    enabled: !!session,
  });

  // Fetch leads
  const { data: leads = [] } = useQuery({
    queryKey: ['leads'],
    queryFn: () => fetch('/api/leads?limit=1000').then(res => res.json()),
    enabled: !!session,
  });

  // Fetch lead lists
  const { data: leadLists = [] } = useQuery({
    queryKey: ['leadLists'],
    queryFn: () => fetch('/api/lead-lists').then(res => res.json()),
    enabled: !!session,
  });

  // Function to fetch leads for a specific list
  const fetchListLeads = async (listId: string) => {
    if (!listId) return;

    try {
      const response = await fetch(`/api/lead-lists/${listId}/leads`);
      const data = await response.json();
      setListLeads(data);
    } catch (error) {
      console.error('Error fetching list leads:', error);
      message.error('Failed to fetch leads for the selected list');
    }
  };

  // Create campaign mutation
  const createCampaignMutation = useMutation({
    mutationFn: async (values: any) => {
      const response = await fetch('/api/campaigns/sequence', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create campaign');
      }

      return response.json();
    },
    onSuccess: (data) => {
      message.success('Campaign created successfully');
      router.push(`/campaigns/sequence/${data.id}`);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to create campaign');
    },
  });

  const handleStepTypeChange = (type: string, index: number) => {
    console.log('🔍 DROPDOWN CHANGED - Changing step type to:', type);
    console.log('🔍 Current step before change:', JSON.stringify(campaignSteps[index]));
    console.log('🔍 All steps before change:', JSON.stringify(campaignSteps));

    // Create a deep copy of the steps array
    const updatedSteps = JSON.parse(JSON.stringify(campaignSteps));

    // Update the type of the selected step
    updatedSteps[index] = {
      ...updatedSteps[index],
      type: type.toLowerCase() // Force lowercase
    };

    console.log('🔍 Updated step:', JSON.stringify(updatedSteps[index]));
    console.log('🔍 All updated steps:', JSON.stringify(updatedSteps));

    // Set the updated steps array
    setCampaignSteps(updatedSteps);

    // Log after state update
    setTimeout(() => {
      console.log('🔍 AFTER STATE UPDATE - Current steps:', JSON.stringify(campaignSteps));
      console.log('🔍 AFTER STATE UPDATE - Selected step:', JSON.stringify(campaignSteps[index]));
    }, 100);
  };

  const handleStepChange = (stepData: any, index: number) => {
    const updatedSteps = [...campaignSteps];
    updatedSteps[index] = {
      ...updatedSteps[index],
      ...stepData
    };
    setCampaignSteps(updatedSteps);
  };

  const handleAddStep = (type: string = STEP_TYPES.EMAIL) => {
    console.log('🔍 BUTTON CLICKED - Adding step with type:', type);
    console.log('🔍 Current steps before adding:', JSON.stringify(campaignSteps));

    // Create a new step with explicit type setting
    const newStep = {
      id: `temp_${Date.now()}_${campaignSteps.length}`,
      position: campaignSteps.length,
      type: type.toLowerCase(), // Force lowercase
      name: '',
      description: '',
      emailAccountId: '',
      subject: '',
      content: '',
      waitDuration: 24, // Default wait time: 24 hours
      conditionType: 'opened',
      conditionStepId: '',
      conditionTimeframe: 48, // Default: 48 hours
      actionType: 'update_lead',
      actionValue: '',
    };

    console.log('🔍 New step created:', JSON.stringify(newStep));

    // Create a completely new array to avoid any reference issues
    const updatedSteps = [...campaignSteps, newStep];
    console.log('🔍 Updated steps array:', JSON.stringify(updatedSteps));

    // Set the new steps array
    setCampaignSteps(updatedSteps);

    // Set the selected step index to the new step
    const newIndex = campaignSteps.length;
    console.log('🔍 Setting selected step index to:', newIndex);
    setSelectedStepIndex(newIndex);

    // Log after state updates (will show in next render cycle)
    setTimeout(() => {
      console.log('🔍 AFTER STATE UPDATE - Current steps:', JSON.stringify(campaignSteps));
      console.log('🔍 AFTER STATE UPDATE - Selected step index:', selectedStepIndex);
    }, 100);
  };

  const handleRemoveStep = (index: number) => {
    if (campaignSteps.length <= 1) {
      message.error('Campaign must have at least one step');
      return;
    }

    const updatedSteps = campaignSteps.filter((_, i) => i !== index);
    // Update positions
    updatedSteps.forEach((step, i) => {
      step.position = i;
    });

    setCampaignSteps(updatedSteps);

    // Select previous step if the last one was removed
    if (index >= updatedSteps.length) {
      setSelectedStepIndex(updatedSteps.length - 1);
    } else {
      setSelectedStepIndex(index);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      // Get the current form values and merge with existing formValues
      const currentFormValues = form.getFieldsValue();
      const mergedValues = { ...formValues, ...currentFormValues };
      console.log('Form values at submission:', mergedValues);

      // Skip validation since we're on the final step and already validated
      // Just check for campaign name directly
      if (!mergedValues.name || mergedValues.name.trim() === '') {
        message.error('Campaign name is required');
        setCurrentStep(0); // Go back to the first step
        return;
      }

      // Validate steps with detailed logging
      console.log('Validating steps before submission:', JSON.stringify(campaignSteps));

      // Check for email steps
      const emailSteps = campaignSteps.filter(step => step.type === STEP_TYPES.EMAIL);
      if (emailSteps.length === 0) {
        message.error('Your sequence must include at least one email step');
        setCurrentStep(2); // Go to the sequence building step
        return;
      }

      // Validate each step
      const invalidSteps = [];
      for (const step of campaignSteps) {
        console.log(`Validating step ${step.position} (${step.type}):`, step);

        let isValid = true;
        let missingFields = [];

        if (step.type === STEP_TYPES.EMAIL) {
          if (!step.subject) missingFields.push('subject');
          if (!step.content) missingFields.push('content');
          if (!step.emailAccountId) missingFields.push('email account');
          isValid = !!(step.subject && step.content && step.emailAccountId);
        } else if (step.type === STEP_TYPES.CONDITION) {
          if (!step.conditionType) missingFields.push('condition type');
          if (!step.conditionStepId) missingFields.push('email step to check');
          isValid = !!(step.conditionType && step.conditionStepId);
        } else if (step.type === STEP_TYPES.ACTION) {
          if (!step.actionType) missingFields.push('action type');
          if (!step.actionValue) missingFields.push('action value');
          isValid = !!(step.actionType && step.actionValue);
        }

        console.log(`Step ${step.position} validation result:`, { isValid, missingFields });

        if (!isValid) {
          invalidSteps.push({
            position: step.position,
            type: step.type,
            missingFields
          });
        }
      }

      if (invalidSteps.length > 0) {
        console.error('Invalid steps found:', invalidSteps);
        const stepTypes = invalidSteps.map(s => `${s.type} (step ${s.position + 1})`).join(', ');
        const firstInvalid = invalidSteps[0];
        const fieldsMessage = firstInvalid.missingFields.join(', ');

        message.error(`Please complete all required fields in ${stepTypes}. Missing: ${fieldsMessage}`);
        setCurrentStep(2); // Go to the sequence building step
        return;
      }

      // Prepare campaign data with scheduling information
      const campaignData = {
        ...mergedValues, // Use merged values
        type: 'sequence',
        steps: campaignSteps,
        recipientType,
        status: sendType === 'now' ? 'active' : 'scheduled',
        scheduledFor: sendType === 'schedule' && scheduledDate ? scheduledDate.toISOString() : null
      };

      console.log('Submitting campaign data:', campaignData);
      createCampaignMutation.mutate(campaignData);
    } catch (error) {
      console.error('Form submission error:', error);
      message.error('An error occurred while creating the campaign');
    }
  };

  const steps = [
    {
      title: 'Campaign Details',
      content: (
        <Card className="mt-4">
          <Form.Item
            name="name"
            label="Campaign Name"
            rules={[{ required: true, message: 'Please enter a campaign name' }]}
          >
            <Input placeholder="Enter campaign name" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea
              placeholder="Enter campaign description"
              rows={4}
            />
          </Form.Item>

          <Form.Item
            name="agentId"
            label={
              <span>
                AI Agent <RobotOutlined /> <Text type="secondary">(Optional)</Text>
              </span>
            }
            tooltip="Select an AI agent to enhance your campaign with personalized content, follow-ups, and appointment scheduling"
          >
            <Select
              placeholder="Select an AI agent (optional)"
              allowClear
              style={{ width: '100%' }}
            >
              {agents.map((agent: any) => (
                <Option key={agent.id} value={agent.id}>
                  <div className="flex items-center">
                    <RobotOutlined className="mr-2" />
                    <div>
                      <div>{agent.name}</div>
                      <div className="text-xs text-gray-500">{agent.description}</div>
                    </div>
                    {agent.capabilities && agent.capabilities.length > 0 && (
                      <div className="ml-auto">
                        {agent.capabilities.includes('web_scraping') && (
                          <Tag color="blue">Web Scraping</Tag>
                        )}
                        {agent.capabilities.includes('linkedin_research') && (
                          <Tag color="purple">LinkedIn Research</Tag>
                        )}
                        {agent.capabilities.includes('personalization') && (
                          <Tag color="green">Personalization</Tag>
                        )}
                        {agent.capabilities.includes('appointment_scheduling') && (
                          <Tag color="orange">Appointment Scheduling</Tag>
                        )}
                      </div>
                    )}
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Card>
      ),
    },
    {
      title: 'Select Recipients',
      content: (
        <Card className="mt-4">
          <Form.Item label="Recipients">
            <Radio.Group
              value={recipientType}
              onChange={(e) => setRecipientType(e.target.value)}
              className="mb-4"
            >
              <Radio.Button value="leads">Select Individual Leads</Radio.Button>
              <Radio.Button value="list">Use Lead List</Radio.Button>
            </Radio.Group>

            {recipientType === 'leads' ? (
              <Form.Item
                name="leadIds"
                rules={[{ required: recipientType === 'leads', message: 'Please select at least one recipient' }]}
                noStyle
              >
                <Select
                  mode="multiple"
                  placeholder="Select recipients"
                  style={{ width: '100%' }}
                  optionFilterProp="label"
                  filterOption={(input, option) => {
                    if (typeof option?.label === 'string') {
                      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }
                    return false;
                  }}
                >
                  {leads.map((lead: any) => {
                    const label = `${lead.email} ${lead.firstName ? `(${lead.firstName} ${lead.lastName || ''})` : ''}`;
                    return (
                      <Option key={lead.id} value={lead.id} label={label}>
                        {label}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            ) : (
              <Form.Item
                name="listId"
                rules={[{ required: recipientType === 'list', message: 'Please select a lead list' }]}
                noStyle
              >
                <Select
                  placeholder="Select a lead list"
                  style={{ width: '100%' }}
                  onChange={(listId) => {
                    // Fetch leads for the selected list
                    fetchListLeads(listId);
                  }}
                >
                  {leadLists.map((list: any) => (
                    <Option key={list.id} value={list.id}>
                      {list.name} ({list._count?.leads || 0} leads)
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            {recipientType === 'list' && form.getFieldValue('listId') && (
              <div className="mt-2">
                <Text type="secondary">
                  This list contains {listLeads.length} leads.
                </Text>
              </div>
            )}
          </Form.Item>
        </Card>
      ),
    },
    {
      title: 'Build Sequence',
      content: (
        <div className="mt-4">
          <div className="flex">
            <div className="w-1/4 pr-4">
              <Card className="mb-4 dark:bg-dark-card dark:border-dark-border">
                <div className="flex justify-between items-center mb-4">
                  <Title level={5} className="m-0 dark:text-white">Steps</Title>
                  <div>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => handleAddStep()}
                    >
                      Add Step
                    </Button>
                  </div>
                </div>
                <div className="overflow-y-auto max-h-96">
                  {campaignSteps.map((step, index) => (
                    <div
                      key={step.id}
                      className={`p-2 mb-2 rounded cursor-pointer ${selectedStepIndex === index ? 'bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800' : 'hover:bg-gray-50 dark:hover:bg-gray-700'}`}
                      onClick={() => setSelectedStepIndex(index)}
                    >
                      <div className="flex items-center">
                        {step.type === STEP_TYPES.EMAIL && <MailOutlined className="mr-2 text-blue-500" />}
                        {step.type === STEP_TYPES.WAIT && <ClockCircleOutlined className="mr-2 text-orange-500" />}
                        {step.type === STEP_TYPES.CONDITION && <BranchesOutlined className="mr-2 text-green-500" />}
                        {step.type === STEP_TYPES.ACTION && <RobotOutlined className="mr-2 text-purple-500" />}
                        <div className="flex-1 overflow-hidden">
                          <div className="font-medium truncate dark:text-white">
                            {step.name || `Step ${index + 1}`}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                            {step.type}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              <Card className="dark:bg-dark-card dark:border-dark-border">
                <Title level={5} className="dark:text-white">Add Step</Title>
                <Space direction="vertical" className="w-full">
                  <Button
                    block
                    icon={<MailOutlined />}
                    onClick={() => handleAddStep('email')}
                  >
                    Email
                  </Button>
                  <Button
                    block
                    icon={<ClockCircleOutlined />}
                    onClick={() => handleAddStep('wait')}
                  >
                    Wait
                  </Button>
                  <Button
                    block
                    icon={<BranchesOutlined />}
                    onClick={() => handleAddStep('condition')}
                  >
                    Condition
                  </Button>
                  <Button
                    block
                    icon={<RobotOutlined />}
                    onClick={() => handleAddStep('action')}
                  >
                    Action
                  </Button>
                </Space>
              </Card>
            </div>

            <div className="w-3/4">
              <Card className="dark:bg-dark-card dark:border-dark-border">
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center">
                    <Select
                      value={campaignSteps[selectedStepIndex].type}
                      onChange={(value) => {
                        console.log('🔍 DROPDOWN CHANGED - Changing step type to:', value);
                        // Create a deep copy of the steps array
                        const updatedSteps = JSON.parse(JSON.stringify(campaignSteps));
                        // Update the type of the selected step
                        updatedSteps[selectedStepIndex] = {
                          ...updatedSteps[selectedStepIndex],
                          type: value.toLowerCase() // Force lowercase
                        };
                        console.log('🔍 Updated step:', JSON.stringify(updatedSteps[selectedStepIndex]));
                        // Set the updated steps array
                        setCampaignSteps(updatedSteps);
                      }}
                      className="w-32 mr-2 dark:bg-dark-card dark:text-white"
                    >
                      <Option value="email">Email</Option>
                      <Option value="wait">Wait</Option>
                      <Option value="condition">Condition</Option>
                      <Option value="action">Action</Option>
                    </Select>
                    <Title level={5} className="m-0 dark:text-white">
                      {campaignSteps[selectedStepIndex].name || `Step ${selectedStepIndex + 1}`}
                    </Title>
                  </div>
                  <Button
                    danger
                    onClick={() => handleRemoveStep(selectedStepIndex)}
                  >
                    Remove Step
                  </Button>
                </div>

                <Divider />

                <CampaignStepEditor
                  step={campaignSteps[selectedStepIndex]}
                  onChange={(data) => handleStepChange(data, selectedStepIndex)}
                  emailAccounts={emailAccounts}
                  steps={campaignSteps}
                  currentStepIndex={selectedStepIndex}
                />
              </Card>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Schedule & Send',
      content: (
        <Card className="mt-4">
          <div className="mb-6">
            <Title level={4}>Ready to Launch Your Campaign</Title>
            <Text type="secondary">
              Choose when to start sending your sequence campaign
            </Text>
          </div>

          <Form.Item label="When to send">
            <Radio.Group
              value={sendType}
              onChange={(e) => setSendType(e.target.value)}
              className="w-full"
            >
              <div className="flex flex-col space-y-4 w-full">
                <Radio value="now" className="p-4 border rounded-lg w-full flex items-start">
                  <div>
                    <div className="flex items-center">
                      <SendOutlined className="mr-2 text-green-500" />
                      <span className="font-medium">Send immediately</span>
                    </div>
                    <div className="text-gray-500 ml-6 mt-1">
                      Your campaign will start processing right away
                    </div>
                  </div>
                </Radio>

                <Radio value="schedule" className="p-4 border rounded-lg w-full flex items-start">
                  <div className="w-full">
                    <div className="flex items-center">
                      <CalendarOutlined className="mr-2 text-blue-500" />
                      <span className="font-medium">Schedule for later</span>
                    </div>
                    <div className="text-gray-500 ml-6 mt-1 mb-3">
                      Choose a specific date and time to start your campaign
                    </div>

                    {sendType === 'schedule' && (
                      <div className="ml-6 mt-2">
                        <DatePicker
                          showTime
                          value={scheduledDate}
                          onChange={(date) => setScheduledDate(date)}
                          className="w-full"
                          format="YYYY-MM-DD HH:mm:ss"
                          disabledDate={(current) => current && current < dayjs().startOf('day')}
                        />
                      </div>
                    )}
                  </div>
                </Radio>
              </div>
            </Radio.Group>
          </Form.Item>

          <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex items-center mb-2">
              <Title level={5} className="m-0">Campaign Summary</Title>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <Text>Campaign Name:</Text>
                <Text strong>{form.getFieldValue('name')}</Text>
              </div>
              <div className="flex justify-between">
                <Text>Total Steps:</Text>
                <Text strong>{campaignSteps.length}</Text>
              </div>
              <div className="flex justify-between">
                <Text>Email Steps:</Text>
                <Text strong>{campaignSteps.filter(step => step.type === 'email').length}</Text>
              </div>
              <div className="flex justify-between">
                <Text>Recipients:</Text>
                <Text strong>
                  {recipientType === 'leads'
                    ? `${form.getFieldValue('leadIds')?.length || 0} individual leads`
                    : `${listLeads.length || 0} leads from list ${form.getFieldValue('listId') ? `(${form.getFieldValue('listId')})` : ''}`}
                </Text>
              </div>
              <div className="flex justify-between">
                <Text>Launch Time:</Text>
                <Text strong>
                  {sendType === 'now' ? 'Immediately' : scheduledDate?.format('YYYY-MM-DD HH:mm:ss')}
                </Text>
              </div>
            </div>
          </div>
        </Card>
      ),
    },
  ];

  const validateFirstStep = async () => {
    try {
      // Save current values first
      const currentValues = form.getFieldsValue(['name', 'description', 'agentId']);
      setFormValues(prev => ({ ...prev, ...currentValues }));

      // Check name directly to avoid validation errors
      if (!currentValues.name || currentValues.name.trim() === '') {
        message.error('Please enter a campaign name');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Form validation error:', error);
      return false;
    }
  };

  const validateSecondStep = async () => {
    try {
      // Save current values first
      const currentValues = form.getFieldsValue(['leadIds', 'listId']);
      setFormValues(prev => ({ ...prev, ...currentValues }));

      // Validate based on recipient type
      if (recipientType === 'leads') {
        if (!currentValues.leadIds || currentValues.leadIds.length === 0) {
          message.error('Please select at least one recipient');
          return false;
        }
      } else {
        if (!currentValues.listId) {
          message.error('Please select a lead list');
          return false;
        }
      }
      return true;
    } catch (error) {
      console.error('Form validation error:', error);
      return false;
    }
  };

  const validateThirdStep = () => {
    console.log('Validating sequence steps:', JSON.stringify(campaignSteps));

    // Check if there are any email steps
    const emailSteps = campaignSteps.filter(step => step.type === STEP_TYPES.EMAIL);
    console.log('Email steps:', emailSteps.length);

    if (emailSteps.length === 0) {
      message.error('Your sequence must include at least one email step');
      return false;
    }

    // Check if all steps are properly configured
    const invalidSteps = campaignSteps.filter(step => {
      console.log(`Validating step ${step.position} (${step.type}):`, JSON.stringify(step));

      if (step.type === STEP_TYPES.EMAIL) {
        const isValid = step.subject && step.content && step.emailAccountId;
        console.log(`Email step validation: subject=${!!step.subject}, content=${!!step.content}, emailAccountId=${!!step.emailAccountId}, isValid=${isValid}`);
        return !isValid;
      }

      if (step.type === STEP_TYPES.CONDITION) {
        const isValid = step.conditionType && step.conditionStepId;
        console.log(`Condition step validation: conditionType=${step.conditionType}, conditionStepId=${step.conditionStepId}, isValid=${isValid}`);
        return !isValid;
      }

      if (step.type === STEP_TYPES.ACTION) {
        const isValid = step.actionType && step.actionValue;
        console.log(`Action step validation: actionType=${step.actionType}, actionValue=${step.actionValue}, isValid=${isValid}`);
        return !isValid;
      }

      return false;
    });

    console.log('Invalid steps:', invalidSteps.length, invalidSteps);

    if (invalidSteps.length > 0) {
      // Show more specific error messages
      const invalidTypes = invalidSteps.map(step => step.type);
      const errorMessage = `Please complete all required fields in ${invalidTypes.join(', ')} steps`;
      message.error(errorMessage);
      return false;
    }

    return true;
  };

  const next = async () => {
    try {
      // Save current form values
      const currentValues = form.getFieldsValue();
      setFormValues(prev => ({ ...prev, ...currentValues }));

      // Validate current step before proceeding
      let isValid = false;

      if (currentStep === 0) {
        isValid = await validateFirstStep();
      } else if (currentStep === 1) {
        isValid = await validateSecondStep();
      } else if (currentStep === 2) {
        isValid = validateThirdStep();
      } else {
        isValid = true;
      }

      if (!isValid) return;

      // Move to the next step
      setCurrentStep(currentStep + 1);

      // Restore form values in the next step
      setTimeout(() => {
        form.setFieldsValue({ ...formValues, ...currentValues });
        console.log('Restored form values:', { ...formValues, ...currentValues });
      }, 0);
    } catch (error) {
      console.error('Error during validation:', error);
      message.error('Please fix the errors before proceeding');
    }
  };

  const prev = () => {
    // Save current form values
    const currentValues = form.getFieldsValue();
    setFormValues(prev => ({ ...prev, ...currentValues }));

    // Move to the previous step
    setCurrentStep(currentStep - 1);

    // Restore form values in the previous step
    setTimeout(() => {
      form.setFieldsValue(formValues);
      console.log('Restored form values when going back:', formValues);
    }, 0);
  };

  // Add debugging for the steps array
  useEffect(() => {
    console.log('Steps array:', steps);
    console.log('Current step:', currentStep);
    console.log('Current step content:', steps[currentStep]?.content);
  }, [steps, currentStep]);

  // Log form values when they change
  useEffect(() => {
    const formValues = form.getFieldsValue();
    console.log('Current form values:', formValues);
  }, [form, currentStep]);

  // Log form values when they change

  // Update formValues when moving between steps
  useEffect(() => {
    // Save current form values
    const currentValues = form.getFieldsValue();
    setFormValues(prev => ({ ...prev, ...currentValues }));
    console.log('Updated form values:', { ...formValues, ...currentValues });

    // Restore form values after a short delay to ensure they're applied
    setTimeout(() => {
      form.setFieldsValue({ ...formValues, ...currentValues });
      console.log('Restored form values after step change:', { ...formValues, ...currentValues });
    }, 50);
  }, [currentStep, form]);

  // Initialize form with formValues
  useEffect(() => {
    form.setFieldsValue(formValues);
    console.log('Form initialized with values:', formValues);
  }, []);

  // Log form submission
  useEffect(() => {
    if (createCampaignMutation.isPending) {
      console.log('Submitting campaign with values:', formValues);
    }
  }, [createCampaignMutation.isPending, formValues]);

  // Check if we have all required components
  if (!Form || !Steps || !Radio || !Select || !DatePicker) {
    console.error('Missing required components:', {
      Form: !!Form,
      Steps: !!Steps,
      Radio: !!Radio,
      Select: !!Select,
      DatePicker: !!DatePicker
    });
    return <div>Loading components...</div>;
  }

  return (
    <Layout title="Create Sequence Campaign">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Title level={2}>Create Sequential Campaign</Title>
          <Text type="secondary">
            Build a multi-step email sequence with conditional logic and automated follow-ups
          </Text>
        </div>

        <Steps current={currentStep} className="mb-8">
          {steps.map(item => (
            <Steps.Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={formValues}
        >
          <div className="steps-content">{steps[currentStep]?.content}</div>

          <div className="steps-action mt-8 flex justify-between">
            {currentStep > 0 && (
              <Button onClick={() => prev()}>
                Previous
              </Button>
            )}

            {currentStep < steps.length - 1 && (
              <Button type="primary" onClick={() => next()}>
                Next
              </Button>
            )}

            {currentStep === steps.length - 1 && (
              <Button
                type="primary"
                onClick={() => {
                  // Save current form values before submitting
                  const currentValues = form.getFieldsValue();
                  const updatedValues = { ...formValues, ...currentValues };
                  setFormValues(updatedValues);

                  // Ensure form has the latest values
                  form.setFieldsValue(updatedValues);

                  // Submit the form after a short delay to ensure values are set
                  setTimeout(() => {
                    console.log('Submitting form with values:', updatedValues);
                    form.submit();
                  }, 50);
                }}
                icon={sendType === 'now' ? <SendOutlined /> : <CalendarOutlined />}
                loading={createCampaignMutation.isPending}
              >
                {sendType === 'now' ? 'Send Campaign Now' : 'Schedule Campaign'}
              </Button>
            )}
          </div>
        </Form>
      </div>
    </Layout>
  );
}
