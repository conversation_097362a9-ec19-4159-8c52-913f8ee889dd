import { useSession } from 'next-auth/react';
import { useState } from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/Layout';
import SenderReport from '@/components/campaigns/SenderReport';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import {
  Button,
  Card,
  Tabs,
  Tag,
  Tooltip,
  Modal,
  Spin,
  Typography,
  Statistic,
  Progress,
  Table,
  Space,
  Divider,
  Alert,
  message
} from 'antd';
import {
  MailOutlined,
  EyeOutlined,
  MessageOutlined,
  ExclamationCircleOutlined,
  StopOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  LinkOutlined,
  WarningOutlined,
  SyncOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { confirm } = Modal;

interface CampaignLead {
  id: string;
  leadId: string;
  status: string;
  lead: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    company?: string;
  };
  stepActivities: Array<{
    id: string;
    type: string;
    createdAt: string;
    step: {
      id: string;
      name: string;
    };
  }>;
}

interface CampaignStep {
  id: string;
  name: string;
  type: string;
  position: number;
  subject?: string;
  content?: string;
  emailAccount?: {
    id: string;
    email: string;
    name?: string;
  };
}

interface Campaign {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
  errorMessage?: string; // Added error message field
  steps: CampaignStep[];
  leads: CampaignLead[];
  analytics: {
    totalLeads: number;
    sentCount: number;
    openCount: number;
    clickCount: number;
    replyCount: number;
    bounceCount: number;
    unsubscribeCount: number;
    openRate: number;
    clickRate: number;
    replyRate: number;
    bounceRate: number;
    unsubscribeRate: number;
    leadsByStatus: {
      active: number;
      completed: number;
      unsubscribed: number;
      bounced: number;
    };
  };
}

export default function CampaignDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const queryClient = useQueryClient();
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const [activeTab, setActiveTab] = useState('overview');

  // Fetch campaign data
  const { data: campaign, isPending: isLoading } = useQuery<Campaign>({
    queryKey: ['campaign', id],
    queryFn: () => api.get(`/campaigns/${id}`).then(res => res.data),
    enabled: !!id && status === 'authenticated',
    // Use a fixed refetch interval for all campaigns
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  // Update campaign status mutation
  const updateCampaignMutation = useMutation({
    mutationFn: async ({ status }: { status: string }) => {
      return api.put(`/campaigns/${id}`, { status }).then(res => res.data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['campaign', id] });
      queryClient.invalidateQueries({ queryKey: ['campaigns'] });
    },
  });

  // Process campaign now mutation
  const processCampaignMutation = useMutation({
    mutationFn: async () => {
      return api.post(`/campaigns/process-now`, { campaignId: id }).then(res => res.data);
    },
    onSuccess: () => {
      message.success('Campaign processing initiated - emails will be sent shortly');

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['campaign', id] });

      // Set up a polling mechanism to show progress
      const pollInterval = setInterval(() => {
        queryClient.invalidateQueries({ queryKey: ['campaign', id] });
      }, 5000); // Poll every 5 seconds

      // Stop polling after 30 seconds
      setTimeout(() => {
        clearInterval(pollInterval);
        message.info('Campaign processing should be complete. Check the recipients tab for details.');
      }, 30000);
    },
    onError: (error) => {
      message.error(`Failed to process campaign: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  });

  // Delete campaign mutation
  const deleteCampaignMutation = useMutation({
    mutationFn: async () => {
      return api.delete(`/campaigns/${id}`).then(res => res.data);
    },
    onSuccess: () => {
      router.push('/campaigns');
    },
  });

  const handleStartCampaign = () => {
    updateCampaignMutation.mutate({ status: 'active' });
  };

  const handlePauseCampaign = () => {
    updateCampaignMutation.mutate({ status: 'paused' });
  };

  const handleResumeCampaign = () => {
    updateCampaignMutation.mutate({ status: 'active' });
  };

  const handleProcessCampaignNow = () => {
    confirm({
      title: 'Process Campaign Now',
      icon: <SyncOutlined />,
      content: (
        <div>
          <p>This will immediately process the campaign and send any pending emails.</p>
          <p>Emails will be sent with minimal delays between them.</p>
          <p>Do you want to continue?</p>
        </div>
      ),
      okText: 'Yes, Process Now',
      cancelText: 'Cancel',
      onOk() {
        processCampaignMutation.mutate();
      },
    });
  };

  const handleDeleteCampaign = () => {
    confirm({
      title: 'Are you sure you want to delete this campaign?',
      icon: <ExclamationCircleOutlined />,
      content: 'This action cannot be undone.',
      okText: 'Yes, delete it',
      okType: 'danger',
      cancelText: 'No, keep it',
      onOk() {
        deleteCampaignMutation.mutate();
      },
    });
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'draft':
        return <Tag color="default">Draft</Tag>;
      case 'active':
        return <Tag color="green">Active</Tag>;
      case 'paused':
        return <Tag color="orange">Paused</Tag>;
      case 'completed':
        return <Tag color="blue">Completed</Tag>;
      case 'failed':
        return <Tag color="red">Failed</Tag>;
      case 'scheduled':
        return <Tag color="purple">Scheduled</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  if (isLoading || !campaign) {
    return (
      <Layout title="Campaign Details">
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  const initialStep = campaign.steps[0];
  const { analytics } = campaign;

  const recipientColumns = [
    {
      title: 'Email',
      dataIndex: ['lead', 'email'],
      key: 'email',
    },
    {
      title: 'Name',
      key: 'name',
      render: (text: string, record: CampaignLead) => (
        <span>
          {record.lead.firstName || ''} {record.lead.lastName || ''}
        </span>
      ),
    },
    {
      title: 'Company',
      dataIndex: ['lead', 'company'],
      key: 'company',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        switch (status) {
          case 'active':
            return <Tag color="green">Active</Tag>;
          case 'completed':
            return <Tag color="blue">Completed</Tag>;
          case 'unsubscribed':
            return <Tag color="red">Unsubscribed</Tag>;
          case 'bounced':
            return <Tag color="orange">Bounced</Tag>;
          default:
            return <Tag>{status}</Tag>;
        }
      },
    },
    {
      title: 'Activities',
      key: 'activities',
      render: (text: string, record: CampaignLead) => {
        const hasSent = record.stepActivities.some(a => a.type === 'email_sent');
        const hasOpened = record.stepActivities.some(a => a.type === 'email_opened');
        const hasClicked = record.stepActivities.some(a => a.type === 'link_clicked');
        const hasReplied = record.stepActivities.some(a => a.type === 'email_replied');

        return (
          <Space wrap>
            {hasSent && (
              <Tag color="default" icon={<MailOutlined />}>
                Sent
              </Tag>
            )}
            {hasOpened && (
              <Tag color="blue" icon={<EyeOutlined />}>
                Opened
              </Tag>
            )}
            {hasClicked && (
              <Tag color="green" icon={<LinkOutlined />}>
                Clicked
              </Tag>
            )}
            {hasReplied && (
              <Tag color="purple" icon={<MessageOutlined />}>
                Replied
              </Tag>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <Layout title={`Campaign: ${campaign.name}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <div className="flex items-center">
              <Title level={2} className="mb-0 mr-2">{campaign.name}</Title>
              {getStatusTag(campaign.status)}
            </div>
            {campaign.description && (
              <Text type="secondary" className="mt-1 block">
                {campaign.description}
              </Text>
            )}
          </div>
          <div className="space-x-2">
            {campaign.status === 'draft' && (
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleStartCampaign}
                loading={updateCampaignMutation.isPending}
              >
                Start Campaign
              </Button>
            )}
            {campaign.status === 'active' && (
              <>
                <Button
                  icon={<PauseCircleOutlined />}
                  onClick={handlePauseCampaign}
                  loading={updateCampaignMutation.isPending}
                >
                  Pause Campaign
                </Button>
                <Button
                  icon={<SyncOutlined />}
                  onClick={handleProcessCampaignNow}
                  loading={processCampaignMutation.isPending}
                >
                  Process Now
                </Button>
              </>
            )}
            {campaign.status === 'paused' && (
              <>
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={handleResumeCampaign}
                  loading={updateCampaignMutation.isPending}
                >
                  Resume Campaign
                </Button>
                <Button
                  icon={<SyncOutlined />}
                  onClick={handleProcessCampaignNow}
                  loading={processCampaignMutation.isPending}
                >
                  Process Now
                </Button>
              </>
            )}
            <Button
              icon={<EditOutlined />}
              onClick={() => router.push(`/campaigns/${id}/edit`)}
            >
              Edit
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleDeleteCampaign}
              loading={deleteCampaignMutation.isPending}
            >
              Delete
            </Button>
          </div>
        </div>

        {/* Status alerts */}
        {campaign.status === 'draft' && (
          <Alert
            message="This campaign is in draft mode"
            description="Start the campaign to begin sending emails to your recipients."
            type="info"
            showIcon
            className="mb-6"
          />
        )}
        {campaign.status === 'in_progress' && (
          <Alert
            className="mb-6"
            message="Campaign is in progress"
            description="Emails are being sent to your recipients. This may take some time depending on the number of recipients."
            type="info"
            showIcon
          />
        )}
        {campaign.status === 'completed' && (
          <Alert
            className="mb-6"
            message="Campaign completed"
            description={`All emails have been sent. Campaign completed on ${new Date(campaign.endDate).toLocaleString()}.`}
            type="success"
            showIcon
          />
        )}

        {campaign.status === 'completed_with_errors' && (
          <Alert
            className="mb-6"
            message="Campaign completed with errors"
            description={
              <div>
                <p>{campaign.errorMessage || 'Some emails could not be delivered. Check the campaign details for more information.'}</p>
                <p>Campaign completed on {new Date(campaign.endDate).toLocaleString()}.</p>
              </div>
            }
            type="warning"
            showIcon
          />
        )}

        {campaign.status === 'failed' && (
          <Alert
            className="mb-6"
            message="Campaign failed"
            description={
              <div>
                <p>{campaign.errorMessage || 'The campaign failed to send any emails. This could be due to email account issues or configuration problems.'}</p>
                <p>Please check your email account settings and try again.</p>
              </div>
            }
            type="error"
            showIcon
          />
        )}

        {campaign.status === 'paused' && campaign.errorMessage && (
          <Alert
            className="mb-6"
            message="Campaign paused due to errors"
            description={campaign.errorMessage}
            type="warning"
            showIcon
          />
        )}

        {/* Campaign content tabs */}

        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'overview',
              label: 'Overview',
              children: (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <Card title="Campaign Performance">
                      <div className="mb-4">
                        <Text>Campaign Progress</Text>
                        <Progress
                          percent={campaign.leads.length > 0 ? Math.round((campaign.analytics.sentCount / campaign.leads.length) * 100) : 0}
                          status={campaign.status === 'completed' ? 'success' : 'active'}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <Statistic
                          title="Total Recipients"
                          value={campaign.leads.length}
                          prefix={<MailOutlined />}
                        />
                        <Statistic
                          title="Emails Sent"
                          value={campaign.analytics.sentCount}
                          prefix={<CheckCircleOutlined />}
                          valueStyle={{ color: '#52c41a' }}
                          suffix={`/ ${campaign.leads.length}`}
                        />
                        <Statistic
                          title="Opened"
                          value={campaign.analytics.openCount}
                          prefix={<EyeOutlined />}
                          valueStyle={{ color: '#1890ff' }}
                        />
                        <Statistic
                          title="Clicked"
                          value={campaign.analytics.clickCount}
                          prefix={<LinkOutlined />}
                          valueStyle={{ color: '#722ed1' }}
                        />
                        <div>
                          <Text>Open Rate</Text>
                          <Progress
                            percent={campaign.analytics.sentCount > 0 ? Math.round((campaign.analytics.openCount / campaign.analytics.sentCount) * 100) : 0}
                            status="active"
                            format={percent => `${percent}%`}
                          />
                        </div>
                        <div>
                          <Text>Click Rate</Text>
                          <Progress
                            percent={campaign.analytics.openCount > 0 ? Math.round((campaign.analytics.clickCount / campaign.analytics.openCount) * 100) : 0}
                            status="active"
                            format={percent => `${percent}%`}
                          />
                        </div>
                      </div>
                    </Card>

                    <Card title="Recipient Status">
                      <div className="grid grid-cols-2 gap-4">
                        <Statistic
                          title="Active"
                          value={campaign.leads.filter(l => l.status === 'active').length}
                          valueStyle={{ color: '#52c41a' }}
                          prefix={<CheckCircleOutlined />}
                        />
                        <Statistic
                          title="Completed"
                          value={campaign.analytics.sentCount}
                          valueStyle={{ color: '#1890ff' }}
                          prefix={<CheckCircleOutlined />}
                          suffix={`/ ${campaign.leads.length}`}
                        />
                        <Statistic
                          title="Unsubscribed"
                          value={campaign.leads.filter(l => l.status === 'unsubscribed').length}
                          valueStyle={{ color: '#ff4d4f' }}
                          prefix={<StopOutlined />}
                        />
                        <Statistic
                          title="Bounced"
                          value={campaign.leads.filter(l => l.status === 'bounced').length}
                          valueStyle={{ color: '#faad14' }}
                          prefix={<ExclamationCircleOutlined />}
                        />
                        <Statistic
                          title="Pending"
                          value={campaign.leads.filter(l => l.status === 'pending').length}
                          valueStyle={{ color: '#faad14' }}
                          prefix={<ClockCircleOutlined />}
                        />
                        <Statistic
                          title="Errors"
                          value={campaign.leads.filter(l => l.status === 'error').length}
                          valueStyle={{ color: '#f5222d' }}
                          prefix={<WarningOutlined />}
                        />
                      </div>
                    </Card>
                  </div>

                  <Card title="Campaign Details" className="mb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <div className="mb-4">
                          <Text strong>Campaign Type:</Text>
                          <div>{campaign.type.charAt(0).toUpperCase() + campaign.type.slice(1)}</div>
                        </div>
                        <div className="mb-4">
                          <Text strong>Created At:</Text>
                          <div>{new Date(campaign.createdAt).toLocaleString()}</div>
                        </div>
                        {campaign.startDate && (
                          <div className="mb-4">
                            <Text strong>Started At:</Text>
                            <div>{new Date(campaign.startDate).toLocaleString()}</div>
                          </div>
                        )}
                        {campaign.endDate && (
                          <div className="mb-4">
                            <Text strong>Ended At:</Text>
                            <div>{new Date(campaign.endDate).toLocaleString()}</div>
                          </div>
                        )}
                      </div>

                      <div>
                        {initialStep && (
                          <>
                            <div className="mb-4">
                              <Text strong>Email Subject:</Text>
                              <div>{initialStep.subject}</div>
                            </div>
                            {initialStep.emailAccount && (
                              <div className="mb-4">
                                <Text strong>Sending From:</Text>
                                <div>{initialStep.emailAccount.email}</div>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </Card>

                  {initialStep && initialStep.content && (
                    <Card title="Email Content" className="mb-6 dark:bg-dark-card">
                      <div
                        className="border p-4 rounded bg-white dark:bg-gray-800 dark:border-gray-700 email-preview-container"
                      >
                        <div
                          className="email-preview-content"
                          dangerouslySetInnerHTML={{ __html: initialStep.content }}
                        />
                      </div>
                    </Card>
                  )}
                </>
              )
            },
            {
              key: 'recipients',
              label: 'Recipients',
              children: (
                <Card>
                  <Table
                    dataSource={campaign.leads}
                    columns={recipientColumns}
                    rowKey="id"
                    pagination={{ pageSize: 10 }}
                  />
                </Card>
              )
            },
            {
              key: 'analytics',
              label: 'Analytics',
              children: (
                <>
                  <Card title="Campaign Metrics" className="mb-6">
                    <div className="mb-4">
                      <Text>Campaign Progress</Text>
                      <Progress
                        percent={campaign.leads.length > 0 ? Math.round((campaign.analytics.sentCount / campaign.leads.length) * 100) : 0}
                        status={campaign.status === 'completed' ? 'success' : 'active'}
                      />
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
                      <Statistic
                        title="Total Recipients"
                        value={campaign.leads.length}
                        prefix={<MailOutlined />}
                      />
                      <Statistic
                        title="Sent"
                        value={campaign.analytics.sentCount}
                        prefix={<CheckCircleOutlined />}
                        valueStyle={{ color: '#52c41a' }}
                      />
                      <Statistic
                        title="Opened"
                        value={campaign.analytics.openCount}
                        prefix={<EyeOutlined />}
                        valueStyle={{ color: '#1890ff' }}
                      />
                      <Statistic
                        title="Clicked"
                        value={campaign.analytics.clickCount}
                        prefix={<LinkOutlined />}
                        valueStyle={{ color: '#722ed1' }}
                      />
                      <Statistic
                        title="Pending"
                        value={campaign.leads.filter(l => l.status === 'pending').length}
                        prefix={<ClockCircleOutlined />}
                        valueStyle={{ color: '#faad14' }}
                      />
                      <Statistic
                        title="Errors"
                        value={campaign.leads.filter(l => l.status === 'error').length}
                        prefix={<WarningOutlined />}
                        valueStyle={{ color: '#f5222d' }}
                      />
                    </div>
                  </Card>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <Card>
                      <Statistic
                        title="Open Rate"
                        value={campaign.analytics.sentCount > 0 ? (campaign.analytics.openCount / campaign.analytics.sentCount) * 100 : 0}
                        precision={2}
                        suffix="%"
                        valueStyle={{ color: '#3f8600' }}
                        prefix={<EyeOutlined />}
                      />
                      <div className="mt-2">
                        <Text type="secondary">{campaign.analytics.openCount} opens out of {campaign.analytics.sentCount} sent</Text>
                      </div>
                    </Card>
                    <Card>
                      <Statistic
                        title="Click Rate"
                        value={campaign.analytics.openCount > 0 ? (campaign.analytics.clickCount / campaign.analytics.openCount) * 100 : 0}
                        precision={2}
                        suffix="%"
                        valueStyle={{ color: '#3f8600' }}
                        prefix={<LinkOutlined />}
                      />
                      <div className="mt-2">
                        <Text type="secondary">{campaign.analytics.clickCount} clicks out of {campaign.analytics.openCount} opens</Text>
                      </div>
                    </Card>
                    <Card>
                      <Statistic
                        title="Reply Rate"
                        value={campaign.analytics.sentCount > 0 ? (campaign.analytics.replyCount / campaign.analytics.sentCount) * 100 : 0}
                        precision={2}
                        suffix="%"
                        valueStyle={{ color: '#3f8600' }}
                        prefix={<MessageOutlined />}
                      />
                      <div className="mt-2">
                        <Text type="secondary">{campaign.analytics.replyCount} replies out of {campaign.analytics.sentCount} sent</Text>
                      </div>
                    </Card>
                  </div>

                  <Card title="Detailed Analytics" className="mb-6">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                      <div>
                        <Text strong>Total Recipients:</Text>
                        <div className="text-2xl">{campaign.leads.length}</div>
                      </div>
                      <div>
                        <Text strong>Emails Sent:</Text>
                        <div className="text-2xl">{campaign.analytics.sentCount}</div>
                      </div>
                      <div>
                        <Text strong>Emails Opened:</Text>
                        <div className="text-2xl">{campaign.analytics.openCount}</div>
                      </div>
                      <div>
                        <Text strong>Links Clicked:</Text>
                        <div className="text-2xl">{campaign.analytics.clickCount}</div>
                      </div>
                      <div>
                        <Text strong>Replies Received:</Text>
                        <div className="text-2xl">{campaign.analytics.replyCount}</div>
                      </div>
                      <div>
                        <Text strong>Bounces:</Text>
                        <div className="text-2xl">{campaign.leads.filter(l => l.status === 'bounced').length}</div>
                      </div>
                      <div>
                        <Text strong>Unsubscribes:</Text>
                        <div className="text-2xl">{campaign.leads.filter(l => l.status === 'unsubscribed').length}</div>
                      </div>
                      <div>
                        <Text strong>Pending:</Text>
                        <div className="text-2xl">{campaign.leads.filter(l => l.status === 'pending').length}</div>
                      </div>
                    </div>
                  </Card>
                </>
              )
            },
            {
              key: 'sender-report',
              label: 'Sender Report',
              children: (
                <SenderReport campaignId={id as string} />
              )
            }
          ]}
        />
      </div>
    </Layout>
  );
}
