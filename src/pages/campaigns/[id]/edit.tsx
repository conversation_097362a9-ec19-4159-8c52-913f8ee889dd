import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Form,
  Input,
  Button,
  Card,
  Select,
  DatePicker,
  message,
  Spin,
  Typography,
  Divider,
  Radio,
  Tabs,
  Modal,
  Tag,
  Space,
  Alert,
  Switch,
  InputNumber,
  Table,
  Steps
} from 'antd';
import {
  SendOutlined,
  SaveOutlined,
  MailOutlined,
  TeamOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  DeleteOutlined,
  ArrowLeftOutlined,
  RightOutlined,
  LeftOutlined
} from '@ant-design/icons';
import Layout from '@/components/Layout';
import dynamic from 'next/dynamic';
import api from '@/lib/api';
import EmailAccountsSelector from '@/components/campaigns/EmailAccountsSelector';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

// Import the rich text editor dynamically to avoid SSR issues
const RichTextEditor = dynamic(() => import('@/components/RichTextEditor'), { ssr: false });

export default function EditCampaignPage() {
  const router = useRouter();
  const { id } = router.query;
  const queryClient = useQueryClient();
  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0); // 0: Setup, 1: Preview, 2: Schedule
  const [recipientType, setRecipientType] = useState('list');
  const [useMultipleSenders, setUseMultipleSenders] = useState(false);
  const [selectedEmailAccounts, setSelectedEmailAccounts] = useState<{ id: string; weight: number }[]>([]);
  const [emailContent, setEmailContent] = useState('');
  const [campaignData, setCampaignData] = useState<any>(null);
  const [isScheduled, setIsScheduled] = useState(false);
  const [scheduledDate, setScheduledDate] = useState<dayjs.Dayjs | null>(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [testEmailModalVisible, setTestEmailModalVisible] = useState(false);
  const [testEmailAddress, setTestEmailAddress] = useState('');
  const [sendingTestEmail, setSendingTestEmail] = useState(false);
  const [contentType, setContentType] = useState('write'); // 'write' or 'template'

  // Fetch email accounts
  const { data: emailAccounts = [], isPending: loadingAccounts } = useQuery({
    queryKey: ['emailAccounts'],
    queryFn: () => api.get('/email-accounts').then(res => res.data),
    enabled: !!session,
  });

  // Fetch leads
  const { data: leads = [], isPending: loadingLeads } = useQuery({
    queryKey: ['leads'],
    queryFn: () => api.get('/leads').then(res => res.data),
    enabled: !!session,
  });

  // Fetch lead lists
  const { data: leadLists = [], isPending: loadingLists } = useQuery({
    queryKey: ['leadLists'],
    queryFn: () => api.get('/lead-lists').then(res => res.data),
    enabled: !!session,
  });

  // Fetch templates
  const { data: templates = [], isPending: loadingTemplates } = useQuery({
    queryKey: ['templates'],
    queryFn: () => api.get('/templates').then(res => res.data),
    enabled: !!session,
  });

  // State for list leads to avoid unnecessary refetches
  const [listLeads, setListLeads] = useState<any[]>([]);
  const [loadingListLeads, setLoadingListLeads] = useState(false);

  // Function to fetch leads for a specific list without triggering a page reload
  const fetchListLeads = useCallback(async (listId: string) => {
    if (!listId) return;

    // Check if we already have leads for this list to prevent unnecessary fetches
    if (listLeads.length > 0 && form.getFieldValue('listId') === listId) {
      console.log('Already have leads for this list, skipping fetch');
      return;
    }

    console.log('Fetching leads for list:', listId);
    setLoadingListLeads(true);
    try {
      const response = await api.get(`/lead-lists/${listId}/leads`);
      setListLeads(response.data);
      console.log(`Fetched ${response.data.length} leads for list ${listId}`);
    } catch (error) {
      console.error('Error fetching list leads:', error);
      message.error('Failed to fetch leads for this list');
    } finally {
      setLoadingListLeads(false);
    }
  }, [listLeads.length, form]);

  // Fetch campaign data
  const { data: campaign, isPending: isLoading } = useQuery({
    queryKey: ['campaign', id],
    queryFn: async () => {
      console.log('Fetching campaign data for ID:', id);
      try {
        const response = await api.get(`/campaigns/${id}`);
        console.log('Raw campaign data:', JSON.stringify(response.data, null, 2));
        console.log('Campaign type:', response.data.type);
        console.log('Has steps:', Array.isArray(response.data.steps) && response.data.steps.length > 0);
        return response.data;
      } catch (error) {
        console.error('Error fetching campaign data:', error);
        throw error;
      }
    },
    enabled: !!id && session?.user !== undefined,
  });

  // Process campaign data when it's loaded
  useEffect(() => {
    if (campaign) {
      console.log('Campaign data loaded:', JSON.stringify(campaign, null, 2));

      // Set basic campaign data
      form.setFieldsValue({
        name: campaign.name,
        description: campaign.description || '',
      });

      // Check if this is a standard campaign or a sequence campaign
      const isStandardCampaign = !campaign.steps || campaign.steps.length === 0;
      console.log('Is standard campaign:', isStandardCampaign);

      if (isStandardCampaign) {
        // Handle standard campaign (no steps)
        console.log('Handling standard campaign');

        // Set form values for standard campaign
        form.setFieldsValue({
          emailAccountId: campaign.emailAccountId || '',
          subject: campaign.subject || '',
        });

        // Set content
        if (campaign.content) {
          console.log('Setting email content:', campaign.content.substring(0, 50) + '...');
          setEmailContent(campaign.content);
        }

        // Set multiple senders if applicable
        const usesMultipleSenders = campaign.useMultipleSenders || false;
        console.log('Uses multiple senders:', usesMultipleSenders);
        setUseMultipleSenders(usesMultipleSenders);

        // Handle email accounts for multiple senders
        if (usesMultipleSenders && campaign.emailAccounts) {
          const emailAccountsWithWeight = campaign.emailAccounts.map((ea: any) => ({
            id: ea.id,
            weight: ea.weight || 1
          }));
          console.log('Selected email accounts:', emailAccountsWithWeight);
          setSelectedEmailAccounts(emailAccountsWithWeight);
        }

        // Set content type based on whether a template is used
        if (campaign.templateId) {
          console.log('Using template:', campaign.templateId);
          setContentType('template');
          form.setFieldsValue({ templateId: campaign.templateId });
        } else {
          console.log('Using direct content');
          setContentType('write');
        }
      } else {
        // Handle sequence campaign (with steps)
        console.log('Handling sequence campaign');
        const initialStep = campaign.steps[0];
        console.log('Initial step:', JSON.stringify(initialStep, null, 2));

        if (initialStep) {
          // Set form values for sequence campaign
          form.setFieldsValue({
            emailAccountId: initialStep.emailAccountId || '',
            subject: initialStep.subject || '',
          });

          // Set content
          if (initialStep.content) {
            console.log('Setting email content:', initialStep.content.substring(0, 50) + '...');
            setEmailContent(initialStep.content);
          }

          // Set multiple senders if applicable
          const usesMultipleSenders = initialStep.useMultipleSenders || false;
          console.log('Uses multiple senders:', usesMultipleSenders);
          setUseMultipleSenders(usesMultipleSenders);

          // Handle email accounts for multiple senders
          if (usesMultipleSenders && initialStep.emailAccounts && initialStep.emailAccounts.length > 0) {
            const emailAccountsWithWeight = initialStep.emailAccounts.map((eas: any) => {
              const id = eas.emailAccount ? eas.emailAccount.id : eas.emailAccountId;
              return {
                id,
                weight: eas.weight || 1
              };
            });
            console.log('Selected email accounts:', emailAccountsWithWeight);
            setSelectedEmailAccounts(emailAccountsWithWeight);
          }

          // Set content type based on whether a template is used
          if (initialStep.templateId) {
            console.log('Using template:', initialStep.templateId);
            setContentType('template');
            form.setFieldsValue({ templateId: initialStep.templateId });
          } else {
            console.log('Using direct content');
            setContentType('write');
          }
        }
      }

      // Set recipient data (common for both campaign types)
      if (campaign.leads && campaign.leads.length > 0) {
        console.log('Campaign leads:', campaign.leads.length);
        const leadIds = campaign.leads.map((cl: any) => cl.leadId);

        // Check if leads have a list
        let foundListId = null;
        for (const campaignLead of campaign.leads) {
          if (campaignLead.lead?.listId) {
            foundListId = campaignLead.lead.listId;
            break;
          }
        }

        if (foundListId) {
          console.log('Using list:', foundListId);
          setRecipientType('list');
          form.setFieldsValue({ listId: foundListId });
          fetchListLeads(foundListId);
        } else {
          console.log('Using individual leads:', leadIds.length);
          setRecipientType('individual');
          form.setFieldsValue({ leadIds });
        }
      }

      // Set scheduled date if applicable (common for both campaign types)
      if (campaign.startDate && campaign.status === 'scheduled') {
        console.log('Scheduled for:', campaign.startDate);
        setIsScheduled(true);
        setScheduledDate(dayjs(campaign.startDate));
      }

      setCampaignData(campaign);

      // Log the form values after setting them
      console.log('Form values after initialization:', form.getFieldsValue());
    }
  }, [campaign, form, setEmailContent, setContentType, setUseMultipleSenders, setSelectedEmailAccounts, setRecipientType, fetchListLeads, setIsScheduled, setScheduledDate, setCampaignData]);

  // We're now handling form initialization in the CampaignEditForm component

  // Loading state
  const isPageLoading = isLoading || loadingAccounts || loadingLeads || loadingLists || loadingTemplates || (loadingListLeads && recipientType === 'list');

  // Update campaign mutation
  const updateCampaignMutation = useMutation({
    mutationFn: async (values: any) => {
      console.log('Updating campaign with values:', values);

      // Check if this is a standard campaign or a sequence campaign
      const isStandardCampaign = !campaign?.steps || campaign.steps.length === 0;
      console.log('Is standard campaign (in update):', isStandardCampaign);

      if (isStandardCampaign) {
        // Update standard campaign (all data in one request)
        console.log('Updating standard campaign');
        const campaignResponse = await api.put(`/campaigns/${id}`, {
          name: values.name,
          description: values.description,
          status: values.status || campaign?.status,
          subject: values.subject,
          content: emailContent,
          emailAccountId: useMultipleSenders ? null : values.emailAccountId,
          useMultipleSenders: useMultipleSenders,
          emailAccountIds: useMultipleSenders ? selectedEmailAccounts : [],
          templateId: contentType === 'template' ? values.templateId : null,
        });
        return campaignResponse.data;
      } else {
        // Update sequence campaign (campaign and step separately)
        console.log('Updating sequence campaign');

        // First update the campaign
        const campaignResponse = await api.put(`/campaigns/${id}`, {
          name: values.name,
          description: values.description,
          status: values.status || campaign?.status,
        });

        // Then update the step
        if (campaign?.steps[0]) {
          const stepId = campaign.steps[0].id;
          await api.put(`/campaigns/${id}/steps/${stepId}`, {
            subject: values.subject,
            content: emailContent,
            emailAccountId: useMultipleSenders ? null : values.emailAccountId,
            useMultipleSenders: useMultipleSenders,
            emailAccountIds: useMultipleSenders ? selectedEmailAccounts : [],
            templateId: contentType === 'template' ? values.templateId : null,
          });
        }

        return campaignResponse.data;
      }
    },
    onSuccess: () => {
      message.success('Campaign updated successfully');
      queryClient.invalidateQueries({ queryKey: ['campaign', id] });
      queryClient.invalidateQueries({ queryKey: ['campaigns'] });
      router.push(`/campaigns/${id}`);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to update campaign');
    },
  });

  const handleFormSubmit = (values: any) => {
    updateCampaignMutation.mutate({
      ...values,
      status: campaign?.status,
    });
  };

  const handleSaveAndActivate = () => {
    form.validateFields().then(values => {
      updateCampaignMutation.mutate({
        ...values,
        status: 'active',
      });
    });
  };

  const handleSaveAndSchedule = () => {
    if (!scheduledDate) {
      message.error('Please select a scheduled date');
      return;
    }

    form.validateFields().then(values => {
      updateCampaignMutation.mutate({
        ...values,
        status: 'scheduled',
        scheduledFor: scheduledDate.toISOString(),
      });
    });
  };

  // Handle form submission
  const handleSubmit = (values: any) => {
    // Combine form values with other state
    const data = {
      ...values,
      content: emailContent,
      useMultipleSenders,
      emailAccounts: useMultipleSenders ? selectedEmailAccounts : [],
      recipientType,
      scheduledFor: isScheduled ? scheduledDate?.toISOString() : null,
    };

    setCampaignData(data);
    setCurrentStep(1); // Move to preview step
  };

  // Handle test email sending
  const handleSendTestEmail = async () => {
    if (!testEmailAddress) {
      message.error('Please enter an email address');
      return;
    }

    setSendingTestEmail(true);
    try {
      await api.post('/campaigns/test', {
        to: testEmailAddress,
        subject: form.getFieldValue('subject'),
        content: emailContent,
        emailAccountId: useMultipleSenders && selectedEmailAccounts.length > 0 ? selectedEmailAccounts[0].id : form.getFieldValue('emailAccountId'),
      });
      message.success('Test email sent successfully');
      setTestEmailModalVisible(false);
    } catch (error) {
      message.error('Failed to send test email');
      console.error('Error sending test email:', error);
    } finally {
      setSendingTestEmail(false);
    }
  };

  // Handle final update
  const handleUpdateCampaign = (status?: string) => {
    if (!campaignData) return;

    const finalData = {
      ...campaignData,
      status: status || campaign?.status,
    };

    updateCampaignMutation.mutate(finalData);
  };

  // Function to render the appropriate step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Setup
        return renderSetupForm();
      case 1: // Preview
        return renderPreview();
      case 2: // Schedule
        return renderSchedule();
      default:
        return renderSetupForm();
    }
  };

  // Import our new component
  const CampaignEditForm = dynamic(() => import('@/components/campaigns/CampaignEditForm'), { ssr: false });

  // Render the setup form
  const renderSetupForm = () => {
    return (
      <CampaignEditForm
        form={form}
        campaign={campaign}
        emailAccounts={emailAccounts}
        templates={templates}
        useMultipleSenders={useMultipleSenders}
        setUseMultipleSenders={setUseMultipleSenders}
        selectedEmailAccounts={selectedEmailAccounts}
        setSelectedEmailAccounts={setSelectedEmailAccounts}
        emailContent={emailContent}
        setEmailContent={setEmailContent}
        contentType={contentType}
        setContentType={setContentType}
        recipientType={recipientType}
        setRecipientType={setRecipientType}
        leads={leads}
        leadLists={leadLists}
        listLeads={listLeads}
        fetchListLeads={fetchListLeads}
        onTestEmail={() => setTestEmailModalVisible(true)}
        onSubmit={handleSubmit}
        onCancel={() => router.push(`/campaigns/${id}`)}
      />
    );
  };

  // Render the preview
  const renderPreview = () => {
    if (!campaignData) return null;

    return (
      <div className="space-y-6">
        <Card title="Campaign Preview" className="shadow-sm dark:bg-dark-card">
          <div className="space-y-4">
            <div>
              <Text strong>Campaign Name:</Text>
              <div>{campaignData.name}</div>
            </div>

            {campaignData.description && (
              <div>
                <Text strong>Description:</Text>
                <div>{campaignData.description}</div>
              </div>
            )}

            <div>
              <Text strong>Email Account:</Text>
              <div>
                {useMultipleSenders
                  ? `Multiple Senders (${selectedEmailAccounts.length} accounts)`
                  : emailAccounts.find((a: any) => a.id === campaignData.emailAccountId)?.email || 'Not selected'}
              </div>
            </div>

            <div>
              <Text strong>Subject:</Text>
              <div>{campaignData.subject}</div>
            </div>

            <div>
              <Text strong>Content:</Text>
              <div className="border p-4 rounded mt-2 bg-white">
                <div dangerouslySetInnerHTML={{ __html: emailContent }} />
              </div>
            </div>

            <div>
              <Text strong>Recipients:</Text>
              <div>
                {recipientType === 'list'
                  ? `Lead List: ${leadLists.find((l: any) => l.id === campaignData.listId)?.name || 'Not selected'} (${listLeads.length} leads)`
                  : `Individual Leads: ${campaignData.leadIds?.length || 0} selected`}
              </div>
            </div>
          </div>
        </Card>

        <div className="flex justify-between space-x-4">
          <Button
            onClick={() => setCurrentStep(0)}
            icon={<ArrowLeftOutlined />}
          >
            Back to Edit
          </Button>
          <Button
            type="primary"
            onClick={() => setCurrentStep(2)}
            icon={<ClockCircleOutlined />}
          >
            Continue to Schedule
          </Button>
        </div>
      </div>
    );
  };

  // Render the schedule
  const renderSchedule = () => {
    return (
      <div className="space-y-6">
        <Card title="Schedule Campaign" className="shadow-sm dark:bg-dark-card">
          <div className="space-y-4">
            <div className="mb-4">
              <Radio.Group
                value={isScheduled ? 'later' : 'now'}
                onChange={(e) => setIsScheduled(e.target.value === 'later')}
              >
                <Radio.Button value="now">Send Now</Radio.Button>
                <Radio.Button value="later">Schedule for Later</Radio.Button>
              </Radio.Group>
            </div>

            {isScheduled && (
              <div>
                <Text strong>Schedule Date and Time:</Text>
                <div className="mt-2">
                  <DatePicker
                    showTime
                    value={scheduledDate}
                    onChange={setScheduledDate}
                    disabledDate={(current) => current && current < dayjs().startOf('day')}
                    className="w-full"
                  />
                </div>
              </div>
            )}

            <Alert
              message={isScheduled ? 'Campaign will be scheduled' : 'Campaign will start immediately'}
              description={isScheduled
                ? `This campaign will be scheduled to start at ${scheduledDate?.format('MMMM D, YYYY h:mm A')}. You can always change this later.`
                : 'This campaign will start sending emails immediately after you click the button below.'}
              type="info"
              showIcon
            />
          </div>
        </Card>

        <div className="flex justify-between space-x-4">
          <Button
            onClick={() => setCurrentStep(1)}
            icon={<ArrowLeftOutlined />}
          >
            Back to Preview
          </Button>
          <div>
            <Button
              type="default"
              onClick={() => router.push(`/campaigns/${id}`)}
              className="mr-2"
            >
              Cancel
            </Button>
            <Button
              type="primary"
              onClick={() => handleUpdateCampaign(isScheduled ? 'scheduled' : 'active')}
              icon={isScheduled ? <ClockCircleOutlined /> : <SendOutlined />}
              loading={updateCampaignMutation.isPending}
            >
              {isScheduled ? 'Schedule Campaign' : 'Start Campaign Now'}
            </Button>
          </div>
        </div>
      </div>
    );
  };

  if (isPageLoading || !campaign) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout title={`Edit Campaign: ${campaign?.name || ''}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center mb-6">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.push(`/campaigns/${id}`)}
            className="mr-4"
          >
            Back to Campaign
          </Button>
          <Title level={2} className="mb-0">Edit Campaign: {campaign.name}</Title>
        </div>

        {/* Step indicator */}
        <div className="mb-6">
          <Steps current={currentStep} className="dark:text-white">
            <Steps.Step title="Setup" description="Configure campaign" />
            <Steps.Step title="Preview" description="Review content" />
            <Steps.Step title="Schedule" description="Set timing" />
          </Steps>
        </div>

        {/* Main content */}
        <div className="mt-8">
          {renderStepContent()}
        </div>

        {/* Test email modal */}
        <Modal
          title="Send Test Email"
          open={testEmailModalVisible}
          onCancel={() => setTestEmailModalVisible(false)}
          footer={[
            <Button key="cancel" onClick={() => setTestEmailModalVisible(false)}>
              Cancel
            </Button>,
            <Button
              key="send"
              type="primary"
              loading={sendingTestEmail}
              onClick={handleSendTestEmail}
            >
              Send Test
            </Button>,
          ]}
        >
          <div className="space-y-4">
            <p>Send a test email to verify how your campaign will look.</p>
            <Input
              placeholder="Enter email address"
              value={testEmailAddress}
              onChange={(e) => setTestEmailAddress(e.target.value)}
            />
          </div>
        </Modal>
      </div>
    </Layout>
  );
}
