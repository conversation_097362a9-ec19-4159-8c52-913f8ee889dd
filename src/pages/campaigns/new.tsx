import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  Form,
  Input,
  Button,
  Card,
  Select,
  DatePicker,
  message,
  Spin,
  Typography,
  Divider,
  Radio,
  Tabs,
  Modal,
  Tag,
  Space,
  Alert,
  Switch,
  InputNumber,
  Table
} from 'antd';
import { SendOutlined, SaveOutlined, MailOutlined, TeamOutlined, EyeOutlined, CheckCircleOutlined, ClockCircleOutlined, InfoCircleOutlined, PlusOutlined, DeleteOutlined, RobotOutlined } from '@ant-design/icons';
import Layout from '@/components/Layout';
import dynamic from 'next/dynamic';
import api from '@/lib/api';
import EmailAccountsSelector from '@/components/campaigns/EmailAccountsSelector';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

// Import the rich text editor dynamically to avoid SSR issues
const RichTextEditor = dynamic(() => import('@/components/RichTextEditor'), { ssr: false });

export default function NewCampaignPage() {
  const router = useRouter();
  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [contentType, setContentType] = useState('write'); // 'write' or 'template'
  const [currentStep, setCurrentStep] = useState(0); // 0: Setup, 1: Preview, 2: Schedule
  const [previewVisible, setPreviewVisible] = useState(false);
  const [testEmailModalVisible, setTestEmailModalVisible] = useState(false);
  const [testEmailAddress, setTestEmailAddress] = useState('');
  const [sendingTestEmail, setSendingTestEmail] = useState(false);
  const [recipientType, setRecipientType] = useState('leads'); // 'leads' or 'list'
  const [useMultipleSenders, setUseMultipleSenders] = useState(false);
  const [campaignData, setCampaignData] = useState<any>(null);

  // Fetch email accounts
  const { data: emailAccountsData, isPending: loadingAccounts } = useQuery({
    queryKey: ['emailAccounts'],
    queryFn: () => fetch('/api/email-accounts').then(res => res.json()),
    enabled: !!session,
  });

  // Ensure email accounts is always an array
  const emailAccounts = Array.isArray(emailAccountsData) ? emailAccountsData : [];

  // Fetch leads
  const { data: leadsData, isPending: loadingLeads } = useQuery({
    queryKey: ['leads'],
    queryFn: () => fetch('/api/leads').then(res => res.json()),
    enabled: !!session,
  });

  // Ensure leads is always an array
  const leads = Array.isArray(leadsData) ? leadsData : [];

  // Fetch lead lists
  const { data: leadListsData, isPending: loadingLists } = useQuery({
    queryKey: ['leadLists'],
    queryFn: () => api.get('/lead-lists').then(res => res.data),
    enabled: !!session,
  });

  // Ensure lead lists is always an array
  const leadLists = Array.isArray(leadListsData) ? leadListsData : [];

  // State for list leads to avoid unnecessary refetches
  const [listLeads, setListLeads] = useState<any[]>([]);
  const [loadingListLeads, setLoadingListLeads] = useState(false);

  // Function to fetch leads for a specific list without triggering a page reload
  const fetchListLeads = async (listId: string) => {
    if (!listId) return;

    setLoadingListLeads(true);
    try {
      const response = await api.get(`/lead-lists/${listId}/leads`);
      setListLeads(response.data);
    } catch (error) {
      console.error('Error fetching list leads:', error);
      message.error('Failed to fetch leads for this list');
    } finally {
      setLoadingListLeads(false);
    }
  };

  // Fetch templates
  const { data: templatesData, isPending: loadingTemplates } = useQuery({
    queryKey: ['templates'],
    queryFn: () => fetch('/api/templates').then(res => res.json()),
    enabled: !!session,
  });

  // Ensure templates is always an array
  const templates = Array.isArray(templatesData) ? templatesData : [];

  // Fetch AI agents
  const { data: agentsData, isPending: loadingAgents } = useQuery({
    queryKey: ['agents'],
    queryFn: () => fetch('/api/agents').then(res => res.json()),
    enabled: !!session,
  });

  // Ensure agents is always an array
  const agents = Array.isArray(agentsData) ? agentsData : [];

  // Create campaign mutation
  const createCampaignMutation = useMutation({
    mutationFn: async (values: any) => {
      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...values,
          type: 'standard'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create campaign');
      }

      return response.json();
    },
    onSuccess: (data) => {
      message.success('Campaign created successfully');
      router.push(`/campaigns/${data.id}`);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to create campaign');
    },
  });

  const handleSubmit = async (values: any) => {
    // Store the form data for preview
    const formData = {
      ...values,
      recipientType,
      leadIds: recipientType === 'leads' ? values.leadIds : [],
      listId: recipientType === 'list' ? values.listId : null,
      // Include multiple senders data if enabled
      useMultipleSenders: values.useMultipleSenders,
      emailAccounts: values.useMultipleSenders ? values.emailAccounts : [],
    };

    setCampaignData(formData);
    setCurrentStep(1); // Move to preview step
  };

  const handleSendTest = async () => {
    if (!testEmailAddress) {
      message.error('Please enter a valid email address');
      return;
    }

    setSendingTestEmail(true);

    try {
      await api.post('/campaigns/test', {
        ...campaignData,
        testEmail: testEmailAddress
      });

      message.success('Test email sent successfully');
      setTestEmailModalVisible(false);
      setTestEmailAddress('');
    } catch (error) {
      message.error('Failed to send test email');
      console.error(error);
    } finally {
      setSendingTestEmail(false);
    }
  };

  const handleCreateCampaign = (scheduled: boolean = false) => {
    if (!campaignData) return;

    const finalData = {
      ...campaignData,
      scheduledFor: scheduled ? campaignData.scheduledFor : null,
      status: scheduled ? 'scheduled' : 'active', // Set status to active for immediate sending
    };

    // Show a success message immediately and redirect to campaigns list
    message.success('Campaign is being created. You will be redirected shortly...');

    // Start the mutation
    createCampaignMutation.mutate(finalData, {
      onSuccess: (data) => {
        // Redirect to campaigns list with a status message
        router.push({
          pathname: '/campaigns',
          query: { status: 'creating', id: data.id, name: data.name }
        });
      }
    });
  };

  if (loadingAccounts || loadingLeads || loadingTemplates || loadingLists || loadingAgents || (loadingListLeads && recipientType === 'list')) {
    return (
      <Layout title="Create Campaign">
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  // Function to render the appropriate step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Setup
        return renderSetupForm();
      case 1: // Preview
        return renderPreview();
      case 2: // Schedule
        return renderSchedule();
      default:
        return renderSetupForm();
    }
  };

  // Render the setup form
  const renderSetupForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          name: '',
          emailAccountId: '',
          subject: '',
          content: '',
          scheduledFor: null,
          useMultipleSenders: false,
          emailAccounts: [],
          agentId: undefined,
        }}
      >
            <Form.Item
              name="name"
              label="Campaign Name"
              rules={[{ required: true, message: 'Please enter a campaign name' }]}
            >
              <Input placeholder="Enter campaign name" />
            </Form.Item>

            <Form.Item
              label="Email Sender Configuration"
              required
            >
              <div className="mb-4 flex items-center">
                <Form.Item
                  name="useMultipleSenders"
                  valuePropName="checked"
                  noStyle
                >
                  <Switch
                    onChange={(checked) => {
                      setUseMultipleSenders(checked);
                      // Clear email accounts when toggling
                      if (checked) {
                        form.setFieldsValue({ emailAccountId: undefined });
                      } else {
                        form.setFieldsValue({ emailAccounts: [] });
                      }
                    }}
                  />
                </Form.Item>
                <span className="ml-2">Use multiple senders</span>
                <InfoCircleOutlined
                  className="ml-2 text-gray-400"
                  title="When enabled, emails will be distributed among multiple accounts based on their reputation"
                />
              </div>

              {!useMultipleSenders ? (
                <Form.Item
                  name="emailAccountId"
                  rules={[{ required: !useMultipleSenders, message: 'Please select an email account' }]}
                  noStyle
                >
                  <Select placeholder="Select an email account">
                    {emailAccounts.map((account: any) => (
                      <Option
                        key={account.id}
                        value={account.id}
                        disabled={account.status === 'suspended'}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <div>{account.email}</div>
                            <div className="text-xs text-gray-500">{account.name}</div>
                          </div>
                          <Space>
                            <Tag color={account.status === 'active' ? 'success' : account.status === 'pending' ? 'warning' : 'error'}>
                              {account.status}
                            </Tag>
                            {account.reputationScore !== null && (
                              <Tag
                                color={
                                  account.reputationScore >= 80 ? 'success' :
                                  account.reputationScore >= 60 ? 'processing' :
                                  account.reputationScore >= 40 ? 'warning' : 'error'
                                }
                              >
                                {account.reputationScore}
                              </Tag>
                            )}
                          </Space>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              ) : (
                <Form.Item
                  name="emailAccounts"
                  rules={[{
                    required: useMultipleSenders,
                    message: 'Please select at least one email account',
                    validator: (_, value) => {
                      if (!useMultipleSenders || (value && value.length > 0)) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('Please select at least one email account'));
                    }
                  }]}
                  noStyle
                >
                  <EmailAccountsSelector
                    emailAccounts={emailAccounts.filter(account => account.status !== 'suspended')}
                  />
                </Form.Item>
              )}
            </Form.Item>

            <Form.Item
              name="subject"
              label="Email Subject"
              rules={[{ required: true, message: 'Please enter an email subject' }]}
            >
              <Input placeholder="Enter email subject" />
            </Form.Item>

            <Form.Item
              name="agentId"
              label={
                <span>
                  AI Agent <RobotOutlined /> <Text type="secondary">(Optional)</Text>
                </span>
              }
              tooltip="Select an AI agent to enhance your campaign with personalized content and follow-ups"
            >
              <Select
                placeholder="Select an AI agent (optional)"
                allowClear
                style={{ width: '100%' }}
                onChange={(value) => {
                  // If an agent is selected, update the form to reflect that content is optional
                  form.validateFields(['content', 'templateId']);
                }}
              >
                {agents.map((agent: any) => (
                  <Option key={agent.id} value={agent.id}>
                    <div className="flex items-center">
                      <RobotOutlined className="mr-2" />
                      <div>
                        <div>{agent.name}</div>
                        <div className="text-xs text-gray-500">{agent.description}</div>
                      </div>
                      {agent.capabilities && agent.capabilities.length > 0 && (
                        <div className="ml-auto">
                          {agent.capabilities.includes('web_scraping') && (
                            <Tag color="blue">Web Scraping</Tag>
                          )}
                          {agent.capabilities.includes('linkedin_research') && (
                            <Tag color="purple">LinkedIn Research</Tag>
                          )}
                          {agent.capabilities.includes('personalization') && (
                            <Tag color="green">Personalization</Tag>
                          )}
                          {agent.capabilities.includes('appointment_scheduling') && (
                            <Tag color="orange">Appointment Scheduling</Tag>
                          )}
                        </div>
                      )}
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            {form.getFieldValue('agentId') && (
              <Alert
                message="AI Agent Selected"
                description="When an AI agent is selected, email content is optional. The AI agent will generate personalized content based on lead data."
                type="info"
                showIcon
                className="mb-4"
              />
            )}

            <Form.Item label="Content Source">
              <Radio.Group
                value={contentType}
                onChange={(e) => {
                  setContentType(e.target.value);
                  // Clear the other field when switching
                  if (e.target.value === 'write') {
                    form.setFieldsValue({ templateId: undefined });
                  } else {
                    form.setFieldsValue({ content: '' });
                  }
                }}
              >
                <Radio.Button value="write">Write Content</Radio.Button>
                <Radio.Button value="template">Use Template</Radio.Button>
              </Radio.Group>
            </Form.Item>

            {contentType === 'write' ? (
              <Form.Item
                name="content"
                label="Email Content"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      // Content is required only if no agent is selected
                      if (getFieldValue('agentId') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('Please enter email content or select an AI agent'));
                    },
                  }),
                ]}
              >
                <RichTextEditor
                  value={form.getFieldValue('content') || ''}
                  onChange={(value) => form.setFieldsValue({ content: value })}
                  placeholder={form.getFieldValue('agentId')
                    ? "Optional: Provide content or let the AI agent generate it..."
                    : "Compose your email..."}
                />
              </Form.Item>
            ) : (
              <Form.Item
                name="templateId"
                label="Select Template"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      // Template is required only if no agent is selected
                      if (getFieldValue('agentId') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('Please select a template or select an AI agent'));
                    },
                  }),
                ]}
              >
                <Select
                  placeholder="Select a template"
                  onChange={(templateId) => {
                    // Find the selected template
                    const template = templates.find((t: any) => t.id === templateId);
                    if (template) {
                      // Update the subject field with the template's subject
                      form.setFieldsValue({ subject: template.subject });
                    }
                  }}
                >
                  {templates.map((template: any) => (
                    <Option key={template.id} value={template.id}>
                      {template.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            <Divider />

            <Form.Item label="Recipients">
              <Radio.Group
                value={recipientType}
                onChange={(e) => setRecipientType(e.target.value)}
                className="mb-4"
              >
                <Radio.Button value="leads">Select Individual Leads</Radio.Button>
                <Radio.Button value="list">Use Lead List</Radio.Button>
              </Radio.Group>

              {recipientType === 'leads' ? (
                <Form.Item
                  name="leadIds"
                  rules={[{ required: recipientType === 'leads', message: 'Please select at least one recipient' }]}
                  noStyle
                >
                  <Select
                    mode="multiple"
                    placeholder="Select recipients"
                    style={{ width: '100%' }}
                    optionFilterProp="label"
                    filterOption={(input, option) => {
                      if (typeof option?.label === 'string') {
                        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }
                      return false;
                    }}
                  >
                    {leads.map((lead: any) => {
                      const label = `${lead.email} ${lead.firstName ? `(${lead.firstName} ${lead.lastName || ''})` : ''}`;
                      return (
                        <Option key={lead.id} value={lead.id} label={label}>
                          {label}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              ) : (
                <Form.Item
                  name="listId"
                  rules={[{ required: recipientType === 'list', message: 'Please select a lead list' }]}
                  noStyle
                >
                  <Select
                    placeholder="Select a lead list"
                    style={{ width: '100%' }}
                    onChange={(listId) => {
                      // Fetch leads for the selected list
                      fetchListLeads(listId);
                    }}
                  >
                    {leadLists.map((list: any) => (
                      <Option key={list.id} value={list.id}>
                        {list.name} ({list._count?.leads || 0} leads)
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              )}

              {recipientType === 'list' && form.getFieldValue('listId') && (
                <div className="mt-2">
                  <Text type="secondary">
                    This list contains {listLeads.length} leads.
                  </Text>
                </div>
              )}
            </Form.Item>

            <Form.Item
              name="scheduledFor"
              label="Schedule"
            >
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="Select date and time (optional)"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <div className="flex justify-end space-x-3 mt-4">
              <Button onClick={() => router.push('/campaigns')}>
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                icon={<EyeOutlined />}
              >
                Preview Campaign
              </Button>
            </div>
          </Form>
      );
  };

  // Render the preview step
  const renderPreview = () => {
    if (!campaignData) return null;

    const { name, subject, content, recipientType, leadIds, listId, emailAccountId, templateId, agentId } = campaignData;
    const selectedLeads = recipientType === 'leads'
      ? leads.filter((lead: any) => leadIds.includes(lead.id))
      : listLeads;

    const selectedList = leadLists.find((list: any) => list.id === listId);
    const selectedAccount = emailAccounts.find((account: any) => account.id === emailAccountId);
    const selectedTemplate = templates.find((template: any) => template.id === templateId);
    const selectedAgent = agents.find((agent: any) => agent.id === agentId);

    const emailContent = templateId ? selectedTemplate?.content : content;

    return (
      <div>
        <Alert
          message="Campaign Preview"
          description="Review your campaign before sending. You can send a test email to verify how it will appear to recipients."
          type="info"
          showIcon
          className="mb-6"
        />

        <Card title="Campaign Details" className="mb-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Text strong>Campaign Name:</Text>
              <div className="mb-2">{name}</div>

              <Text strong>From:</Text>
              <div className="mb-2">{selectedAccount?.email}</div>

              <Text strong>Subject:</Text>
              <div className="mb-2">{subject}</div>
            </div>

            <div>
              <Text strong>Recipients:</Text>
              <div className="mb-2">
                {recipientType === 'leads' ? (
                  <span>{selectedLeads.length} selected leads</span>
                ) : (
                  <span>List: {selectedList?.name} ({selectedLeads.length} leads)</span>
                )}
              </div>

              {selectedAgent && (
                <>
                  <Text strong>AI Agent:</Text>
                  <div className="mb-2 flex items-center">
                    <RobotOutlined className="mr-2" />
                    {selectedAgent.name}
                    {selectedAgent.capabilities && selectedAgent.capabilities.length > 0 && (
                      <div className="ml-2">
                        {selectedAgent.capabilities.map((capability: string) => (
                          <Tag key={capability} color={
                            capability === 'web_scraping' ? 'blue' :
                            capability === 'linkedin_research' ? 'purple' :
                            capability === 'personalization' ? 'green' :
                            capability === 'appointment_scheduling' ? 'orange' : 'default'
                          }>
                            {capability.replace('_', ' ')}
                          </Tag>
                        ))}
                      </div>
                    )}
                  </div>
                </>
              )}

              {campaignData.scheduledFor && (
                <>
                  <Text strong>Scheduled For:</Text>
                  <div className="mb-2">
                    {new Date(campaignData.scheduledFor).toLocaleString()}
                  </div>
                </>
              )}
            </div>
          </div>
        </Card>

        <Card title="Email Preview" className="mb-6 dark:bg-dark-card dark:border-dark-border">
          <div className="border p-4 rounded bg-white dark:bg-dark-content dark:border-dark-border email-preview-container">
            <div className="mb-4">
              <Text strong className="dark:text-white">Subject: </Text>
              <span className="dark:text-white">{subject}</span>
            </div>

            <Divider className="my-2 dark:border-dark-border" />

            {campaignData?.agentId && !emailContent ? (
              <Alert
                message="AI-Generated Content"
                description="The AI agent will generate personalized content for each recipient based on their data. This content will be created when the campaign is sent."
                type="info"
                showIcon
              />
            ) : (
              <div
                dangerouslySetInnerHTML={{ __html: emailContent }}
                className="email-preview-content"
                style={{ backgroundColor: 'white' }}
              />
            )}
          </div>
        </Card>

        <div className="flex justify-between mt-6">
          <Button onClick={() => setCurrentStep(0)}>
            Back to Edit
          </Button>

          <Space>
            <Button
              onClick={() => setTestEmailModalVisible(true)}
              icon={<MailOutlined />}
            >
              Send Test Email
            </Button>

            <Button
              type="primary"
              onClick={() => setCurrentStep(2)}
              icon={<ClockCircleOutlined />}
            >
              Schedule Options
            </Button>

            <Button
              type="primary"
              onClick={() => handleCreateCampaign(false)}
              loading={createCampaignMutation.isPending}
              icon={<SendOutlined />}
            >
              Send Now
            </Button>
          </Space>
        </div>

        <Modal
          title="Send Test Email"
          open={testEmailModalVisible}
          onOk={handleSendTest}
          onCancel={() => setTestEmailModalVisible(false)}
          confirmLoading={sendingTestEmail}
        >
          <p>Send a test email to verify how your campaign will appear to recipients.</p>
          <Form.Item
            label="Email Address"
            rules={[{ required: true, type: 'email', message: 'Please enter a valid email address' }]}
          >
            <Input
              placeholder="Enter email address"
              value={testEmailAddress}
              onChange={(e) => setTestEmailAddress(e.target.value)}
            />
          </Form.Item>
        </Modal>
      </div>
    );
  };

  // Render the schedule step
  const renderSchedule = () => {
    if (!campaignData) return null;

    return (
      <div>
        <Alert
          message="Schedule Campaign"
          description="Choose when to send your campaign. You can send it immediately or schedule it for a future date and time."
          type="info"
          showIcon
          className="mb-6"
        />

        <Card className="mb-6">
          <div className="grid grid-cols-2 gap-8">
            <div className="border p-6 rounded hover:shadow-md cursor-pointer transition-shadow"
                 onClick={() => handleCreateCampaign(false)}>
              <div className="flex justify-center mb-4">
                <SendOutlined className="text-4xl text-blue-500" />
              </div>
              <Title level={4} className="text-center mb-4">Send Now</Title>
              <Text className="block text-center">
                Send your campaign immediately to all recipients.
              </Text>
            </div>

            <div className="border p-6 rounded hover:shadow-md cursor-pointer transition-shadow"
                 onClick={() => handleCreateCampaign(true)}>
              <div className="flex justify-center mb-4">
                <ClockCircleOutlined className="text-4xl text-green-500" />
              </div>
              <Title level={4} className="text-center mb-4">Schedule for Later</Title>
              <Text className="block text-center mb-4">
                Schedule your campaign for:
              </Text>
              <div className="text-center font-semibold">
                {campaignData.scheduledFor
                  ? new Date(campaignData.scheduledFor).toLocaleString()
                  : 'No date selected'}
              </div>
              {!campaignData.scheduledFor && (
                <div className="text-center mt-2 text-red-500">
                  Please go back and select a date and time
                </div>
              )}
            </div>
          </div>
        </Card>

        <div className="flex justify-between mt-6">
          <Button onClick={() => setCurrentStep(1)}>
            Back to Preview
          </Button>

          <Space>
            <Button
              type="primary"
              onClick={() => handleCreateCampaign(false)}
              loading={createCampaignMutation.isPending}
              icon={<SendOutlined />}
            >
              Send Now
            </Button>

            <Button
              type="primary"
              onClick={() => handleCreateCampaign(true)}
              disabled={!campaignData.scheduledFor}
              loading={createCampaignMutation.isPending}
              icon={<ClockCircleOutlined />}
            >
              Schedule Campaign
            </Button>
          </Space>
        </div>
      </div>
    );
  };

  return (
    <Layout title="Create Campaign">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Title level={2}>Create New Campaign</Title>
          <Text type="secondary">
            Create a standard email campaign to send to your leads
          </Text>
        </div>

        <Card>
          <div className="mb-6">
            <div className="flex justify-between items-center">
              <div
                className={`flex-1 pb-4 text-center border-b-2 ${currentStep >= 0 ? 'border-blue-500 text-blue-500' : 'border-gray-200 text-gray-500'}`}
              >
                1. Setup Campaign
              </div>
              <div
                className={`flex-1 pb-4 text-center border-b-2 ${currentStep >= 1 ? 'border-blue-500 text-blue-500' : 'border-gray-200 text-gray-500'}`}
              >
                2. Preview & Test
              </div>
              <div
                className={`flex-1 pb-4 text-center border-b-2 ${currentStep >= 2 ? 'border-blue-500 text-blue-500' : 'border-gray-200 text-gray-500'}`}
              >
                3. Schedule & Send
              </div>
            </div>
          </div>

          {renderStepContent()}
        </Card>
      </div>
    </Layout>
  );
}
