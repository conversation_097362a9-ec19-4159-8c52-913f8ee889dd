import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/Layout';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import { Button, Dropdown, Menu, Tabs, Tag, Tooltip, Modal, message, Popconfirm } from 'antd';
import { PlusOutlined, MailOutlined, ThunderboltOutlined, RobotOutlined, DownOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

const { TabPane } = Tabs;

interface Campaign {
  id: string;
  name: string;
  type: 'standard' | 'sequence' | 'drip';
  status: 'draft' | 'scheduled' | 'sending' | 'completed' | 'failed' | 'active' | 'paused';
  sentCount?: number;
  totalRecipients?: number;
  createdAt: string;
  description?: string;
}

export default function CampaignsPage() {
  const router = useRouter();
  const { status: creatingStatus, id: creatingId, name: creatingName } = router.query;
  const queryClient = useQueryClient();

  const { status } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  // State for delete modal
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [campaignToDelete, setCampaignToDelete] = useState<Campaign | null>(null);

  // State for bulk selection and deletion
  const [selectedCampaignIds, setSelectedCampaignIds] = useState<string[]>([]);
  const [isBulkDeleteModalVisible, setIsBulkDeleteModalVisible] = useState(false);
  const [selectAll, setSelectAll] = useState(false);

  const [timeRange, setTimeRange] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);

  // Delete campaign mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      return api.delete(`/campaigns/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['campaigns'] });
      message.success('Campaign deleted successfully');
      setIsDeleteModalVisible(false);
      setCampaignToDelete(null);
    },
    onError: (error) => {
      console.error('Error deleting campaign:', error);
      message.error('Failed to delete campaign');
    },
  });

  // Handle delete button click
  const handleDeleteClick = (campaign: Campaign) => {
    setCampaignToDelete(campaign);
    setIsDeleteModalVisible(true);
  };

  // Confirm delete
  const confirmDelete = () => {
    if (campaignToDelete) {
      deleteMutation.mutate(campaignToDelete.id);
    }
  };

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: async (ids: string[]) => {
      // Use Promise.all to delete multiple campaigns in parallel
      return Promise.all(ids.map(id => api.delete(`/campaigns/${id}`)));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['campaigns'] });
      message.success(`Successfully deleted ${selectedCampaignIds.length} campaigns`);
      setSelectedCampaignIds([]);
      setIsBulkDeleteModalVisible(false);
      setSelectAll(false);
    },
    onError: (error) => {
      console.error('Error deleting campaigns:', error);
      message.error('Failed to delete campaigns');
    },
  });

  // Handle bulk delete button click
  const handleBulkDeleteClick = () => {
    if (selectedCampaignIds.length === 0) {
      message.warning('Please select campaigns to delete');
      return;
    }
    setIsBulkDeleteModalVisible(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = () => {
    if (selectedCampaignIds.length > 0) {
      bulkDeleteMutation.mutate(selectedCampaignIds);
    }
  };

  // Handle select all checkbox change
  const handleSelectAllChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setSelectAll(isChecked);
    if (isChecked && campaigns) {
      // Select all campaigns
      setSelectedCampaignIds(campaigns.map(campaign => campaign.id));
    } else {
      // Deselect all campaigns
      setSelectedCampaignIds([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (campaignId: string, checked: boolean) => {
    if (checked) {
      setSelectedCampaignIds(prev => [...prev, campaignId]);
    } else {
      setSelectedCampaignIds(prev => prev.filter(id => id !== campaignId));
    }
  };

  // We'll move this useEffect after the campaigns query is defined

  // Show notification for campaign being created
  useEffect(() => {
    if (creatingStatus === 'creating' && creatingId && creatingName) {
      message.info({
        content: (
          <div>
            <p>Campaign "{creatingName}" is being created.</p>
            <p>You can continue working while it processes.</p>
          </div>
        ),
        duration: 5,
        key: `creating-${creatingId}`,
      });

      // Clear the query params after showing the message
      router.replace('/campaigns', undefined, { shallow: true });
    }
  }, [creatingStatus, creatingId, creatingName, router]);

  const { data: campaigns = [], isPending: isLoading } = useQuery<Campaign[]>({
    queryKey: ['campaigns', timeRange, statusFilter, typeFilter],
    queryFn: () => api.get('/campaigns', {
      params: {
        timeRange,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        type: typeFilter !== 'all' ? typeFilter : undefined
      }
    }).then(res => res.data),
    enabled: status === 'authenticated',
    staleTime: 5000, // Reduce stale time to 5 seconds for more frequent updates
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  // Update selectAll state when selectedCampaignIds changes
  useEffect(() => {
    if (campaigns.length > 0 && selectedCampaignIds.length === campaigns.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [selectedCampaignIds, campaigns.length]);

  const handleCreateCampaign = (type: string) => {
    setIsCreateModalVisible(false);

    switch (type) {
      case 'standard':
        router.push('/campaigns/new');
        break;
      case 'sequence':
        router.push('/campaigns/sequence/new');
        break;
      case 'drip':
        // Future implementation
        Modal.info({
          title: <span className="dark:text-white">Coming Soon</span>,
          content: <div className="dark:text-gray-300">Drip campaigns will be available in a future update.</div>,
          className: 'dark:bg-dark-card',
          okButtonProps: { className: 'dark:border-blue-500 dark:bg-blue-500 dark:text-white' },
        });
        break;
    }
  };

  if (isLoading) {
    return (
      <Layout title="Campaigns">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-8 dark:text-white">Loading...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Campaigns">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Campaigns</h1>
            <div className="flex items-center gap-4">
              {selectedCampaignIds.length > 0 && (
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleBulkDeleteClick}
                >
                  Delete Selected ({selectedCampaignIds.length})
                </Button>
              )}
              <div className="flex gap-2">
                <select
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value)}
                  className="rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="all">All Time</option>
                  <option value="7d">Last 7 Days</option>
                  <option value="30d">Last 30 Days</option>
                  <option value="90d">Last 90 Days</option>
                </select>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="all">All Status</option>
                  <option value="draft">Draft</option>
                  <option value="active">Active</option>
                  <option value="paused">Paused</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="sending">Sending</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                </select>
                {/* Type filter hidden - only standard campaigns available */}
              </div>

              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => handleCreateCampaign('standard')}
              >
                Create Campaign
              </Button>
            </div>
          </div>

          <div className="bg-white dark:bg-dark-card p-6 rounded-lg shadow mb-6 transition-colors duration-200">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-dark-header">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={handleSelectAllChange}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:bg-dark-card dark:border-gray-600"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Campaign Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Created At
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                  {campaigns.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                        No campaigns found
                      </td>
                    </tr>
                  ) : (
                    campaigns.map((campaign) => (
                      <tr key={campaign.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          <input
                            type="checkbox"
                            checked={selectedCampaignIds.includes(campaign.id)}
                            onChange={(e) => handleCheckboxChange(campaign.id, e.target.checked)}
                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:bg-dark-card dark:border-gray-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          {campaign.name}
                          {campaign.description && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate max-w-xs">{campaign.description}</p>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <Tag color={campaign.type === 'standard' ? 'blue' : campaign.type === 'sequence' ? 'purple' : 'orange'}>
                            {campaign.type === 'standard' ? 'Standard' :
                             campaign.type === 'sequence' ? 'Sequence' : 'Drip'}
                          </Tag>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                            ${campaign.status === 'completed' ? 'bg-green-100 text-green-800' :
                            campaign.status === 'failed' ? 'bg-red-100 text-red-800' :
                            campaign.status === 'sending' ? 'bg-blue-100 text-blue-800' :
                            campaign.status === 'active' ? 'bg-green-100 text-green-800' :
                            campaign.status === 'paused' ? 'bg-orange-100 text-orange-800' :
                            campaign.status === 'scheduled' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'}`}>
                            {campaign.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {campaign.sentCount !== undefined && campaign.totalRecipients !== undefined ? (
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm">{`${campaign.sentCount}/${campaign.totalRecipients}`}</span>
                                <span className="text-xs font-medium">
                                  {Math.min(100, Math.round((campaign.sentCount / campaign.totalRecipients) * 100))}%
                                </span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                <div
                                  className={`h-2.5 rounded-full ${campaign.status === 'completed' ? 'bg-green-600' : 'bg-blue-600'} transition-all duration-500`}
                                  style={{ width: `${Math.min(100, Math.round((campaign.sentCount / campaign.totalRecipients) * 100))}%` }}
                                ></div>
                              </div>
                            </div>
                          ) : (
                            '-'
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(campaign.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => router.push(`/campaigns/${campaign.type === 'sequence' ? 'sequence/' : ''}${campaign.id}`)}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              View
                            </button>
                            <button
                              onClick={() => router.push(`/campaigns/${campaign.type === 'sequence' ? 'sequence/' : ''}${campaign.id}/edit`)}
                              className="text-green-600 hover:text-green-900"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleDeleteClick(campaign)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        <Modal
          title="Delete Campaign"
          open={isDeleteModalVisible}
          onOk={confirmDelete}
          onCancel={() => setIsDeleteModalVisible(false)}
          confirmLoading={deleteMutation.isPending}
          okButtonProps={{ danger: true }}
          okText="Delete"
          className="dark:bg-dark-card"
        >
          <div className="flex items-center">
            <ExclamationCircleOutlined className="text-yellow-500 text-xl mr-2" />
            <span className="dark:text-gray-300">
              Are you sure you want to delete the campaign "{campaignToDelete?.name}"?
              <p className="mt-2 text-sm text-red-500">
                This action cannot be undone. All campaign data including leads, steps, and analytics will be permanently deleted.
              </p>
            </span>
          </div>
        </Modal>

        {/* Bulk Delete Confirmation Modal */}
        <Modal
          title="Delete Multiple Campaigns"
          open={isBulkDeleteModalVisible}
          onOk={confirmBulkDelete}
          onCancel={() => setIsBulkDeleteModalVisible(false)}
          confirmLoading={bulkDeleteMutation.isPending}
          okButtonProps={{ danger: true }}
          okText="Delete All"
          className="dark:bg-dark-card"
        >
          <div className="flex items-center">
            <ExclamationCircleOutlined className="text-yellow-500 text-xl mr-2" />
            <span className="dark:text-gray-300">
              Are you sure you want to delete {selectedCampaignIds.length} campaigns?
              <p className="mt-2 text-sm text-red-500">
                This action cannot be undone. All campaign data including leads, steps, and analytics will be permanently deleted.
              </p>
            </span>
          </div>
        </Modal>
      </div>
    </Layout>
  );
}
