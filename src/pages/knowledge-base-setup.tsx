import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';

export default function KnowledgeBaseSetup() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [content, setContent] = useState('');
  const [title, setTitle] = useState('Company Information');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  // Sample content for quick setup
  const sampleContent = `Avian Email Platform - Professional Email Marketing Solution

About Us:
We are Avian Email, a cutting-edge email marketing and automation platform designed to help businesses of all sizes create, send, and track professional email campaigns. Our platform combines powerful AI technology with user-friendly interfaces to deliver exceptional results.

Our Services:
- Email Campaign Management: Create and send professional email campaigns with advanced targeting
- AI-Powered Email Generation: Use artificial intelligence to create personalized, engaging email content
- Advanced Analytics: Track opens, clicks, bounces, and conversions with detailed reporting
- Lead Management: Import, organize, and segment your leads for targeted campaigns
- Email Account Management: Manage multiple email accounts with reputation monitoring
- Knowledge Base Integration: Connect your company information to AI agents for personalized outreach

Key Features:
- Multi-sender email distribution for better deliverability
- A/B testing capabilities for optimizing campaign performance
- Real-time campaign monitoring and analytics
- GDPR-compliant unsubscribe management
- Integration with popular CRM systems
- Advanced email tracking and reputation monitoring

Target Customers:
- Small to medium businesses looking to improve their email marketing
- Sales teams needing personalized outreach capabilities
- Marketing agencies managing multiple client campaigns
- E-commerce businesses wanting to increase customer engagement
- SaaS companies looking to improve user onboarding and retention

Value Proposition:
- Increase email open rates by up to 40% with AI-powered personalization
- Save 60% of time on email creation with automated content generation
- Improve deliverability with multi-sender distribution and reputation monitoring
- Get detailed insights to optimize your email marketing strategy

Contact Information:
- Website: https://avian-mail.wattlesol.com
- Support: Available 24/7 through our platform
- Pricing: Flexible plans starting from free tier to enterprise solutions`;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!content.trim()) return;

    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/knowledge-bases/quick-setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: content.trim(),
          title: title.trim(),
        }),
      });

      const data = await response.json();
      setResult(data);

      if (data.success) {
        alert('Knowledge base content added successfully!');
      }
    } catch (error) {
      console.error('Error setting up knowledge base:', error);
      setResult({ error: 'Failed to setup knowledge base' });
    } finally {
      setLoading(false);
    }
  };

  const loadSampleContent = () => {
    setContent(sampleContent);
    setTitle('Avian Email Platform Information');
  };

  if (status === 'loading') {
    return <div className="p-8">Loading...</div>;
  }

  if (!session) {
    return <div className="p-8">Please sign in to access this page.</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            Quick Knowledge Base Setup
          </h1>
          
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">
              Fix Empty Knowledge Base Issue
            </h2>
            <p className="text-blue-800 mb-3">
              This tool helps you quickly add company information to your knowledge base so AI agents can generate personalized emails.
            </p>
            <button
              onClick={loadSampleContent}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Load Sample Content
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                Document Title
              </label>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Company Information"
              />
            </div>

            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                Knowledge Base Content
              </label>
              <textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                rows={20}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your company information, services, value propositions, etc..."
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                Characters: {content.length}
              </p>
            </div>

            <button
              type="submit"
              disabled={loading || !content.trim()}
              className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {loading ? 'Adding to Knowledge Base...' : 'Add to Knowledge Base'}
            </button>
          </form>

          {result && (
            <div className={`mt-6 p-4 rounded-lg ${result.success ? 'bg-green-50' : 'bg-red-50'}`}>
              <h3 className={`font-semibold ${result.success ? 'text-green-900' : 'text-red-900'}`}>
                {result.success ? 'Success!' : 'Error'}
              </h3>
              <pre className={`mt-2 text-sm ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}

          <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
            <h3 className="font-semibold text-yellow-900 mb-2">Next Steps:</h3>
            <ol className="list-decimal list-inside text-yellow-800 space-y-1">
              <li>Add your company information using this form</li>
              <li>Go to your campaigns and test email generation</li>
              <li>Check the logs to see if knowledge base content is being found</li>
              <li>The AI should now generate personalized emails instead of fallback messages</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
