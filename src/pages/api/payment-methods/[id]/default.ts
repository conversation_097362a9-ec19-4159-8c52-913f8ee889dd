import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { stripe } from '@/config/stripe';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Payment method ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        ownedOrganization: true,
      },
    });

    if (!user?.ownedOrganization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const { customerId } = user.ownedOrganization;

    if (!customerId) {
      return res.status(404).json({ error: 'No Stripe customer found' });
    }

    // Set the payment method as the default
    await stripe.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: id,
      },
    });

    return res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error in set default payment method endpoint', {
      method,
      userId: req.user.id,
      paymentMethodId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
