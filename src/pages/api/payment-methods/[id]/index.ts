import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { stripe } from '@/config/stripe';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'DELETE') {
    res.setHeader('Allow', ['DELETE']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Payment method ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        ownedOrganization: true,
      },
    });

    if (!user?.ownedOrganization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const { customerId } = user.ownedOrganization;

    if (!customerId) {
      return res.status(404).json({ error: 'No Stripe customer found' });
    }

    // Check if this is the default payment method
    const customer = await stripe.customers.retrieve(customerId);
    // Cast customer to any to access invoice_settings
    const customerObj = typeof customer !== 'string' ? customer as any : null;
    const defaultPaymentMethodId = customerObj?.invoice_settings?.default_payment_method || null;

    if (id === defaultPaymentMethodId) {
      return res.status(400).json({ error: 'Cannot remove the default payment method' });
    }

    // Detach the payment method from the customer
    await stripe.paymentMethods.detach(id);

    return res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error in delete payment method endpoint', {
      method,
      userId: req.user.id,
      paymentMethodId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
