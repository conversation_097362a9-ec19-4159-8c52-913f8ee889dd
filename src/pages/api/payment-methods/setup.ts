import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { stripe } from '@/config/stripe';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        ownedOrganization: true,
      },
    });

    if (!user?.ownedOrganization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    let { customerId } = user.ownedOrganization;

    // If the customer doesn't exist in Stripe, create one
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name || undefined,
        metadata: {
          organizationId: user.ownedOrganization.id,
        },
      });

      customerId = customer.id;

      // Update the organization with the new customer ID
      await prisma.organization.update({
        where: { id: user.ownedOrganization.id },
        data: { customerId },
      });
    }

    // Create a SetupIntent to securely collect the customer's payment details
    const setupIntent = await stripe.setupIntents.create({
      customer: customerId,
      payment_method_types: ['card'],
    });

    // Create a Stripe Checkout session for adding a payment method
    const session = await stripe.checkout.sessions.create({
      mode: 'setup',
      customer: customerId,
      payment_method_types: ['card'],
      success_url: `${process.env.NEXTAUTH_URL}/settings/account?setup_success=true`,
      cancel_url: `${process.env.NEXTAUTH_URL}/settings/account?setup_canceled=true`,
    });

    return res.status(200).json({ url: session.url });
  } catch (error) {
    logger.error('Error in payment method setup endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
