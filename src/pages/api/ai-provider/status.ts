import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { AIProviderService } from '@/services/ai-provider.service';
import { logger } from '@/lib/logger';

/**
 * API endpoint to get AI provider status and configuration
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const providerInfo = AIProviderService.getProviderInfo();
    
    // Get environment configuration (without sensitive data)
    const config = {
      currentProvider: providerInfo.provider,
      chatModel: providerInfo.chatModel,
      embeddingModel: providerInfo.embeddingModel,
      baseUrl: providerInfo.baseUrl,
      availableProviders: ['openai', 'ollama'],
      configuration: {
        openai: {
          hasApiKey: !!(process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your-openai-api-key-here'),
          model: process.env.OPENAI_MODEL || 'gpt-4',
        },
        ollama: {
          hasBaseUrl: !!process.env.OLLAMA_BASE_URL,
          baseUrl: process.env.OLLAMA_BASE_URL || 'Not configured',
          model: process.env.OLLAMA_MODEL || 'deepseek-r1',
          embeddingModel: process.env.OLLAMA_EMBEDDING_MODEL || 'nomic-embed-text',
        },
      },
    };

    logger.info('AI provider status requested', {
      provider: providerInfo.provider,
      userId: session.user?.id,
    });

    return res.status(200).json({
      success: true,
      ...config,
    });
  } catch (error) {
    logger.error('Error in AI provider status endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
