import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { AIProviderService } from '@/services/ai-provider.service';
import { logger } from '@/lib/logger';

/**
 * API endpoint to test AI provider connection and get provider info
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (req.method === 'GET') {
      // Get provider information
      const providerInfo = AIProviderService.getProviderInfo();
      
      logger.info('AI provider info requested', {
        provider: providerInfo.provider,
        userId: session.user?.id,
      });

      return res.status(200).json({
        success: true,
        provider: providerInfo,
      });
    }

    if (req.method === 'POST') {
      // Test provider connection
      logger.info('Testing AI provider connection', {
        provider: AIProviderService.getProvider(),
        userId: session.user?.id,
      });

      const isConnected = await AIProviderService.testConnection();
      const providerInfo = AIProviderService.getProviderInfo();

      return res.status(200).json({
        success: true,
        connected: isConnected,
        provider: providerInfo,
        message: isConnected 
          ? `Successfully connected to ${providerInfo.provider}` 
          : `Failed to connect to ${providerInfo.provider}`,
      });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Error in AI provider test endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method,
    });

    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
