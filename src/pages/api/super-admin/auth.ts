import { NextApiRequest, NextApiResponse } from 'next';
import { SuperAdminService } from '@/services/super-admin.service';
import { logger } from '@/lib/logger';

/**
 * Super Admin Authentication API
 * Handles login, token verification, and initial setup
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // CORS headers for super admin dashboard
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3001');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  try {
    const { method } = req;
    const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'unknown';

    switch (method) {
      case 'POST':
        return await handleAuth(req, res, clientIP as string);
      case 'GET':
        return await handleVerifyToken(req, res);
      default:
        res.setHeader('Allow', ['GET', 'POST', 'OPTIONS']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in super admin auth endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleAuth(req: NextApiRequest, res: NextApiResponse, clientIP: string) {
  try {
    const { action, email, password, name, adminSecret } = req.body;

    switch (action) {
      case 'login':
        return await handleLogin(req, res, email, password, clientIP);
      case 'setup':
        return await handleInitialSetup(req, res, email, password, name, adminSecret, clientIP);
      case 'check-setup':
        return await handleCheckSetup(req, res);
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    logger.error('Error handling auth action', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return res.status(500).json({ error: 'Authentication failed' });
  }
}

async function handleLogin(req: NextApiRequest, res: NextApiResponse, email: string, password: string, clientIP: string) {
  if (!email || !password) {
    return res.status(400).json({ error: 'Email and password are required' });
  }

  const authResult = await SuperAdminService.authenticateAdmin(email, password, clientIP);

  if (!authResult) {
    return res.status(401).json({ error: 'Invalid credentials' });
  }

  return res.status(200).json({
    message: 'Authentication successful',
    token: authResult.token,
    admin: authResult.admin
  });
}

async function handleInitialSetup(req: NextApiRequest, res: NextApiResponse, email: string, password: string, name: string, adminSecret: string, clientIP: string) {
  // Validate admin secret for initial setup
  if (!SuperAdminService.validateAdminSecret(adminSecret)) {
    logger.warn('Invalid admin secret provided for initial setup', { clientIP });
    return res.status(403).json({ error: 'Invalid admin secret' });
  }

  if (!email || !password) {
    return res.status(400).json({ error: 'Email and password are required' });
  }

  // Check if super admin already exists
  const hasSuperAdmin = await SuperAdminService.hasSuperAdmin();
  if (hasSuperAdmin) {
    return res.status(400).json({ error: 'Super admin already exists' });
  }

  const success = await SuperAdminService.createFirstSuperAdmin(email, password, name);

  if (!success) {
    return res.status(500).json({ error: 'Failed to create super admin' });
  }

  // Automatically log in the new admin
  const authResult = await SuperAdminService.authenticateAdmin(email, password, clientIP);

  if (!authResult) {
    return res.status(500).json({ error: 'Super admin created but login failed' });
  }

  return res.status(201).json({
    message: 'Super admin created and authenticated successfully',
    token: authResult.token,
    admin: authResult.admin
  });
}

async function handleCheckSetup(req: NextApiRequest, res: NextApiResponse) {
  const hasSuperAdmin = await SuperAdminService.hasSuperAdmin();

  return res.status(200).json({
    setupRequired: !hasSuperAdmin,
    hasSuperAdmin
  });
}

async function handleVerifyToken(req: NextApiRequest, res: NextApiResponse) {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'No token provided' });
  }

  const token = authHeader.substring(7);
  const admin = await SuperAdminService.verifyAdminToken(token);

  if (!admin) {
    return res.status(401).json({ error: 'Invalid or expired token' });
  }

  return res.status(200).json({
    valid: true,
    admin
  });
}
