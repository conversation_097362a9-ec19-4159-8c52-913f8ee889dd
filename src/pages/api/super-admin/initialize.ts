import { NextApiRequest, NextApiResponse } from 'next';
import { SuperAdminService } from '@/services/super-admin.service';
import { PackageManagementService } from '@/services/package-management.service';
import { logger } from '@/lib/logger';

/**
 * Super Admin System Initialization API
 * Handles initial setup of the package system and default data
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // CORS headers for super admin dashboard
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3001');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');

  try {
    // Verify admin authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const token = authHeader.substring(7);
    const admin = await SuperAdminService.verifyAdminToken(token);

    if (!admin) {
      return res.status(401).json({ error: 'Invalid or expired token' });
    }

    const { method } = req;

    switch (method) {
      case 'POST':
        return await handleInitialization(req, res, admin.id);
      case 'GET':
        return await handleGetInitializationStatus(req, res);
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in super admin initialization endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleInitialization(req: NextApiRequest, res: NextApiResponse, adminId: string) {
  try {
    const { action } = req.body;

    switch (action) {
      case 'setup-packages':
        return await handleSetupPackages(req, res, adminId);
      case 'assign-free-packages':
        return await handleAssignFreePackages(req, res, adminId);
      case 'full-setup':
        return await handleFullSetup(req, res, adminId);
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    logger.error('Error handling initialization', {
      error: error instanceof Error ? error.message : 'Unknown error',
      adminId
    });
    return res.status(500).json({ error: 'Initialization failed' });
  }
}

async function handleSetupPackages(req: NextApiRequest, res: NextApiResponse, adminId: string) {
  try {
    logger.info('Starting package system setup', { adminId });

    // Create default free package
    const freePackage = await PackageManagementService.createDefaultFreePackage();

    // Create sample premium packages
    const premiumPackages = [];

    // Pro Package
    try {
      const proPackage = await PackageManagementService.createPackage({
        name: 'Pro',
        description: 'Professional package with AI features and increased email limits',
        price: 29.99,
        billingCycle: 'MONTHLY',
        dailyEmailLimit: 1000,
        monthlyEmailLimit: 30000,
        emailAccountLimit: 5,
        aiFeatures: ['REPLY_AGENT', 'TEMPLATE_DESIGNER', 'CONTENT_GENERATOR'],
        status: 'ACTIVE'
      }, adminId);
      premiumPackages.push(proPackage);
    } catch (error) {
      logger.warn('Pro package might already exist', { error });
    }

    // Enterprise Package
    try {
      const enterprisePackage = await PackageManagementService.createPackage({
        name: 'Enterprise',
        description: 'Enterprise package with all AI features and unlimited emails',
        price: 99.99,
        billingCycle: 'MONTHLY',
        dailyEmailLimit: 10000,
        monthlyEmailLimit: 300000,
        emailAccountLimit: 20,
        aiFeatures: ['REPLY_AGENT', 'TEMPLATE_DESIGNER', 'CONTENT_GENERATOR', 'IMAGE_GENERATOR', 'KNOWLEDGE_BASE'],
        status: 'ACTIVE'
      }, adminId);
      premiumPackages.push(enterprisePackage);
    } catch (error) {
      logger.warn('Enterprise package might already exist', { error });
    }

    logger.info('Package system setup completed', {
      adminId,
      freePackageId: freePackage.id,
      premiumPackagesCreated: premiumPackages.length
    });

    return res.status(200).json({
      message: 'Package system setup completed successfully',
      data: {
        freePackage,
        premiumPackages,
        totalPackagesCreated: 1 + premiumPackages.length
      }
    });
  } catch (error) {
    logger.error('Error setting up packages', {
      error: error instanceof Error ? error.message : 'Unknown error',
      adminId
    });
    return res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to setup packages'
    });
  }
}

async function handleAssignFreePackages(req: NextApiRequest, res: NextApiResponse, adminId: string) {
  try {
    logger.info('Starting free package assignment', { adminId });

    const result = await PackageManagementService.assignFreePackageToOrganizations();

    logger.info('Free package assignment completed', {
      adminId,
      assignedCount: result.assignedCount
    });

    return res.status(200).json({
      message: 'Free packages assigned successfully',
      data: result
    });
  } catch (error) {
    logger.error('Error assigning free packages', {
      error: error instanceof Error ? error.message : 'Unknown error',
      adminId
    });
    return res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to assign free packages'
    });
  }
}

async function handleFullSetup(req: NextApiRequest, res: NextApiResponse, adminId: string) {
  try {
    logger.info('Starting full system setup', { adminId });

    // Step 1: Setup packages
    const freePackage = await PackageManagementService.createDefaultFreePackage();

    // Step 2: Assign free packages to existing organizations
    const assignmentResult = await PackageManagementService.assignFreePackageToOrganizations();

    // Step 3: Create premium packages (optional)
    const premiumPackages = [];
    try {
      const proPackage = await PackageManagementService.createPackage({
        name: 'Pro',
        description: 'Professional package with AI features and increased email limits',
        price: 29.99,
        billingCycle: 'MONTHLY',
        dailyEmailLimit: 1000,
        monthlyEmailLimit: 30000,
        emailAccountLimit: 5,
        aiFeatures: ['REPLY_AGENT', 'TEMPLATE_DESIGNER', 'CONTENT_GENERATOR'],
        status: 'ACTIVE'
      }, adminId);
      premiumPackages.push(proPackage);

      const enterprisePackage = await PackageManagementService.createPackage({
        name: 'Enterprise',
        description: 'Enterprise package with all AI features and unlimited emails',
        price: 99.99,
        billingCycle: 'MONTHLY',
        dailyEmailLimit: 10000,
        monthlyEmailLimit: 300000,
        emailAccountLimit: 20,
        aiFeatures: ['REPLY_AGENT', 'TEMPLATE_DESIGNER', 'CONTENT_GENERATOR', 'IMAGE_GENERATOR', 'KNOWLEDGE_BASE'],
        status: 'ACTIVE'
      }, adminId);
      premiumPackages.push(enterprisePackage);
    } catch (error) {
      logger.warn('Some premium packages might already exist', { error });
    }

    logger.info('Full system setup completed', {
      adminId,
      freePackageId: freePackage.id,
      organizationsAssigned: assignmentResult.assignedCount,
      premiumPackagesCreated: premiumPackages.length
    });

    return res.status(200).json({
      message: 'Full system setup completed successfully',
      data: {
        freePackage,
        assignmentResult,
        premiumPackages,
        summary: {
          packagesCreated: 1 + premiumPackages.length,
          organizationsAssigned: assignmentResult.assignedCount
        }
      }
    });
  } catch (error) {
    logger.error('Error in full setup', {
      error: error instanceof Error ? error.message : 'Unknown error',
      adminId
    });
    return res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to complete full setup'
    });
  }
}

async function handleGetInitializationStatus(req: NextApiRequest, res: NextApiResponse) {
  try {
    const packages = await PackageManagementService.getAllPackages();
    const hasFreePackage = packages.some(pkg => pkg.name === 'Free' && pkg.status === 'ACTIVE');
    const hasProPackage = packages.some(pkg => pkg.name === 'Pro' && pkg.status === 'ACTIVE');
    const hasEnterprisePackage = packages.some(pkg => pkg.name === 'Enterprise' && pkg.status === 'ACTIVE');

    return res.status(200).json({
      initialized: hasFreePackage,
      packages: {
        total: packages.length,
        hasFree: hasFreePackage,
        hasPro: hasProPackage,
        hasEnterprise: hasEnterprisePackage
      },
      setupRequired: !hasFreePackage
    });
  } catch (error) {
    logger.error('Error getting initialization status', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return res.status(500).json({ error: 'Failed to get initialization status' });
  }
}
