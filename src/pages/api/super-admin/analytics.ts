import { NextApiRequest, NextApiResponse } from 'next';
import { SuperAdminService } from '@/services/super-admin.service';
import { BusinessAnalyticsService } from '@/services/business-analytics.service';
import { logger } from '@/lib/logger';

/**
 * Super Admin Business Analytics API
 * Provides comprehensive business metrics and analytics
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');

  try {
    // Verify admin authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const token = authHeader.substring(7);
    const admin = await SuperAdminService.verifyAdminToken(token);
    
    if (!admin) {
      return res.status(401).json({ error: 'Invalid or expired token' });
    }

    const { method } = req;

    if (method !== 'GET') {
      res.setHeader('Allow', ['GET']);
      return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }

    return await handleGetAnalytics(req, res);
  } catch (error) {
    logger.error('Error in super admin analytics endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetAnalytics(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { type } = req.query;

    switch (type) {
      case 'overview':
        return await handleOverviewAnalytics(req, res);
      case 'organizations':
        return await handleOrganizationAnalytics(req, res);
      case 'detailed':
        return await handleDetailedAnalytics(req, res);
      default:
        return await handleOverviewAnalytics(req, res);
    }
  } catch (error) {
    logger.error('Error getting analytics', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return res.status(500).json({ error: 'Failed to get analytics' });
  }
}

async function handleOverviewAnalytics(req: NextApiRequest, res: NextApiResponse) {
  try {
    const metrics = await BusinessAnalyticsService.getBusinessMetrics();

    return res.status(200).json({
      success: true,
      data: metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting overview analytics', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return res.status(500).json({ error: 'Failed to get overview analytics' });
  }
}

async function handleOrganizationAnalytics(req: NextApiRequest, res: NextApiResponse) {
  try {
    const organizations = await BusinessAnalyticsService.getOrganizationAnalytics();

    return res.status(200).json({
      success: true,
      data: organizations,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting organization analytics', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return res.status(500).json({ error: 'Failed to get organization analytics' });
  }
}

async function handleDetailedAnalytics(req: NextApiRequest, res: NextApiResponse) {
  try {
    const [metrics, organizations] = await Promise.all([
      BusinessAnalyticsService.getBusinessMetrics(),
      BusinessAnalyticsService.getOrganizationAnalytics()
    ]);

    return res.status(200).json({
      success: true,
      data: {
        overview: metrics,
        organizations: organizations,
        summary: {
          totalRevenue: metrics.revenue.total,
          totalUsers: metrics.users.total,
          totalOrganizations: organizations.length,
          averageRevenuePerUser: metrics.users.total > 0 ? metrics.revenue.total / metrics.users.total : 0,
          conversionRate: metrics.users.total > 0 ? (metrics.subscriptions.active / metrics.users.total) * 100 : 0
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting detailed analytics', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return res.status(500).json({ error: 'Failed to get detailed analytics' });
  }
}
