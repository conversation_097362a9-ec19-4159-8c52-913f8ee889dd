import { NextApiRequest, NextApiResponse } from 'next';
import { SuperAdminService } from '@/services/super-admin.service';
import { PackageManagementService } from '@/services/package-management.service';
import { logger } from '@/lib/logger';

/**
 * Super Admin Package Management API
 * Handles CRUD operations for subscription packages
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');

  try {
    // Verify admin authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const token = authHeader.substring(7);
    const admin = await SuperAdminService.verifyAdminToken(token);
    
    if (!admin) {
      return res.status(401).json({ error: 'Invalid or expired token' });
    }

    const { method } = req;

    switch (method) {
      case 'GET':
        return await handleGetPackages(req, res);
      case 'POST':
        return await handleCreatePackage(req, res, admin.id);
      case 'PUT':
        return await handleUpdatePackage(req, res, admin.id);
      case 'DELETE':
        return await handleDeletePackage(req, res, admin.id);
      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in super admin packages endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetPackages(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { id } = req.query;

    if (id) {
      // Get specific package
      const packageData = await PackageManagementService.getPackageById(id as string);
      if (!packageData) {
        return res.status(404).json({ error: 'Package not found' });
      }
      return res.status(200).json(packageData);
    } else {
      // Get all packages
      const packages = await PackageManagementService.getAllPackages();
      return res.status(200).json(packages);
    }
  } catch (error) {
    logger.error('Error getting packages', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return res.status(500).json({ error: 'Failed to get packages' });
  }
}

async function handleCreatePackage(req: NextApiRequest, res: NextApiResponse, adminId: string) {
  try {
    const {
      name,
      description,
      price,
      billingCycle,
      dailyEmailLimit,
      monthlyEmailLimit,
      emailAccountLimit,
      aiFeatures,
      status
    } = req.body;

    // Validation
    if (!name || !description || price === undefined || !billingCycle || !dailyEmailLimit || !emailAccountLimit) {
      return res.status(400).json({ 
        error: 'Missing required fields: name, description, price, billingCycle, dailyEmailLimit, emailAccountLimit' 
      });
    }

    if (!['WEEKLY', 'MONTHLY', 'YEARLY'].includes(billingCycle)) {
      return res.status(400).json({ error: 'Invalid billing cycle' });
    }

    if (!['ACTIVE', 'INACTIVE', 'ARCHIVED'].includes(status || 'ACTIVE')) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    const packageData = {
      name,
      description,
      price: parseFloat(price),
      billingCycle,
      dailyEmailLimit: parseInt(dailyEmailLimit),
      monthlyEmailLimit: monthlyEmailLimit ? parseInt(monthlyEmailLimit) : undefined,
      emailAccountLimit: parseInt(emailAccountLimit),
      aiFeatures: aiFeatures || [],
      status: status || 'ACTIVE'
    };

    const newPackage = await PackageManagementService.createPackage(packageData, adminId);

    return res.status(201).json({
      message: 'Package created successfully',
      package: newPackage
    });
  } catch (error) {
    logger.error('Error creating package', {
      error: error instanceof Error ? error.message : 'Unknown error',
      adminId
    });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to create package' 
    });
  }
}

async function handleUpdatePackage(req: NextApiRequest, res: NextApiResponse, adminId: string) {
  try {
    const { id } = req.query;
    
    if (!id) {
      return res.status(400).json({ error: 'Package ID is required' });
    }

    const {
      name,
      description,
      price,
      billingCycle,
      dailyEmailLimit,
      monthlyEmailLimit,
      emailAccountLimit,
      aiFeatures,
      status
    } = req.body;

    const updateData: any = {};
    
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (price !== undefined) updateData.price = parseFloat(price);
    if (billingCycle !== undefined) {
      if (!['WEEKLY', 'MONTHLY', 'YEARLY'].includes(billingCycle)) {
        return res.status(400).json({ error: 'Invalid billing cycle' });
      }
      updateData.billingCycle = billingCycle;
    }
    if (dailyEmailLimit !== undefined) updateData.dailyEmailLimit = parseInt(dailyEmailLimit);
    if (monthlyEmailLimit !== undefined) updateData.monthlyEmailLimit = parseInt(monthlyEmailLimit);
    if (emailAccountLimit !== undefined) updateData.emailAccountLimit = parseInt(emailAccountLimit);
    if (aiFeatures !== undefined) updateData.aiFeatures = aiFeatures;
    if (status !== undefined) {
      if (!['ACTIVE', 'INACTIVE', 'ARCHIVED'].includes(status)) {
        return res.status(400).json({ error: 'Invalid status' });
      }
      updateData.status = status;
    }

    const updatedPackage = await PackageManagementService.updatePackage(id as string, updateData, adminId);

    return res.status(200).json({
      message: 'Package updated successfully',
      package: updatedPackage
    });
  } catch (error) {
    logger.error('Error updating package', {
      error: error instanceof Error ? error.message : 'Unknown error',
      adminId
    });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to update package' 
    });
  }
}

async function handleDeletePackage(req: NextApiRequest, res: NextApiResponse, adminId: string) {
  try {
    const { id } = req.query;
    
    if (!id) {
      return res.status(400).json({ error: 'Package ID is required' });
    }

    const success = await PackageManagementService.deletePackage(id as string, adminId);

    if (!success) {
      return res.status(500).json({ error: 'Failed to delete package' });
    }

    return res.status(200).json({
      message: 'Package archived successfully'
    });
  } catch (error) {
    logger.error('Error deleting package', {
      error: error instanceof Error ? error.message : 'Unknown error',
      adminId
    });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to delete package' 
    });
  }
}
