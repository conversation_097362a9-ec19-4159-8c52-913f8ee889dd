import { NextApiRequest, NextApiResponse } from 'next';
import { TemplateService } from '@/services/template.service';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'GET':
      const templates = await TemplateService.listTemplates(req.query.organizationId as string);
      return res.json(templates);

    case 'POST':
      const newTemplate = await TemplateService.createTemplate(req.body);
      return res.json(newTemplate);

    default:
      res.setHeader('Allow', ['GET', 'POST']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}