import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { AdminAccessService } from '@/services/admin-access.service';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

/**
 * Admin setup endpoint for initializing the first admin user
 * This endpoint can be used to:
 * 1. Initialize the first admin user if no admins exist
 * 2. Check admin status
 * 3. Manage admin users (for existing admins)
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.email) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { method } = req;

    switch (method) {
      case 'GET':
        return await handleGetAdminStatus(req, res, session.user.email);
      case 'POST':
        return await handleAdminSetup(req, res, session.user.email);
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in admin setup endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetAdminStatus(req: NextApiRequest, res: NextApiResponse, userEmail: string) {
  try {
    // Check if any global admins exist
    const globalAdmins = await AdminAccessService.getGlobalAdmins();
    const hasGlobalAdmins = globalAdmins.length > 0;
    
    // Check current user's admin status
    const isAdmin = await AdminAccessService.isAdmin(userEmail);
    const adminLevel = await AdminAccessService.getAdminLevel(userEmail);
    
    // Get current user info
    const user = await prisma.user.findUnique({
      where: { email: userEmail },
      select: {
        id: true,
        name: true,
        email: true,
        isAdmin: true,
        ownedOrganization: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    return res.status(200).json({
      hasGlobalAdmins,
      isAdmin,
      adminLevel,
      user,
      globalAdmins: isAdmin ? globalAdmins : [], // Only show to admins
      canInitialize: !hasGlobalAdmins
    });
  } catch (error) {
    logger.error('Error getting admin status', { error, userEmail });
    return res.status(500).json({ error: 'Failed to get admin status' });
  }
}

async function handleAdminSetup(req: NextApiRequest, res: NextApiResponse, userEmail: string) {
  try {
    const { action, targetEmail } = req.body;

    switch (action) {
      case 'initialize':
        return await handleInitializeFirstAdmin(req, res, userEmail);
      case 'makeAdmin':
        return await handleMakeAdmin(req, res, userEmail, targetEmail);
      case 'removeAdmin':
        return await handleRemoveAdmin(req, res, userEmail, targetEmail);
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    logger.error('Error handling admin setup', { error, userEmail });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to handle admin setup' 
    });
  }
}

async function handleInitializeFirstAdmin(req: NextApiRequest, res: NextApiResponse, userEmail: string) {
  try {
    // Check if any global admins already exist
    const existingAdmins = await AdminAccessService.getGlobalAdmins();
    if (existingAdmins.length > 0) {
      return res.status(400).json({ 
        error: 'Global admins already exist. Cannot initialize.' 
      });
    }

    // Initialize the current user as the first admin
    await AdminAccessService.initializeFirstAdmin(userEmail);

    logger.info('First admin initialized', { userEmail });

    return res.status(200).json({
      message: 'Successfully initialized as the first admin',
      user: {
        email: userEmail,
        isAdmin: true,
        adminLevel: 'global'
      }
    });
  } catch (error) {
    logger.error('Error initializing first admin', { error, userEmail });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to initialize admin' 
    });
  }
}

async function handleMakeAdmin(req: NextApiRequest, res: NextApiResponse, userEmail: string, targetEmail: string) {
  try {
    if (!targetEmail) {
      return res.status(400).json({ error: 'Target email is required' });
    }

    await AdminAccessService.makeGlobalAdmin(targetEmail, userEmail);

    return res.status(200).json({
      message: `Successfully made ${targetEmail} a global admin`
    });
  } catch (error) {
    logger.error('Error making user admin', { error, userEmail, targetEmail });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to make user admin' 
    });
  }
}

async function handleRemoveAdmin(req: NextApiRequest, res: NextApiResponse, userEmail: string, targetEmail: string) {
  try {
    if (!targetEmail) {
      return res.status(400).json({ error: 'Target email is required' });
    }

    await AdminAccessService.removeGlobalAdmin(targetEmail, userEmail);

    return res.status(200).json({
      message: `Successfully removed admin access from ${targetEmail}`
    });
  } catch (error) {
    logger.error('Error removing admin access', { error, userEmail, targetEmail });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to remove admin access' 
    });
  }
}
