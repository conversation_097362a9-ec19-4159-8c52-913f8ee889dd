import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { AdminPackageService } from '@/services/admin-package.service';
import { EnhancedUsageService } from '@/services/enhanced-usage.service';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

/**
 * Admin API endpoint for managing organization subscriptions
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.email) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        ownedOrganization: true
      }
    });

    if (!user?.ownedOrganization) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { method } = req;

    switch (method) {
      case 'GET':
        return await handleGetOrganizations(req, res);
      case 'POST':
        return await handleAssignPackage(req, res);
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in admin organizations endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetOrganizations(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { page = '1', limit = '20', search } = req.query;
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const offset = (pageNum - 1) * limitNum;

    const where = search ? {
      OR: [
        { name: { contains: search as string, mode: 'insensitive' as const } },
        { owner: { email: { contains: search as string, mode: 'insensitive' as const } } }
      ]
    } : {};

    const [organizations, total] = await Promise.all([
      prisma.organization.findMany({
        where,
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          package: {
            select: {
              id: true,
              name: true,
              price: true,
              billingCycle: true
            }
          },
          subscription: {
            select: {
              id: true,
              tier: true,
              status: true
            }
          },
          _count: {
            select: {
              members: true,
              campaigns: true,
              leads: true,
              agents: true,
              knowledgeBases: true
            }
          }
        },
        skip: offset,
        take: limitNum,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.organization.count({ where })
    ]);

    // Get usage data for each organization
    const organizationsWithUsage = await Promise.all(
      organizations.map(async (org) => {
        const usage = await EnhancedUsageService.getCurrentUsage(org.id);
        return {
          ...org,
          usage
        };
      })
    );

    return res.status(200).json({
      organizations: organizationsWithUsage,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    logger.error('Error fetching organizations', { error });
    return res.status(500).json({ error: 'Failed to fetch organizations' });
  }
}

async function handleAssignPackage(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { organizationId, packageId } = req.body;

    if (!organizationId || !packageId) {
      return res.status(400).json({ error: 'Organization ID and Package ID are required' });
    }

    const updatedOrganization = await AdminPackageService.assignPackageToOrganization(
      organizationId,
      packageId
    );

    return res.status(200).json({ organization: updatedOrganization });
  } catch (error) {
    logger.error('Error assigning package to organization', { error });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to assign package' 
    });
  }
}
