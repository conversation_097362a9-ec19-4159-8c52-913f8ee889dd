import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { prisma } from '@/lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const timeframe = req.query.timeframe as string;
    const startDate = new Date();

    switch (timeframe) {
      case 'day':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
      default:
        startDate.setDate(startDate.getDate() - 30);
        break;
    }

    const organizations = await prisma.organization.findMany({
      include: {
        subscription: true,
        usage: {
          where: {
            period: {
              gte: startDate,
            },
          },
        },
      },
    });

    const organizationsUsage = organizations.map((org) => {
      const usage = org.usage[0] || {
        emailsSent: 0,
        emailAccounts: 0,
        storageUsed: 0,
      };

      return {
        id: org.id,
        name: org.name,
        usage,
        subscription: {
          plan: org.subscription?.stripePriceId || 'free',
          status: org.subscription?.status || 'inactive',
        },
      };
    });

    return res.status(200).json({ organizations: organizationsUsage });
  } catch (error) {
    console.error('Failed to get organizations usage:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
