import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { BusinessAnalyticsService } from '@/services/business-analytics.service';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

/**
 * Admin API endpoint for business analytics
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.email) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        ownedOrganization: true
      }
    });

    if (!user?.ownedOrganization) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { method } = req;

    switch (method) {
      case 'GET':
        return await handleGetAnalytics(req, res);
      default:
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in admin analytics endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetAnalytics(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { days = '30', type = 'dashboard' } = req.query;
    
    if (type === 'dashboard') {
      const dashboardMetrics = await BusinessAnalyticsService.getDashboardMetrics();
      return res.status(200).json({ metrics: dashboardMetrics });
    } else if (type === 'detailed') {
      const analyticsData = await BusinessAnalyticsService.getAnalyticsData(parseInt(days as string));
      return res.status(200).json({ analytics: analyticsData });
    } else {
      return res.status(400).json({ error: 'Invalid analytics type' });
    }
  } catch (error) {
    logger.error('Error fetching analytics', { error });
    return res.status(500).json({ error: 'Failed to fetch analytics' });
  }
}
