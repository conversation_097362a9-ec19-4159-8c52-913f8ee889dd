import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { AdminPackageService } from '@/services/admin-package.service';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

/**
 * Admin API endpoint for managing individual packages
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.email) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        ownedOrganization: true
      }
    });

    if (!user?.ownedOrganization) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { method } = req;
    const { id } = req.query;

    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: 'Package ID is required' });
    }

    switch (method) {
      case 'GET':
        return await handleGetPackage(req, res, id);
      case 'PUT':
        return await handleUpdatePackage(req, res, id);
      case 'DELETE':
        return await handleDeletePackage(req, res, id);
      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in admin package endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method,
      packageId: req.query.id
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetPackage(req: NextApiRequest, res: NextApiResponse, packageId: string) {
  try {
    const package_ = await AdminPackageService.getPackageById(packageId);
    return res.status(200).json({ package: package_ });
  } catch (error) {
    if (error instanceof Error && error.message === 'Package not found') {
      return res.status(404).json({ error: 'Package not found' });
    }
    logger.error('Error fetching package', { error, packageId });
    return res.status(500).json({ error: 'Failed to fetch package' });
  }
}

async function handleUpdatePackage(req: NextApiRequest, res: NextApiResponse, packageId: string) {
  try {
    const updateData = req.body;
    const updatedPackage = await AdminPackageService.updatePackage(packageId, updateData);
    return res.status(200).json({ package: updatedPackage });
  } catch (error) {
    logger.error('Error updating package', { error, packageId });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to update package' 
    });
  }
}

async function handleDeletePackage(req: NextApiRequest, res: NextApiResponse, packageId: string) {
  try {
    const deletedPackage = await AdminPackageService.deletePackage(packageId);
    return res.status(200).json({ package: deletedPackage });
  } catch (error) {
    logger.error('Error deleting package', { error, packageId });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to delete package' 
    });
  }
}
