import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { AdminPackageService } from '@/services/admin-package.service';
import { AdminAccessService } from '@/services/admin-access.service';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

/**
 * Admin API endpoint for managing packages
 * Only accessible by admin users
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user?.email) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user has admin access
    const isAdmin = await AdminAccessService.isAdmin(session.user.email);
    if (!isAdmin) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    // Get user for audit logging
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    const { method } = req;

    switch (method) {
      case 'GET':
        return await handleGetPackages(req, res);
      case 'POST':
        return await handleCreatePackage(req, res, user?.id);
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in admin packages endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetPackages(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { includeArchived } = req.query;
    const packages = await AdminPackageService.getAllPackages(includeArchived === 'true');

    return res.status(200).json({ packages });
  } catch (error) {
    logger.error('Error fetching packages', { error });
    return res.status(500).json({ error: 'Failed to fetch packages' });
  }
}

async function handleCreatePackage(req: NextApiRequest, res: NextApiResponse, createdBy?: string) {
  try {
    const packageData = req.body;

    // Validate required fields
    if (!packageData.name || typeof packageData.price !== 'number') {
      return res.status(400).json({ error: 'Name and price are required' });
    }

    const newPackage = await AdminPackageService.createPackage(packageData, createdBy);

    return res.status(201).json({ package: newPackage });
  } catch (error) {
    logger.error('Error creating package', { error });
    return res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to create package'
    });
  }
}
