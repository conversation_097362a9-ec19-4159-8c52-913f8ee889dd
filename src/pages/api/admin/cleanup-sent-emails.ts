import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { ImapService } from '@/services/imap.service';
import { logger } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get the session
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.email) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    logger.info('Starting cleanup of sent emails from received emails table', {
      requestedBy: session.user.email
    });

    // Run the cleanup
    await ImapService.cleanupSentEmails();

    logger.info('Cleanup completed successfully', {
      requestedBy: session.user.email
    });

    return res.status(200).json({ 
      success: true, 
      message: 'Cleanup completed successfully' 
    });
  } catch (error) {
    logger.error('Error running cleanup', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    return res.status(500).json({ 
      error: 'Failed to run cleanup',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
