import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'GET':
        // Get all lead lists for the organization
        const leadLists = await prisma.leadList.findMany({
          where: { organizationId },
          orderBy: { createdAt: 'desc' },
          include: {
            _count: {
              select: { leads: true },
            },
          },
        });

        return res.status(200).json(leadLists);

      case 'POST':
        // Create a new lead list
        const { name, description, source = 'manual' } = req.body;

        if (!name) {
          return res.status(400).json({ error: 'Name is required' });
        }

        // Check if a list with the same name already exists
        const existingList = await prisma.leadList.findFirst({
          where: {
            name,
            organizationId,
          },
        });

        if (existingList) {
          return res.status(400).json({ error: 'A list with this name already exists' });
        }

        const newList = await prisma.leadList.create({
          data: {
            name,
            description,
            source,
            organization: {
              connect: { id: organizationId }
            }
          },
        });

        return res.status(201).json(newList);

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in lead lists endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
