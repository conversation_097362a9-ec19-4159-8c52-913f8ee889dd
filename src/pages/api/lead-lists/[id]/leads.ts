import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'List ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the list belongs to the user's organization
    const list = await prisma.leadList.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    switch (method) {
      case 'GET':
        // Get all leads in the list
        const leads = await prisma.lead.findMany({
          where: {
            listId: id,
            organizationId,
          },
          orderBy: { createdAt: 'desc' },
        });

        return res.status(200).json(leads);

      case 'POST':
        // Add leads to the list
        const { leadIds } = req.body;

        if (!leadIds || !Array.isArray(leadIds) || leadIds.length === 0) {
          return res.status(400).json({ error: 'Lead IDs are required' });
        }

        // Verify that all leads belong to the user's organization
        const leadsToUpdate = await prisma.lead.findMany({
          where: {
            id: { in: leadIds },
            organizationId,
          },
          select: { id: true },
        });

        if (leadsToUpdate.length !== leadIds.length) {
          return res.status(403).json({ error: 'Some leads do not belong to your organization' });
        }

        // Update the leads to be in this list
        await prisma.lead.updateMany({
          where: {
            id: { in: leadIds },
            organizationId,
          },
          data: {
            listId: id,
          },
        });

        return res.status(200).json({ success: true, count: leadIds.length });

      case 'DELETE':
        // Remove leads from the list
        const { leadIdsToRemove } = req.body;

        if (!leadIdsToRemove || !Array.isArray(leadIdsToRemove) || leadIdsToRemove.length === 0) {
          return res.status(400).json({ error: 'Lead IDs are required' });
        }

        // Verify that all leads belong to the user's organization and this list
        const leadsToRemove = await prisma.lead.findMany({
          where: {
            id: { in: leadIdsToRemove },
            organizationId,
            listId: id,
          },
          select: { id: true },
        });

        if (leadsToRemove.length !== leadIdsToRemove.length) {
          return res.status(403).json({ error: 'Some leads do not belong to your organization or this list' });
        }

        // Update the leads to remove them from this list
        await prisma.lead.updateMany({
          where: {
            id: { in: leadIdsToRemove },
            organizationId,
            listId: id,
          },
          data: {
            listId: null,
          },
        });

        return res.status(200).json({ success: true, count: leadIdsToRemove.length });

      default:
        res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in list leads endpoint', {
      method,
      userId: req.user.id,
      listId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
