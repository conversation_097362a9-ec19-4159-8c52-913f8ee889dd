import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'List ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the list belongs to the user's organization
    const list = await prisma.leadList.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    switch (method) {
      case 'GET':
        // Get the list with lead count
        const listWithCount = await prisma.leadList.findUnique({
          where: { id },
          include: {
            _count: {
              select: { leads: true },
            },
          },
        });

        return res.status(200).json(listWithCount);

      case 'PUT':
        const { name, description } = req.body;

        if (name) {
          // Check if another list with the same name exists
          const existingList = await prisma.leadList.findFirst({
            where: {
              name,
              organizationId,
              id: { not: id },
            },
          });

          if (existingList) {
            return res.status(400).json({ error: 'Another list with this name already exists' });
          }
        }

        const updatedList = await prisma.leadList.update({
          where: { id },
          data: {
            name: name || undefined,
            description: description !== undefined ? description : undefined,
          },
        });

        return res.status(200).json(updatedList);

      case 'DELETE':
        // Check if the list has leads
        const leadCount = await prisma.lead.count({
          where: { listId: id },
        });

        if (leadCount > 0) {
          // Remove the list association from leads
          await prisma.lead.updateMany({
            where: { listId: id },
            data: { listId: null },
          });
        }

        await prisma.leadList.delete({
          where: { id },
        });

        return res.status(204).end();

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in lead list endpoint', {
      method,
      userId: req.user.id,
      listId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
