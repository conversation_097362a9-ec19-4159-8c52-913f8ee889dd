import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { sendEmail } from '@/lib/email';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  try {
    const {
      emailAccountId,
      subject,
      content,
      templateId,
      testEmail
    } = req.body;

    if (!testEmail) {
      return res.status(400).json({ error: 'Test email address is required' });
    }

    if (!emailAccountId) {
      return res.status(400).json({ error: 'Email account is required' });
    }

    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Get the email account
    const emailAccount = await prisma.emailAccount.findFirst({
      where: {
        id: emailAccountId,
        userId: req.user.id,
      },
    });

    if (!emailAccount) {
      return res.status(404).json({ error: 'Email account not found' });
    }

    // Get the template if provided
    let emailContent = content;
    let emailSubject = subject;

    if (templateId) {
      const template = await prisma.emailTemplate.findFirst({
        where: {
          id: templateId,
          organizationId,
        },
      });

      if (!template) {
        return res.status(404).json({ error: 'Template not found' });
      }

      emailContent = template.content;
      emailSubject = template.subject;
    }

    // Send the test email
    await sendEmail({
      from: emailAccount.email,
      to: testEmail,
      subject: emailSubject,
      html: emailContent,
      emailAccountId: emailAccount.id,
    });

    return res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error sending test email', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Failed to send test email' });
  }
}, {
  requireAuth: true,
});
