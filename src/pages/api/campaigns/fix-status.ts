import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'POST':
        // Get all campaigns for the organization
        const campaigns = await prisma.campaign.findMany({
          where: {
            organizationId
          },
          include: {
            leads: true,
          },
        });

        const updatedCampaigns = [];

        // Fix campaign statuses
        for (const campaign of campaigns) {
          // If all leads are processed and there are no pending leads, mark as completed
          if (campaign.sentCount > 0 && campaign.pendingCount === 0) {
            const updatedCampaign = await prisma.campaign.update({
              where: { id: campaign.id },
              data: {
                status: 'completed',
              },
            });
            updatedCampaigns.push(updatedCampaign);
          }
        }

        return res.status(200).json({ 
          message: 'Campaign statuses fixed', 
          updatedCampaigns 
        });

      default:
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error fixing campaign statuses', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.user.id,
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
