import { NextApiRequest, NextApiResponse } from 'next';
import { SequenceCampaignService } from '@/services/sequence-campaign.service';
import { logger } from '@/lib/logger';
import { apiHandler } from '@/lib/apiHandler';
import { prisma } from '@/lib/prisma';

/**
 * API route for manually checking sequence campaign conditions
 * This endpoint can be called to check conditions for a specific campaign
 */
export default apiHandler(async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { campaignId } = req.query;

    if (!campaignId || typeof campaignId !== 'string') {
      return res.status(400).json({ error: 'Campaign ID is required' });
    }

    // Get the campaign with all necessary data
    const campaign = await prisma.campaign.findUnique({
      where: {
        id: campaignId,
        type: 'sequence',
      },
      include: {
        steps: {
          orderBy: {
            position: 'asc',
          },
          include: {
            emailAccount: true,
            emailAccounts: {
              include: {
                emailAccount: true
              }
            },
            conditionStep: true,
            conditionedSteps: true,
          },
        },
        leads: {
          include: {
            lead: true,
            currentStep: true,
            stepActivities: {
              orderBy: {
                createdAt: 'desc',
              },
              include: {
                step: true,
              }
            },
          },
        },
      },
    });

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }

    logger.info(`[SEQUENCE_DEBUG] Manually checking conditions for campaign ${campaignId}`);

    // If the campaign is completed, reactivate it first
    if (campaign.status === 'completed') {
      logger.info(`[SEQUENCE_DEBUG] Reactivating completed campaign ${campaignId} to process conditions`);

      // Reactivate the campaign
      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          status: 'active',
          endDate: null // Clear the end date
        }
      });

      // Also reactivate any leads that were marked as completed but didn't actually finish the sequence
      const lastStep = campaign.steps.sort((a, b) => b.position - a.position)[0];

      // Find leads that were marked as completed but aren't on the last step
      const leadsToReactivate = campaign.leads.filter(lead =>
        lead.status === 'completed' &&
        lead.currentStepId !== lastStep.id
      );

      if (leadsToReactivate.length > 0) {
        logger.info(`[SEQUENCE_DEBUG] Reactivating ${leadsToReactivate.length} leads that didn't complete the full sequence`);

        // Reactivate each lead
        for (const lead of leadsToReactivate) {
          await prisma.campaignLead.update({
            where: { id: lead.id },
            data: {
              status: 'active',
              completedAt: null
            }
          });

          // Add a reactivation activity
          await prisma.stepActivity.create({
            data: {
              campaignLeadId: lead.id,
              stepId: lead.currentStepId,
              type: 'lead_reactivated',
              status: 'active',
              metadata: {
                reason: 'manual_reactivation',
                timestamp: new Date().toISOString()
              }
            }
          });
        }
      }
    }

    // Process the campaign
    await SequenceCampaignService.processSequenceCampaign(campaign);

    // Get updated campaign data
    const updatedCampaign = await prisma.campaign.findUnique({
      where: { id: campaignId },
      include: {
        leads: {
          include: {
            lead: {
              select: {
                email: true,
              }
            },
            currentStep: {
              select: {
                position: true,
                type: true,
                name: true,
              }
            },
            stepActivities: {
              orderBy: {
                createdAt: 'desc',
              },
              take: 5,
              select: {
                type: true,
                status: true,
                createdAt: true,
                step: {
                  select: {
                    position: true,
                    type: true,
                    name: true,
                  }
                }
              }
            }
          }
        }
      }
    });

    // Prepare a summary of lead progress
    const leadProgress = updatedCampaign?.leads.map(lead => ({
      id: lead.id,
      email: lead.lead.email,
      status: lead.status,
      currentStep: lead.currentStep ? {
        position: lead.currentStep.position,
        type: lead.currentStep.type,
        name: lead.currentStep.name,
      } : null,
      recentActivities: lead.stepActivities.map(activity => ({
        type: activity.type,
        status: activity.status,
        timestamp: activity.createdAt,
        step: activity.step ? {
          position: activity.step.position,
          type: activity.step.type,
          name: activity.step.name,
        } : null,
      })),
    }));

    return res.status(200).json({
      success: true,
      message: 'Sequence campaign conditions checked successfully',
      leadProgress,
    });
  } catch (error) {
    logger.error('[SEQUENCE_DEBUG] Error checking sequence campaign conditions', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });

    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}, {
  rateLimit: true
});
