import { NextApiRequest, NextApiResponse } from 'next';
import { SequenceCampaignService } from '@/services/sequence-campaign.service';
import { logger } from '@/lib/logger';
import { apiHandler } from '@/lib/apiHandler';

/**
 * API route for processing sequence campaigns
 * This endpoint can be called by cron jobs or manually
 */
export default apiHandler(async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    // Verify cron job secret if provided
    const cronSecret = req.headers['x-cron-secret'];
    if (process.env.CRON_SECRET && cronSecret !== process.env.CRON_SECRET) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    logger.info('[SEQUENCE_DEBUG] Starting sequence campaign processor via API', {
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel'
    });

    // Process sequence campaigns
    await SequenceCampaignService.processActiveSequenceCampaigns();

    logger.info('[SEQUENCE_DEBUG] Sequence campaign processor completed via API');

    return res.status(200).json({
      success: true,
      timestamp: new Date().toISOString(),
      message: 'Sequence campaign processor completed successfully'
    });
  } catch (error) {
    logger.error('[SEQUENCE_DEBUG] Error in sequence campaign processor', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });

    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}, {
  rateLimit: true
});
