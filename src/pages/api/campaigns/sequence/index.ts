import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'GET':
        // Get all sequence campaigns for the organization
        const campaigns = await prisma.campaign.findMany({
          where: {
            organizationId,
            type: 'sequence'
          },
          include: {
            steps: {
              orderBy: {
                position: 'asc'
              }
            }
          },
          orderBy: { createdAt: 'desc' },
        });

        return res.status(200).json(campaigns);

      case 'POST':
        // Create a new sequence campaign
        const { name, description, agentId, steps, status, scheduledFor, recipientType, leadIds, listId } = req.body;

        // Log the request body for debugging
        logger.info('Creating sequence campaign with data:', {
          name,
          recipientType,
          stepsCount: steps?.length,
          leadIds: leadIds?.length,
          listId
        });

        if (!name || name.trim() === '') {
          logger.error('Campaign name validation failed', { name });
          return res.status(400).json({ error: 'Campaign name is required' });
        }

        if (!steps || !Array.isArray(steps) || steps.length === 0) {
          return res.status(400).json({ error: 'At least one step is required' });
        }

        // Validate recipient selection
        if (recipientType === 'leads' && (!leadIds || !Array.isArray(leadIds) || leadIds.length === 0)) {
          return res.status(400).json({ error: 'At least one recipient is required' });
        }

        if (recipientType === 'list' && !listId) {
          return res.status(400).json({ error: 'Lead list is required' });
        }

        // Determine campaign status
        const campaignStatus = status || 'draft';

        // Create the campaign
        const campaign = await prisma.campaign.create({
          data: {
            name,
            description,
            type: 'sequence',
            status: campaignStatus,
            userId: req.user.id,
            organizationId,
            agentId: agentId || undefined,
            startDate: scheduledFor ? new Date(scheduledFor) : (campaignStatus === 'active' ? new Date() : null),
            // Save the recipient type and list ID
            recipientType: recipientType || 'individual',
            listId: recipientType === 'list' ? listId : null,
          },
        });

        // Log campaign creation details
        logger.info(`Creating sequence campaign: ${name}`, {
          campaignId: campaign.id,
          status: campaignStatus,
          scheduledFor: scheduledFor || 'immediate',
          steps: steps.length
        });

        // Process steps and handle templates
        const stepsData = await Promise.all(steps.map(async (step: any) => {
          // If the step uses a template, get the template content
          let stepContent = step.content;
          let stepSubject = step.subject;

          if (step.type === 'email' && step.templateId) {
            const template = await prisma.emailTemplate.findUnique({
              where: { id: step.templateId },
            });

            if (template) {
              stepContent = template.content;
              // Use the provided subject or the template subject if not provided
              if (!step.subject) {
                stepSubject = template.subject;
              }
            }
          }

          return {
            campaignId: campaign.id,
            name: step.name || `Step ${step.position + 1}`,
            description: step.description,
            type: step.type,
            position: step.position,
            emailAccountId: step.emailAccountId || null,
            subject: stepSubject || null,
            content: stepContent || null,
            templateId: step.templateId || null,
            waitDuration: step.waitDuration || null,
            waitUntil: step.waitUntil ? new Date(step.waitUntil) : null,
            conditionType: step.conditionType || null,
            // Don't set conditionStepId yet - we'll update it in a second pass
            conditionStepId: null,
            conditionTimeframe: step.conditionTimeframe || null,
            actionType: step.actionType || null,
            actionValue: step.actionValue || null,
            useMultipleSenders: step.useMultipleSenders || false,
            // Store the original step ID for reference
            originalStepId: step.id || null,
            // Store the referenced step position for later update
            conditionStepPosition: step.type === 'condition' ? step.conditionStepId : null,
          };
        }));

        // Create the steps first (without condition references)
        const createdSteps = [];
        const stepIdMap = new Map(); // Map to store original step ID to created step ID

        for (const stepData of stepsData) {
          // Remove the temporary fields before creating the step
          const { originalStepId, conditionStepPosition, ...dataToCreate } = stepData;

          const step = await prisma.campaignStep.create({
            data: dataToCreate,
          });
          createdSteps.push(step);

          // Store the mapping between original step ID/position and created step ID
          if (originalStepId) {
            stepIdMap.set(originalStepId, step.id);
          }
          stepIdMap.set(`position_${stepData.position}`, step.id);

          // If the step uses multiple senders, create the email account associations
          if (step.useMultipleSenders && steps[step.position].emailAccounts) {
            const emailAccounts = steps[step.position].emailAccounts || [];

            // Create the email account associations
            await Promise.all(emailAccounts.map(async (account: any) => {
              await prisma.campaignStepEmailAccount.create({
                data: {
                  campaignStepId: step.id,
                  emailAccountId: account.id,
                  weight: account.weight || 1,
                }
              });
            }));
          }
        }

        // Second pass: Update condition steps with the correct conditionStepId
        for (let i = 0; i < steps.length; i++) {
          const originalStep = steps[i];
          const createdStep = createdSteps[i];

          if (originalStep.type === 'condition' && originalStep.conditionStepId) {
            // Find the referenced step ID using either the original ID or position
            let referencedStepId;

            if (stepIdMap.has(originalStep.conditionStepId)) {
              // If we have a direct mapping for the ID
              referencedStepId = stepIdMap.get(originalStep.conditionStepId);
            } else if (typeof originalStep.conditionStepId === 'number') {
              // If it's a position reference
              referencedStepId = stepIdMap.get(`position_${originalStep.conditionStepId}`);
            }

            if (referencedStepId) {
              // Update the step with the correct conditionStepId
              await prisma.campaignStep.update({
                where: { id: createdStep.id },
                data: { conditionStepId: referencedStepId }
              });

              // Update the local object as well
              createdStep.conditionStepId = referencedStepId;
            } else {
              logger.warn(`Could not find referenced step for condition in step ${createdStep.id}`);
            }
          }
        }

        // Get the created campaign with steps
        const createdCampaign = await prisma.campaign.findUnique({
          where: { id: campaign.id },
          include: {
            steps: {
              orderBy: {
                position: 'asc'
              }
            }
          },
        });

        // Get campaign leads based on recipient type
        let campaignLeadIds: string[] = [];

        if (recipientType === 'leads' && Array.isArray(leadIds)) {
          campaignLeadIds = leadIds;
        } else if (recipientType === 'list' && listId) {
          // Get all leads in the list
          const listLeads = await prisma.lead.findMany({
            where: {
              listId,
              organizationId,
            },
            select: { id: true },
          });

          if (listLeads.length === 0) {
            return res.status(400).json({ error: 'The selected list has no leads' });
          }

          campaignLeadIds = listLeads.map(lead => lead.id);
        }

        // Get the first email step to use as the initial step
        const firstEmailStep = createdSteps.find(step => step.type === 'email');

        if (!firstEmailStep) {
          return res.status(400).json({ error: 'Campaign must have at least one email step' });
        }

        // Add leads to the campaign
        if (campaignLeadIds.length > 0) {
          await Promise.all(
            campaignLeadIds.map(async (leadId: string) => {
              return prisma.campaignLead.create({
                data: {
                  campaignId: campaign.id,
                  leadId,
                  currentStepId: firstEmailStep.id,
                },
              });
            })
          );

          logger.info(`Added ${campaignLeadIds.length} leads to campaign ${campaign.id}`);
        }

        // If the campaign is active, process it immediately in the background
        if (campaignStatus === 'active') {
          // Import the campaign service dynamically to avoid circular dependencies
          const { CampaignService } = await import('@/services/campaign.service');

          // Process the campaign in the background to avoid blocking the response
          setTimeout(async () => {
            try {
              logger.info(`[CAMPAIGN_DEBUG] Starting background processing for new sequence campaign ${campaign.id}`);
              await CampaignService.processActivatedCampaign(campaign.id);
              logger.info(`[CAMPAIGN_DEBUG] Background processing completed for new sequence campaign ${campaign.id}`);
            } catch (bgError) {
              logger.error(`[CAMPAIGN_DEBUG] Error in background processing for new sequence campaign ${campaign.id}`, {
                error: bgError instanceof Error ? bgError.message : 'Unknown error',
                stack: bgError instanceof Error ? bgError.stack : 'No stack trace',
                campaignId: campaign.id,
                timestamp: new Date().toISOString()
              });
            }
          }, 100); // Small delay to ensure the response is sent first

          logger.info(`[CAMPAIGN_DEBUG] Background processing initiated for new sequence campaign ${campaign.id}`);
        }

        return res.status(201).json(createdCampaign);

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    // Log detailed error information
    logger.error('Error in sequence campaigns endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      timestamp: new Date().toISOString()
    });

    // Check for specific error types
    if (error instanceof Error) {
      // Check for Prisma foreign key constraint errors
      if (error.message.includes('Foreign key constraint')) {
        if (error.message.includes('CampaignStep_conditionStepId_fkey')) {
          return res.status(400).json({
            error: 'Invalid condition step reference. Make sure all condition steps reference valid email steps.'
          });
        }
      }

      // Return the actual error message in development
      if (process.env.NODE_ENV === 'development') {
        return res.status(500).json({
          error: 'Server error: ' + error.message,
          stack: error.stack
        });
      }
    }

    // Generic error for production
    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
