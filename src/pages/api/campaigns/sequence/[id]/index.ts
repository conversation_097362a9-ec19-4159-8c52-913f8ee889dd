import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Campaign ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user || !user.organizations || user.organizations.length === 0) {
      return res.status(400).json({ error: 'User has no organization' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the campaign belongs to the user's organization
    const campaign = await prisma.campaign.findFirst({
      where: {
        id,
        organizationId,
        type: 'sequence',
      },
      include: {
        steps: {
          include: {
            emailAccount: true,
            template: true,
            emailAccounts: {
              include: {
                emailAccount: true
              }
            },
            // Include activities to get real-time step progress
            activities: {
              select: {
                id: true,
                status: true,
                sentAt: true,
                openedAt: true,
                clickedAt: true,
                repliedAt: true,
                bouncedAt: true,
                error: true,
                campaignLeadId: true,
              }
            },
            // Include condition step references
            conditionStep: true,
            conditionedSteps: true,
          },
          orderBy: {
            position: 'asc',
          },
        },
        leads: {
          include: {
            lead: true,
            stepActivities: {
              include: {
                step: true,
              },
              orderBy: {
                createdAt: 'desc',
              },
            },
            // Include current step to track progress
            currentStep: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }

    switch (method) {
      case 'GET':
        // Calculate analytics
        const totalLeads = campaign.leads.length;

        // Count emails sent, opened, clicked, replied, bounced
        let sentCount = 0;
        let openCount = 0;
        let clickCount = 0;
        let replyCount = 0;
        let bounceCount = 0;
        let unsubscribeCount = 0;

        // Count leads by status
        const leadsByStatus = {
          active: 0,
          completed: 0,
          unsubscribed: 0,
          bounced: 0,
        };

        // Track step completion - using actual step activities from the database
        const stepCompletion: Record<string, { total: number; completed: number; pending: number; failed: number }> = {};

        // Initialize step completion tracking with real data from the database
        campaign.steps.forEach(step => {
          // Initialize counters for this step
          let completed = 0;
          let pending = 0;
          let failed = 0;
          let total = 0;

          // Count leads at each step
          campaign.leads.forEach(lead => {
            // Check if this lead is currently at this step
            const isCurrentStep = lead.currentStepId === step.id;

            // Check if this lead has completed this step
            const hasCompletedStep = lead.stepActivities.some(activity =>
              activity.stepId === step.id &&
              (activity.status === 'completed' ||
               activity.type === 'step_completed' ||
               (step.type === 'email' && (activity.status === 'sent' || activity.type === 'email_sent')))
            );

            // Check if this lead has failed this step
            const hasFailedStep = lead.stepActivities.some(activity =>
              activity.stepId === step.id &&
              (activity.status === 'failed' || activity.status === 'error' || activity.type === 'step_failed')
            );

            // Count based on status
            if (hasCompletedStep) {
              completed++;
              total++;
            } else if (hasFailedStep) {
              failed++;
              total++;
            } else if (isCurrentStep) {
              pending++;
              total++;
            }
          });

          stepCompletion[step.id] = {
            total,
            completed,
            pending,
            failed,
          };
        });

        // Process lead data
        campaign.leads.forEach(lead => {
          // Count by status
          if (lead.status in leadsByStatus) {
            leadsByStatus[lead.status as keyof typeof leadsByStatus]++;
          }

          // Process step activities for email metrics
          const processedSteps = new Set<string>(); // Track processed steps to avoid double counting

          lead.stepActivities.forEach(activity => {
            // Only count each step once for sent emails
            if (activity.type === 'email_sent' || activity.status === 'sent') {
              if (!processedSteps.has(`${activity.stepId}_sent`)) {
                sentCount++;
                processedSteps.add(`${activity.stepId}_sent`);
              }
            }

            // Count other metrics
            if (activity.type === 'email_opened' || activity.status === 'opened') {
              if (!processedSteps.has(`${activity.stepId}_opened`)) {
                openCount++;
                processedSteps.add(`${activity.stepId}_opened`);
              }
            }

            if (activity.type === 'link_clicked' || activity.status === 'clicked') {
              if (!processedSteps.has(`${activity.stepId}_clicked`)) {
                clickCount++;
                processedSteps.add(`${activity.stepId}_clicked`);
              }
            }

            if (activity.type === 'email_replied' || activity.status === 'replied') {
              if (!processedSteps.has(`${activity.stepId}_replied`)) {
                replyCount++;
                processedSteps.add(`${activity.stepId}_replied`);
              }
            }

            if (activity.type === 'email_bounced' || activity.status === 'bounced') {
              if (!processedSteps.has(`${activity.stepId}_bounced`)) {
                bounceCount++;
                processedSteps.add(`${activity.stepId}_bounced`);
              }
            }
          });

          // Count unsubscribes
          if (lead.status === 'unsubscribed') {
            unsubscribeCount++;
          }
        });

        // Calculate rates
        const openRate = sentCount > 0 ? (openCount / sentCount) * 100 : 0;
        const clickRate = sentCount > 0 ? (clickCount / sentCount) * 100 : 0;
        const replyRate = sentCount > 0 ? (replyCount / sentCount) * 100 : 0;
        const bounceRate = sentCount > 0 ? (bounceCount / sentCount) * 100 : 0;
        const unsubscribeRate = sentCount > 0 ? (unsubscribeCount / sentCount) * 100 : 0;

        // Add analytics to the campaign
        const campaignWithAnalytics = {
          ...campaign,
          analytics: {
            totalLeads,
            sentCount,
            openCount,
            clickCount,
            replyCount,
            bounceCount,
            unsubscribeCount,
            openRate,
            clickRate,
            replyRate,
            bounceRate,
            unsubscribeRate,
            leadsByStatus,
            stepCompletion,
          },
        };

        return res.status(200).json(campaignWithAnalytics);

      case 'PUT':
        // Update the campaign
        const { status, name, description } = req.body;

        const updatedCampaign = await prisma.campaign.update({
          where: { id },
          data: {
            status: status || undefined,
            name: name || undefined,
            description: description || undefined,
            ...(status === 'active' && !campaign.startDate ? { startDate: new Date() } : {}),
            ...(status === 'completed' && !campaign.endDate ? { endDate: new Date() } : {}),
          },
        });

        return res.status(200).json(updatedCampaign);

      case 'DELETE':
        // Delete the campaign
        // First delete related records
        // Delete step activities (using the correct model name from the schema)
        await prisma.stepActivity.deleteMany({
          where: {
            step: {
              campaignId: id,
            },
          },
        });

        await prisma.campaignLead.deleteMany({
          where: { campaignId: id },
        });

        await prisma.campaignStepEmailAccount.deleteMany({
          where: {
            campaignStep: {
              campaignId: id,
            },
          },
        });

        await prisma.campaignStep.deleteMany({
          where: { campaignId: id },
        });

        await prisma.campaign.delete({
          where: { id },
        });

        return res.status(200).json({ success: true });

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in sequence campaign detail endpoint', {
      method,
      userId: req.user.id,
      campaignId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
