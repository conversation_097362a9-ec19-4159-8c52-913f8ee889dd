import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'GET':
        // Get all campaigns for the organization
        const { timeRange, status, type } = req.query;

        // Build the where clause
        const where: any = {
          userId: req.user.id,
          organizationId
        };

        if (status) {
          where.status = status;
        }

        if (type) {
          where.type = type;
        }

        if (timeRange && timeRange !== 'all') {
          const now = new Date();
          let startDate = new Date();

          switch (timeRange) {
            case '7d':
              startDate.setDate(now.getDate() - 7);
              break;
            case '30d':
              startDate.setDate(now.getDate() - 30);
              break;
            case '90d':
              startDate.setDate(now.getDate() - 90);
              break;
          }

          where.createdAt = {
            gte: startDate,
          };
        }

        const campaigns = await prisma.campaign.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          include: {
            steps: {
              orderBy: {
                position: 'asc',
              },
            },
            leads: true,
          },
        });

        // Transform the campaigns to include totalRecipients
        const transformedCampaigns = campaigns.map(campaign => ({
          ...campaign,
          totalRecipients: campaign.leads.length,
          // Remove the leads array to reduce payload size
          leads: undefined
        }));

        return res.status(200).json(transformedCampaigns);

      case 'POST':
        // Create a new campaign
        const {
          name,
          emailAccountId,
          subject,
          content,
          templateId,
          leadIds,
          listId,
          recipientType,
          scheduledFor,
          type: campaignType,
          useMultipleSenders,
          emailAccounts,
          agentId
        } = req.body;

        if (!name) {
          return res.status(400).json({ error: 'Campaign name is required' });
        }

        // Check email account configuration
        if (!useMultipleSenders && !emailAccountId) {
          return res.status(400).json({ error: 'Email account is required' });
        }

        if (useMultipleSenders && (!emailAccounts || !Array.isArray(emailAccounts) || emailAccounts.length === 0)) {
          return res.status(400).json({ error: 'At least one email account is required when using multiple senders' });
        }

        if (!subject) {
          return res.status(400).json({ error: 'Subject is required' });
        }

        // Content is required unless an AI agent is selected
        if (!content && !templateId && !agentId) {
          return res.status(400).json({ error: 'Either content, template, or AI agent is required' });
        }

        // Check recipients based on recipient type
        if (recipientType === 'leads' && (!leadIds || !Array.isArray(leadIds) || leadIds.length === 0)) {
          return res.status(400).json({ error: 'At least one recipient is required' });
        }

        if (recipientType === 'list' && !listId) {
          return res.status(400).json({ error: 'Lead list is required' });
        }

        // Get leads from list if using a list
        let campaignLeadIds = leadIds || [];

        if (recipientType === 'list' && listId) {
          // Verify the list belongs to the organization
          const list = await prisma.leadList.findFirst({
            where: {
              id: listId,
              organizationId,
            },
          });

          if (!list) {
            return res.status(404).json({ error: 'Lead list not found' });
          }

          // Get all leads in the list
          const listLeads = await prisma.lead.findMany({
            where: {
              listId,
              organizationId,
            },
            select: { id: true },
          });

          if (listLeads.length === 0) {
            return res.status(400).json({ error: 'The selected list has no leads' });
          }

          campaignLeadIds = listLeads.map(lead => lead.id);
        }

        // If using a template, get the template content
        let emailContent = content;
        let emailSubject = subject;

        if (templateId) {
          const template = await prisma.emailTemplate.findUnique({
            where: { id: templateId },
          });

          if (!template) {
            return res.status(404).json({ error: 'Template not found' });
          }

          emailContent = template.content;
          // Use the provided subject or the template subject if not overridden
          if (!subject) {
            emailSubject = template.subject;
          }
        }

        // Create the campaign
        const campaign = await prisma.campaign.create({
          data: {
            name,
            type: campaignType || 'standard',
            status: req.body.status || (scheduledFor ? 'scheduled' : 'draft'), // Use the provided status or default based on schedule
            userId: req.user.id,
            organizationId,
            startDate: scheduledFor ? new Date(scheduledFor) : (req.body.status === 'active' ? new Date() : null),
            // Save the recipient type and list ID
            recipientType: recipientType || 'individual',
            listId: recipientType === 'list' ? listId : null,
            agentId: agentId || null, // Include the AI agent ID if provided
          },
        });

        // Create the first step (email)
        const step = await prisma.campaignStep.create({
          data: {
            campaignId: campaign.id,
            name: 'Initial Email',
            type: 'email',
            position: 0,
            emailAccountId: useMultipleSenders ? null : emailAccountId,
            subject: emailSubject,
            content: emailContent,
            useMultipleSenders: useMultipleSenders || false,
            ...(agentId ? { agentId } : {}), // Include the AI agent ID if provided
          },
        });

        // If using multiple senders, create the email account associations
        if (useMultipleSenders && Array.isArray(emailAccounts) && emailAccounts.length > 0) {
          await Promise.all(emailAccounts.map(async (account: any) => {
            await prisma.campaignStepEmailAccount.create({
              data: {
                campaignStepId: step.id,
                emailAccountId: account.id,
                weight: account.weight || 1,
              }
            });
          }));
        }

        // Add leads to the campaign
        const campaignLeads = await Promise.all(
          campaignLeadIds.map(async (leadId: string) => {
            return prisma.campaignLead.create({
              data: {
                campaignId: campaign.id,
                leadId,
                currentStepId: step.id,
              },
            });
          })
        );

        // If the campaign is active, process it immediately in the background
        if (req.body.status === 'active') {
          // Import the campaign service dynamically to avoid circular dependencies
          const { CampaignService } = await import('@/services/campaign.service');

          // Process the campaign in the background to avoid blocking the response
          setTimeout(async () => {
            try {
              logger.info(`[CAMPAIGN_DEBUG] Starting background processing for new campaign ${campaign.id}`);
              await CampaignService.processActivatedCampaign(campaign.id);
              logger.info(`[CAMPAIGN_DEBUG] Background processing completed for new campaign ${campaign.id}`);
            } catch (bgError) {
              logger.error(`[CAMPAIGN_DEBUG] Error in background processing for new campaign ${campaign.id}`, {
                error: bgError instanceof Error ? bgError.message : 'Unknown error',
                stack: bgError instanceof Error ? bgError.stack : 'No stack trace',
                campaignId: campaign.id,
                timestamp: new Date().toISOString()
              });
            }
          }, 100); // Small delay to ensure the response is sent first

          logger.info(`[CAMPAIGN_DEBUG] Background processing initiated for new campaign ${campaign.id}`);
        }

        return res.status(201).json({
          ...campaign,
          step,
          leadCount: campaignLeads.length,
        });

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in campaigns endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
