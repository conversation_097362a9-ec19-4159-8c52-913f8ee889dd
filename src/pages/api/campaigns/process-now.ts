import { ExtendedNextApiRequest } from '@/lib/apiHandler';
import { CampaignService } from '@/services/campaign.service';
import { logger } from '@/lib/logger';
import { apiHandler } from '@/lib/apiHandler';
import { checkQueueHealth } from '@/services/queue.service';

/**
 * API route for manually processing active campaigns
 * This endpoint can be called from the UI to immediately process campaigns
 */
export default apiHandler(async (req: ExtendedNextApiRequest, res) => {
  try {
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get the campaign ID from the request body
    const { campaignId } = req.body;

    logger.info('[CAMPAIGN_DEBUG] Manual campaign processing request', {
      campaignId: campaignId || 'all',
      userId: req.user?.id,
      timestamp: new Date().toISOString(),
      source: 'manual-api'
    });

    // First, check if the queue is healthy
    const queueHealth = await checkQueueHealth();
    logger.info(`[CAMPAIGN_DEBUG] Queue health check before processing:`, queueHealth);

    // If the queue is not healthy, log a warning but still try to process
    if (!queueHealth.healthy) {
      logger.warn(`[CAMPAIGN_DEBUG] Queue health check failed, but will attempt to process anyway`, {
        queueHealth,
        campaignId: campaignId || 'all',
        timestamp: new Date().toISOString()
      });
    }

    // Process campaigns directly - we'll use a different approach to handle timeouts
    if (campaignId) {
      // Process a specific campaign
      logger.info(`[CAMPAIGN_DEBUG] Processing specific campaign ${campaignId}`);

      try {
        // Set environment variable to indicate manual processing
        // This will be used to reduce the delay between emails
        process.env.MANUAL_PROCESSING = 'true';

        // Start the processing but don't await it
        // Instead, we'll return a response immediately while processing continues
        const processingPromise = CampaignService.processActivatedCampaign(campaignId);

        // Reset the environment variable after processing starts
        processingPromise.finally(() => {
          process.env.MANUAL_PROCESSING = 'false';

          // Check queue health after processing to see if jobs were added
          checkQueueHealth().then(healthAfter => {
            logger.info(`[CAMPAIGN_DEBUG] Queue health check after processing:`, {
              ...healthAfter,
              campaignId,
              timestamp: new Date().toISOString()
            });
          }).catch(healthError => {
            logger.error(`[CAMPAIGN_DEBUG] Error checking queue health after processing:`, {
              error: healthError instanceof Error ? healthError.message : 'Unknown error',
              campaignId,
              timestamp: new Date().toISOString()
            });
          });
        });

        // Fire and forget - the processing will continue even after we return
        processingPromise.catch(error => {
          logger.error(`[CAMPAIGN_DEBUG] Error processing campaign ${campaignId}`, {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : 'No stack trace',
            campaignId,
            timestamp: new Date().toISOString()
          });
        });

        logger.info(`[CAMPAIGN_DEBUG] Campaign processing initiated for ${campaignId}`);
      } catch (error) {
        logger.error(`[CAMPAIGN_DEBUG] Error initiating campaign processing for ${campaignId}`, {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : 'No stack trace',
          campaignId,
          timestamp: new Date().toISOString()
        });

        // Don't throw - we still want to return a success response
        // The user can try again if needed
      }
    } else {
      // Process all active campaigns
      logger.info('[CAMPAIGN_DEBUG] Processing all active campaigns');

      try {
        // Set environment variable to indicate manual processing
        // This will be used to reduce the delay between emails
        process.env.MANUAL_PROCESSING = 'true';

        // Start the processing but don't await it
        const processingPromise = CampaignService.processActiveCampaigns();

        // Reset the environment variable after processing starts
        processingPromise.finally(() => {
          process.env.MANUAL_PROCESSING = 'false';
        });

        // Fire and forget - the processing will continue even after we return
        processingPromise.catch(error => {
          logger.error('[CAMPAIGN_DEBUG] Error processing all active campaigns', {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : 'No stack trace',
            timestamp: new Date().toISOString()
          });
        });

        logger.info('[CAMPAIGN_DEBUG] All active campaigns processing initiated');
      } catch (error) {
        logger.error('[CAMPAIGN_DEBUG] Error initiating processing for all active campaigns', {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : 'No stack trace',
          timestamp: new Date().toISOString()
        });

        // Don't throw - we still want to return a success response
      }
    }

    return res.status(200).json({
      success: true,
      message: campaignId
        ? `Campaign ${campaignId} processing initiated`
        : 'All active campaigns processing initiated',
      queueHealth,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('[CAMPAIGN_DEBUG] Error in manual campaign processing', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });

    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}, {
  requireAuth: true
});
