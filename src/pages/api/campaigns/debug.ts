import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'GET':
        // Get all campaigns for the organization without any filters
        const campaigns = await prisma.campaign.findMany({
          where: {
            organizationId
          },
          orderBy: { createdAt: 'desc' },
          include: {
            steps: {
              orderBy: {
                position: 'asc',
              },
            },
            leads: true,
          },
        });

        // Transform the campaigns to include totalRecipients and detailed status
        const transformedCampaigns = campaigns.map(campaign => ({
          ...campaign,
          totalRecipients: campaign.leads.length,
          // Remove the leads array to reduce payload size
          leads: undefined,
          // Add detailed metrics
          metrics: {
            sentCount: campaign.sentCount,
            openedCount: campaign.openedCount,
            clickedCount: campaign.clickedCount,
            repliedCount: campaign.repliedCount,
            pendingCount: campaign.pendingCount,
            errorCount: campaign.errorCount,
          }
        }));

        return res.status(200).json(transformedCampaigns);

      default:
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in campaigns debug endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.user.id,
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
