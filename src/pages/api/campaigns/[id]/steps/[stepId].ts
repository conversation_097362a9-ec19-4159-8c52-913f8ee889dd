import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id, stepId } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string' || !stepId || typeof stepId !== 'string') {
    return res.status(400).json({ error: 'Campaign ID and Step ID are required' });
  }

  try {
    // Check if the campaign exists and belongs to the user
    const campaign = await prisma.campaign.findFirst({
      where: {
        id,
        userId: req.user.id,
      },
      include: {
        steps: {
          where: {
            id: stepId,
          },
        },
      },
    });

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }

    if (campaign.steps.length === 0) {
      return res.status(404).json({ error: 'Step not found' });
    }

    switch (method) {
      case 'GET':
        return res.status(200).json(campaign.steps[0]);

      case 'PUT':
        const {
          name,
          subject,
          content,
          emailAccountId,
          useMultipleSenders,
          emailAccountIds
        } = req.body;

        // Update the step
        const updatedStep = await prisma.campaignStep.update({
          where: { id: stepId },
          data: {
            name: name || undefined,
            subject: subject || undefined,
            content: content || undefined,
            emailAccountId: useMultipleSenders ? null : emailAccountId || undefined,
            useMultipleSenders: useMultipleSenders !== undefined ? useMultipleSenders : undefined,
          },
        });

        // If using multiple senders, update the email accounts for this step
        if (useMultipleSenders && emailAccountIds && Array.isArray(emailAccountIds)) {
          // First, remove all existing email accounts for this step
          await prisma.campaignStepEmailAccount.deleteMany({
            where: { campaignStepId: stepId },
          });

          // Then, add the new email accounts
          const emailAccountsOnStep = await Promise.all(
            emailAccountIds.map((emailAccount: any) => {
              const emailAccountId = typeof emailAccount === 'string' ? emailAccount : emailAccount.id;
              const weight = typeof emailAccount === 'string' ? 1 : (emailAccount.weight || 1);

              return prisma.campaignStepEmailAccount.create({
                data: {
                  campaignStepId: stepId,
                  emailAccountId,
                  weight,
                },
              });
            })
          );

          return res.status(200).json({
            ...updatedStep,
            emailAccountsOnStep,
          });
        }

        return res.status(200).json(updatedStep);

      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in campaign step endpoint', {
      method,
      userId: req.user.id,
      campaignId: id,
      stepId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
