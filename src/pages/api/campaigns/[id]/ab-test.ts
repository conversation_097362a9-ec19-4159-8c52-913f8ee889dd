import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Campaign ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the campaign belongs to the user's organization
    const campaign = await prisma.campaign.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }

    switch (method) {
      case 'GET':
        // Get all A/B tests for the campaign
        const abTests = await prisma.aBTest.findMany({
          where: { campaignId: id },
          include: {
            variants: true,
          },
          orderBy: { createdAt: 'desc' },
        });
        
        return res.status(200).json(abTests);

      case 'POST':
        // Create a new A/B test
        const { name, testType, splitPercent, winnerMetric, variants } = req.body;

        if (!name) {
          return res.status(400).json({ error: 'Test name is required' });
        }

        if (!testType) {
          return res.status(400).json({ error: 'Test type is required' });
        }

        if (!variants || !Array.isArray(variants) || variants.length < 2) {
          return res.status(400).json({ error: 'At least two variants are required' });
        }

        // Update campaign to mark it as an A/B test
        await prisma.campaign.update({
          where: { id },
          data: { isAbTest: true },
        });

        // Create the A/B test
        const abTest = await prisma.aBTest.create({
          data: {
            name,
            campaignId: id,
            testType,
            splitPercent: splitPercent || 50,
            winnerMetric: winnerMetric || 'openRate',
          },
        });

        // Create the variants
        const variantPromises = variants.map((variant: any) => {
          return prisma.aBTestVariant.create({
            data: {
              testId: abTest.id,
              name: variant.name,
              subject: variant.subject,
              content: variant.content,
              templateId: variant.templateId,
              emailAccountId: variant.emailAccountId,
              sendTime: variant.sendTime ? new Date(variant.sendTime) : undefined,
            },
          });
        });

        const createdVariants = await Promise.all(variantPromises);

        return res.status(201).json({
          ...abTest,
          variants: createdVariants,
        });

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in A/B test endpoint', {
      method,
      userId: req.user.id,
      campaignId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
