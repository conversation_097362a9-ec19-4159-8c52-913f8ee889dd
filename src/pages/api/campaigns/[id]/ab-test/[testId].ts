import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id, testId } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Campaign ID is required' });
  }

  if (!testId || typeof testId !== 'string') {
    return res.status(400).json({ error: 'Test ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the campaign belongs to the user's organization
    const campaign = await prisma.campaign.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }

    // Check if the A/B test belongs to the campaign
    const abTest = await prisma.aBTest.findFirst({
      where: {
        id: testId,
        campaignId: id,
      },
      include: {
        variants: true,
      },
    });

    if (!abTest) {
      return res.status(404).json({ error: 'A/B test not found' });
    }

    switch (method) {
      case 'GET':
        return res.status(200).json(abTest);

      case 'PUT':
        // Update the A/B test
        const { status, winnerVariantId } = req.body;

        const updatedTest = await prisma.aBTest.update({
          where: { id: testId },
          data: {
            status: status || undefined,
            endDate: status === 'completed' ? new Date() : undefined,
          },
          include: {
            variants: true,
          },
        });

        // If a winner is declared, update the campaign with the winning variant's content
        if (status === 'completed' && winnerVariantId) {
          const winnerVariant = abTest.variants.find(v => v.id === winnerVariantId);
          
          if (winnerVariant) {
            // Update the campaign with the winning variant's content
            // This would depend on your specific implementation
          }
        }

        return res.status(200).json(updatedTest);

      case 'DELETE':
        // Delete the A/B test
        await prisma.aBTestVariant.deleteMany({
          where: { testId },
        });

        await prisma.aBTest.delete({
          where: { id: testId },
        });

        return res.status(200).json({ success: true });

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in A/B test detail endpoint', {
      method,
      userId: req.user.id,
      campaignId: id,
      testId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
