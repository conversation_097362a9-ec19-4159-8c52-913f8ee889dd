import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  try {
    // Verify campaign exists and belongs to the user
    const campaign = await prisma.campaign.findFirst({
      where: {
        id: id as string,
        userId: req.user.id,
      },
    });

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }

    // Get all campaign steps
    const steps = await prisma.campaignStep.findMany({
      where: {
        campaignId: id as string,
      },
      include: {
        emailAccount: true,
        emailAccounts: {
          include: {
            emailAccount: true,
          },
        },
      },
      orderBy: {
        position: 'asc',
      },
    });

    // Get all campaign leads with their activities
    const campaignLeads = await prisma.campaignLead.findMany({
      where: {
        campaignId: id as string,
      },
      include: {
        lead: true,
        stepActivities: {
          include: {
            step: {
              include: {
                emailAccount: true,
              },
            },
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });

    // Fetch all step activities with metadata that includes the sender
    const stepActivities = await prisma.stepActivity.findMany({
      where: {
        step: {
          campaignId: id as string,
        },
        type: 'email_sent',
      },
      include: {
        campaignLead: {
          include: {
            lead: true,
          },
        },
        step: {
          include: {
            emailAccount: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Build the sender report
    const senderReport = stepActivities.map(activity => {
      // Extract sender email from metadata if available
      const metadata = activity.metadata as any;
      const senderEmail = metadata?.senderEmail || activity.step.emailAccount?.email || 'Unknown';

      return {
        leadId: activity.campaignLead.leadId,
        leadEmail: activity.campaignLead.lead.email,
        leadName: `${activity.campaignLead.lead.firstName || ''} ${activity.campaignLead.lead.lastName || ''}`.trim(),
        stepId: activity.stepId,
        stepName: activity.step.name,
        senderEmail: senderEmail,
        sentAt: activity.sentAt,
        status: activity.status,
        openedAt: activity.openedAt,
        clickedAt: activity.clickedAt,
        repliedAt: activity.repliedAt,
      };
    });

    // Calculate distribution statistics
    const senderStats = {};
    senderReport.forEach(record => {
      if (!senderStats[record.senderEmail]) {
        senderStats[record.senderEmail] = {
          totalSent: 0,
          opened: 0,
          clicked: 0,
          replied: 0,
        };
      }

      senderStats[record.senderEmail].totalSent++;

      if (record.openedAt) {
        senderStats[record.senderEmail].opened++;
      }

      if (record.clickedAt) {
        senderStats[record.senderEmail].clicked++;
      }

      if (record.repliedAt) {
        senderStats[record.senderEmail].replied++;
      }
    });

    // If there's no data, return empty stats
    if (senderReport.length === 0) {
      return res.status(200).json({
        campaign: {
          id: campaign.id,
          name: campaign.name,
        },
        senderReport: [],
        senderStats: {},
        totalLeads: campaignLeads.length,
        totalEmailsSent: 0,
        hasData: false
      });
    }

    return res.status(200).json({
      campaign: {
        id: campaign.id,
        name: campaign.name,
      },
      senderReport,
      senderStats,
      totalLeads: campaignLeads.length,
      totalEmailsSent: senderReport.length,
      hasData: true
    });
  } catch (error) {
    logger.error('Error fetching campaign sender report', {
      error: error instanceof Error ? error.message : 'Unknown error',
      campaignId: id,
      userId: req.user.id,
    });

    return res.status(500).json({ error: 'Failed to fetch campaign sender report' });
  }
}, {
  requireAuth: true,
});
