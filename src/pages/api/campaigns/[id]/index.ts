import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Campaign ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the campaign belongs to the user's organization
    const campaign = await prisma.campaign.findFirst({
      where: {
        id,
        organizationId,
      },
      include: {
        steps: {
          include: {
            emailAccount: true,
            template: true,
            emailAccounts: {
              include: {
                emailAccount: true
              }
            }
          },
          orderBy: {
            position: 'asc',
          },
        },
        leads: {
          include: {
            lead: true,
            stepActivities: {
              include: {
                step: true,
              },
              orderBy: {
                createdAt: 'desc',
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }

    switch (method) {
      case 'GET':
        // Calculate some analytics
        const totalLeads = campaign.leads.length;

        // Count sent emails - check both activity type and status
        const sentCount = campaign.leads.filter(cl =>
          cl.stepActivities.some(sa => sa.type === 'email_sent' || sa.status === 'sent')
        ).length;

        // Count completed leads
        const completedCount = campaign.leads.filter(cl => cl.status === 'completed').length;

        // Other analytics
        const openCount = campaign.leads.filter(cl =>
          cl.stepActivities.some(sa => sa.type === 'email_opened' || sa.status === 'opened')
        ).length;
        const clickCount = campaign.leads.filter(cl =>
          cl.stepActivities.some(sa => sa.type === 'link_clicked' || sa.status === 'clicked')
        ).length;
        const replyCount = campaign.leads.filter(cl =>
          cl.stepActivities.some(sa => sa.type === 'email_replied' || sa.status === 'replied')
        ).length;
        const bounceCount = campaign.leads.filter(cl =>
          cl.stepActivities.some(sa => sa.type === 'email_bounced' || sa.status === 'bounced') || cl.status === 'bounced'
        ).length;
        const unsubscribeCount = campaign.leads.filter(cl =>
          cl.status === 'unsubscribed'
        ).length;

        // Count pending leads
        const pendingCount = campaign.leads.filter(cl =>
          cl.status === 'pending' || cl.stepActivities.some(sa => sa.status === 'pending')
        ).length;

        // Calculate rates
        const openRate = sentCount > 0 ? (openCount / sentCount) * 100 : 0;
        const clickRate = openCount > 0 ? (clickCount / openCount) * 100 : 0;
        const replyRate = sentCount > 0 ? (replyCount / sentCount) * 100 : 0;
        const bounceRate = sentCount > 0 ? (bounceCount / sentCount) * 100 : 0;
        const unsubscribeRate = sentCount > 0 ? (unsubscribeCount / sentCount) * 100 : 0;

        // Group leads by status
        const leadsByStatus = {
          active: campaign.leads.filter(cl => cl.status === 'active').length,
          completed: campaign.leads.filter(cl => cl.status === 'completed').length,
          unsubscribed: unsubscribeCount,
          bounced: bounceCount,
        };

        // Return the campaign with analytics
        return res.status(200).json({
          ...campaign,
          analytics: {
            totalLeads,
            sentCount,
            openCount,
            clickCount,
            replyCount,
            bounceCount,
            unsubscribeCount,
            openRate,
            clickRate,
            replyRate,
            bounceRate,
            unsubscribeRate,
            leadsByStatus,
          },
        });

      case 'PUT':
        // Update the campaign
        const {
          name,
          description,
          status,
          subject,
          content,
          emailAccountId,
          useMultipleSenders,
          emailAccountIds,
          templateId,
          recipientType,
          listId,
          leadIds
        } = req.body;

        // Check if the campaign is being activated
        const isActivating = status === 'active' && campaign.status !== 'active';

        // Check if this is a standard campaign or a sequence campaign
        const isStandardCampaign = !campaign.steps || campaign.steps.length === 0;
        logger.info('Updating campaign', { isStandardCampaign, id });

        // Prepare the update data
        const updateData: any = {
          name: name || undefined,
          description: description || undefined,
          status: status || undefined,
          startDate: isActivating ? new Date() : campaign.startDate,
          // Update recipient type and list ID if provided
          recipientType: recipientType || undefined,
          listId: recipientType === 'list' ? listId : null,
        };

        // Add standard campaign fields if this is a standard campaign
        if (isStandardCampaign) {
          updateData.subject = subject || undefined;
          updateData.content = content || undefined;
          updateData.emailAccountId = useMultipleSenders ? null : emailAccountId || undefined;
          updateData.useMultipleSenders = useMultipleSenders !== undefined ? useMultipleSenders : undefined;
          updateData.templateId = templateId || undefined;
        }

        const updatedCampaign = await prisma.campaign.update({
          where: { id },
          data: updateData,
        });

        // Update email accounts for standard campaign with multiple senders
        if (isStandardCampaign && useMultipleSenders && emailAccountIds && Array.isArray(emailAccountIds)) {
          // For standard campaigns, we need to create a step if it doesn't exist
          let step = await prisma.campaignStep.findFirst({
            where: { campaignId: id },
          });

          if (!step) {
            // Create a default step for the campaign
            step = await prisma.campaignStep.create({
              data: {
                campaignId: id,
                name: 'Email',
                position: 0,
                type: 'email',
                subject: subject || '',
                content: content || '',
                useMultipleSenders: true,
              },
            });
          } else {
            // Update the existing step
            step = await prisma.campaignStep.update({
              where: { id: step.id },
              data: {
                subject: subject || step.subject,
                content: content || step.content,
                useMultipleSenders: true,
              },
            });
          }

          // First, remove all existing email accounts for this step
          await prisma.campaignStepEmailAccount.deleteMany({
            where: { campaignStepId: step.id },
          });

          // Then, add the new email accounts
          await Promise.all(
            emailAccountIds.map((emailAccount: any) => {
              const emailAccountId = typeof emailAccount === 'string' ? emailAccount : emailAccount.id;
              const weight = typeof emailAccount === 'string' ? 1 : (emailAccount.weight || 1);

              return prisma.campaignStepEmailAccount.create({
                data: {
                  campaignStepId: step.id,
                  emailAccountId,
                  weight,
                },
              });
            })
          );
        }

        // If the campaign is being activated, process it immediately
        if (isActivating) {
          logger.info(`[CAMPAIGN_DEBUG] Campaign ${id} is being activated, processing immediately`, {
            campaignId: id,
            userId: req.user.id,
            timestamp: new Date().toISOString()
          });

          try {
            // Import the campaign service dynamically to avoid circular dependencies
            const { CampaignService } = await import('@/services/campaign.service');

            // Process the campaign immediately in the background
            // We'll use a timeout to avoid blocking the response
            setTimeout(async () => {
              try {
                logger.info(`[CAMPAIGN_DEBUG] Starting background processing for campaign ${id}`);
                await CampaignService.processActivatedCampaign(id);
                logger.info(`[CAMPAIGN_DEBUG] Background processing completed for campaign ${id}`);
              } catch (bgError) {
                logger.error(`[CAMPAIGN_DEBUG] Error in background processing for campaign ${id}`, {
                  error: bgError instanceof Error ? bgError.message : 'Unknown error',
                  stack: bgError instanceof Error ? bgError.stack : 'No stack trace',
                  campaignId: id,
                  timestamp: new Date().toISOString()
                });
              }
            }, 100); // Small delay to ensure the response is sent first

            logger.info(`[CAMPAIGN_DEBUG] Campaign ${id} background processing initiated`);
          } catch (processingError) {
            logger.error(`[CAMPAIGN_DEBUG] Error setting up background processing for campaign ${id}`, {
              error: processingError instanceof Error ? processingError.message : 'Unknown error',
              stack: processingError instanceof Error ? processingError.stack : 'No stack trace',
              campaignId: id,
              userId: req.user.id,
              timestamp: new Date().toISOString()
            });

            // Don't throw the error - we still want to return the updated campaign
            // The user can use the "Process Now" button if needed
          }
        }

        return res.status(200).json(updatedCampaign);

      case 'DELETE':
        // Delete the campaign
        await prisma.campaignLead.deleteMany({
          where: { campaignId: id },
        });

        await prisma.campaignStep.deleteMany({
          where: { campaignId: id },
        });

        await prisma.campaign.delete({
          where: { id },
        });

        return res.status(200).json({ success: true });

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in campaign detail endpoint', {
      method,
      userId: req.user.id,
      campaignId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
