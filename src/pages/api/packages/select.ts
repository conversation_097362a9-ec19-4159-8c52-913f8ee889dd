import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { stripe } from '@/config/stripe';

/**
 * Package Selection API
 * Allows users to select a subscription package
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    // Get user session
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.email) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { packageId } = req.body;

    if (!packageId) {
      return res.status(400).json({ error: 'Package ID is required' });
    }

    // Get user and organization
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        ownedOrganization: {
          include: {
            package: true
          }
        }
      }
    });

    if (!user?.ownedOrganization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    // Get the selected package
    const selectedPackage = await prisma.package.findUnique({
      where: { id: packageId }
    });

    if (!selectedPackage) {
      return res.status(404).json({ error: 'Package not found' });
    }

    if (selectedPackage.status !== 'ACTIVE') {
      return res.status(400).json({ error: 'Package is not available' });
    }

    const organization = user.ownedOrganization;

    // If selecting free package, just assign it
    if (selectedPackage.price === 0) {
      await prisma.organization.update({
        where: { id: organization.id },
        data: { packageId: selectedPackage.id }
      });

      logger.info('Free package assigned to organization', {
        organizationId: organization.id,
        packageId: selectedPackage.id,
        packageName: selectedPackage.name,
        userId: user.id
      });

      return res.status(200).json({
        success: true,
        message: 'Free package assigned successfully',
        package: {
          id: selectedPackage.id,
          name: selectedPackage.name,
          price: selectedPackage.price
        }
      });
    }

    // For paid packages, create Stripe checkout session
    if (!selectedPackage.stripePriceId) {
      return res.status(400).json({ 
        error: 'Package not configured for billing',
        message: 'This package is not properly set up for payments. Please contact support.'
      });
    }

    try {
      // Create or get Stripe customer
      let customerId = organization.customerId;
      
      if (!customerId) {
        const customer = await stripe.customers.create({
          email: user.email,
          name: user.name || undefined,
          metadata: {
            organizationId: organization.id,
            userId: user.id
          }
        });
        
        customerId = customer.id;
        
        // Update organization with customer ID
        await prisma.organization.update({
          where: { id: organization.id },
          data: { customerId }
        });
      }

      // Create Stripe checkout session
      const checkoutSession = await stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price: selectedPackage.stripePriceId,
            quantity: 1
          }
        ],
        mode: 'subscription',
        success_url: `${process.env.NEXTAUTH_URL}/settings/billing?success=true&session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.NEXTAUTH_URL}/settings/billing?canceled=true`,
        metadata: {
          organizationId: organization.id,
          packageId: selectedPackage.id,
          userId: user.id
        },
        subscription_data: {
          metadata: {
            organizationId: organization.id,
            packageId: selectedPackage.id
          }
        }
      });

      logger.info('Stripe checkout session created for package selection', {
        organizationId: organization.id,
        packageId: selectedPackage.id,
        packageName: selectedPackage.name,
        checkoutSessionId: checkoutSession.id,
        userId: user.id
      });

      return res.status(200).json({
        success: true,
        message: 'Checkout session created',
        checkoutUrl: checkoutSession.url,
        sessionId: checkoutSession.id,
        package: {
          id: selectedPackage.id,
          name: selectedPackage.name,
          price: selectedPackage.price
        }
      });

    } catch (stripeError) {
      logger.error('Error creating Stripe checkout session', {
        error: stripeError instanceof Error ? stripeError.message : 'Unknown error',
        organizationId: organization.id,
        packageId: selectedPackage.id
      });

      return res.status(500).json({
        error: 'Failed to create checkout session',
        message: 'Unable to process payment. Please try again later.'
      });
    }

  } catch (error) {
    logger.error('Error selecting package', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return res.status(500).json({
      error: 'Failed to select package',
      message: 'Please try again later'
    });
  }
}
