import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

/**
 * Available Packages API
 * Returns packages available for user selection
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    // Get all active packages
    const packages = await prisma.package.findMany({
      where: {
        status: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        description: true,
        price: true,
        billingCycle: true,
        dailyEmailLimit: true,
        monthlyEmailLimit: true,
        emailAccountLimit: true,
        aiFeatures: true,
        isDefault: true
      },
      orderBy: [
        { isDefault: 'desc' }, // Default (free) package first
        { price: 'asc' }       // Then by price
      ]
    });

    // Format packages for frontend display
    const formattedPackages = packages.map(pkg => ({
      id: pkg.id,
      name: pkg.name,
      description: pkg.description,
      price: pkg.price,
      billingCycle: pkg.billingCycle,
      features: {
        dailyEmails: pkg.dailyEmailLimit,
        monthlyEmails: pkg.monthlyEmailLimit,
        emailAccounts: pkg.emailAccountLimit,
        aiFeatures: pkg.aiFeatures,
        hasAI: pkg.aiFeatures.length > 0
      },
      isDefault: pkg.isDefault,
      isFree: pkg.price === 0,
      displayPrice: pkg.price === 0 ? 'Free' : `$${pkg.price}/${pkg.billingCycle.toLowerCase()}`
    }));

    return res.status(200).json({
      success: true,
      packages: formattedPackages,
      total: formattedPackages.length
    });

  } catch (error) {
    logger.error('Error getting available packages', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return res.status(500).json({
      error: 'Failed to get available packages',
      message: 'Please try again later'
    });
  }
}
