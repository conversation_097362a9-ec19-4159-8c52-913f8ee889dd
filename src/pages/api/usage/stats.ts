import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from 'next-auth/react';
import { UsageService } from '@/services/usage.service';
import { prisma } from '@/lib/prisma';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const session = await getSession({ req });
    if (!session) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        organizations: {
          include: {
            organization: {
              include: {
                subscription: true,
              },
            },
          },
        },
      },
    });

    const organizationId = user?.organizations[0]?.organization.id;
    if (!organizationId) {
      return res.status(404).json({ message: 'Organization not found' });
    }

    const usage = await UsageService.getCurrentUsage(organizationId);
    const subscription = user?.organizations[0]?.organization.subscription;

    return res.status(200).json({
      usage,
      subscription,
    });
  } catch (error) {
    console.error('Failed to get usage stats:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}