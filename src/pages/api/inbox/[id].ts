import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/pages/api/auth/[...nextauth]';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { email: session.user?.email as string },
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const { id } = req.query;

    if (req.method === 'GET') {
      // Get the email
      const email = await prisma.receivedEmail.findUnique({
        where: {
          id: id as string,
        },
        include: {
          emailAccount: {
            select: {
              id: true,
              email: true,
              name: true,
              status: true,
              userId: true,
            },
          },
          campaign: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          lead: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          agentReply: {
            select: {
              id: true,
              subject: true,
              textContent: true,
              htmlContent: true,
              status: true,
              sentAt: true,
            },
          },
        },
      });

      if (!email) {
        return res.status(404).json({ error: 'Email not found' });
      }

      // Check if the email belongs to the user
      if (email.emailAccount.userId !== user.id) {
        return res.status(403).json({ error: 'Forbidden' });
      }

      // Mark email as read if it's not already read
      if (!email.isRead) {
        await prisma.receivedEmail.update({
          where: { id: id as string },
          data: {
            isRead: true,
            readAt: new Date()
          }
        });

        // Update the email object to reflect the change
        email.isRead = true;
        email.readAt = new Date();
      }

      // Return the email
      return res.status(200).json({ email });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Error in inbox email API', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
