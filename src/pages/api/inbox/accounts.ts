import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/pages/api/auth/[...nextauth]';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { email: session.user?.email as string },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user || !user.organizations.length) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organizationId;

    if (req.method === 'GET') {
      // Get all email accounts for the user with unread email counts
      const emailAccounts = await prisma.emailAccount.findMany({
        where: {
          userId: user.id,
        },
        select: {
          id: true,
          email: true,
          name: true,
          status: true,
          imapEnabled: true,
          lastImapSync: true,
          _count: {
            select: {
              receivedEmails: {
                where: {
                  isRead: false,
                  // Exclude emails sent from this account
                  NOT: {
                    from: {
                      contains: '',  // Will be replaced with actual email
                      mode: 'insensitive'
                    }
                  }
                }
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // Update the count to exclude emails sent from each account
      const emailAccountsWithCounts = await Promise.all(
        emailAccounts.map(async (account) => {
          const unreadCount = await prisma.receivedEmail.count({
            where: {
              emailAccountId: account.id,
              isRead: false,
              NOT: {
                from: {
                  contains: account.email,
                  mode: 'insensitive'
                }
              }
            }
          });

          return {
            ...account,
            _count: {
              receivedEmails: unreadCount
            }
          };
        })
      );



      // Return the email accounts with unread counts
      return res.status(200).json({ emailAccounts: emailAccountsWithCounts });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Error in inbox accounts API', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
