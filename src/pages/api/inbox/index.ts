import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/pages/api/auth/[...nextauth]';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { email: session.user?.email as string },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user || !user.organizations.length) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organizationId;

    if (req.method === 'GET') {
      // Get query parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const campaignId = req.query.campaignId as string;
      const emailAccountId = req.query.emailAccountId as string;
      const search = req.query.search as string;
      const showCampaignReplies = req.query.showCampaignReplies as string;

      // Build the query - only show incoming emails, not sent campaign emails
      const where: any = {
        emailAccount: {
          userId: user.id,
        }
      };

      // Get the email account to check sender
      let emailAccountEmail = null;
      if (emailAccountId) {
        const emailAccount = await prisma.emailAccount.findFirst({
          where: { id: emailAccountId, userId: user.id },
          select: { email: true }
        });
        emailAccountEmail = emailAccount?.email;
      }

      // Exclude emails that are actually sent FROM this account
      // (emails where the 'from' field matches the account email)
      if (emailAccountEmail) {
        where.NOT = [
          {
            from: {
              contains: emailAccountEmail,
              mode: 'insensitive'
            }
          }
        ];
      }

      // Filter by campaign if provided
      if (campaignId) {
        where.campaignId = campaignId;
      }

      // Filter by email account if provided
      if (emailAccountId) {
        where.emailAccountId = emailAccountId;
      }

      // Filter campaign replies if specified
      if (showCampaignReplies === 'only') {
        // Show only emails that are replies to campaigns
        where.campaignId = { not: null };
      } else if (showCampaignReplies === 'exclude') {
        // Exclude emails that are replies to campaigns
        where.campaignId = null;
      }
      // If showCampaignReplies is 'all' or undefined, show all emails (no additional filter)

      // Search in subject or body
      if (search) {
        where.OR = [
          { subject: { contains: search, mode: 'insensitive' } },
          { textBody: { contains: search, mode: 'insensitive' } },
          { htmlBody: { contains: search, mode: 'insensitive' } },
          { from: { contains: search, mode: 'insensitive' } },
          { to: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Get the total count
      const totalCount = await prisma.receivedEmail.count({ where });

      // Get the emails with pagination
      const emails = await prisma.receivedEmail.findMany({
        where,
        include: {
          emailAccount: {
            select: {
              id: true,
              email: true,
              name: true,
              status: true,
              imapEnabled: true,
            },
          },
          campaign: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          lead: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          agentReply: {
            select: {
              id: true,
              subject: true,
              status: true,
              sentAt: true,
            },
          },
        },
        orderBy: {
          receivedAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      });



      // Note: We no longer automatically delete emails based on headers
      // as this was too aggressive and was removing legitimate received emails

      // Return the emails with pagination info
      return res.status(200).json({
        emails,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
        },
      });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Error in inbox API', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
