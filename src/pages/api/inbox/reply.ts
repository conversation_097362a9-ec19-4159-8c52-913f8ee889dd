import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/pages/api/auth/[...nextauth]';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { EmailService } from '@/services/email.service';
import CryptoJS from 'crypto-js';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the user
    const user = await prisma.user.findUnique({
      where: { email: session.user?.email as string },
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (req.method === 'POST') {
      const { emailAccountId, to, subject, textContent, inReplyTo, references } = req.body;

      if (!emailAccountId || !to || !subject || !textContent) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      // Check if the email account belongs to the user
      const emailAccount = await prisma.emailAccount.findFirst({
        where: {
          id: emailAccountId,
          userId: user.id,
        },
      });

      if (!emailAccount) {
        return res.status(404).json({ error: 'Email account not found' });
      }

      // Check if the email account is verified
      if (emailAccount.status !== 'active' && emailAccount.status !== 'verified') {
        return res.status(400).json({ error: 'Email account is not verified' });
      }

      // Decrypt the SMTP password
      let smtpPassword = '';
      if (emailAccount.smtpPassword) {
        if (!process.env.ENCRYPTION_KEY) {
          logger.error('ENCRYPTION_KEY is not set in environment variables');
          return res.status(500).json({ error: 'Server configuration error' });
        }

        try {
          const bytes = CryptoJS.AES.decrypt(emailAccount.smtpPassword, process.env.ENCRYPTION_KEY);
          smtpPassword = bytes.toString(CryptoJS.enc.Utf8);
        } catch (error) {
          logger.error('Failed to decrypt SMTP password', {
            error: error instanceof Error ? error.message : 'Unknown error',
          });
          return res.status(500).json({ error: 'Failed to decrypt SMTP password' });
        }
      }

      // Send the email
      try {
        // Prepare headers for proper email threading
        const headers = {
          'In-Reply-To': inReplyTo,
          'References': references || inReplyTo
        };

        const result = await EmailService.sendEmail({
          emailAccountId,
          to,
          subject,
          html: textContent,
          from: emailAccount.email,
          overridePassword: smtpPassword,
          headers: headers
        });

        // Find the original email
        const originalEmail = await prisma.receivedEmail.findFirst({
          where: {
            messageId: inReplyTo,
          },
        });

        // Create a record of the reply
        if (originalEmail) {
          // Update the original email to mark it as replied to
          await prisma.receivedEmail.update({
            where: {
              id: originalEmail.id,
            },
            data: {
              isRepliedTo: true,
            },
          });

          // Create a new ReceivedEmail record for the sent reply
          // This helps with threading and viewing conversation history
          // We add a special field to the headers to mark this as a sent email
          await prisma.receivedEmail.create({
            data: {
              emailAccountId,
              messageId: result.messageId || `reply-${Date.now()}`,
              inReplyTo,
              references: references || inReplyTo,
              from: emailAccount.email,
              to,
              subject,
              textBody: textContent,
              htmlBody: textContent, // Store HTML version too for consistent display
              receivedAt: new Date(),
              isRepliedTo: false,
              // If the original email was from a campaign, link this reply to the same campaign
              campaignId: originalEmail.campaignId,
              campaignStepId: originalEmail.campaignStepId,
              leadId: originalEmail.leadId,
              // Store metadata to identify this as a sent email
              headers: {
                "X-Email-Type": "sent",
                "X-Message-ID": result.messageId || `reply-${Date.now()}`,
                "X-In-Reply-To": inReplyTo || "",
                "X-References": references || inReplyTo || ""
              }
            },
          });
        }

        // Update the email account's last used timestamp
        await prisma.emailAccount.update({
          where: {
            id: emailAccountId,
          },
          data: {
            lastUsed: new Date(),
          },
        });

        return res.status(200).json({ success: true, messageId: result.messageId });
      } catch (error) {
        logger.error('Failed to send email', {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        });
        return res.status(500).json({ error: 'Failed to send email' });
      }
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Error in inbox reply API', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
