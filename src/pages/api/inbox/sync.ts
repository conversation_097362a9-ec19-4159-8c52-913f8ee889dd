import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/pages/api/auth/[...nextauth]';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { ImapService } from '@/services/imap.service';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { email: session.user?.email as string },
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (req.method === 'POST') {
      const { emailAccountId } = req.body;

      if (!emailAccountId) {
        return res.status(400).json({ error: 'Email account ID is required' });
      }

      // Check if the email account belongs to the user
      const emailAccount = await prisma.emailAccount.findFirst({
        where: {
          id: emailAccountId,
          userId: user.id,
        },
      });

      if (!emailAccount) {
        return res.status(404).json({ error: 'Email account not found' });
      }

      // Check if IMAP is enabled
      if (!emailAccount.imapEnabled) {
        return res.status(400).json({ error: 'IMAP is not enabled for this email account' });
      }

      // Sync the email account
      await ImapService.fetchEmails(emailAccountId);

      // Return success
      return res.status(200).json({ success: true, message: 'Email account synced successfully' });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Error in inbox sync API', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
