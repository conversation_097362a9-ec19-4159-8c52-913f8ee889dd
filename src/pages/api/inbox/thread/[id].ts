import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;
  const user = req.user;

  if (!user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Email ID is required' });
  }

  try {
    // Get the original email
    const originalEmail = await prisma.receivedEmail.findUnique({
      where: { id },
      include: {
        emailAccount: {
          select: {
            id: true,
            email: true,
            name: true,
            userId: true,
          },
        },
      },
    });

    if (!originalEmail) {
      return res.status(404).json({ error: 'Email not found' });
    }

    // Check if the email belongs to the user
    if (originalEmail.emailAccount.userId !== user.id) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Get the message ID of the original email
    const messageId = originalEmail.messageId;

    // Find all emails in the thread
    // This includes:
    // 1. Emails that have this email's messageId in their inReplyTo field (direct replies)
    // 2. Emails that have this email's messageId in their references field (part of the thread)
    // 3. Emails that this email references (previous emails in the thread)
    // 4. Emails that this email is a reply to (parent emails)

    // First, build a list of all message IDs in the thread
    let threadMessageIds = new Set<string>();
    threadMessageIds.add(messageId);

    // Add the inReplyTo if it exists
    if (originalEmail.inReplyTo) {
      threadMessageIds.add(originalEmail.inReplyTo);
    }

    // Add references if they exist
    if (originalEmail.references) {
      const refs = originalEmail.references.split(' ').filter(Boolean);
      refs.forEach(ref => threadMessageIds.add(ref));
    }

    // Find all emails that are part of this thread
    const emails = await prisma.receivedEmail.findMany({
      where: {
        OR: [
          // Emails that are part of the thread
          { messageId: { in: Array.from(threadMessageIds) } },
          // Emails that reference this thread
          { inReplyTo: { in: Array.from(threadMessageIds) } },
          // Emails that have references to this thread
          { references: { contains: messageId } },
        ],
        // Only include emails from accounts the user has access to
        emailAccount: {
          userId: user.id,
        },
      },
      include: {
        emailAccount: {
          select: {
            id: true,
            email: true,
            name: true,
            status: true,
          },
        },
        campaign: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        lead: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        agentReply: {
          select: {
            id: true,
            subject: true,
            textContent: true,
            htmlContent: true,
            status: true,
            sentAt: true,
            agent: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // Also get agent replies that are part of this thread
    const agentReplies = await prisma.agentReply.findMany({
      where: {
        receivedEmail: {
          OR: [
            { messageId: { in: Array.from(threadMessageIds) } },
            { inReplyTo: { in: Array.from(threadMessageIds) } },
            { references: { contains: messageId } },
          ],
          emailAccount: {
            userId: user.id,
          },
        },
        status: 'SENT',
      },
      include: {
        agent: {
          select: {
            id: true,
            name: true,
          },
        },
        receivedEmail: {
          include: {
            emailAccount: {
              select: {
                id: true,
                email: true,
                name: true,
                status: true,
              },
            },
          },
        },
      },
    });

    // Process emails to identify sent vs received emails
    const processedEmails = emails.map(email => {
      // Check if this is a sent email (has X-Email-Type header)
      const isSent = email.headers &&
                    typeof email.headers === 'object' &&
                    email.headers['X-Email-Type'] === 'sent';

      return {
        ...email,
        type: isSent ? 'sent' : 'received',
        htmlBody: email.htmlBody || email.textBody,
        textBody: email.textBody || email.htmlBody?.replace(/<[^>]*>/g, ''),
        messageType: 'email',
      };
    });

    // Convert agent replies to email-like format
    const processedAgentReplies = agentReplies.map(reply => ({
      id: `agent-reply-${reply.id}`,
      messageId: `agent-reply-${reply.id}@system`,
      subject: reply.subject,
      from: reply.receivedEmail?.emailAccount?.email || 'AI Agent',
      to: reply.receivedEmail?.from || '',
      htmlBody: reply.htmlContent || reply.textContent,
      textBody: reply.textContent,
      receivedAt: reply.sentAt || reply.createdAt,
      isRead: true,
      type: 'sent',
      messageType: 'agent-reply',
      emailAccount: reply.receivedEmail?.emailAccount,
      agent: reply.agent,
      agentReply: {
        id: reply.id,
        status: reply.status,
        sentAt: reply.sentAt,
      },
    }));

    // Combine emails and agent replies
    const allThreadItems = [...processedEmails, ...processedAgentReplies];

    // Sort by date (oldest first)
    const threadEmails = allThreadItems.sort((a, b) => {
      const dateA = new Date(a.receivedAt).getTime();
      const dateB = new Date(b.receivedAt).getTime();
      return dateA - dateB;
    });

    return res.status(200).json({
      thread: threadEmails,
      originalEmailId: id,
    });
  } catch (error) {
    logger.error('Error retrieving email thread', {
      method,
      userId: user.id,
      emailId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
