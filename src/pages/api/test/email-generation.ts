import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { AgentEnhancementService } from '@/services/agent-enhancement.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  try {
    const { agentId, leadId } = req.body;

    if (!agentId || !leadId) {
      return res.status(400).json({ error: 'agentId and leadId are required' });
    }

    // Get user's organization
    const organizationMember = await prisma.organizationMember.findFirst({
      where: { userId: req.user.id },
      include: { organization: true },
    });

    if (!organizationMember) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = organizationMember.organizationId;

    // Verify the agent belongs to the user's organization
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        organizationId,
      },
    });

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    // Verify the lead belongs to the user's organization
    const lead = await prisma.lead.findFirst({
      where: {
        id: leadId,
        organizationId,
      },
    });

    if (!lead) {
      return res.status(404).json({ error: 'Lead not found' });
    }

    logger.info('Testing email generation', {
      agentId,
      leadId,
      organizationId,
      userId: req.user.id,
    });

    // Generate the email
    const scrapedData = {
      linkedInProfile: {
        name: lead.firstName + ' ' + (lead.lastName || ''),
        title: 'Test Title',
        company: lead.company,
        about: 'Test professional with experience in their field.',
      },
    };

    const result = await AgentEnhancementService.generatePersonalizedEmail(
      leadId,
      agentId,
      scrapedData
    );

    logger.info('Email generation test completed', {
      agentId,
      leadId,
      hasSubject: !!result.subject,
      hasHtml: !!result.html,
      hasText: !!result.text,
      subjectLength: result.subject?.length || 0,
      htmlLength: result.html?.length || 0,
      textLength: result.text?.length || 0,
    });

    return res.status(200).json({
      success: true,
      result,
      metadata: {
        agentId,
        leadId,
        organizationId,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    logger.error('Error in email generation test endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Internal server error',
      success: false,
    });
  }
}, {
  requireAuth: true,
});
