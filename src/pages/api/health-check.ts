import { NextApiRequest, NextApiResponse } from 'next';
import redis from '@/lib/redis';
import { prisma } from '@/lib/prisma';

// This endpoint is not wrapped with apiHandler to avoid authentication
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check Redis connection (skip in development when queue is disabled)
    let redisStatus = false;
    const shouldCheckRedis = process.env.NODE_ENV === 'production' ||
                             process.env.ENABLE_QUEUE_WORKER === 'true' ||
                             process.env.VERCEL_ENV === 'production';

    if (shouldCheckRedis) {
      try {
        await redis.ping();
        redisStatus = true;
      } catch (redisError) {
        console.error('Redis health check failed:', redisError);
      }
    } else {
      redisStatus = true; // Mark as healthy when <PERSON><PERSON> is disabled
      console.log('Redis health check skipped (queue disabled in development)');
    }

    // Check database connection
    let dbStatus = false;
    try {
      await prisma.$queryRaw`SELECT 1`;
      dbStatus = true;
    } catch (dbError) {
      console.error('Database health check failed:', dbError);
    }

    return res.status(200).json({
      status: redisStatus && dbStatus ? 'healthy' : 'unhealthy',
      redis: redisStatus ? 'connected' : 'disconnected',
      database: dbStatus ? 'connected' : 'disconnected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Health check error:', error);
    return res.status(500).json({
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
