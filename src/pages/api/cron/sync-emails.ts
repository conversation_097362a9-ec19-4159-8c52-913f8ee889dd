import { NextApiRequest, NextApiResponse } from 'next';
import { ImapService } from '@/services/imap.service';
import { logger } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check for API key if this is a public endpoint
    const apiKey = req.headers['x-api-key'];
    if (process.env.CRON_API_KEY && apiKey !== process.env.CRON_API_KEY) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Schedule IMAP sync for all enabled accounts
    logger.info('Starting scheduled IMAP sync');
    await ImapService.scheduleImapSync();
    logger.info('Scheduled IMAP sync completed');

    return res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error in scheduled IMAP sync', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
