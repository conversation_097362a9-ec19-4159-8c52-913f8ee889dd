import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { ReputationMonitoringService } from '@/services/reputation-monitoring.service';
import { logger } from '@/lib/logger';

/**
 * API route for checking email account reputation
 * This endpoint is called by Vercel's cron system weekly on Sunday at 2 AM
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify cron job secret if provided
    const cronSecret = req.headers['x-cron-secret'];
    if (process.env.CRON_SECRET && cronSecret !== process.env.CRON_SECRET) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    logger.info('Starting reputation checker via Vercel cron');

    // Get all active email accounts
    const emailAccounts = await prisma.emailAccount.findMany({
      where: {
        status: {
          in: ['active', 'verified']
        }
      }
    });

    logger.info(`Checking reputation for ${emailAccounts.length} email accounts`);

    // Check reputation for each account
    for (const account of emailAccounts) {
      try {
        // Add a small delay between checks to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check reputation
        await ReputationMonitoringService.checkReputation(account.id);

        logger.info(`Successfully checked reputation for ${account.email}`);
      } catch (error) {
        logger.error(`Failed to check reputation for ${account.email}`, {
          emailAccountId: account.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    logger.info('Completed reputation check for all email accounts');

    return res.status(200).json({ 
      success: true,
      accountsChecked: emailAccounts.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error in reputation checker cron job', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    return res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
