import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';
import { AgentEnhancementService } from '@/services/agent-enhancement.service';

/**
 * API route for processing AI agent tasks
 * This endpoint can be called by Vercel's cron system or external cron jobs
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify cron job secret if provided
    const cronSecret = req.headers['x-cron-secret'];
    if (process.env.CRON_SECRET && cronSecret !== process.env.CRON_SECRET) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    logger.info('[AGENT_PROCESSOR] Starting agent processor via API', {
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel'
    });

    // Find active campaigns with AI agents
    const campaigns = await prisma.campaign.findMany({
      where: {
        status: 'active',
        agentId: {
          not: null,
        },
      },
      include: {
        agent: true,
        leads: {
          include: {
            lead: true,
            currentStep: true,
          },
        },
      },
    });

    logger.info(`[AGENT_PROCESSOR] Found ${campaigns.length} active campaigns with AI agents`);

    const results = {
      processedCampaigns: 0,
      processedLeads: 0,
      errors: 0,
    };

    // Process each campaign
    for (const campaign of campaigns) {
      try {
        logger.info(`[AGENT_PROCESSOR] Processing campaign ${campaign.id} with agent ${campaign.agent.name}`);

        // Check if agent has required capabilities
        const capabilities = campaign.agent.capabilities || [];
        const hasWebScraping = capabilities.includes('web_scraping');
        const hasLinkedInResearch = capabilities.includes('linkedin_research');
        const hasPersonalization = capabilities.includes('personalization');

        let processedLeadsCount = 0;

        // Process each lead in the campaign
        for (const campaignLead of campaign.leads) {
          // Skip leads that are not active
          if (campaignLead.status !== 'active') {
            continue;
          }

          const lead = campaignLead.lead;
          logger.info(`[AGENT_PROCESSOR] Processing lead ${lead.email} for campaign ${campaign.id}`);

          // Get lead's custom fields
          const customFields = lead.customFields as Record<string, any> || {};

          // Check if we have URLs to scrape
          const websiteUrl = customFields.websiteUrl || customFields.website;
          const linkedInUrl = customFields.linkedInUrl || customFields.linkedInProfile;
          const companyLinkedInUrl = customFields.companyLinkedInUrl || customFields.companyLinkedIn;

          // Gather data if we have capabilities and URLs
          let scrapedData: any = {};

          if (hasWebScraping && websiteUrl) {
            try {
              const websiteData = await AgentEnhancementService.scrapeWebsite(websiteUrl);
              scrapedData.website = websiteData;
            } catch (error) {
              logger.error(`[AGENT_PROCESSOR] Error scraping website for lead ${lead.id}`, {
                error: error instanceof Error ? error.message : 'Unknown error',
                websiteUrl,
              });
              results.errors++;
            }
          }

          if (hasLinkedInResearch && linkedInUrl) {
            try {
              const profileData = await AgentEnhancementService.scrapeLinkedInProfile(linkedInUrl);
              scrapedData.linkedInProfile = profileData;
            } catch (error) {
              logger.error(`[AGENT_PROCESSOR] Error scraping LinkedIn profile for lead ${lead.id}`, {
                error: error instanceof Error ? error.message : 'Unknown error',
                linkedInUrl,
              });
              results.errors++;
            }
          }

          if (hasLinkedInResearch && companyLinkedInUrl) {
            try {
              const companyData = await AgentEnhancementService.scrapeLinkedInProfile(companyLinkedInUrl);
              scrapedData.companyLinkedIn = companyData;
            } catch (error) {
              logger.error(`[AGENT_PROCESSOR] Error scraping company LinkedIn for lead ${lead.id}`, {
                error: error instanceof Error ? error.message : 'Unknown error',
                companyLinkedInUrl,
              });
              results.errors++;
            }
          }

          // Generate personalized email if we have personalization capability
          if (hasPersonalization && Object.keys(scrapedData).length > 0) {
            try {
              const emailContent = await AgentEnhancementService.generatePersonalizedEmail(
                lead.id,
                campaign.agent.id,
                scrapedData
              );

              // Store the generated email for the current step
              if (campaignLead.currentStep) {
                await prisma.campaignStep.update({
                  where: { id: campaignLead.currentStepId },
                  data: {
                    subject: emailContent.subject,
                    content: emailContent.html,
                  },
                });

                logger.info(`[AGENT_PROCESSOR] Updated step ${campaignLead.currentStepId} with personalized content for lead ${lead.id}`);
                processedLeadsCount++;
                results.processedLeads++;
              }
            } catch (error) {
              logger.error(`[AGENT_PROCESSOR] Error generating personalized email for lead ${lead.id}`, {
                error: error instanceof Error ? error.message : 'Unknown error',
              });
              results.errors++;
            }
          }
        }

        logger.info(`[AGENT_PROCESSOR] Processed ${processedLeadsCount} leads for campaign ${campaign.id}`);
        results.processedCampaigns++;
      } catch (error) {
        logger.error(`[AGENT_PROCESSOR] Error processing campaign ${campaign.id}`, {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          campaignId: campaign.id,
        });
        results.errors++;
      }
    }

    logger.info('[AGENT_PROCESSOR] Agent processor completed via API', {
      results,
      timestamp: new Date().toISOString(),
    });

    return res.status(200).json({
      success: true,
      timestamp: new Date().toISOString(),
      results,
      message: 'Agent processor completed successfully'
    });
  } catch (error) {
    logger.error('[AGENT_PROCESSOR] Error in agent processor API', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });

    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
