import { NextApiRequest, NextApiResponse } from 'next';
import { MonitoringService } from '@/services/monitoring.service';
import { HealthService } from '@/services/health.service';
import { apiHandler } from '@/lib/apiHandler';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Verify cron job secret
  if (req.headers['x-cron-secret'] !== process.env.CRON_SECRET) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const [health, metrics] = await Promise.all([
    HealthService.performHealthCheck(),
    MonitoringService.getPerformanceMetrics(),
  ]);

  await MonitoringService.checkThresholds();

  res.status(200).json({ health, metrics });
}

export default apiHandler(handler);