import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { CampaignService } from '@/services/campaign.service';
import { ImapService } from '@/services/imap.service';
import { ReputationMonitoringService } from '@/services/reputation-monitoring.service';
import { logger } from '@/lib/logger';

/**
 * API route for daily tasks
 * This endpoint is called by Vercel's cron system once per day
 * It handles:
 * 1. Processing scheduled campaigns
 * 2. Syncing emails from IMAP accounts
 * 3. Checking email reputation (on Sundays)
 * 4. Cleaning up old data
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify cron job secret if provided
    const cronSecret = req.headers['x-cron-secret'];
    if (process.env.CRON_SECRET && cronSecret !== process.env.CRON_SECRET) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    logger.info('[DAILY_TASKS] Starting daily tasks', {
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel'
    });

    const results = {
      scheduledCampaigns: 0,
      emailSync: 0,
      reputationChecks: 0,
      cleanupTasks: 0
    };

    // 1. Process scheduled campaigns
    try {
      logger.info('[DAILY_TASKS] Processing scheduled campaigns');

      // Find campaigns scheduled for today
      const today = new Date();
      const scheduledCampaigns = await prisma.campaign.findMany({
        where: {
          status: 'scheduled',
          startDate: {
            lte: today
          }
        }
      });

      logger.info(`[DAILY_TASKS] Found ${scheduledCampaigns.length} scheduled campaigns to process`);

      // Activate and process each scheduled campaign
      for (const campaign of scheduledCampaigns) {
        try {
          // Update campaign status to active
          await prisma.campaign.update({
            where: { id: campaign.id },
            data: { status: 'active' }
          });

          // Process the campaign
          await CampaignService.processActivatedCampaign(campaign.id);

          logger.info(`[DAILY_TASKS] Successfully processed scheduled campaign: ${campaign.id}`);
          results.scheduledCampaigns++;
        } catch (campaignError) {
          logger.error(`[DAILY_TASKS] Error processing scheduled campaign ${campaign.id}`, {
            error: campaignError instanceof Error ? campaignError.message : 'Unknown error',
            stack: campaignError instanceof Error ? campaignError.stack : 'No stack trace',
            campaignId: campaign.id
          });
        }
      }
    } catch (scheduledError) {
      logger.error('[DAILY_TASKS] Error processing scheduled campaigns', {
        error: scheduledError instanceof Error ? scheduledError.message : 'Unknown error',
        stack: scheduledError instanceof Error ? scheduledError.stack : 'No stack trace'
      });
    }

    // 2. Sync emails from IMAP accounts
    try {
      logger.info('[DAILY_TASKS] Syncing emails from IMAP accounts');
      await ImapService.scheduleImapSync();
      results.emailSync = 1;
      logger.info('[DAILY_TASKS] Email sync completed');
    } catch (syncError) {
      logger.error('[DAILY_TASKS] Error syncing emails', {
        error: syncError instanceof Error ? syncError.message : 'Unknown error',
        stack: syncError instanceof Error ? syncError.stack : 'No stack trace'
      });
    }

    // 3. Check email reputation (on Sundays)
    const now = new Date();
    const dayOfWeek = now.getDay();
    if (dayOfWeek === 0) { // Sunday
      try {
        logger.info('[DAILY_TASKS] Checking email reputation (Sunday task)');

        // Get all active email accounts
        const emailAccounts = await prisma.emailAccount.findMany({
          where: {
            status: {
              in: ['active', 'verified']
            }
          }
        });

        logger.info(`[DAILY_TASKS] Checking reputation for ${emailAccounts.length} email accounts`);

        // Check reputation for each account
        for (const account of emailAccounts) {
          try {
            await ReputationMonitoringService.checkReputation(account.id);
            results.reputationChecks++;
            logger.info(`[DAILY_TASKS] Successfully checked reputation for ${account.email}`);
          } catch (accountError) {
            logger.error(`[DAILY_TASKS] Failed to check reputation for ${account.email}`, {
              emailAccountId: account.id,
              error: accountError instanceof Error ? accountError.message : 'Unknown error'
            });
          }

          // Add a small delay between checks to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 2000));
        }

        logger.info('[DAILY_TASKS] Reputation checks completed');
      } catch (reputationError) {
        logger.error('[DAILY_TASKS] Error checking reputation', {
          error: reputationError instanceof Error ? reputationError.message : 'Unknown error',
          stack: reputationError instanceof Error ? reputationError.stack : 'No stack trace'
        });
      }
    }

    // 4. Clean up old data
    try {
      logger.info('[DAILY_TASKS] Cleaning up old data');

      // Clean up old notifications (older than 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const deletedNotifications = await prisma.notification.deleteMany({
        where: {
          createdAt: {
            lt: thirtyDaysAgo
          },
          isRead: true
        }
      });

      logger.info(`[DAILY_TASKS] Deleted ${deletedNotifications.count} old notifications`);
      results.cleanupTasks += deletedNotifications.count;

      // Other cleanup tasks can be added here

      logger.info('[DAILY_TASKS] Cleanup tasks completed');
    } catch (cleanupError) {
      logger.error('[DAILY_TASKS] Error cleaning up old data', {
        error: cleanupError instanceof Error ? cleanupError.message : 'Unknown error',
        stack: cleanupError instanceof Error ? cleanupError.stack : 'No stack trace'
      });
    }

    logger.info('[DAILY_TASKS] Daily tasks completed', { results });

    return res.status(200).json({
      success: true,
      results,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('[DAILY_TASKS] Error in daily tasks', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
    });

    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
