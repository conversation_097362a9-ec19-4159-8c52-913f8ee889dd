import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';
import { ReminderService } from '@/services/reminder.service';

/**
 * API route for processing appointment reminders
 * This endpoint can be called by Vercel's cron system or external cron jobs
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify cron job secret if provided
    const cronSecret = req.headers['x-cron-secret'];
    if (process.env.CRON_SECRET && cronSecret !== process.env.CRON_SECRET) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    logger.info('[REMINDER_PROCESSOR] Starting reminder processor via API', {
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel'
    });

    const results = {
      processedReminders: 0,
      sentReminders: 0,
      errors: 0,
    };

    // Find pending reminders that need to be sent
    const now = new Date();
    const pendingReminders = await prisma.reminder.findMany({
      where: {
        status: 'pending',
        appointment: {
          startTime: {
            gte: now, // Only process reminders for future appointments
          },
          status: 'scheduled', // Only process reminders for scheduled appointments
        },
      },
      include: {
        appointment: {
          include: {
            lead: true,
          },
        },
        agent: true,
      },
    });

    logger.info(`[REMINDER_PROCESSOR] Found ${pendingReminders.length} pending reminders`);

    // Process each reminder
    for (const reminder of pendingReminders) {
      try {
        results.processedReminders++;
        
        // Calculate when the reminder should be sent
        const reminderTime = new Date(
          reminder.appointment.startTime.getTime() - reminder.timeBeforeAppointment * 60 * 1000
        );

        // Check if it's time to send the reminder
        if (reminderTime <= now) {
          logger.info(`[REMINDER_PROCESSOR] Sending reminder ${reminder.id} for appointment ${reminder.appointmentId}`);
          
          // Process the reminder
          await ReminderService.processReminderJob({
            reminderId: reminder.id,
            appointmentId: reminder.appointmentId,
            leadId: reminder.appointment.leadId,
            agentId: reminder.agentId,
            type: reminder.type,
            content: reminder.content,
          });
          
          results.sentReminders++;
        } else {
          logger.info(`[REMINDER_PROCESSOR] Reminder ${reminder.id} is scheduled for later`, {
            reminderTime,
            timeUntilReminder: Math.floor((reminderTime.getTime() - now.getTime()) / 60000) + ' minutes',
          });
        }
      } catch (error) {
        logger.error(`[REMINDER_PROCESSOR] Error processing reminder ${reminder.id}`, {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          reminderId: reminder.id,
          appointmentId: reminder.appointmentId,
        });
        
        results.errors++;
        
        // Update reminder status to failed
        await prisma.reminder.update({
          where: { id: reminder.id },
          data: {
            status: 'failed',
          },
        });
      }
    }

    logger.info('[REMINDER_PROCESSOR] Reminder processor completed via API', {
      results,
      timestamp: new Date().toISOString(),
    });

    return res.status(200).json({
      success: true,
      timestamp: new Date().toISOString(),
      results,
      message: 'Reminder processor completed successfully'
    });
  } catch (error) {
    logger.error('[REMINDER_PROCESSOR] Error in reminder processor API', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });

    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
