import { NextApiRequest, NextApiResponse } from 'next';
import { CampaignService } from '@/services/campaign.service';
import { SequenceCampaignService } from '@/services/sequence-campaign.service';
import { logger } from '@/lib/logger';

/**
 * API route for processing active campaigns
 * This endpoint is called by Vercel's cron system every 5 minutes
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify cron job secret if provided
    const cronSecret = req.headers['x-cron-secret'];
    if (process.env.CRON_SECRET && cronSecret !== process.env.CRON_SECRET) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    logger.info('[CAMPAIGN_DEBUG] Starting campaign processor via Vercel cron', {
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel'
    });

    // Process standard campaigns
    await CampaignService.processActiveCampaigns();

    // Process sequence campaigns
    await SequenceCampaignService.processActiveSequenceCampaigns();

    logger.info('[CAMPAIGN_DEBUG] Campaign processor completed via Vercel cron');

    return res.status(200).json({
      success: true,
      timestamp: new Date().toISOString(),
      message: 'Campaign processor completed successfully'
    });
  } catch (error) {
    logger.error('[CAMPAIGN_DEBUG] Error in campaign processor cron job', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });

    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
