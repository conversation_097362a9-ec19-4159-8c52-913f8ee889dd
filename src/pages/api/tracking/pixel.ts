import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { TrackingFactoryService } from '@/services/tracking-factory.service';

/**
 * Email tracking pixel endpoint
 * This endpoint returns a 1x1 transparent GIF and records email opens
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set headers first to ensure they're always included, even if an error occurs
  // Set the content type to a transparent 1x1 pixel GIF
  res.setHeader('Content-Type', 'image/gif');

  // Disable caching with strong cache-control headers
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');

  // Add CORS headers to ensure the pixel loads in any email client
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Requested-With');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours

  // Create a 1x1 transparent GIF
  const pixel = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).send(pixel);
  }

  // Log the request for debugging with console.log for immediate visibility in Vercel logs
  console.log(`🔍 TRACKING PIXEL REQUEST RECEIVED at ${new Date().toISOString()}`);
  console.log(`🔍 URL: ${req.url}`);
  console.log(`🔍 Query params:`, JSON.stringify(req.query));
  console.log(`🔍 User-Agent: ${req.headers['user-agent']}`);
  console.log(`🔍 Referer: ${req.headers['referer']}`);
  console.log(`🔍 Host: ${req.headers['host']}`);
  console.log(`🔍 IP: ${req.headers['x-forwarded-for'] || req.socket.remoteAddress}`);
  console.log(`🔍 Request Method: ${req.method}`);
  console.log(`🔍 Request Headers:`, JSON.stringify(req.headers, null, 2));

  logger.info('Tracking pixel request received', {
    query: req.query,
    headers: {
      userAgent: req.headers['user-agent'],
      referer: req.headers['referer'],
      host: req.headers['host'],
      ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
      forwardedFor: req.headers['x-forwarded-for'],
      forwardedHost: req.headers['x-forwarded-host'],
      forwardedProto: req.headers['x-forwarded-proto'],
      accept: req.headers['accept'],
      acceptLanguage: req.headers['accept-language'],
      cacheControl: req.headers['cache-control'],
      connection: req.headers['connection']
    },
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString(),
    rawHeaders: req.rawHeaders
  });

  try {
    // Extract campaign and lead IDs from query parameters
    const { cid, lid, stepId } = req.query;

    // Log the tracking attempt with console.log for immediate visibility
    console.log(`💬 PROCESSING TRACKING PIXEL REQUEST`);
    console.log(`💬 Campaign ID: ${cid}`);
    console.log(`💬 Lead ID: ${lid}`);
    console.log(`💬 Step ID: ${stepId || 'Not provided'}`);
    console.log(`💬 IP: ${req.headers['x-forwarded-for'] || req.socket.remoteAddress}`);
    console.log(`💬 User Agent: ${req.headers['user-agent']}`);
    console.log(`💬 Referrer: ${req.headers['referer'] || 'None'}`);
    console.log(`💬 Request Time: ${new Date().toISOString()}`);
    console.log(`💬 Request ID: ${req.headers['x-request-id'] || 'Not provided'}`);
    console.log(`💬 Request Trace: ${req.headers['x-trace-id'] || 'Not provided'}`);

    // Validate parameters
    if (!cid || !lid) {
      console.log(`⚠️ INVALID TRACKING PIXEL REQUEST - Missing parameters`);
      console.log(`⚠️ Campaign ID: ${cid || 'Missing'}`);
      console.log(`⚠️ Lead ID: ${lid || 'Missing'}`);
      logger.warn('Invalid tracking pixel request - missing parameters', {
        query: req.query,
        headers: {
          userAgent: req.headers['user-agent'],
          ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
          referrer: req.headers['referer']
        }
      });
      // Still return the pixel to avoid errors in email clients
      return res.status(200).send(pixel);
    }

    // Ensure parameters are strings
    const campaignId = Array.isArray(cid) ? cid[0] : cid;
    const leadId = Array.isArray(lid) ? lid[0] : lid;
    const stepIdStr = Array.isArray(stepId) ? stepId[0] : stepId;

    logger.info('Processing tracking pixel request', {
      campaignId,
      leadId,
      stepId: stepIdStr,
      userAgent: req.headers['user-agent'],
      ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
      referrer: req.headers['referer'],
      timestamp: new Date().toISOString()
    });

    // Use the TrackingFactoryService to track the open
    const success = await TrackingFactoryService.trackOpen(
      campaignId,
      leadId,
      {
        userAgent: req.headers['user-agent'] as string,
        ip: (req.headers['x-forwarded-for'] || req.socket.remoteAddress) as string,
        referrer: req.headers['referer'] as string,
        timestamp: new Date().toISOString(),
        stepId: stepIdStr
      }
    );

    if (success) {
      console.log(`🎉 EMAIL OPEN TRACKED SUCCESSFULLY!`);
      console.log(`🎉 Campaign ID: ${campaignId}`);
      console.log(`🎉 Lead ID: ${leadId}`);
      console.log(`🎉 Step ID: ${stepIdStr || 'Not provided'}`);
      console.log(`🎉 Timestamp: ${new Date().toISOString()}`);
      console.log(`🎉 User Agent: ${req.headers['user-agent']}`);
      console.log(`🎉 IP: ${req.headers['x-forwarded-for'] || req.socket.remoteAddress}`);
      
      logger.info('Email open tracked successfully', {
        campaignId,
        leadId,
        stepId: stepIdStr,
        timestamp: new Date().toISOString(),
        userAgent: req.headers['user-agent'],
        ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
        referrer: req.headers['referer'],
        requestId: req.headers['x-request-id'],
        traceId: req.headers['x-trace-id']
      });
    } else {
      console.log(`⚠️ EMAIL OPEN TRACKING FAILED`);
      console.log(`⚠️ Campaign ID: ${campaignId}`);
      console.log(`⚠️ Lead ID: ${leadId}`);
      console.log(`⚠️ Step ID: ${stepIdStr || 'Not provided'}`);
      console.log(`⚠️ Timestamp: ${new Date().toISOString()}`);
      console.log(`⚠️ User Agent: ${req.headers['user-agent']}`);
      console.log(`⚠️ IP: ${req.headers['x-forwarded-for'] || req.socket.remoteAddress}`);
      logger.warn('Email open tracking failed', {
        campaignId,
        leadId,
        userAgent: req.headers['user-agent'],
        ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress
      });
    }

    // For sequence campaigns, queue condition checking
    // This ensures that conditions are properly evaluated
    try {
      const campaign = await prisma.campaign.findUnique({
        where: { id: campaignId },
        select: { type: true, name: true }
      });

      if (campaign?.type === 'sequence') {
        console.log(`🔄 QUEUEING SEQUENCE CAMPAIGN PROCESSING FOR "${campaign.name}"`);

        // Create a record of the processing request
        await prisma.processingQueue.create({
          data: {
            type: 'sequence_condition_check',
            status: 'pending',
            payload: {
              campaignId,
              leadId,
              eventType: 'open',
              timestamp: new Date().toISOString()
            },
            priority: 1, // High priority
            createdAt: new Date()
          }
        }).catch(async (queueError) => {
          // If the queue table doesn't exist yet, fall back to immediate processing
          logger.warn('Failed to queue sequence processing - falling back to immediate processing', {
            error: queueError instanceof Error ? queueError.message : 'Unknown error',
            campaignId,
            leadId,
            campaignName: campaign.name
          });

          // Import dynamically to avoid circular dependencies
          const { SequenceCampaignService } = await import('@/services/sequence-campaign.service');

          // Process the sequence campaign
          await SequenceCampaignService.processLeadAfterInteraction(campaignId, leadId, 'open');
        });

        console.log(`🔄 SEQUENCE CAMPAIGN PROCESSING QUEUED SUCCESSFULLY`);
      }
    } catch (sequenceError) {
      logger.error('Error processing sequence campaign after open', {
        error: sequenceError instanceof Error ? sequenceError.message : 'Unknown error',
        campaignId,
        leadId,
        stack: sequenceError instanceof Error ? sequenceError.stack : undefined
      });
    }

    // Return the tracking pixel
    return res.status(200).send(pixel);
  } catch (error) {
    // Log detailed error information
    logger.error('Error tracking email open', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      query: req.query,
      headers: {
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        host: req.headers['host'],
      },
      timestamp: new Date().toISOString()
    });

    // Always return the pixel even if there's an error to avoid breaking email clients
    return res.status(200).send(pixel);
  }
}
