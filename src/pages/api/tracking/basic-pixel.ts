import { NextApiRequest, NextApiResponse } from 'next';

/**
 * A basic tracking pixel endpoint that just returns a transparent GIF
 * This is the simplest possible implementation for testing
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set the content type to a transparent 1x1 pixel GIF
  res.setHeader('Content-Type', 'image/gif');

  // Disable caching
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');

  // Create a 1x1 transparent GIF
  const pixel = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');

  // Return the pixel
  res.status(200).send(pixel);
}
