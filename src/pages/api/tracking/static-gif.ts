import type { NextApiRequest, NextApiResponse } from 'next';

// 1x1 transparent GIF in base64
const TRANSPARENT_GIF = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Set headers
    res.setHeader('Content-Type', 'image/gif');
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Log the request parameters for debugging
    console.log('Static GIF request:', {
      campaignId: req.query.cid,
      leadId: req.query.lid,
      timestamp: new Date().toISOString()
    });

    // End the response with the GIF data
    return res.end(TRANSPARENT_GIF);
  } catch (error) {
    console.error('Error serving tracking pixel:', error);
    // Even on error, try to return a valid GIF
    res.setHeader('Content-Type', 'image/gif');
    return res.end(TRANSPARENT_GIF);
  }
}
