import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { TrackingFactoryService } from '@/services/tracking-factory.service';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // This endpoint handles bounce notifications from email providers
  // It can be called via webhook or processed from incoming bounce emails

  // Add CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, GET');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Support both POST (webhooks) and GET (email parsing) methods
  if (req.method !== 'POST' && req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Log the full request for debugging
    logger.info('Bounce tracking request received', {
      method: req.method,
      query: req.query,
      body: req.body,
      headers: req.headers,
      url: req.url
    });

    // Extract bounce information from the request body or query parameters
    // This supports multiple email provider formats
    let campaignId, leadId, email, bounceType, bounceReason;

    if (req.method === 'POST') {
      // Extract from POST body (webhook format)
      ({ campaignId, leadId, email, bounceType, bounceReason } = req.body);
    } else {
      // Extract from GET query parameters (email parsing format)
      ({ campaignId, leadId, email, bounceType, bounceReason } = req.query);
    }

    if (!campaignId || !leadId) {
      logger.warn('Invalid bounce tracking request', { body: req.body });
      return res.status(400).json({ error: 'Invalid parameters' });
    }

    // Validate bounce type
    if (!bounceType) {
      bounceType = 'hard'; // Default to hard bounce if not specified
    }

    // Extract step ID if provided
    let stepId;
    if (req.method === 'POST' && req.body.stepId) {
      stepId = req.body.stepId;
    } else if (req.method === 'GET' && req.query.stepId) {
      stepId = req.query.stepId;
    }

    // Use the TrackingFactoryService to track the bounce
    const success = await TrackingFactoryService.trackBounce(
      campaignId,
      leadId,
      bounceType === 'hard' ? 'hard' : 'soft',
      bounceReason,
      { stepId }
    );

    if (!success) {
      logger.warn('Failed to track email bounce', { campaignId, leadId, bounceType });
      return res.status(500).json({ error: 'Failed to track email bounce' });
    }

    logger.info('Email bounce tracked', {
      campaignId,
      leadId,
      email,
      bounceType,
      bounceReason
    });

    return res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error tracking email bounce', {
      error: error instanceof Error ? error.message : 'Unknown error',
      body: req.body,
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}
