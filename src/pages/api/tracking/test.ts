import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { addTrackingToEmail } from '@/lib/email-tracking';
import { DnsTrackingService } from '@/services/dns-tracking.service';

/**
 * API endpoint for testing email tracking
 * This endpoint allows testing the tracking system by generating test emails with tracking
 * and simulating tracking events
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // This endpoint is for testing only and should be disabled in production
  if (process.env.NODE_ENV === 'production') {
    return res.status(404).json({ error: 'Not found' });
  }

  try {
    const { action } = req.query;

    switch (action) {
      case 'generate-test-email':
        return await generateTestEmail(req, res);
      case 'simulate-open':
        return await simulateOpen(req, res);
      case 'simulate-click':
        return await simulateClick(req, res);
      case 'simulate-bounce':
        return await simulateBounce(req, res);
      default:
        return res.status(400).json({
          error: 'Invalid action',
          validActions: [
            'generate-test-email',
            'simulate-open',
            'simulate-click',
            'simulate-bounce'
          ]
        });
    }
  } catch (error) {
    logger.error('Error in tracking test endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      query: req.query,
      body: req.body,
    });

    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Generate a test email with tracking
 */
async function generateTestEmail(req: NextApiRequest, res: NextApiResponse) {
  const { campaignId, leadId, organizationId } = req.query;

  if (!campaignId || !leadId || !organizationId ||
      typeof campaignId !== 'string' ||
      typeof leadId !== 'string' ||
      typeof organizationId !== 'string') {
    return res.status(400).json({
      error: 'Missing required parameters',
      required: ['campaignId', 'leadId', 'organizationId']
    });
  }

  // Get tracking domains
  const trackingDomains = await DnsTrackingService.getTrackingDomains(organizationId);

  // Create a sample HTML email with multiple tracking test elements
  const timestamp = new Date().toISOString();
  const sampleHtml = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Test Email</title>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <h1 style="color: #2c3e50; margin-top: 0;">Email Tracking Test</h1>
          <p>This is a test email to verify tracking functionality. Generated at: ${timestamp}</p>
          <p><strong>Campaign ID:</strong> ${campaignId}</p>
          <p><strong>Lead ID:</strong> ${leadId}</p>
        </div>

        <div style="margin-bottom: 20px;">
          <h2 style="color: #3498db;">Test Links</h2>
          <p>Click the links below to test tracking:</p>
          <ul>
            <li><a href="https://example.com/page1" style="color: #3498db;">Regular Link 1</a></li>
            <li><a href="https://example.com/page2" style="color: #3498db;">Regular Link 2</a></li>
            <li><a href="https://example.com/unsubscribe" style="color: #e74c3c;">Unsubscribe Link</a></li>
          </ul>
        </div>

        <div style="margin-bottom: 20px;">
          <h2 style="color: #3498db;">Images</h2>
          <p>The email contains tracking pixels that should be loaded when you open it:</p>
          <img src="https://via.placeholder.com/300x200" alt="Test Image" style="max-width: 100%; height: auto; display: block; margin: 10px 0;" />
        </div>

        <div style="border-top: 1px solid #ddd; padding-top: 20px; font-size: 12px; color: #777;">
          <p>This is a test email sent from the Avian Email platform.</p>
          <p>If you received this by mistake, please ignore it.</p>
        </div>
      </body>
    </html>
  `;

  // Add tracking to the email
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const emailWithTracking = addTrackingToEmail(
    sampleHtml,
    campaignId,
    leadId,
    baseUrl,
    {
      openDomain: trackingDomains.openDomain,
      clickDomain: trackingDomains.clickDomain
    }
  );

  return res.status(200).json({
    success: true,
    emailHtml: emailWithTracking,
    trackingDomains,
    baseUrl,
    testLinks: {
      openPixel: `${baseUrl}/api/tracking/pixel?cid=${campaignId}&lid=${leadId}`,
      clickLink: `${baseUrl}/api/tracking/link?cid=${campaignId}&lid=${leadId}&url=${encodeURIComponent('https://example.com/page1')}`,
      bounce: `${baseUrl}/api/tracking/bounce?campaignId=${campaignId}&leadId=${leadId}&bounceType=hard&bounceReason=Test%20bounce`
    }
  });
}

/**
 * Simulate an email open event
 */
async function simulateOpen(req: NextApiRequest, res: NextApiResponse) {
  const { campaignId, leadId } = req.query;

  if (!campaignId || !leadId || typeof campaignId !== 'string' || typeof leadId !== 'string') {
    return res.status(400).json({
      error: 'Missing required parameters',
      required: ['campaignId', 'leadId']
    });
  }

  // Find the campaign lead
  const campaignLead = await prisma.campaignLead.findFirst({
    where: {
      id: leadId,
      campaignId,
    },
    include: {
      stepActivities: {
        orderBy: {
          createdAt: 'desc',
        },
        take: 1,
      },
    },
  });

  if (!campaignLead || campaignLead.stepActivities.length === 0) {
    return res.status(404).json({ error: 'Campaign lead not found' });
  }

  const latestActivity = campaignLead.stepActivities[0];

  // Record the email open
  await prisma.stepActivity.create({
    data: {
      campaignLeadId: leadId,
      stepId: latestActivity.stepId,
      type: 'email_opened',
      status: 'opened',
      openedAt: new Date(),
      metadata: {
        userAgent: 'Test User Agent',
        ip: '127.0.0.1',
        timestamp: new Date().toISOString(),
        isTest: true
      },
    },
  });

  // Update the campaign lead status
  await prisma.campaignLead.update({
    where: { id: leadId },
    data: { status: 'active' },
  });

  // Update campaign metrics
  await prisma.campaign.update({
    where: { id: campaignId },
    data: {
      openedCount: {
        increment: 1
      }
    },
  });

  return res.status(200).json({
    success: true,
    message: 'Email open simulated successfully'
  });
}

/**
 * Simulate a link click event
 */
async function simulateClick(req: NextApiRequest, res: NextApiResponse) {
  const { campaignId, leadId, url } = req.query;

  if (!campaignId || !leadId || !url ||
      typeof campaignId !== 'string' ||
      typeof leadId !== 'string' ||
      typeof url !== 'string') {
    return res.status(400).json({
      error: 'Missing required parameters',
      required: ['campaignId', 'leadId', 'url']
    });
  }

  // Find the campaign lead
  const campaignLead = await prisma.campaignLead.findFirst({
    where: {
      id: leadId,
      campaignId,
    },
    include: {
      stepActivities: {
        orderBy: {
          createdAt: 'desc',
        },
        take: 1,
      },
    },
  });

  if (!campaignLead || campaignLead.stepActivities.length === 0) {
    return res.status(404).json({ error: 'Campaign lead not found' });
  }

  const latestActivity = campaignLead.stepActivities[0];

  // Record the link click
  await prisma.stepActivity.create({
    data: {
      campaignLeadId: leadId,
      stepId: latestActivity.stepId,
      type: 'link_clicked',
      status: 'clicked',
      clickedAt: new Date(),
      metadata: {
        url,
        userAgent: 'Test User Agent',
        ip: '127.0.0.1',
        timestamp: new Date().toISOString(),
        isTest: true
      },
    },
  });

  // Update the campaign lead status
  await prisma.campaignLead.update({
    where: { id: leadId },
    data: { status: 'active' },
  });

  // Update campaign metrics
  await prisma.campaign.update({
    where: { id: campaignId },
    data: {
      clickedCount: {
        increment: 1
      }
    },
  });

  return res.status(200).json({
    success: true,
    message: 'Link click simulated successfully',
    url
  });
}

/**
 * Simulate a bounce event
 */
async function simulateBounce(req: NextApiRequest, res: NextApiResponse) {
  const { campaignId, leadId, bounceType, bounceReason } = req.query;

  if (!campaignId || !leadId ||
      typeof campaignId !== 'string' ||
      typeof leadId !== 'string') {
    return res.status(400).json({
      error: 'Missing required parameters',
      required: ['campaignId', 'leadId']
    });
  }

  // Find the campaign lead
  const campaignLead = await prisma.campaignLead.findFirst({
    where: {
      id: leadId,
      campaignId,
    },
    include: {
      lead: true,
      stepActivities: {
        orderBy: {
          createdAt: 'desc',
        },
        take: 1,
      },
    },
  });

  if (!campaignLead || campaignLead.stepActivities.length === 0) {
    return res.status(404).json({ error: 'Campaign lead not found' });
  }

  const latestActivity = campaignLead.stepActivities[0];
  const bounceTypeValue = typeof bounceType === 'string' ? bounceType : 'hard';
  const bounceReasonValue = typeof bounceReason === 'string' ? bounceReason : 'Test bounce';

  // Update the campaign lead status to bounced
  await prisma.campaignLead.update({
    where: {
      id: leadId,
    },
    data: {
      status: 'bounced',
    },
  });

  // Record the bounce activity
  await prisma.stepActivity.create({
    data: {
      campaignLeadId: leadId,
      stepId: latestActivity.stepId,
      type: 'email_bounced',
      status: 'bounced',
      bouncedAt: new Date(),
      metadata: {
        bounceType: bounceTypeValue,
        bounceReason: bounceReasonValue,
        timestamp: new Date().toISOString(),
        isTest: true
      },
    },
  });

  // Update campaign metrics
  await prisma.campaign.update({
    where: { id: campaignId },
    data: {
      errorCount: {
        increment: 1
      }
    },
  });

  // Add to suppression list for hard bounces
  if (bounceTypeValue === 'hard' && campaignLead.lead?.email) {
    await prisma.suppressionList.upsert({
      where: { email: campaignLead.lead.email },
      update: {
        reason: `Bounce: ${bounceReasonValue}`
      },
      create: {
        email: campaignLead.lead.email,
        reason: `Bounce: ${bounceReasonValue}`
      }
    });
  }

  return res.status(200).json({
    success: true,
    message: 'Bounce simulated successfully',
    bounceType: bounceTypeValue,
    bounceReason: bounceReasonValue
  });
}
