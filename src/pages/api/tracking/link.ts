import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { TrackingFactoryService } from '@/services/tracking-factory.service';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Add CORS headers to ensure the redirect works in any context
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Requested-With');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Get the target URL early so we can redirect even if tracking fails
  let targetUrl = '';
  try {
    if (req.query.url && typeof req.query.url === 'string') {
      targetUrl = decodeURIComponent(req.query.url);
    }
  } catch (decodeError) {
    logger.error('[TRACKING API] Error decoding URL', {
      error: decodeError instanceof Error ? decodeError.message : 'Unknown error',
      url: req.query.url
    });
  }

  try {
    // Log the full request for debugging
    console.log(`🔍 LINK TRACKING REQUEST RECEIVED at ${new Date().toISOString()}`);
    console.log(`🔍 URL: ${req.url}`);
    console.log(`🔍 Query params:`, JSON.stringify(req.query));
    console.log(`🔍 User-Agent: ${req.headers['user-agent']}`);
    console.log(`🔍 Referer: ${req.headers['referer']}`);
    console.log(`🔍 Host: ${req.headers['host']}`);
    console.log(`🔍 IP: ${req.headers['x-forwarded-for'] || req.socket.remoteAddress}`);
    console.log(`🔍 Request Method: ${req.method}`);
    console.log(`🔍 Request Headers:`, JSON.stringify(req.headers, null, 2));

    logger.info('[TRACKING API] Link tracking request received', {
      query: req.query,
      headers: {
        host: req.headers.host,
        referer: req.headers.referer,
        'user-agent': req.headers['user-agent'],
        'x-forwarded-for': req.headers['x-forwarded-for'],
        'x-forwarded-host': req.headers['x-forwarded-host'],
        'x-forwarded-proto': req.headers['x-forwarded-proto'],
        accept: req.headers['accept'],
        acceptLanguage: req.headers['accept-language'],
        cacheControl: req.headers['cache-control'],
        connection: req.headers['connection']
      },
      url: req.url,
      method: req.method,
      hostname: req.headers.host,
      timestamp: new Date().toISOString(),
      rawHeaders: req.rawHeaders,
      requestId: req.headers['x-request-id'],
      traceId: req.headers['x-trace-id']
    });

    const { cid, lid, url, stepId } = req.query;

    if (!cid || !lid || !url || typeof cid !== 'string' || typeof lid !== 'string' || typeof url !== 'string') {
      console.log(`⚠️ INVALID LINK TRACKING REQUEST - Missing or invalid parameters`);
      console.log(`⚠️ Campaign ID: ${cid || 'Missing'}`);
      console.log(`⚠️ Lead ID: ${lid || 'Missing'}`);
      console.log(`⚠️ URL: ${url || 'Missing'}`);
      console.log(`⚠️ Step ID: ${stepId || 'Not provided'}`);
      console.log(`⚠️ Timestamp: ${new Date().toISOString()}`);

      logger.warn('[TRACKING API] Invalid link tracking request', { 
        query: req.query,
        timestamp: new Date().toISOString(),
        userAgent: req.headers['user-agent'],
        ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress
      });

      // If we have a target URL, redirect anyway
      if (targetUrl) {
        logger.info('[TRACKING API] Redirecting despite invalid parameters', { targetUrl });
        return res.redirect(302, targetUrl);
      }

      return res.status(400).json({ error: 'Invalid parameters' });
    }

    // Convert stepId to string if it exists
    const stepIdStr = stepId ? (Array.isArray(stepId) ? stepId[0] : stepId) : undefined;

    logger.info('[TRACKING API] Processing valid link tracking request', {
      campaignId: cid,
      leadId: lid,
      url: url,
      stepId: stepIdStr,
      decodedUrl: targetUrl || decodeURIComponent(url)
    });

    // Use the TrackingFactoryService to track the click
    console.log(`💬 PROCESSING LINK CLICK TRACKING`);
    console.log(`💬 Campaign ID: ${cid}`);
    console.log(`💬 Lead ID: ${lid}`);
    console.log(`💬 URL: ${url}`);
    console.log(`💬 Step ID: ${stepIdStr || 'Not provided'}`);
    console.log(`💬 IP: ${req.headers['x-forwarded-for'] || req.socket.remoteAddress}`);
    console.log(`💬 User Agent: ${req.headers['user-agent']}`);
    console.log(`💬 Referrer: ${req.headers['referer'] || 'None'}`);
    console.log(`💬 Request Time: ${new Date().toISOString()}`);
    console.log(`💬 Request ID: ${req.headers['x-request-id'] || 'Not provided'}`);
    console.log(`💬 Request Trace: ${req.headers['x-trace-id'] || 'Not provided'}`);

    const success = await TrackingFactoryService.trackClick(
      cid,
      lid,
      url,
      {
        userAgent: req.headers['user-agent'] as string,
        ip: (req.headers['x-forwarded-for'] || req.socket.remoteAddress) as string,
        referrer: req.headers['referer'] as string,
        timestamp: new Date().toISOString(),
        stepId: stepIdStr
      }
    );

    if (!success) {
      console.log(`⚠️ LINK CLICK TRACKING FAILED`);
      console.log(`⚠️ Campaign ID: ${cid}`);
      console.log(`⚠️ Lead ID: ${lid}`);
      console.log(`⚠️ URL: ${url}`);
      console.log(`⚠️ Step ID: ${stepIdStr || 'Not provided'}`);
      console.log(`⚠️ Timestamp: ${new Date().toISOString()}`);
      console.log(`⚠️ User Agent: ${req.headers['user-agent']}`);
      console.log(`⚠️ IP: ${req.headers['x-forwarded-for'] || req.socket.remoteAddress}`);

      logger.warn('Failed to track link click', { 
        campaignId: cid, 
        leadId: lid, 
        url,
        timestamp: new Date().toISOString(),
        userAgent: req.headers['user-agent'],
        ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
        requestId: req.headers['x-request-id'],
        traceId: req.headers['x-trace-id']
      });
      // Still redirect to the original URL even if tracking fails
      const decodedUrl = decodeURIComponent(url);
      return res.redirect(302, decodedUrl);
    }

    // Get the campaign type to handle sequence campaigns properly
    const campaign = await prisma.campaign.findUnique({
      where: { id: cid },
      select: { type: true }
    });

    const isSequenceCampaign = campaign?.type === 'sequence';
    logger.info('Processing link click', {
      campaignId: cid,
      leadId: lid,
      url,
      campaignType: campaign?.type,
      isSequenceCampaign
    });

    // For sequence campaigns, we need to check if this click should trigger a condition
    if (isSequenceCampaign) {
      console.log(`🔄 PROCESSING SEQUENCE CAMPAIGN CLICK`);
      console.log(`🔄 Campaign ID: ${cid}`);
      console.log(`🔄 Lead ID: ${lid}`);
      console.log(`🔄 URL: ${url}`);
      console.log(`🔄 Step ID: ${stepIdStr || 'Not provided'}`);
      console.log(`🔄 Timestamp: ${new Date().toISOString()}`);

      logger.info('Processing sequence campaign click', { 
        campaignId: cid, 
        leadId: lid,
        url,
        stepId: stepIdStr,
        timestamp: new Date().toISOString(),
        userAgent: req.headers['user-agent'],
        ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
        requestId: req.headers['x-request-id'],
        traceId: req.headers['x-trace-id']
      });

      // Instead of processing the sequence immediately, queue it for background processing
      // This ensures the link redirect returns quickly while the sequence processing happens asynchronously
      try {
        // Create a record of the processing request
        await prisma.processingQueue.create({
          data: {
            type: 'sequence_condition_check',
            status: 'pending',
            payload: {
              campaignId: cid,
              leadId: lid,
              eventType: 'click',
              url: url,
              timestamp: new Date().toISOString()
            },
            priority: 1, // High priority
            createdAt: new Date()
          }
        }).catch(queueError => {
          // If the queue table doesn't exist yet, fall back to immediate processing
          logger.warn('Failed to queue sequence processing - falling back to immediate processing', {
            error: queueError instanceof Error ? queueError.message : 'Unknown error',
            campaignId: cid,
            leadId: lid
          });

          // Process immediately as fallback
          processSequenceImmediately(cid, lid);
        });

        logger.info('Queued sequence campaign processing for click event', { campaignId: cid, leadId: lid });
      } catch (queueError) {
        logger.error('Error queueing sequence campaign processing for click', {
          error: queueError instanceof Error ? queueError.message : 'Unknown error',
          campaignId: cid,
          leadId: lid,
          stack: queueError instanceof Error ? queueError.stack : undefined
        });

        // Fall back to immediate processing if queueing fails
        processSequenceImmediately(cid, lid);
      }
    }

    // Helper function to process sequence immediately if queueing fails
    async function processSequenceImmediately(campaignId, leadId) {
      const startTime = Date.now();
      try {
        logger.info('Starting immediate sequence campaign processing for click', { campaignId, leadId });
        // Import dynamically to avoid circular dependencies
        const { SequenceCampaignService } = await import('@/services/sequence-campaign.service');

        // Get the full campaign with all data needed for processing
        // Use optimized query with only the fields we need
        const fullCampaign = await prisma.campaign.findUnique({
          where: { id: campaignId },
          include: {
            steps: {
              orderBy: {
                position: 'asc',
              },
              include: {
                emailAccount: true,
                emailAccounts: {
                  include: {
                    emailAccount: true
                  }
                },
                conditionStep: true,
                conditionedSteps: true,
              },
            },
            leads: {
              where: { id: leadId }, // Only get the specific lead we need
              include: {
                lead: true,
                currentStep: true,
                stepActivities: {
                  orderBy: {
                    createdAt: 'desc',
                  },
                  include: {
                    step: true,
                  }
                },
              },
            },
          },
        });

        if (fullCampaign && fullCampaign.leads.length > 0) {
          const campaignLeadToProcess = fullCampaign.leads[0]; // We only queried for one lead
          logger.info('Processing lead in sequence after click', {
            campaignId,
            leadId,
            leadEmail: campaignLeadToProcess.lead.email
          });
          await SequenceCampaignService.processLeadInSequence(fullCampaign, campaignLeadToProcess);

          const duration = Date.now() - startTime;
          logger.info('Completed sequence processing for click', { campaignId, leadId, duration });
        } else {
          logger.warn('Campaign or lead not found for immediate sequence processing', {
            campaignId,
            leadId
          });
        }
      } catch (sequenceError) {
        const duration = Date.now() - startTime;
        logger.error('Error processing sequence campaign after click', {
          error: sequenceError instanceof Error ? sequenceError.message : 'Unknown error',
          campaignId,
          leadId,
          stack: sequenceError instanceof Error ? sequenceError.stack : undefined,
          duration
        });

        // Report the error to monitoring systems if available
        if (process.env.NODE_ENV === 'production') {
          try {
            // This could be integrated with error reporting services like Sentry
            console.error(`PRODUCTION ERROR: Sequence campaign click processing failed for ${campaignId}`);
          } catch (reportError) {
            // Just log and continue if error reporting fails
            logger.error(`Failed to report error to monitoring system`, {
              originalError: sequenceError instanceof Error ? sequenceError.message : 'Unknown error',
              reportError: reportError instanceof Error ? reportError.message : 'Unknown error'
            });
          }
        }
      }
    }

    logger.info('[TRACKING API] Link click tracked successfully', {
      campaignId: cid,
      leadId: lid,
      url,
      decodedUrl: targetUrl || decodeURIComponent(url)
    });

    // Redirect to the original URL
    // Use the targetUrl we decoded earlier to ensure consistency
    const finalUrl = targetUrl || decodeURIComponent(url);

    // Validate the URL to ensure it's safe to redirect to
    if (!finalUrl.startsWith('http://') && !finalUrl.startsWith('https://')) {
      logger.warn('[TRACKING API] Unsafe redirect URL detected', { finalUrl });
      return res.status(400).json({ error: 'Invalid redirect URL' });
    }

    logger.info('[TRACKING API] Redirecting to original URL', {
      finalUrl,
      statusCode: 302
    });

    // Set cache control headers to prevent caching of the redirect
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    return res.redirect(302, finalUrl);
  } catch (error) {
    logger.error('[TRACKING API] Error tracking link click', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      query: req.query,
      headers: {
        host: req.headers.host,
        referer: req.headers.referer,
        'user-agent': req.headers['user-agent']
      },
      timestamp: new Date().toISOString()
    });

    // If there's an error, try to redirect to the original URL if possible
    // Use the targetUrl we decoded earlier if available
    if (targetUrl) {
      logger.info('[TRACKING API] Redirecting to original URL after error', {
        targetUrl,
        statusCode: 302
      });

      // Set cache control headers to prevent caching of the redirect
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');

      return res.redirect(302, targetUrl);
    }

    // If we can't redirect, return an error
    return res.status(500).json({ error: 'Internal server error' });
  }
}
