import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '@/lib/logger';

/**
 * A simplified tracking pixel endpoint that just returns a transparent GIF
 * This is useful for testing the basic functionality without database operations
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Log the request
  logger.info('Simple tracking pixel request received', {
    query: req.query,
    headers: req.headers,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString()
  });

  // Set the content type to a transparent 1x1 pixel GIF
  res.setHeader('Content-Type', 'image/gif');

  // Disable caching with strong cache-control headers
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');

  // Add CORS headers to ensure the pixel loads in any email client
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Requested-With');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Create a 1x1 transparent GIF
  const pixel = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');

  // Extract parameters for logging
  const { cid, lid } = req.query;

  // Log the tracking attempt
  logger.info('Simple tracking pixel processed', {
    campaignId: cid,
    leadId: lid,
    userAgent: req.headers['user-agent'],
    ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
    timestamp: new Date().toISOString()
  });

  // Return the tracking pixel
  return res.status(200).send(pixel);
}
