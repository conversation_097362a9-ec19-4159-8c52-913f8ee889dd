import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '@/lib/logger';

/**
 * Health check endpoint for tracking routes
 * This endpoint helps diagnose issues with tracking routes
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Log the request for debugging
    logger.info('Tracking health check request', {
      method: req.method,
      url: req.url,
      headers: req.headers,
      query: req.query,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel'
    });

    // Return a success response
    return res.status(200).json({
      status: 'healthy',
      message: 'Tracking routes are working correctly',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
      headers: {
        host: req.headers.host,
        userAgent: req.headers['user-agent'],
        referer: req.headers.referer
      }
    });
  } catch (error) {
    // Log the error
    logger.error('Error in tracking health check', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });

    // Return an error response
    return res.status(500).json({
      status: 'error',
      message: 'Error in tracking health check',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
