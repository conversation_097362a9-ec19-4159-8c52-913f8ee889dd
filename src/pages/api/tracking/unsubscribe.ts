import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { TrackingFactoryService } from '@/services/tracking-factory.service';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { cid, lid } = req.query;

    if (!cid || !lid || typeof cid !== 'string' || typeof lid !== 'string') {
      logger.warn('Invalid unsubscribe request', { query: req.query });
      return res.status(400).json({ error: 'Invalid parameters' });
    }

    // Use the TrackingFactoryService to track the unsubscribe
    const success = await TrackingFactoryService.trackUnsubscribe(
      cid,
      lid,
      {
        userAgent: req.headers['user-agent'] as string,
        ip: (req.headers['x-forwarded-for'] || req.socket.remoteAddress) as string,
        timestamp: new Date().toISOString(),
        reason: 'User clicked unsubscribe link'
      }
    );

    if (!success) {
      logger.warn('Failed to process unsubscribe request', { campaignId: cid, leadId: lid });
      return res.status(500).json({ error: 'Failed to process unsubscribe request' });
    }

    // Get the lead email for the confirmation page
    const lead = await prisma.campaignLead.findFirst({
      where: {
        id: lid,
        campaignId: cid,
      },
      include: {
        lead: {
          select: {
            email: true
          }
        }
      }
    });

    logger.info('Lead unsubscribed', {
      campaignId: cid,
      leadId: lid,
      email: lead?.lead?.email || 'unknown'
    });

    // Render a simple unsubscribe confirmation page
    res.setHeader('Content-Type', 'text/html');
    return res.status(200).send(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Unsubscribe Confirmation</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
            }
            .container {
              background-color: #f9f9f9;
              border-radius: 5px;
              padding: 20px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            h1 {
              color: #333;
            }
            .success {
              color: #4CAF50;
              font-weight: bold;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>Unsubscribe Successful</h1>
            <p class="success">You have been successfully unsubscribed.</p>
            <p>You will no longer receive emails from this campaign.</p>
            <p>If you unsubscribed by mistake, please contact the sender.</p>
          </div>
        </body>
      </html>
    `);
  } catch (error) {
    logger.error('Error processing unsubscribe', {
      error: error instanceof Error ? error.message : 'Unknown error',
      query: req.query,
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}
