import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

/**
 * Test endpoint for email tracking pixel
 * This endpoint returns a 1x1 transparent GIF and logs the request
 * It's used for testing the tracking system without requiring a real campaign
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set headers first to ensure they're always included, even if an error occurs
  // Set the content type to a transparent 1x1 pixel GIF
  res.setHeader('Content-Type', 'image/gif');

  // Disable caching with strong cache-control headers
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');

  // Add CORS headers to ensure the pixel loads in any email client
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Requested-With');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours

  // Create a 1x1 transparent GIF
  const pixel = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).send(pixel);
  }

  // Log the request for debugging with console.log for immediate visibility in Vercel logs
  console.log(`🧪 TEST TRACKING PIXEL REQUEST RECEIVED at ${new Date().toISOString()}`);
  console.log(`🧪 URL: ${req.url}`);
  console.log(`🧪 Query params:`, JSON.stringify(req.query));
  console.log(`🧪 User-Agent: ${req.headers['user-agent']}`);
  console.log(`🧪 Referer: ${req.headers['referer']}`);
  console.log(`🧪 Host: ${req.headers['host']}`);
  
  logger.info('Test tracking pixel request received', {
    query: req.query,
    headers: {
      userAgent: req.headers['user-agent'],
      referer: req.headers['referer'],
      host: req.headers['host']
    },
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString()
  });

  try {
    // Extract test parameters
    const { test, email } = req.query;

    console.log(`🧪 TEST PARAMETER: ${test}`);
    console.log(`🧪 EMAIL PARAMETER: ${email}`);

    // Return the tracking pixel
    return res.status(200).send(pixel);
  } catch (error) {
    // Log detailed error information
    console.error(`❌ ERROR IN TEST TRACKING:`, error instanceof Error ? error.message : 'Unknown error');
    logger.error('Error in test tracking pixel', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      query: req.query,
      headers: {
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        host: req.headers['host'],
      },
      timestamp: new Date().toISOString()
    });

    // Always return the pixel even if there's an error to avoid breaking email clients
    return res.status(200).send(pixel);
  }
}
