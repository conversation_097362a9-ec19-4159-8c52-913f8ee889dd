import type { NextApiRequest, NextApiResponse } from 'next';

// 1x1 transparent GIF in base64
const TRANSPARENT_GIF_BASE64 = 'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set headers
  res.setHeader('Content-Type', 'image/gif');
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  
  // Create the GIF buffer
  const buffer = Buffer.from(TRANSPARENT_GIF_BASE64, 'base64');
  
  // Send the response
  res.status(200).send(buffer);
}
