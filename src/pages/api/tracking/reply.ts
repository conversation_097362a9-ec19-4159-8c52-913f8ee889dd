import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { TrackingFactoryService } from '@/services/tracking-factory.service';
import { TrackingService } from '@/services/tracking.service'; // Keep for parseReplyTrackingAddress

/**
 * Email reply tracking endpoint
 * This endpoint handles incoming email replies and tracks them for campaigns
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Add CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, GET');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Support both POST (webhooks) and GET (email parsing) methods
  if (req.method !== 'POST' && req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Log the full request for debugging
    logger.info('Reply tracking request received', {
      method: req.method,
      query: req.query,
      body: req.body,
      headers: req.headers,
      url: req.url
    });

    // Extract reply information from the request body or query parameters
    let replyTo, messageId, subject, text, html, from, stepId;

    if (req.method === 'POST') {
      // Extract from POST body (webhook format)
      ({ replyTo, messageId, subject, text, html, from, stepId } = req.body);
    } else {
      // Extract from GET query parameters (email parsing format)
      ({ replyTo, messageId, subject, text, html, from, stepId } = req.query);
    }

    if (!replyTo || !messageId) {
      logger.warn('Invalid reply tracking request', { body: req.body, query: req.query });
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Parse the reply-to address to extract campaign and lead IDs
    const { campaignId, leadId } = TrackingService.parseReplyTrackingAddress(replyTo);

    if (!campaignId || !leadId) {
      logger.warn('Could not parse campaign or lead ID from reply-to address', { replyTo });
      return res.status(400).json({ error: 'Invalid reply-to address format' });
    }

    // Track the reply using the TrackingFactoryService
    const success = await TrackingFactoryService.trackReply(
      campaignId,
      leadId,
      messageId,
      {
        subject,
        text,
        html,
        from,
        inReplyTo: messageId
      },
      { stepId }
    );

    if (!success) {
      logger.warn('Failed to track email reply', { campaignId, leadId, messageId });
      return res.status(500).json({ error: 'Failed to track email reply' });
    }

    logger.info('Email reply tracked', {
      campaignId,
      leadId,
      messageId,
      from
    });

    return res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error tracking email reply', {
      error: error instanceof Error ? error.message : 'Unknown error',
      body: req.body,
      query: req.query,
      stack: error instanceof Error ? error.stack : undefined
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}
