import { a<PERSON><PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { stripe } from '@/config/stripe';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  const { planId } = req.body;

  if (!planId) {
    return res.status(400).json({ error: 'Plan ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        ownedOrganization: {
          include: {
            subscription: true,
          },
        },
      },
    });

    if (!user?.ownedOrganization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const { customerId } = user.ownedOrganization;
    const subscription = user.ownedOrganization.subscription;

    if (!customerId) {
      return res.status(404).json({ error: 'No Stripe customer found' });
    }

    // For demonstration purposes, we'll just update the plan ID in the database
    // In a real implementation, you would update the subscription in Stripe
    await prisma.organization.update({
      where: { id: user.ownedOrganization.id },
      data: { planId },
    });

    // If there's an existing subscription, update it
    if (subscription) {
      // In a real implementation, you would update the subscription in Stripe
      // For now, we'll just update the database
      await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          stripePriceId: planId,
          // In a real implementation, you would update other fields like status, etc.
        },
      });
    }

    return res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error in update subscription plan endpoint', {
      method,
      userId: req.user.id,
      planId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
