import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { IncomingForm } from 'formidable';
import fs from 'fs';
import { parse } from 'csv-parse';
import * as path from 'path';
import * as os from 'os';

// Disable the default body parser to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Create upload directory if it doesn't exist
    const uploadDir = path.join(os.tmpdir(), 'avian-uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Parse the form data
    const form = new IncomingForm({
      uploadDir,
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB limit for CSV files
    });

    const [fields, files] = await new Promise<[any, any]>((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        resolve([fields, files]);
      });
    });

    // Get the list name from the form data or use the file name
    const listName = fields.listName?.[0] ||
                     (files.file?.[0]?.originalFilename?.replace(/\.[^/.]+$/, '') || 'Imported Leads') +
                     ' - ' + new Date().toLocaleDateString();

    const file = files.file?.[0] || null;
    if (!file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const filePath = file.filepath || file.path;

    logger.info('Processing CSV import file', {
      originalName: file.originalFilename,
      filePath,
      fileSize: file.size,
      mimeType: file.mimetype,
      fileExists: fs.existsSync(filePath),
    });

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Uploaded file not found at path: ${filePath}`);
    }

    // Read the CSV file
    let fileContent: string;
    try {
      fileContent = fs.readFileSync(filePath, 'utf8');
    } finally {
      // Clean up temporary file
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          logger.info('Cleaned up temporary CSV file', { filePath });
        }
      } catch (cleanupError) {
        logger.warn('Failed to clean up temporary CSV file', {
          filePath,
          error: cleanupError instanceof Error ? cleanupError.message : 'Unknown error',
        });
      }
    }

    // Parse the CSV
    const records: any[] = await new Promise((resolve, reject) => {
      parse(fileContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
      }, (err, records) => {
        if (err) reject(err);
        resolve(records);
      });
    });

    if (records.length === 0) {
      return res.status(400).json({ error: 'No records found in the CSV file' });
    }

    // Process and validate the records
    const processedRecords = [];
    const errors = [];

    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      const rowNumber = i + 2; // +2 because of 0-indexing and header row

      // Check for email (required)
      if (!record.email && !record.Email) {
        errors.push(`Row ${rowNumber}: Missing email address`);
        continue;
      }

      const email = (record.email || record.Email || '').trim();
      if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        errors.push(`Row ${rowNumber}: Invalid email address - ${email}`);
        continue;
      }

      // Process name fields
      let firstName = null;
      let lastName = null;

      // Check for first name and last name columns
      const hasFirstName = 'firstName' in record || 'FirstName' in record || 'first_name' in record || 'First Name' in record || 'first name' in record;
      const hasLastName = 'lastName' in record || 'LastName' in record || 'last_name' in record || 'Last Name' in record || 'last name' in record;

      // Get first name from various possible column names
      if (hasFirstName) {
        firstName = (record.firstName || record.FirstName || record.first_name || record['First Name'] || record['first name'] || '').trim();
      }

      // Get last name from various possible column names
      if (hasLastName) {
        lastName = (record.lastName || record.LastName || record.last_name || record['Last Name'] || record['last name'] || '').trim();
      }

      // If we have a name/Name column but no first/last name columns
      if ((!hasFirstName || !hasLastName) && (record.name || record.Name)) {
        const fullName = (record.name || record.Name || '').trim();
        const nameParts = fullName.split(' ');

        // If we don't have a first name yet
        if (!hasFirstName) {
          if (nameParts.length >= 1) {
            firstName = nameParts[0];
          }
        }

        // If we don't have a last name yet and there are multiple parts
        if (!hasLastName && nameParts.length > 1) {
          lastName = nameParts.slice(1).join(' ');
        }
      }

      // If we have a single column with both first and last name
      if (!hasFirstName && !hasLastName && (record['full name'] || record['Full Name'] || record.fullName || record.FullName)) {
        const fullName = (record['full name'] || record['Full Name'] || record.fullName || record.FullName || '').trim();
        const nameParts = fullName.split(' ');

        if (nameParts.length === 1) {
          firstName = nameParts[0];
        } else if (nameParts.length > 1) {
          firstName = nameParts[0];
          lastName = nameParts.slice(1).join(' ');
        }
      }

      // Process other fields
      const company = (record.company || record.Company || '').trim() || null;
      const phone = (record.phone || record.Phone || '').trim() || null;

      processedRecords.push({
        email,
        firstName,
        lastName,
        company,
        phone,
        status: 'active',
        source: 'import',
      });
    }

    if (processedRecords.length === 0) {
      return res.status(400).json({
        error: 'No valid records found in the CSV file',
        details: errors
      });
    }

    // Get existing leads to avoid duplicates
    const existingLeads = await prisma.lead.findMany({
      where: {
        email: { in: processedRecords.map(record => record.email) },
        organizationId,
      },
      select: { email: true },
    });

    const existingEmails = new Set(existingLeads.map(lead => lead.email));

    // Filter out duplicates
    const newRecords = processedRecords.filter(record => !existingEmails.has(record.email));
    const duplicates = processedRecords.filter(record => existingEmails.has(record.email));

    // Create or find the list
    let leadList = await prisma.leadList.findFirst({
      where: {
        name: listName,
        organizationId,
      },
    });

    if (!leadList) {
      leadList = await prisma.leadList.create({
        data: {
          name: listName,
          description: `Imported from ${files.file?.[0]?.originalFilename || 'CSV'} on ${new Date().toLocaleString()}`,
          source: 'import',
          organization: {
            connect: { id: organizationId }
          }
        },
      });
    }

    // Create the leads
    if (newRecords.length > 0) {
      await prisma.lead.createMany({
        data: newRecords.map(record => ({
          ...record,
          organizationId,
          listId: leadList.id,
        })),
        skipDuplicates: true,
      });
    }

    return res.status(200).json({
      total: records.length,
      valid: processedRecords.length,
      imported: newRecords.length,
      duplicates: duplicates.length,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    logger.error('Error in leads import endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
