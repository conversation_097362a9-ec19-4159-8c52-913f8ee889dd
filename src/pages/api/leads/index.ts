import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'GET':
        const leads = await prisma.lead.findMany({
          where: { organizationId },
          orderBy: { createdAt: 'desc' },
        });
        return res.status(200).json(leads);

      case 'POST':
        const { email, firstName, lastName, company, phone, status, source, tags } = req.body;

        if (!email) {
          return res.status(400).json({ error: 'Email is required' });
        }

        // Check if lead already exists
        const existingLead = await prisma.lead.findFirst({
          where: {
            email,
            organizationId,
          },
        });

        if (existingLead) {
          return res.status(400).json({ error: 'Lead with this email already exists' });
        }

        const lead = await prisma.lead.create({
          data: {
            email,
            firstName: firstName || null,
            lastName: lastName || null,
            company: company || null,
            phone: phone || null,
            status: status || 'active',
            source: source || 'manual',
            tags: tags || [],
            organizationId,
          },
        });

        return res.status(201).json(lead);

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in leads endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
