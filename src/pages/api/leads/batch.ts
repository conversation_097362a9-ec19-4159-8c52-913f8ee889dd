import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Get the lead IDs from the request body
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ error: 'Lead IDs are required' });
    }

    // Verify that all leads belong to the user's organization
    const leads = await prisma.lead.findMany({
      where: {
        id: { in: ids },
        organizationId,
      },
      select: { id: true },
    });

    if (leads.length !== ids.length) {
      return res.status(403).json({ error: 'Some leads do not belong to your organization' });
    }

    switch (method) {
      case 'DELETE':
        // Delete the leads
        await prisma.lead.deleteMany({
          where: {
            id: { in: ids },
            organizationId,
          },
        });

        return res.status(200).json({ success: true, count: ids.length });

      case 'PATCH':
        const { status, tags } = req.body;

        if (!status && !tags) {
          return res.status(400).json({ error: 'Status or tags are required' });
        }

        // Update the leads
        await prisma.lead.updateMany({
          where: {
            id: { in: ids },
            organizationId,
          },
          data: {
            status: status || undefined,
            // Note: updateMany doesn't support array operations, so we can't update tags here
          },
        });

        // If tags are provided, we need to update each lead individually
        if (tags && Array.isArray(tags)) {
          for (const id of ids) {
            const lead = await prisma.lead.findUnique({
              where: { id },
              select: { tags: true },
            });

            if (lead) {
              // Add new tags without duplicates
              const newTags = [...new Set([...lead.tags, ...tags])];
              
              await prisma.lead.update({
                where: { id },
                data: { tags: newTags },
              });
            }
          }
        }

        return res.status(200).json({ success: true, count: ids.length });

      default:
        res.setHeader('Allow', ['DELETE', 'PATCH']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in batch leads endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
