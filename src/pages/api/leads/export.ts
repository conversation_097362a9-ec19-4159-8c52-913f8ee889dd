import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { stringify } from 'csv-stringify/sync';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Get all leads for the organization
    const leads = await prisma.lead.findMany({
      where: { organizationId },
      orderBy: { createdAt: 'desc' },
    });

    // Format the leads for CSV export
    const formattedLeads = leads.map(lead => ({
      email: lead.email,
      firstName: lead.firstName || '',
      lastName: lead.lastName || '',
      company: lead.company || '',
      phone: lead.phone || '',
      status: lead.status,
      source: lead.source,
      tags: lead.tags.join(', '),
      createdAt: lead.createdAt.toISOString(),
    }));

    // Generate CSV
    const csv = stringify(formattedLeads, {
      header: true,
      columns: [
        'email',
        'firstName',
        'lastName',
        'company',
        'phone',
        'status',
        'source',
        'tags',
        'createdAt',
      ],
    });

    // Set headers for file download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=leads.csv');
    
    return res.status(200).send(csv);
  } catch (error) {
    logger.error('Error in leads export endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
