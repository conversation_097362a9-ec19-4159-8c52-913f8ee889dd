import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Lead ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the lead belongs to the user's organization
    const lead = await prisma.lead.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!lead) {
      return res.status(404).json({ error: 'Lead not found' });
    }

    switch (method) {
      case 'GET':
        return res.status(200).json(lead);

      case 'PUT':
        const { email, firstName, lastName, company, phone, status, source, tags } = req.body;

        if (email) {
          // Check if another lead with the same email exists
          const existingLead = await prisma.lead.findFirst({
            where: {
              email,
              organizationId,
              id: { not: id },
            },
          });

          if (existingLead) {
            return res.status(400).json({ error: 'Another lead with this email already exists' });
          }
        }

        const updatedLead = await prisma.lead.update({
          where: { id },
          data: {
            email: email || undefined,
            firstName: firstName !== undefined ? firstName : undefined,
            lastName: lastName !== undefined ? lastName : undefined,
            company: company !== undefined ? company : undefined,
            phone: phone !== undefined ? phone : undefined,
            status: status || undefined,
            source: source || undefined,
            tags: tags || undefined,
          },
        });

        return res.status(200).json(updatedLead);

      case 'DELETE':
        await prisma.lead.delete({
          where: { id },
        });

        return res.status(204).end();

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in lead endpoint', {
      method,
      userId: req.user.id,
      leadId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
