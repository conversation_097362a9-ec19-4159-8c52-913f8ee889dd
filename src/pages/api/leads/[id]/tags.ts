import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Lead ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the lead belongs to the user's organization
    const lead = await prisma.lead.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!lead) {
      return res.status(404).json({ error: 'Lead not found' });
    }

    switch (method) {
      case 'POST':
        const { tag } = req.body;

        if (!tag || typeof tag !== 'string') {
          return res.status(400).json({ error: 'Tag is required' });
        }

        // Check if the tag already exists
        if (lead.tags.includes(tag)) {
          return res.status(400).json({ error: 'Tag already exists' });
        }

        // Add the tag
        const updatedLead = await prisma.lead.update({
          where: { id },
          data: {
            tags: {
              push: tag,
            },
          },
        });

        return res.status(200).json(updatedLead);

      case 'DELETE':
        const { tag: tagToRemove } = req.body;

        if (!tagToRemove || typeof tagToRemove !== 'string') {
          return res.status(400).json({ error: 'Tag is required' });
        }

        // Check if the tag exists
        if (!lead.tags.includes(tagToRemove)) {
          return res.status(400).json({ error: 'Tag not found' });
        }

        // Remove the tag
        const updatedLeadAfterRemove = await prisma.lead.update({
          where: { id },
          data: {
            tags: lead.tags.filter(t => t !== tagToRemove),
          },
        });

        return res.status(200).json(updatedLeadAfterRemove);

      default:
        res.setHeader('Allow', ['POST', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in lead tags endpoint', {
      method,
      userId: req.user.id,
      leadId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
