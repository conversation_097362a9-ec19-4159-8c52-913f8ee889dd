import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { DnsTrackingService } from '@/services/dns-tracking.service';
import { z } from 'zod';

const dnsTrackingSchema = z.object({
  openTrackingDomain: z.string().optional(),
  clickTrackingDomain: z.string().optional(),
  bounceTrackingDomain: z.string().optional(),
});

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  
  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  // Get the user's organization
  const organizationMember = await prisma.organizationMember.findFirst({
    where: { userId: req.user.id },
    include: { organization: true },
  });
  
  if (!organizationMember) {
    return res.status(404).json({ error: 'Organization not found' });
  }
  
  const organizationId = organizationMember.organizationId;
  
  try {
    switch (method) {
      case 'GET':
        // Get DNS tracking configuration
        const config = await DnsTrackingService.getConfig(organizationId);
        
        if (!config) {
          return res.status(200).json({
            organizationId,
            openTrackingDomain: null,
            clickTrackingDomain: null,
            bounceTrackingDomain: null,
            isVerified: false,
            verifiedAt: null,
          });
        }
        
        return res.status(200).json(config);
        
      case 'POST':
        // Validate request body
        const validationResult = dnsTrackingSchema.safeParse(req.body);
        
        if (!validationResult.success) {
          return res.status(400).json({ error: 'Invalid request body', details: validationResult.error });
        }
        
        const { openTrackingDomain, clickTrackingDomain, bounceTrackingDomain } = validationResult.data;
        
        // Save DNS tracking configuration
        const updatedConfig = await DnsTrackingService.saveConfig({
          organizationId,
          openTrackingDomain,
          clickTrackingDomain,
          bounceTrackingDomain,
          isVerified: false, // Reset verification status when updating domains
        });
        
        return res.status(200).json(updatedConfig);
        
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in DNS tracking endpoint', {
      method,
      userId: req.user.id,
      organizationId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    
    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
