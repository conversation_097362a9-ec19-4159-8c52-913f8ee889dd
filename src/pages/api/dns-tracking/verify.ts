import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { DnsTrackingService } from '@/services/dns-tracking.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Get the user's organization
  const organizationMember = await prisma.organizationMember.findFirst({
    where: { userId: req.user.id },
    include: { organization: true },
  });

  if (!organizationMember) {
    return res.status(404).json({ error: 'Organization not found' });
  }

  const organizationId = organizationMember.organizationId;

  try {
    if (method !== 'POST') {
      res.setHeader('Allow', ['POST']);
      return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }

    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Verify DNS configuration
    const verificationResult = await DnsTrackingService.verifyDnsConfig(organizationId);

    // Get the updated config
    const config = await DnsTrackingService.getConfig(organizationId);

    // Check if records match but verification failed
    const matchingButFailed = verificationResult.results.filter(result => {
      if (!result.actual || result.status === 'success') return false;
      // Normalize and compare
      const normalizedExpected = result.expected.replace(/\s+/g, '').toLowerCase();
      const normalizedActual = result.actual.replace(/\s+/g, '').toLowerCase();
      return normalizedExpected === normalizedActual;
    });

    // Log detailed verification results
    logger.info('DNS verification results', {
      organizationId,
      userId: req.user.id,
      success: verificationResult.success,
      resultsCount: verificationResult.results.length,
      successCount: verificationResult.results.filter(r => r.status === 'success').length,
      failureCount: verificationResult.results.filter(r => r.status === 'failure').length,
      matchingButFailedCount: matchingButFailed.length,
      isDevelopment
    });

    // If verification is successful, update the database to mark as verified
    if (verificationResult.success) {
      // Update the organization's DNS tracking configuration to mark as verified
      await prisma.dnsTrackingConfig.update({
        where: { organizationId },
        data: {
          isVerified: true,
          verifiedAt: new Date(),
        },
      });

      logger.info('DNS configuration verified and saved successfully', {
        organizationId,
        userId: req.user.id,
      });
    }

    // In development mode, if records match but verification failed, we'll mark as verified anyway
    // This is only for development convenience and will not happen in production
    if (isDevelopment && matchingButFailed.length > 0 && !verificationResult.success) {
      logger.info('Development mode: Force verifying matching DNS records', {
        organizationId,
        userId: req.user.id,
        matchingButFailedCount: matchingButFailed.length
      });

      // Only in development, update as verified
      await prisma.dnsTrackingConfig.update({
        where: { organizationId },
        data: {
          isVerified: true,
          verifiedAt: new Date(),
        },
      });

      // Get the updated config after force verification
      const updatedConfig = await DnsTrackingService.getConfig(organizationId);

      return res.status(200).json({
        success: true,
        message: 'DNS configuration verified successfully (development mode)',
        results: verificationResult.results,
        config: updatedConfig
      });
    }

    if (!verificationResult.success) {
      return res.status(200).json({
        success: false,
        message: 'One or more DNS records are not correctly configured. Please check your DNS settings and try again.',
        results: verificationResult.results,
        config
      });
    }

    // Config is already fetched above

    return res.status(200).json({
      success: true,
      message: 'DNS configuration verified successfully',
      results: verificationResult.results,
      config
    });

  } catch (error) {
    logger.error('Error in DNS verification endpoint', {
      method,
      userId: req.user.id,
      organizationId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
