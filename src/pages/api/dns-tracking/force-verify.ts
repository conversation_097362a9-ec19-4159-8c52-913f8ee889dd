import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { DnsTrackingService } from '@/services/dns-tracking.service';

/**
 * API endpoint to force DNS verification
 * This endpoint is used when automatic verification fails but the user confirms
 * that their DNS records are correctly configured
 */
export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Get the user's organization
  const organizationMember = await prisma.organizationMember.findFirst({
    where: { userId: req.user.id },
    include: { organization: true },
  });

  if (!organizationMember) {
    return res.status(404).json({ error: 'Organization not found' });
  }

  const organizationId = organizationMember.organizationId;

  try {
    if (method !== 'POST') {
      res.setHeader('Allow', ['POST']);
      return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }

    // Get the current DNS tracking configuration
    const config = await DnsTrackingService.getConfig(organizationId);
    
    if (!config) {
      return res.status(404).json({ error: 'DNS tracking configuration not found' });
    }

    // Force verification by directly updating the database
    await prisma.dnsTrackingConfig.update({
      where: { organizationId },
      data: {
        isVerified: true,
        verifiedAt: new Date(),
      },
    });

    logger.info('DNS configuration force-verified successfully', {
      organizationId,
      userId: req.user.id,
    });

    // Get the updated config
    const updatedConfig = await DnsTrackingService.getConfig(organizationId);

    return res.status(200).json({
      success: true,
      message: 'DNS configuration verified successfully (manual override)',
      config: updatedConfig
    });

  } catch (error) {
    logger.error('Error in DNS force verification endpoint', {
      method,
      userId: req.user.id,
      organizationId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
