import { NextApiRequest, NextApiResponse } from 'next';
import bcrypt from 'bcryptjs';
import { prisma } from '@/lib/prisma';
import { emailVerification } from '@/lib/email/verification';
import { logger } from '@/lib/logger';
import { StripeService } from '@/services/stripe.service';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { name, email, password, organizationName } = req.body;

    if (!email || !password || !name) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return res.status(400).json({ error: 'User already exists' });
    }

    // Start a transaction for user and organization creation
    const result = await prisma.$transaction(async (tx) => {
      const hashedPassword = await bcrypt.hash(password, 12);
      const user = await tx.user.create({
        data: {
          name,
          email,
          password: hashedPassword,
          emailVerified: null,
        },
      });

      const orgName = organizationName || `${name}'s Organization`;
      const organization = await tx.organization.create({
        data: {
          name: orgName,
          isPersonal: true,
          owner: {
            connect: { id: user.id }
          },
          members: {
            create: {
              userId: user.id,
              role: 'OWNER',
            }
          }
        },
      });

      return { user, organization };
    }, {
      timeout: 10000 // Increase timeout to 10 seconds
    });

    // Create Stripe customer outside the transaction
    if (process.env.STRIPE_SECRET_KEY) {
      try {
        const stripeCustomer = await StripeService.createCustomer(
          result.organization.id,
          email,
          result.organization.name
        );

        // Update organization with Stripe customer ID
        await prisma.organization.update({
          where: { id: result.organization.id },
          data: { customerId: stripeCustomer.id }
        });
      } catch (stripeError) {
        logger.error('Failed to create Stripe customer:', stripeError);
        // Continue without Stripe customer
      }
    }

    // Send verification email
    try {
      await emailVerification.sendVerificationEmail(result.user.id, email);
    } catch (emailError) {
      logger.error('Failed to send verification email:', emailError);
    }

    // Don't send password in response
    const { password: _, ...userWithoutPassword } = result.user;

    return res.status(201).json({
      user: userWithoutPassword,
      organization: result.organization
    });

  } catch (error) {
    logger.error('Registration error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
