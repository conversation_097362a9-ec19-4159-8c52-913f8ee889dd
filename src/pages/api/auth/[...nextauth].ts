import NextAuth, { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import CredentialsProvider from 'next-auth/providers/credentials';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Please enter an email and password')
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email }
        });

        if (!user || !user.password) {
          throw new Error('No user found with this email')
        }

        // Check if email is verified
        // The emailVerified field could be null or a Date object
        console.log('Email verification status for', user.email, ':', user.emailVerified);
        console.log('Email verification type:', typeof user.emailVerified);

        try {
          // First try to check directly from the user object
          const isVerified = user.emailVerified instanceof Date ||
                            (user.emailVerified && !isNaN(new Date(user.emailVerified).getTime()));

          console.log('Direct verification check:', {
            isVerified,
            emailVerified: user.emailVerified,
            emailVerifiedType: typeof user.emailVerified
          });

          if (isVerified) {
            console.log('User email verified directly:', user.email, user.emailVerified);
          } else {
            // Fallback to raw SQL if direct check fails
            try {
              const result = await prisma.$queryRaw`
                SELECT "emailVerified" FROM "User" WHERE "id" = ${user.id}
              `;

              console.log('Raw SQL query result:', result);

              // Check if emailVerified exists and is not null in the database
              if (!result[0].emailVerified) {
                console.log('User email not verified (null in database):', user.email);
                throw new Error('Please verify your email before signing in');
              }

              console.log('Email is verified in database:', result[0].emailVerified);
            } catch (sqlError) {
              console.error('Error checking email verification with SQL:', sqlError);

              // Last resort: try a direct Prisma query
              const freshUser = await prisma.user.findUnique({
                where: { id: user.id },
                select: { emailVerified: true }
              });

              if (!freshUser?.emailVerified) {
                console.log('User email not verified (fresh query):', user.email);
                throw new Error('Please verify your email before signing in');
              }

              console.log('Email is verified (fresh query):', freshUser.emailVerified);
            }
          }
        } catch (verificationError) {
          console.error('Error during email verification check:', verificationError);
          throw new Error('Please verify your email before signing in');
        }

        console.log('User email verification passed:', user.email);

        const isPasswordValid = await bcrypt.compare(credentials.password, user.password)

        if (!isPasswordValid) {
          throw new Error('Invalid password')
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: 'ADMIN', // Default role for all users
        }
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          scope: 'https://mail.google.com/ email profile',
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async session({ session, token }) {
      if (session?.user) {
        // Add user ID to session
        session.user.id = token.sub!;

        // Get user's organizations
        const userOrgs = await prisma.organizationMember.findMany({
          where: { userId: token.sub },
          include: {
            organization: true,
          },
        });

        // Add organization info to session
        session.user.organizations = userOrgs.map(org => ({
          id: org.organization.id,
          name: org.organization.name,
          role: org.role,
          isPersonal: org.organization.isPersonal,
        }));

        // Set current organization (default to personal org)
        session.user.currentOrganization = userOrgs.find(
          org => org.organization.isPersonal
        )?.organization || userOrgs[0]?.organization;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.sub = user.id;
        token.role = 'ADMIN';
      }
      return token;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  debug: process.env.NODE_ENV === 'development',
};

export default NextAuth(authOptions);
