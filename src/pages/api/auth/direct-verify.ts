import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    // Add detailed logging for debugging
    logger.info('Direct verify API called', {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel'
    });

    // Extract token or email from request
    const { token, email, userId } = req.body;

    // Log the request parameters
    console.log('Direct verify request parameters:', {
      token: token ? `${token.substring(0, 10)}...` : 'undefined',
      email,
      userId,
      timestamp: new Date().toISOString()
    });

    if (!token && !email && !userId) {
      logger.warn('No token, email, or userId provided in request body');
      return res.status(400).json({
        success: false,
        error: 'Verification token, email, or userId is required',
        timestamp: new Date().toISOString()
      });
    }

    // Try to find the user
    let user = null;

    // If userId is provided, use it directly
    if (userId) {
      logger.info(`Looking up user by ID: ${userId}`);
      user = await prisma.user.findUnique({
        where: { id: userId }
      });
    }

    // If email is provided, use it
    if (!user && email) {
      logger.info(`Looking up user by email: ${email}`);
      user = await prisma.user.findUnique({
        where: { email }
      });
    }

    // If token is provided, try to find the verification token
    if (!user && token) {
      logger.info(`Looking up verification token: ${token.substring(0, 10)}...`);

      // Try to find the token in the database
      const verificationToken = await prisma.verificationToken.findUnique({
        where: { token }
      });

      if (verificationToken) {
        logger.info(`Found verification token for identifier: ${verificationToken.identifier}`);

        // Find the user by email
        user = await prisma.user.findUnique({
          where: { email: verificationToken.identifier }
        });
      } else {
        logger.warn(`Verification token not found: ${token.substring(0, 10)}...`);

        // Try to extract email from token (if token contains email)
        if (token.includes('@')) {
          try {
            const extractedEmail = token.split('@')[0] + '@' + token.split('@')[1].split('.')[0] + '.' + token.split('@')[1].split('.')[1];
            logger.info(`Extracted email from token: ${extractedEmail}`);

            // Find the user by email
            user = await prisma.user.findUnique({
              where: { email: extractedEmail }
            });
          } catch (extractError) {
            logger.error(`Error extracting email from token:`, extractError);
          }
        }
      }
    }

    // If user not found, return error
    if (!user) {
      logger.error('User not found');
      return res.status(404).json({
        success: false,
        error: 'User not found',
        timestamp: new Date().toISOString()
      });
    }

    logger.info(`Found user: ${user.email}`, {
      userId: user.id,
      emailVerified: user.emailVerified
    });

    // If user is already verified, return success
    if (user.emailVerified) {
      logger.info(`User already verified: ${user.email}`);
      return res.status(200).json({
        success: true,
        message: 'Email already verified',
        user: {
          email: user.email,
          emailVerified: user.emailVerified
        },
        timestamp: new Date().toISOString()
      });
    }

    // Update the user's emailVerified field
    const now = new Date();
    const nowIso = now.toISOString();

    logger.info(`Updating user's emailVerified field: ${user.email}`);

    try {
      // First try using the Prisma client's update method
      await prisma.user.update({
        where: { id: user.id },
        data: { emailVerified: now }
      });

      logger.info(`User email verified successfully with Prisma client: ${user.email}`);
    } catch (updateError) {
      logger.error(`Error updating user with Prisma client:`, updateError);

      // Fallback to raw SQL if Prisma update fails
      try {
        // Use raw SQL to ensure the date is stored correctly
        await prisma.$executeRawUnsafe(
          `UPDATE "User" SET "emailVerified" = $1 WHERE "id" = $2`,
          nowIso,
          user.id
        );

        logger.info(`User email verified successfully with raw SQL: ${user.email}`);
      } catch (sqlError) {
        logger.error(`Error updating user with raw SQL:`, sqlError);
        throw new Error(`Failed to update user: ${sqlError instanceof Error ? sqlError.message : 'Unknown error'}`);
      }
    }

    logger.info(`User email verified successfully: ${user.email}`);

    // Delete the verification token if it exists
    if (token) {
      try {
        await prisma.verificationToken.deleteMany({
          where: { identifier: user.email }
        });
        logger.info(`Deleted verification tokens for user: ${user.email}`);
      } catch (deleteError) {
        logger.error(`Error deleting verification tokens:`, deleteError);
      }
    }

    return res.status(200).json({
      success: true,
      message: 'Email verified successfully',
      user: {
        email: user.email,
        emailVerified: now
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    // Enhanced error logging
    logger.error('Direct verification failed:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel'
    });

    // Log to console for immediate visibility
    console.error('Direct verification error:', error instanceof Error ? error.message : 'Unknown error');

    // Check for specific database errors
    const errorMessage = error instanceof Error ? error.message : 'Verification failed';
    const isPrismaError = errorMessage.includes('Prisma') || errorMessage.includes('database');

    // Return a more user-friendly error
    return res.status(500).json({
      success: false,
      error: 'Email verification failed. Please try again or contact support.',
      technicalDetails: process.env.NODE_ENV === 'development' ? {
        message: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        isPrismaError
      } : undefined,
      timestamp: new Date().toISOString()
    });
  }
}
