import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method, query } = req;
  const { id } = query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Reminder ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the reminder exists and belongs to the user's organization
    const reminder = await prisma.reminder.findFirst({
      where: {
        id,
        appointment: {
          campaign: {
            organizationId,
          },
        },
      },
      include: {
        appointment: {
          include: {
            lead: true,
            campaign: true,
          },
        },
        agent: true,
      },
    });

    if (!reminder) {
      return res.status(404).json({ error: 'Reminder not found' });
    }

    switch (method) {
      case 'GET':
        // Return the reminder
        return res.status(200).json(reminder);

      case 'PUT':
        // Update the reminder
        const {
          type,
          timeBeforeAppointment,
          status,
          content,
        } = req.body;

        // Validate required fields
        if (!type || timeBeforeAppointment === undefined) {
          return res.status(400).json({
            error: 'Missing required fields',
            required: ['type', 'timeBeforeAppointment'],
          });
        }

        // Update the reminder
        const updatedReminder = await prisma.reminder.update({
          where: { id },
          data: {
            type,
            timeBeforeAppointment,
            status,
            content,
          },
        });

        // If the reminder time changed, reschedule it
        if (timeBeforeAppointment !== reminder.timeBeforeAppointment) {
          // Import the service here to avoid circular dependencies
          const { ReminderService } = require('@/services/reminder.service');
          await ReminderService.scheduleReminderJob(id);
        }

        return res.status(200).json(updatedReminder);

      case 'DELETE':
        // Delete the reminder
        await prisma.reminder.delete({
          where: { id },
        });

        return res.status(200).json({ message: 'Reminder deleted successfully' });

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in reminder endpoint', {
      method,
      reminderId: id,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
