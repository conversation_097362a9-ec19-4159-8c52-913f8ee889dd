import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { ReminderService } from '@/services/reminder.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'GET':
        // Get query parameters
        const { appointmentId: queryAppointmentId, status: queryStatus } = req.query;

        // Build the query
        const query: any = {
          where: {},
          include: {
            appointment: {
              include: {
                lead: true,
                campaign: true,
              },
            },
            agent: true,
          },
          orderBy: {
            timeBeforeAppointment: 'desc',
          },
        };

        // Filter by appointment ID
        if (queryAppointmentId && typeof queryAppointmentId === 'string') {
          query.where.appointmentId = queryAppointmentId;
        }

        // Filter by status
        if (queryStatus && typeof queryStatus === 'string') {
          query.where.status = queryStatus;
        }

        // Add organization filter to ensure only reminders for this organization are returned
        // This is done by filtering appointments that belong to campaigns that belong to this organization
        query.where.appointment = {
          campaign: {
            organizationId,
          },
        };

        // Get reminders from the database
        const reminders = await prisma.reminder.findMany(query);

        return res.status(200).json(reminders);

      case 'POST':
        // Create a new reminder
        const {
          appointmentId,
          agentId,
          type,
          timeBeforeAppointment,
          content,
        } = req.body;

        // Validate required fields
        if (!appointmentId || !agentId || !type || timeBeforeAppointment === undefined) {
          return res.status(400).json({
            error: 'Missing required fields',
            required: ['appointmentId', 'agentId', 'type', 'timeBeforeAppointment'],
          });
        }

        // Verify the appointment belongs to this organization
        const appointment = await prisma.appointment.findFirst({
          where: {
            id: appointmentId,
            campaign: {
              organizationId,
            },
          },
        });

        if (!appointment) {
          return res.status(404).json({ error: 'Appointment not found or does not belong to your organization' });
        }

        // Create the reminder using the service
        const reminder = await ReminderService.createReminder({
          appointmentId,
          agentId,
          type,
          timeBeforeAppointment,
          content,
        });

        return res.status(201).json(reminder);

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in reminders endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
