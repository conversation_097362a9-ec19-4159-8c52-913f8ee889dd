import { api<PERSON><PERSON><PERSON> } from '@/lib/apiHandler'
import { prisma } from '@/lib/prisma'

export default apiHandler(async (req, res) => {
  if (req.method !== 'PUT') {
    res.setHeader('Allow', ['PUT'])
    return res.status(405).end(`Method ${req.method} Not Allowed`)
  }

  const { name } = req.body

  const updatedUser = await prisma.user.update({
    where: { id: req.user.id },
    data: { name },
  })

  res.json({
    name: updatedUser.name,
    email: updatedUser.email,
  })
}, {
  requireAuth: true,
  audit: {
    action: 'UPDATE_PROFILE',
    resourceType: 'USER'
  }
})