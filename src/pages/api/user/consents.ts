import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { gdprService } from '@/lib/gdpr';
import { prisma } from '@/lib/prisma';
import { NextApiResponse } from 'next';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    logger.warn('Unauthorized consent access attempt');
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    switch (method) {
      case 'GET':
        const consents = await prisma.userConsent.findUnique({
          where: { userId: req.user.id }
        });
        res.json(consents || {
          marketingEmails: false,
          analyticsTracking: false,
          thirdPartySharing: false
        });
        break;

      case 'PUT':
        logger.debug('Consent update request', {
          userId: req.user.id,
          body: req.body
        });

        if (typeof req.body !== 'object' || req.body === null) {
          return res.status(400).json({ error: 'Invalid request body' });
        }

        const updatedConsents = await gdprService.updateConsent(
          req.user.id,
          req.body
        );
        res.json(updatedConsents);
        break;

      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    logger.error('Error in consent endpoint', {
      method,
      userId: req.user.id,
      body: req.body,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'An unexpected error occurred' });
    }
  }
}, {
  requireAuth: true,
  audit: {
    action: 'UPDATE_CONSENT',
    resourceType: 'USER_CONSENT'
  }
});
