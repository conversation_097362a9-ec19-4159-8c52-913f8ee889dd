import { apiHand<PERSON> } from '@/lib/apiHandler'
import { gdprService } from '@/lib/gdpr'

export default apiHandler(async (req, res) => {
  if (req.method !== 'DELETE') {
    res.setHeader('Allow', ['DELETE'])
    return res.status(405).end(`Method ${req.method} Not Allowed`)
  }

  await gdprService.deleteUserData(req.user.id)
  res.status(200).json({ message: 'Account deleted successfully' })
}, {
  requireAuth: true,
  audit: {
    action: 'DELETE_ACCOUNT',
    resourceType: 'USER'
  }
})