import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { stripe } from '@/config/stripe';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        ownedOrganization: true,
      },
    });

    if (!user?.ownedOrganization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const { customerId } = user.ownedOrganization;

    if (!customerId) {
      return res.status(404).json({ error: 'No Stripe customer found' });
    }

    switch (method) {
      case 'GET':
        // Get the customer from Stripe
        const customer = await stripe.customers.retrieve(customerId);

        // Cast customer to any to access address
        const customerObj = typeof customer !== 'string' ? customer as any : null;

        // Return the billing address
        return res.status(200).json({
          line1: customerObj?.address?.line1 || '',
          line2: customerObj?.address?.line2 || '',
          city: customerObj?.address?.city || '',
          state: customerObj?.address?.state || '',
          postalCode: customerObj?.address?.postal_code || '',
          country: customerObj?.address?.country || '',
        });

      case 'PUT':
        // Validate the request body
        const { line1, line2, city, state, postalCode, country } = req.body;

        if (!line1 || !city || !state || !postalCode || !country) {
          return res.status(400).json({ error: 'Missing required fields' });
        }

        // Update the customer in Stripe
        await stripe.customers.update(customerId, {
          address: {
            line1,
            line2,
            city,
            state,
            postal_code: postalCode,
            country,
          },
        });

        return res.status(200).json({ success: true });

      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in billing address endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
