import { api<PERSON><PERSON><PERSON> } from '@/lib/apiHandler'
import { prisma } from '@/lib/prisma'

export default apiHandler(async (req, res) => {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET'])
    return res.status(405).end(`Method ${req.method} Not Allowed`)
  }

  // Fetch user data
  const userData = await prisma.user.findUnique({
    where: { id: req.user.id },
    include: {
      emailAccounts: true,
      campaigns: true,
      consent: true,
    },
  })

  // Remove sensitive information
  const sanitizedData = {
    ...userData,
    password: undefined,
  }

  // Set headers for file download
  res.setHeader('Content-Type', 'application/json')
  res.setHeader('Content-Disposition', 'attachment; filename=user-data-export.json')
  
  res.json(sanitizedData)
}, {
  requireAuth: true,
  audit: {
    action: 'EXPORT_DATA',
    resourceType: 'USER'
  }
})
