import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from 'next-auth/react';
import { prisma } from '@/lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getSession({ req });
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Make sure session.user.id exists
    if (!session.user || !session.user.id) {
      return res.status(401).json({ error: 'User not authenticated properly' });
    }

    const userId = session.user.id;

    // Find the organization member record
    const organizationMember = await prisma.organizationMember.findFirst({
      where: { userId },
    });

    if (!organizationMember) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = organizationMember.organizationId;

    // Fetch all required data in parallel
    const [organization, leadLists, emailAccounts] = await Promise.all([
      // Get organization details
      prisma.organization.findUnique({
        where: { id: organizationId },
      }),

      // Get lead lists with counts
      prisma.leadList.findMany({
        where: { organizationId },
        orderBy: { createdAt: 'desc' },
        include: {
          _count: {
            select: { leads: true },
          },
        },
      }),

      // Get email accounts
      prisma.emailAccount.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
      }),
    ]);

    // Return all data in a single response
    return res.status(200).json({
      organization,
      leadLists,
      emailAccounts,
    });
  } catch (error) {
    console.error('Error fetching common data:', error);
    return res.status(500).json({ error: 'Failed to fetch data' });
  }
}
