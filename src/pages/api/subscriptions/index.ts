import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { SubscriptionService } from '@/services/subscription.service';
import { EnhancedUsageService } from '@/services/enhanced-usage.service';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        ownedOrganization: {
          include: {
            subscription: true,
          },
        },
      },
    });

    if (!user?.ownedOrganization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.ownedOrganization.id;

    switch (method) {
      case 'GET':
        // Get subscription details
        const subscriptionDetails = await SubscriptionService.getSubscriptionDetails(organizationId);

        // Get current usage
        const currentUsage = await EnhancedUsageService.getCurrentUsage(organizationId);

        return res.status(200).json({
          subscription: subscriptionDetails,
          usage: currentUsage,
        });

      default:
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in subscriptions endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
