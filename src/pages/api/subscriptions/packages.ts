import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { StripePackageService } from '@/services/stripe-package.service';
import { AdminPackageService } from '@/services/admin-package.service';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

/**
 * API endpoint for package subscription management
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.email) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get user's organization
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        ownedOrganization: true,
        organizations: {
          include: {
            organization: true
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const organization = user.ownedOrganization || user.organizations[0]?.organization;
    
    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const { method } = req;

    switch (method) {
      case 'GET':
        return await handleGetSubscriptionInfo(req, res, organization.id);
      case 'POST':
        return await handleCreateSubscription(req, res, organization.id, user.email, user.name);
      case 'PUT':
        return await handleUpdateSubscription(req, res, organization.id);
      case 'DELETE':
        return await handleCancelSubscription(req, res, organization.id);
      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in package subscription endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method
    });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetSubscriptionInfo(req: NextApiRequest, res: NextApiResponse, organizationId: string) {
  try {
    // Get current subscription status
    const subscriptionStatus = await StripePackageService.getSubscriptionStatus(organizationId);
    
    // Get available packages
    const packages = await AdminPackageService.getAllPackages();
    
    return res.status(200).json({
      currentSubscription: subscriptionStatus,
      availablePackages: packages
    });
  } catch (error) {
    logger.error('Error getting subscription info', { error, organizationId });
    return res.status(500).json({ error: 'Failed to get subscription info' });
  }
}

async function handleCreateSubscription(
  req: NextApiRequest, 
  res: NextApiResponse, 
  organizationId: string,
  userEmail: string,
  userName?: string
) {
  try {
    const { packageId, paymentMethodId, trial = false, trialDays = 14 } = req.body;

    if (!packageId) {
      return res.status(400).json({ error: 'Package ID is required' });
    }

    // Verify package exists
    const package_ = await AdminPackageService.getPackageById(packageId);
    if (!package_) {
      return res.status(404).json({ error: 'Package not found' });
    }

    // Create or get Stripe customer
    const customerId = await StripePackageService.createOrGetCustomer(
      organizationId,
      userEmail,
      userName
    );

    // Create subscription
    const subscription = await StripePackageService.createSubscription({
      packageId,
      organizationId,
      customerId,
      paymentMethodId,
      trial,
      trialDays
    });

    return res.status(201).json({
      subscription,
      message: 'Subscription created successfully'
    });
  } catch (error) {
    logger.error('Error creating subscription', { error, organizationId });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to create subscription' 
    });
  }
}

async function handleUpdateSubscription(req: NextApiRequest, res: NextApiResponse, organizationId: string) {
  try {
    const { packageId } = req.body;

    if (!packageId) {
      return res.status(400).json({ error: 'Package ID is required' });
    }

    // Verify package exists
    const package_ = await AdminPackageService.getPackageById(packageId);
    if (!package_) {
      return res.status(404).json({ error: 'Package not found' });
    }

    // Update subscription
    const subscription = await StripePackageService.updateSubscription(organizationId, packageId);

    return res.status(200).json({
      subscription,
      message: 'Subscription updated successfully'
    });
  } catch (error) {
    logger.error('Error updating subscription', { error, organizationId });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to update subscription' 
    });
  }
}

async function handleCancelSubscription(req: NextApiRequest, res: NextApiResponse, organizationId: string) {
  try {
    const { immediate = false } = req.body;

    await StripePackageService.cancelSubscription(organizationId, immediate);

    return res.status(200).json({
      message: immediate ? 'Subscription cancelled immediately' : 'Subscription will be cancelled at the end of the billing period'
    });
  } catch (error) {
    logger.error('Error cancelling subscription', { error, organizationId });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to cancel subscription' 
    });
  }
}
