import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { stripe } from '@/config/stripe';
import { StripePackageService } from '@/services/stripe-package.service';
import { AdminPackageService } from '@/services/admin-package.service';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

/**
 * Create Stripe Checkout session for package subscription
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.email) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get user's organization
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        ownedOrganization: true,
        organizations: {
          include: {
            organization: true
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const organization = user.ownedOrganization || user.organizations[0]?.organization;
    
    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const { packageId, trial = false, trialDays = 14 } = req.body;

    if (!packageId) {
      return res.status(400).json({ error: 'Package ID is required' });
    }

    // Get package details
    const package_ = await AdminPackageService.getPackageById(packageId);
    if (!package_) {
      return res.status(404).json({ error: 'Package not found' });
    }

    // Handle free package
    if (package_.price === 0) {
      // Directly assign free package
      await AdminPackageService.assignPackageToOrganization(organization.id, packageId);
      
      return res.status(200).json({
        type: 'free_package',
        message: 'Free package assigned successfully',
        redirectUrl: `${process.env.NEXTAUTH_URL}/dashboard?subscription_success=true`
      });
    }

    // Create or get Stripe customer
    const customerId = await StripePackageService.createOrGetCustomer(
      organization.id,
      user.email,
      user.name
    );

    // Create Stripe product and price if not exists
    const { priceId } = await StripePackageService.createStripeProductForPackage(packageId);

    // Create Checkout session
    const checkoutSession = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXTAUTH_URL}/dashboard?subscription_success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXTAUTH_URL}/settings/billing?subscription_canceled=true`,
      metadata: {
        organizationId: organization.id,
        packageId: packageId,
        userId: user.id,
        source: 'avian-email-platform'
      },
      subscription_data: {
        metadata: {
          organizationId: organization.id,
          packageId: packageId,
          source: 'avian-email-platform'
        },
        ...(trial && trialDays > 0 ? { trial_period_days: trialDays } : {})
      },
      allow_promotion_codes: true,
      billing_address_collection: 'required',
      customer_update: {
        address: 'auto',
        name: 'auto'
      }
    });

    logger.info('Created Stripe checkout session', {
      sessionId: checkoutSession.id,
      organizationId: organization.id,
      packageId,
      customerId,
      trial
    });

    return res.status(200).json({
      type: 'checkout_session',
      sessionId: checkoutSession.id,
      url: checkoutSession.url
    });
  } catch (error) {
    logger.error('Error creating checkout session', {
      error: error instanceof Error ? error.message : 'Unknown error',
      packageId: req.body.packageId
    });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to create checkout session' 
    });
  }
}
