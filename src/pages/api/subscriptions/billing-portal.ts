import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { stripe } from '@/config/stripe';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

/**
 * Create Stripe Customer Portal session for subscription management
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.email) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get user's organization
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        ownedOrganization: true,
        organizations: {
          include: {
            organization: true
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const organization = user.ownedOrganization || user.organizations[0]?.organization;
    
    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    if (!organization.customerId) {
      return res.status(400).json({ 
        error: 'No customer found. Please subscribe to a package first.' 
      });
    }

    // Create billing portal session
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: organization.customerId,
      return_url: `${process.env.NEXTAUTH_URL}/settings/billing`,
    });

    logger.info('Created billing portal session', {
      sessionId: portalSession.id,
      organizationId: organization.id,
      customerId: organization.customerId
    });

    return res.status(200).json({
      url: portalSession.url
    });
  } catch (error) {
    logger.error('Error creating billing portal session', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to create billing portal session' 
    });
  }
}
