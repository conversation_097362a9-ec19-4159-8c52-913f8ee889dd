import { NextApiRequest, NextApiResponse } from 'next';
import { buffer } from 'micro';
import { stripe } from '@/config/stripe';
import { prisma } from '@/lib/prisma';
import { AlertService } from '@/services/alert.service';
import { UsageService } from '@/services/usage.service';
import <PERSON><PERSON> from 'stripe';

export const config = {
  api: {
    bodyParser: false,
  },
};

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  await prisma.subscription.update({
    where: { stripeSubscriptionId: subscription.id },
    data: {
      status: subscription.status.toUpperCase() as any,
      currentPeriodStart: new Date(subscription.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
    },
  });
}

async function handleUsageReport(event: Stripe.Event) {
  // Handle usage report events
  if (event.type === 'customer.subscription.updated') {
    const subscription = event.data.object as Stripe.Subscription;
    const organizationId = subscription.metadata.organizationId;

    if (!organizationId) return;

    // Check if the update was usage-related
    const hasUsageUpdates = subscription.items.data.some(item =>
      item.price?.type === 'recurring' && item.price.recurring?.usage_type === 'metered'
    );

    if (hasUsageUpdates) {
      // Get current usage
      const usage = await UsageService.getCurrentUsage(organizationId);

      // Check for high usage and create alerts if needed
      if (usage.emailsSent > 900) { // 90% of basic plan limit
        await AlertService.createAlert({
          type: 'SENDING_VOLUME_LIMIT',
          severity: 'MEDIUM',
          message: `You've used ${usage.emailsSent} of your 1000 email sending limit this month.`,
          metadata: { usage }
        });
      }
    }
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const buf = await buffer(req);
  const sig = req.headers['stripe-signature']!;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      buf,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err: any) {
    console.error(`Webhook Error: ${err.message}`);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  try {
    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionUpdated(subscription);
        // Also check for usage updates
        if (event.type === 'customer.subscription.updated') {
          await handleUsageReport(event);
        }
        break;
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    return res.json({ received: true });
  } catch (error) {
    console.error('Webhook handler failed:', error);
    return res.status(500).json({ message: 'Webhook handler failed' });
  }
}
