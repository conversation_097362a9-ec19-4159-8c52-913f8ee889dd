import { NextApiRequest, NextApiResponse } from 'next';
import { buffer } from 'micro';
import { stripe } from '@/config/stripe';
import { prisma } from '@/lib/prisma';
import { AlertService } from '@/services/alert.service';
import { UsageService } from '@/services/usage.service';
import { BusinessAnalyticsService } from '@/services/business-analytics.service';
import { logger } from '@/lib/logger';
import Strip<PERSON> from 'stripe';

export const config = {
  api: {
    bodyParser: false,
  },
};

async function handlePackageSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    const organizationId = subscription.metadata.organizationId;
    const packageId = subscription.metadata.packageId;

    if (!organizationId || !packageId) {
      logger.warn('Missing metadata in subscription webhook', {
        subscriptionId: subscription.id,
        metadata: subscription.metadata
      });
      return;
    }

    // Update package subscription
    await prisma.packageSubscription.updateMany({
      where: {
        stripeSubscriptionId: subscription.id,
        organizationId
      },
      data: {
        isActive: subscription.status === 'active' || subscription.status === 'trialing',
        lastPaymentDate: subscription.status === 'active' ? new Date() : undefined,
        nextPaymentDate: new Date(subscription.current_period_end * 1000),
        autoRenew: !subscription.cancel_at_period_end
      }
    });

    // If subscription is cancelled, assign free package
    if (subscription.status === 'canceled') {
      const freePackage = await prisma.package.findFirst({
        where: { isDefault: true }
      });

      if (freePackage) {
        await prisma.organization.update({
          where: { id: organizationId },
          data: { packageId: freePackage.id }
        });

        // Create free package subscription
        await prisma.packageSubscription.create({
          data: {
            organizationId,
            packageId: freePackage.id,
            startDate: new Date(),
            isActive: true,
            autoRenew: true
          }
        });
      }
    }

    logger.info('Updated package subscription from webhook', {
      subscriptionId: subscription.id,
      organizationId,
      packageId,
      status: subscription.status
    });
  } catch (error) {
    logger.error('Error handling package subscription update', {
      error: error instanceof Error ? error.message : 'Unknown error',
      subscriptionId: subscription.id
    });
  }
}

// Legacy subscription handler for backward compatibility
async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    await prisma.subscription.update({
      where: { stripeSubscriptionId: subscription.id },
      data: {
        status: subscription.status.toUpperCase() as any,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      },
    });
  } catch (error) {
    // If legacy subscription doesn't exist, try package subscription
    await handlePackageSubscriptionUpdated(subscription);
  }
}

async function handleUsageReport(event: Stripe.Event) {
  // Handle usage report events
  if (event.type === 'customer.subscription.updated') {
    const subscription = event.data.object as Stripe.Subscription;
    const organizationId = subscription.metadata.organizationId;

    if (!organizationId) return;

    // Check if the update was usage-related
    const hasUsageUpdates = subscription.items.data.some(item =>
      item.price?.type === 'recurring' && item.price.recurring?.usage_type === 'metered'
    );

    if (hasUsageUpdates) {
      // Get current usage
      const usage = await UsageService.getCurrentUsage(organizationId);

      // Check for high usage and create alerts if needed
      if (usage.emailsSent > 900) { // 90% of basic plan limit
        await AlertService.createAlert({
          type: 'SENDING_VOLUME_LIMIT',
          severity: 'MEDIUM',
          message: `You've used ${usage.emailsSent} of your 1000 email sending limit this month.`,
          metadata: { usage }
        });
      }
    }
  }
}

async function trackSubscriptionEvent(eventType: 'new' | 'updated' | 'canceled', subscription: Stripe.Subscription) {
  try {
    const organizationId = subscription.metadata.organizationId;
    if (!organizationId) return;

    // Record analytics based on event type
    if (eventType === 'new') {
      // Track new subscription
      logger.info('New subscription created', {
        subscriptionId: subscription.id,
        organizationId,
        status: subscription.status
      });
    } else if (eventType === 'canceled') {
      // Track cancellation
      logger.info('Subscription canceled', {
        subscriptionId: subscription.id,
        organizationId
      });
    }

    // Trigger daily analytics update
    await BusinessAnalyticsService.recordDailyAnalytics();
  } catch (error) {
    logger.error('Error tracking subscription event', {
      error: error instanceof Error ? error.message : 'Unknown error',
      eventType,
      subscriptionId: subscription.id
    });
  }
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    const subscriptionId = invoice.subscription as string;
    if (!subscriptionId) return;

    // Update payment date in package subscription
    await prisma.packageSubscription.updateMany({
      where: { stripeSubscriptionId: subscriptionId },
      data: {
        lastPaymentDate: new Date(),
        nextPaymentDate: invoice.period_end ? new Date(invoice.period_end * 1000) : undefined
      }
    });

    logger.info('Payment succeeded', {
      invoiceId: invoice.id,
      subscriptionId,
      amount: invoice.amount_paid
    });
  } catch (error) {
    logger.error('Error handling payment success', {
      error: error instanceof Error ? error.message : 'Unknown error',
      invoiceId: invoice.id
    });
  }
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  try {
    const subscriptionId = invoice.subscription as string;
    if (!subscriptionId) return;

    // Get organization from subscription
    const packageSubscription = await prisma.packageSubscription.findFirst({
      where: { stripeSubscriptionId: subscriptionId },
      include: { package: true }
    });

    if (packageSubscription) {
      // Create alert for failed payment
      await AlertService.createAlert({
        type: 'PAYMENT_FAILED',
        severity: 'HIGH',
        message: `Payment failed for ${packageSubscription.package.name} subscription. Please update your payment method.`,
        metadata: {
          invoiceId: invoice.id,
          subscriptionId,
          amount: invoice.amount_due
        }
      });
    }

    logger.warn('Payment failed', {
      invoiceId: invoice.id,
      subscriptionId,
      amount: invoice.amount_due
    });
  } catch (error) {
    logger.error('Error handling payment failure', {
      error: error instanceof Error ? error.message : 'Unknown error',
      invoiceId: invoice.id
    });
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const buf = await buffer(req);
  const sig = req.headers['stripe-signature']!;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      buf,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err: any) {
    console.error(`Webhook Error: ${err.message}`);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  try {
    switch (event.type) {
      case 'customer.subscription.created':
        const createdSubscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionUpdated(createdSubscription);
        // Track new subscription for analytics
        await trackSubscriptionEvent('new', createdSubscription);
        break;

      case 'customer.subscription.updated':
        const updatedSubscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionUpdated(updatedSubscription);
        await handleUsageReport(event);
        // Track subscription changes
        await trackSubscriptionEvent('updated', updatedSubscription);
        break;

      case 'customer.subscription.deleted':
        const deletedSubscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionUpdated(deletedSubscription);
        // Track cancellation for analytics
        await trackSubscriptionEvent('canceled', deletedSubscription);
        break;

      case 'invoice.payment_succeeded':
        const invoice = event.data.object as Stripe.Invoice;
        await handlePaymentSucceeded(invoice);
        break;

      case 'invoice.payment_failed':
        const failedInvoice = event.data.object as Stripe.Invoice;
        await handlePaymentFailed(failedInvoice);
        break;

      default:
        logger.info(`Unhandled Stripe event type: ${event.type}`);
    }

    return res.json({ received: true });
  } catch (error) {
    logger.error('Webhook handler failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      eventType: event.type,
      eventId: event.id
    });
    return res.status(500).json({ message: 'Webhook handler failed' });
  }
}
