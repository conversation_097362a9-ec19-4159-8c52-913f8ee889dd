import { startCampaignProcessor } from '../../cron/campaign-processor';
import { startReputationChecker } from '../../cron/reputation-checker';
import { startAgentProcessor } from '../../cron/agent-processor';
import { logger } from '@/lib/logger';

// Start the campaign processor
try {
  logger.info('Initializing campaign processor');
  startCampaignProcessor();
} catch (error) {
  logger.error('Failed to initialize campaign processor', {
    error: error instanceof Error ? error.message : 'Unknown error',
  });
}

// Start the reputation checker
try {
  logger.info('Initializing reputation checker');
  startReputationChecker();
} catch (error) {
  logger.error('Failed to initialize reputation checker', {
    error: error instanceof Error ? error.message : 'Unknown error',
  });
}

// Start the agent processor
try {
  logger.info('Initializing agent processor');
  startAgentProcessor();
} catch (error) {
  logger.error('Failed to initialize agent processor', {
    error: error instanceof Error ? error.message : 'Unknown error',
  });
}

// This is a dummy API route that will never be called
export default function handler(req, res) {
  res.status(404).end();
}
