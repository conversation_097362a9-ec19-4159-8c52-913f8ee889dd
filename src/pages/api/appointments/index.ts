import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { AppointmentService } from '@/services/appointment.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'GET':
        // Get query parameters
        const { campaignId: queryCampaignId, leadId: queryLeadId, status: queryStatus, upcoming } = req.query;

        // Build the query
        const query: any = {
          where: {},
          include: {
            lead: true,
            agent: true,
            campaign: true,
            reminders: true,
          },
          orderBy: {
            startTime: 'asc',
          },
        };

        // Filter by campaign ID
        if (queryCampaignId && typeof queryCampaignId === 'string') {
          query.where.campaignId = queryCampaignId;
        }

        // Filter by lead ID
        if (queryLeadId && typeof queryLeadId === 'string') {
          query.where.leadId = queryLeadId;
        }

        // Filter by status
        if (queryStatus && typeof queryStatus === 'string') {
          query.where.status = queryStatus;
        }

        // Filter by upcoming appointments
        if (upcoming === 'true') {
          query.where.startTime = {
            gte: new Date(),
          };
        }

        // Add organization filter to ensure only appointments for this organization are returned
        // This is done by filtering campaigns that belong to this organization
        query.where.campaign = {
          organizationId,
        };

        // Get appointments from the database
        const appointments = await prisma.appointment.findMany(query);

        return res.status(200).json(appointments);

      case 'POST':
        // Create a new appointment
        const {
          leadId,
          campaignId,
          agentId,
          title,
          description,
          startTime,
          endTime,
          location,
          meetingLink,
          notes,
          metadata,
          reminders,
        } = req.body;

        // Validate required fields
        if (!leadId || !campaignId || !agentId || !title || !startTime || !endTime) {
          return res.status(400).json({
            error: 'Missing required fields',
            required: ['leadId', 'campaignId', 'agentId', 'title', 'startTime', 'endTime'],
          });
        }

        // Verify the campaign belongs to this organization
        const campaign = await prisma.campaign.findFirst({
          where: {
            id: campaignId,
            organizationId,
          },
        });

        if (!campaign) {
          return res.status(404).json({ error: 'Campaign not found or does not belong to your organization' });
        }

        // Create the appointment using the service
        const appointment = await AppointmentService.createAppointment({
          leadId,
          campaignId,
          agentId,
          title,
          description,
          startTime: new Date(startTime),
          endTime: new Date(endTime),
          location,
          meetingLink,
          notes,
          metadata,
          reminders,
        });

        return res.status(201).json(appointment);

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in appointments endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
