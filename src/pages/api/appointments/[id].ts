import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { AppointmentService } from '@/services/appointment.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method, query } = req;
  const { id } = query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Appointment ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the appointment exists and belongs to the user's organization
    const appointment = await prisma.appointment.findFirst({
      where: {
        id,
        campaign: {
          organizationId,
        },
      },
      include: {
        lead: true,
        agent: true,
        campaign: true,
        reminders: true,
      },
    });

    if (!appointment) {
      return res.status(404).json({ error: 'Appointment not found' });
    }

    switch (method) {
      case 'GET':
        // Return the appointment
        return res.status(200).json(appointment);

      case 'PUT':
        // Update the appointment
        const {
          title,
          description,
          startTime,
          endTime,
          status,
          location,
          meetingLink,
          notes,
          metadata,
        } = req.body;

        // Validate required fields
        if (!title || !startTime || !endTime) {
          return res.status(400).json({
            error: 'Missing required fields',
            required: ['title', 'startTime', 'endTime'],
          });
        }

        // Update the appointment using the service
        const updatedAppointment = await AppointmentService.updateAppointment({
          id,
          title,
          description,
          startTime: startTime ? new Date(startTime) : undefined,
          endTime: endTime ? new Date(endTime) : undefined,
          status,
          location,
          meetingLink,
          notes,
          metadata,
        });

        return res.status(200).json(updatedAppointment);

      case 'DELETE':
        // Delete the appointment and its reminders
        await prisma.reminder.deleteMany({
          where: { appointmentId: id },
        });

        await prisma.appointment.delete({
          where: { id },
        });

        return res.status(200).json({ message: 'Appointment deleted successfully' });

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in appointment endpoint', {
      method,
      appointmentId: id,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
