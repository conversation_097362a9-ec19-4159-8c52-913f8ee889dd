import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { ChromaService } from '@/services/chroma.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method, query } = req;
  const { id } = query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Knowledge base ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the knowledge base exists and belongs to the user's organization
    const knowledgeBase = await prisma.knowledgeBase.findFirst({
      where: {
        id,
        organizationId,
      },
      include: {
        agents: {
          include: {
            agent: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        documents: true,
      },
    });

    if (!knowledgeBase) {
      return res.status(404).json({ error: 'Knowledge base not found' });
    }

    switch (method) {
      case 'GET':
        // Return the knowledge base
        return res.status(200).json(knowledgeBase);

      case 'PUT':
        // Update the knowledge base
        const { name, description } = req.body;

        // Validate required fields
        if (!name) {
          return res.status(400).json({ error: 'Knowledge base name is required' });
        }

        // Update the knowledge base
        const updatedKnowledgeBase = await prisma.knowledgeBase.update({
          where: { id },
          data: {
            name,
            description,
          },
          include: {
            agents: {
              include: {
                agent: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        });

        return res.status(200).json(updatedKnowledgeBase);

      case 'DELETE':
        // Check if the knowledge base is linked to any agents
        const linkedAgents = await prisma.knowledgeBaseAgent.findMany({
          where: {
            knowledgeBaseId: id,
          },
          include: {
            agent: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        });

        if (linkedAgents.length > 0) {
          return res.status(400).json({
            error: 'Cannot delete knowledge base that is linked to agents',
            linkedAgents: linkedAgents.map(link => link.agent),
          });
        }

        // Delete from Chroma if collection exists
        if (knowledgeBase.chromaCollectionId) {
          try {
            await ChromaService.deleteCollection(organizationId);
          } catch (chromaError) {
            logger.warn('Failed to delete Chroma collection', {
              organizationId,
              collectionId: knowledgeBase.chromaCollectionId,
              error: chromaError instanceof Error ? chromaError.message : 'Unknown error',
            });
          }
        }

        // Delete the knowledge base (this will cascade delete documents)
        await prisma.knowledgeBase.delete({
          where: { id },
        });

        return res.status(200).json({ message: 'Knowledge base deleted successfully' });

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in knowledge base endpoint', {
      method,
      knowledgeBaseId: id,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
