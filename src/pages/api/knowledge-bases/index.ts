import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { KnowledgeBaseService } from '@/services/knowledge-base.service';
import { IncomingForm } from 'formidable';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'GET':
        // Get the organization's knowledge base
        const knowledgeBase = await KnowledgeBaseService.getOrCreateKnowledgeBase(organizationId);
        return res.status(200).json(knowledgeBase);

      case 'POST':
        // Create upload directory if it doesn't exist
        const uploadDir = path.join(os.tmpdir(), 'avian-uploads');
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Handle file upload using formidable
        const form = new IncomingForm({
          uploadDir,
          keepExtensions: true,
          maxFileSize: 50 * 1024 * 1024, // 50MB limit
        });

        const { fields, files } = await new Promise<{ fields: any; files: any }>((resolve, reject) => {
          form.parse(req, (err, fields, files) => {
            if (err) reject(err);
            else resolve({ fields, files });
          });
        });

        const title = Array.isArray(fields.title) ? fields.title[0] : fields.title;
        const type = Array.isArray(fields.type) ? fields.type[0] : fields.type || 'TEXT';
        const sourceUrl = Array.isArray(fields.sourceUrl) ? fields.sourceUrl[0] : fields.sourceUrl;
        const content = Array.isArray(fields.content) ? fields.content[0] : fields.content;

        // Validate required fields
        if (!title) {
          return res.status(400).json({
            error: 'Missing required fields',
            required: ['title'],
          });
        }

        let extractedContent = '';
        let metadata: Record<string, any> = {};

        try {
          // Process content based on type
          if (type === 'TEXT' && content) {
            extractedContent = content;
          } else if (type === 'WEB_URL' && sourceUrl) {
            extractedContent = await KnowledgeBaseService.scrapeWebContent(sourceUrl);
            metadata.sourceUrl = sourceUrl;
          } else if (['PDF', 'IMAGE', 'DOCUMENT'].includes(type) && files.file) {
            const file = Array.isArray(files.file) ? files.file[0] : files.file;
            const filePath = file.filepath || file.path;

            logger.info('Processing uploaded file', {
              originalName: file.originalFilename,
              filePath,
              fileSize: file.size,
              mimeType: file.mimetype,
              type,
              fileExists: fs.existsSync(filePath),
            });

            // Check if file exists
            if (!fs.existsSync(filePath)) {
              throw new Error(`Uploaded file not found at path: ${filePath}`);
            }

            metadata = {
              fileName: file.originalFilename || 'unknown',
              fileSize: file.size,
              mimeType: file.mimetype || 'application/octet-stream',
            };

            try {
              // Extract content based on file type
              if (type === 'PDF') {
                extractedContent = await KnowledgeBaseService.processPDF(filePath);
              } else if (type === 'IMAGE') {
                extractedContent = await KnowledgeBaseService.processImage(filePath);
              } else {
                // For other document types, try to read as text
                extractedContent = fs.readFileSync(filePath, 'utf-8');
              }
            } finally {
              // Clean up temporary file (always try to clean up)
              try {
                if (fs.existsSync(filePath)) {
                  fs.unlinkSync(filePath);
                  logger.info('Cleaned up temporary file', { filePath });
                }
              } catch (cleanupError) {
                logger.warn('Failed to clean up temporary file', {
                  filePath,
                  error: cleanupError instanceof Error ? cleanupError.message : 'Unknown error',
                });
              }
            }
          }

          // Add document to the knowledge base if we have any content
          if (extractedContent.trim()) {
            const result = await KnowledgeBaseService.addDocument(
              organizationId,
              title,
              extractedContent,
              type,
              metadata
            );

            return res.status(201).json(result);
          } else {
            return res.status(400).json({
              error: 'No content could be extracted from the provided input',
            });
          }
        } catch (processingError) {
          logger.error('Error processing knowledge base document', {
            error: processingError instanceof Error ? processingError.message : 'Unknown error',
            stack: processingError instanceof Error ? processingError.stack : undefined,
            type,
            title,
            hasFile: !!files.file,
            uploadDir,
          });

          return res.status(400).json({
            error: 'Failed to process content',
            details: processingError instanceof Error ? processingError.message : 'Unknown error',
          });
        }

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in knowledge bases endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
