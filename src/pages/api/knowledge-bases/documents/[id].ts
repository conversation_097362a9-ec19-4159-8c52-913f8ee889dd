import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { KnowledgeBaseService } from '@/services/knowledge-base.service';
import { ChromaService } from '@/services/chroma.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method, query } = req;
  const { id } = query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Document ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'GET':
        // Get document by ID - find the document chunk and get all related chunks
        const documentChunk = await prisma.knowledgeBaseDocument.findFirst({
          where: {
            id,
            knowledgeBase: {
              organizationId,
            },
          },
          include: {
            knowledgeBase: true,
          },
        });

        if (!documentChunk) {
          return res.status(404).json({ error: 'Document not found' });
        }

        // Get all chunks for this document (same title, fileName, or sourceUrl)
        const allChunks = await prisma.knowledgeBaseDocument.findMany({
          where: {
            knowledgeBaseId: documentChunk.knowledgeBaseId,
            OR: [
              { title: documentChunk.title },
              { fileName: documentChunk.fileName },
              { sourceUrl: documentChunk.sourceUrl },
            ],
          },
          orderBy: {
            chunkIndex: 'asc',
          },
        });

        // Reconstruct the full document content from all chunks
        const fullContent = allChunks.map(chunk => chunk.content).join('');

        const fullDocument = {
          id: documentChunk.id,
          title: documentChunk.title,
          content: fullContent,
          type: documentChunk.type,
          sourceUrl: documentChunk.sourceUrl,
          fileName: documentChunk.fileName,
          fileSize: documentChunk.fileSize,
          mimeType: documentChunk.mimeType,
          metadata: documentChunk.metadata,
          createdAt: documentChunk.createdAt,
          knowledgeBase: documentChunk.knowledgeBase,
          chunks: allChunks,
        };

        return res.status(200).json(fullDocument);

      case 'PUT':
        // Update document content
        const { title, content } = req.body;

        if (!content) {
          return res.status(400).json({ error: 'Content is required' });
        }

        // Check if document exists and belongs to user's organization
        const existingDocument = await prisma.knowledgeBaseDocument.findFirst({
          where: {
            id,
            knowledgeBase: {
              organizationId,
            },
          },
          include: {
            knowledgeBase: true,
          },
        });

        if (!existingDocument) {
          return res.status(404).json({ error: 'Document not found' });
        }

        // Get all chunks for this document
        const allExistingChunks = await prisma.knowledgeBaseDocument.findMany({
          where: {
            knowledgeBaseId: existingDocument.knowledgeBaseId,
            OR: [
              { title: existingDocument.title },
              { fileName: existingDocument.fileName },
              { sourceUrl: existingDocument.sourceUrl },
            ],
          },
        });

        // Delete all existing chunks
        await prisma.knowledgeBaseDocument.deleteMany({
          where: {
            id: {
              in: allExistingChunks.map(chunk => chunk.id),
            },
          },
        });

        // Delete old vectors for this document
        try {
          const vectorIds = allExistingChunks
            .map(chunk => chunk.vectorId)
            .filter(id => id !== null) as string[];

          if (vectorIds.length > 0) {
            await ChromaService.deleteDocuments(organizationId, vectorIds);
          }
        } catch (vectorError) {
          logger.error('Error deleting old document vectors', {
            documentId: id,
            organizationId,
            error: vectorError instanceof Error ? vectorError.message : 'Unknown error',
          });
        }

        // Re-add the document with new content
        const result = await KnowledgeBaseService.addDocument(
          organizationId,
          title || existingDocument.title || 'Untitled',
          content,
          existingDocument.type,
          {
            sourceUrl: existingDocument.sourceUrl,
            fileName: existingDocument.fileName,
            fileSize: existingDocument.fileSize,
            mimeType: existingDocument.mimeType,
            ...existingDocument.metadata as any,
          }
        );

        logger.info('Document updated successfully', {
          documentId: id,
          organizationId,
          documentsAdded: result.documentsAdded,
          success: result.success,
        });

        return res.status(200).json({
          id: id, // Keep the original document ID
          title: title || existingDocument.title,
          content,
          type: existingDocument.type,
          sourceUrl: existingDocument.sourceUrl,
          fileName: existingDocument.fileName,
          fileSize: existingDocument.fileSize,
          mimeType: existingDocument.mimeType,
          metadata: existingDocument.metadata,
          createdAt: new Date(),
          documentsAdded: result.documentsAdded,
          success: result.success,
        });

      case 'DELETE':
        // Delete document
        const documentToDelete = await prisma.knowledgeBaseDocument.findFirst({
          where: {
            id,
            knowledgeBase: {
              organizationId,
            },
          },
        });

        if (!documentToDelete) {
          return res.status(404).json({ error: 'Document not found' });
        }

        // Delete from vector store
        try {
          await KnowledgeBaseService.deleteDocumentVectors(organizationId, id);
        } catch (vectorError) {
          logger.error('Error deleting document vectors', {
            documentId: id,
            organizationId,
            error: vectorError instanceof Error ? vectorError.message : 'Unknown error',
          });
        }

        // Delete from database
        await prisma.knowledgeBaseDocument.delete({
          where: { id },
        });

        // Update vector count
        await prisma.knowledgeBase.update({
          where: { organizationId },
          data: {
            vectorCount: {
              decrement: 1,
            },
          },
        });

        return res.status(200).json({ message: 'Document deleted successfully' });

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in document endpoint', {
      method,
      documentId: id,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
