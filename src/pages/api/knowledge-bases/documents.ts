import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { KnowledgeBaseService } from '@/services/knowledge-base.service';
import { ChromaService } from '@/services/chroma.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'GET':
        // Get all documents in the organization's knowledge base
        const knowledgeBase = await prisma.knowledgeBase.findUnique({
          where: { organizationId },
          include: {
            documents: {
              orderBy: {
                createdAt: 'desc',
              },
            },
          },
        });

        if (!knowledgeBase) {
          return res.status(200).json([]);
        }

        // Group documents by their source (title, sourceUrl, fileName) but keep the first document's ID
        const groupedDocuments = knowledgeBase.documents.reduce((acc: any, doc) => {
          const key = doc.title || doc.fileName || doc.sourceUrl || 'Untitled';
          if (!acc[key]) {
            acc[key] = {
              id: doc.id, // Use the first document chunk's ID as the main document ID
              title: doc.title,
              type: doc.type,
              sourceUrl: doc.sourceUrl,
              fileName: doc.fileName,
              fileSize: doc.fileSize,
              mimeType: doc.mimeType,
              createdAt: doc.createdAt,
              chunks: [],
            };
          }
          acc[key].chunks.push({
            id: doc.id,
            content: doc.content,
            chunkIndex: doc.chunkIndex,
            vectorId: doc.vectorId,
          });
          return acc;
        }, {});

        const documents = Object.values(groupedDocuments).map((value: any) => ({
          ...value,
          chunkCount: value.chunks.length,
        }));

        return res.status(200).json(documents);

      case 'DELETE':
        // Delete a document and its vectors
        const { documentId } = req.body;

        if (!documentId) {
          return res.status(400).json({ error: 'Document ID is required' });
        }

        // Get the organization's knowledge base
        const orgKnowledgeBase = await prisma.knowledgeBase.findUnique({
          where: { organizationId },
        });

        if (!orgKnowledgeBase) {
          return res.status(404).json({ error: 'Knowledge base not found' });
        }

        // Find documents to delete (by title, fileName, or sourceUrl)
        const documentsToDelete = await prisma.knowledgeBaseDocument.findMany({
          where: {
            knowledgeBaseId: orgKnowledgeBase.id,
            OR: [
              { title: documentId },
              { fileName: documentId },
              { sourceUrl: documentId },
            ],
          },
        });

        if (documentsToDelete.length === 0) {
          return res.status(404).json({ error: 'Document not found' });
        }

        // Delete vectors from Chroma
        if (orgKnowledgeBase.chromaCollectionId) {
          const vectorIds = documentsToDelete
            .map(doc => doc.vectorId)
            .filter(id => id !== null) as string[];

          if (vectorIds.length > 0) {
            try {
              await ChromaService.deleteDocuments(orgKnowledgeBase.chromaCollectionId, vectorIds);
            } catch (chromaError) {
              logger.warn('Failed to delete vectors from Chroma', {
                error: chromaError instanceof Error ? chromaError.message : 'Unknown error',
                vectorIds,
              });
            }
          }
        }

        // Delete documents from database
        await prisma.knowledgeBaseDocument.deleteMany({
          where: {
            id: {
              in: documentsToDelete.map(doc => doc.id),
            },
          },
        });

        // Update vector count
        await prisma.knowledgeBase.update({
          where: { id: orgKnowledgeBase.id },
          data: {
            vectorCount: {
              decrement: documentsToDelete.length,
            },
          },
        });

        logger.info(`Deleted ${documentsToDelete.length} document chunks`, {
          documentId,
          organizationId,
        });

        return res.status(200).json({
          message: 'Document deleted successfully',
          deletedChunks: documentsToDelete.length,
        });

      default:
        res.setHeader('Allow', ['GET', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in knowledge base documents endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
