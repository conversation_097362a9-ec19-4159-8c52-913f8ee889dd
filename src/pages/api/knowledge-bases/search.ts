import { a<PERSON><PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { KnowledgeBaseService } from '@/services/knowledge-base.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;
    const { query, nResults = 10 } = req.body;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    if (query.trim().length === 0) {
      return res.status(400).json({ error: 'Search query cannot be empty' });
    }

    // Check if knowledge base exists
    const knowledgeBase = await prisma.knowledgeBase.findUnique({
      where: { organizationId },
    });

    if (!knowledgeBase) {
      return res.status(200).json({
        results: [],
        message: 'No knowledge base found for this organization',
      });
    }

    if (knowledgeBase.vectorCount === 0) {
      return res.status(200).json({
        results: [],
        message: 'Knowledge base is empty',
      });
    }

    // Search the knowledge base
    const results = await KnowledgeBaseService.queryKnowledgeBase(
      organizationId,
      query.trim(),
      Math.min(Math.max(1, parseInt(nResults) || 10), 100) // Limit between 1 and 100
    );

    logger.info('Knowledge base search completed', {
      organizationId,
      query: query.trim(),
      resultsCount: results.length,
      nResults,
    });

    return res.status(200).json({
      results,
      query: query.trim(),
      totalResults: results.length,
    });
  } catch (error) {
    logger.error('Error searching knowledge base', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    return res.status(500).json({
      error: 'Failed to search knowledge base',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}, {
  requireAuth: true,
});
