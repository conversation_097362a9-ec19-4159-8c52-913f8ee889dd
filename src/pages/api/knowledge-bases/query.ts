import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { KnowledgeBaseService } from '@/services/knowledge-base.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Get request body
    const {
      knowledgeBaseId,
      agentId,
      query,
      numResults,
    } = req.body;

    // Validate required fields
    if ((!knowledgeBaseId && !agentId) || !query) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['Either knowledgeBaseId or agentId', 'query'],
      });
    }

    // If knowledgeBaseId is provided, query a specific knowledge base
    if (knowledgeBaseId) {
      // Check if the knowledge base exists and belongs to the user's organization
      const knowledgeBase = await prisma.knowledgeBase.findFirst({
        where: {
          id: knowledgeBaseId,
          organizationId,
        },
      });

      if (!knowledgeBase) {
        return res.status(404).json({ error: 'Knowledge base not found' });
      }

      // Query the knowledge base
      const results = await KnowledgeBaseService.queryKnowledgeBase(
        knowledgeBaseId,
        query
      );

      return res.status(200).json({ results });
    }

    // If agentId is provided, generate a response using all knowledge bases for the agent
    if (agentId) {
      // Check if the agent exists and belongs to the user's organization
      const agent = await prisma.agent.findFirst({
        where: {
          id: agentId,
          organizationId,
        },
      });

      if (!agent) {
        return res.status(404).json({ error: 'Agent not found' });
      }

      // Generate a response
      const response = await KnowledgeBaseService.generateResponse(
        agentId,
        query
      );

      return res.status(200).json({ response });
    }
  } catch (error) {
    logger.error('Error in knowledge base query endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
