import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { KnowledgeBaseService } from '@/services/knowledge-base.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method, query } = req;
  const { id } = query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Knowledge base ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the knowledge base exists and belongs to the user's organization
    const knowledgeBase = await prisma.knowledgeBase.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!knowledgeBase) {
      return res.status(404).json({ error: 'Knowledge base not found' });
    }

    switch (method) {
      case 'GET':
        // Get linked agents
        const linkedAgents = await prisma.knowledgeBaseAgent.findMany({
          where: {
            knowledgeBaseId: id,
          },
          include: {
            agent: {
              select: {
                id: true,
                name: true,
                description: true,
                type: true,
                isActive: true,
              },
            },
          },
        });

        return res.status(200).json(linkedAgents.map(link => link.agent));

      case 'POST':
        // Link agent to knowledge base
        const { agentId } = req.body;

        if (!agentId) {
          return res.status(400).json({ error: 'Agent ID is required' });
        }

        // Verify the agent belongs to this organization
        const agent = await prisma.agent.findFirst({
          where: {
            id: agentId,
            organizationId,
          },
        });

        if (!agent) {
          return res.status(404).json({ error: 'Agent not found or does not belong to your organization' });
        }

        // Link the knowledge base to the agent
        const result = await KnowledgeBaseService.linkToAgent(id, agentId);

        return res.status(200).json(result);

      case 'DELETE':
        // Unlink agent from knowledge base
        const { agentId: unlinkAgentId } = req.body;

        if (!unlinkAgentId) {
          return res.status(400).json({ error: 'Agent ID is required' });
        }

        // Unlink the knowledge base from the agent
        const unlinkResult = await KnowledgeBaseService.unlinkFromAgent(id, unlinkAgentId);

        return res.status(200).json(unlinkResult);

      default:
        res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in knowledge base agents endpoint', {
      method,
      knowledgeBaseId: id,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
