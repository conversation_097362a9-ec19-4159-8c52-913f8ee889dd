import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { KnowledgeBaseService } from '@/services/knowledge-base.service';

/**
 * Quick setup endpoint for knowledge bases
 * POST /api/knowledge-bases/quick-setup
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get user's organization
    const organizationMember = await prisma.organizationMember.findFirst({
      where: { userId: session.user.id },
      include: { organization: true },
    });

    if (!organizationMember) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = organizationMember.organizationId;
    const { content, title = 'Company Information' } = req.body;

    if (!content || typeof content !== 'string') {
      return res.status(400).json({ error: 'Content is required' });
    }

    logger.info('Quick knowledge base setup requested', {
      organizationId,
      userId: session.user.id,
      contentLength: content.length,
      title,
    });

    // Add the content to the knowledge base
    await KnowledgeBaseService.addDocument(
      organizationId,
      title,
      content,
      'TEXT',
      {
        source: 'quick-setup',
        createdBy: session.user.id,
        timestamp: new Date().toISOString(),
      }
    );

    logger.info('Knowledge base content added successfully', {
      organizationId,
      title,
      contentLength: content.length,
    });

    return res.status(200).json({
      success: true,
      message: 'Knowledge base content added successfully',
      organizationId,
      title,
      contentLength: content.length,
    });

  } catch (error) {
    logger.error('Error in quick knowledge base setup', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.body?.userId,
    });

    return res.status(500).json({ 
      error: 'Failed to setup knowledge base',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
