import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { EmailReputationService } from '@/services/email-reputation.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Organization ID is required' });
  }

  try {
    // Check if the user is a member of the organization
    const organizationMember = await prisma.organizationMember.findFirst({
      where: {
        organizationId: id,
        userId: req.user.id
      }
    });

    if (!organizationMember) {
      return res.status(403).json({ error: 'You do not have access to this organization' });
    }

    switch (method) {
      case 'POST':
        // Start the reputation check process
        // This will run asynchronously to avoid timeout
        EmailReputationService.checkAllAccountsReputation(id)
          .catch(error => {
            logger.error('Error in bulk reputation check', {
              organizationId: id,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          });

        return res.status(200).json({
          success: true,
          message: 'Reputation check started for all email accounts in the organization'
        });

      default:
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in organization reputation check endpoint', {
      method,
      userId: req.user.id,
      organizationId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({
      error: 'Failed to start reputation check',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}, {
  requireAuth: true,
});
