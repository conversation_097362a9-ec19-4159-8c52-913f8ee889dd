import { api<PERSON><PERSON><PERSON> } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { logger } from '@/lib/logger';
import { EmailVerificationService } from '@/services/email-verification.service';

export default apiHandler(async (req, res: NextApiResponse) => {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const { token } = req.body;

    logger.info('Verifying token', { token: token ? token.substring(0, 10) + '...' : 'undefined' });

    if (!token) {
      return res.status(400).json({ error: 'Verification token is required' });
    }

    const emailAccountId = await EmailVerificationService.verifyEmailAccount(token);

    res.status(200).json({
      success: true,
      message: 'Email account verified successfully',
      emailAccountId
    });
  } catch (error) {
    logger.error('Email account verification failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    res.status(400).json({ error: error instanceof Error ? error.message : 'Verification failed' });
  }
});
