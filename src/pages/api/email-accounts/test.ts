import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { sendEmail } from '@/lib/email';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  try {
    const {
      emailAccountId,
      testEmail,
      subject,
      content,
      overridePassword
    } = req.body;

    if (!testEmail) {
      return res.status(400).json({ error: 'Test email address is required' });
    }

    if (!emailAccountId) {
      return res.status(400).json({ error: 'Email account is required' });
    }

    // Get the email account
    const emailAccount = await prisma.emailAccount.findFirst({
      where: {
        id: emailAccountId,
        userId: req.user.id,
      },
    });

    if (!emailAccount) {
      return res.status(404).json({ error: 'Email account not found' });
    }

    // Send the test email
    await sendEmail({
      from: emailAccount.email,
      to: testEmail,
      subject: subject || 'Test Email',
      html: content || 'This is a test email to verify your SMTP settings are working correctly.',
      emailAccountId: emailAccount.id,
      overridePassword: overridePassword,
    });

    // Update the lastUsed timestamp
    await prisma.emailAccount.update({
      where: { id: emailAccountId },
      data: { lastUsed: new Date() },
    });

    return res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error sending test email', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: error instanceof Error ? error.message : 'Failed to send test email' });
  }
}, {
  requireAuth: true,
});
