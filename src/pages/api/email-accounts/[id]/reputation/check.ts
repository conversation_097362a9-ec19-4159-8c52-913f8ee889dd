import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

import { ReputationMonitoringService } from '@/services/reputation-monitoring.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Email account ID is required' });
  }

  try {
    // Check if the email account belongs to the user
    const emailAccount = await prisma.emailAccount.findFirst({
      where: {
        id,
        userId: req.user.id,
      },
    });

    if (!emailAccount) {
      return res.status(404).json({ error: 'Email account not found' });
    }

    switch (method) {
      case 'POST':
        try {
          // Use the ReputationMonitoringService to check reputation
          const reputationResult = await ReputationMonitoringService.checkReputation(id);

          // Get the latest reputation check
          const reputationCheck = await prisma.reputationCheck.findFirst({
            where: { emailAccountId: id },
            orderBy: { createdAt: 'desc' },
          });

          if (!reputationCheck) {
            return res.status(500).json({ error: 'Failed to check reputation' });
          }

          return res.status(200).json({
            score: reputationResult.score,
            lastChecked: reputationCheck.createdAt,
            provider: reputationResult.provider,
            details: reputationResult.details,
            recommendations: await ReputationMonitoringService.getReputationRecommendations(id),
          });
        } catch (error) {
          logger.error('Error checking reputation', {
            emailAccountId: id,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
          return res.status(500).json({ error: 'Failed to check reputation' });
        }

      default:
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in check email reputation endpoint', {
      method,
      userId: req.user.id,
      emailAccountId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
