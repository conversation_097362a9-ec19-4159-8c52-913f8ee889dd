import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { ImapService } from '@/services/imap.service';
import Imap from 'imap';
import { encrypt, decrypt } from '@/lib/crypto';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Email account ID is required' });
  }

  try {
    // Check if the email account belongs to the user
    const emailAccount = await prisma.emailAccount.findFirst({
      where: {
        id,
        userId: req.user.id,
      },
    });

    if (!emailAccount) {
      return res.status(404).json({ error: 'Email account not found' });
    }

    switch (method) {
      case 'GET':
        // Get IMAP settings
        return res.status(200).json({
          imapHost: emailAccount.imapHost,
          imapPort: emailAccount.imapPort,
          imapUsername: emailAccount.imapUsername,
          imapEnabled: emailAccount.imapEnabled,
          lastImapSync: emailAccount.lastImapSync,
        });

      case 'POST':
        // Update IMAP settings
        const { imapHost, imapPort, imapUsername, imapPassword, imapEnabled } = req.body;

        // Validate required fields if enabling IMAP
        if (imapEnabled && (!imapHost || !imapPort)) {
          return res.status(400).json({ error: 'IMAP host and port are required' });
        }

        // Get the password to use for testing
        let testPassword = '';
        if (imapPassword) {
          // Use the provided password
          testPassword = imapPassword;
        } else if (emailAccount.imapPassword) {
          // Use the existing password
          try {
            testPassword = await decrypt(emailAccount.imapPassword);
          } catch (decryptError) {
            logger.error('Failed to decrypt existing IMAP password for testing', {
              error: decryptError instanceof Error ? decryptError.message : 'Unknown error',
            });
            return res.status(500).json({ error: 'Failed to decrypt existing IMAP password' });
          }
        } else if (emailAccount.smtpPassword) {
          // Fall back to SMTP password
          try {
            testPassword = await decrypt(emailAccount.smtpPassword);
          } catch (decryptError) {
            logger.error('Failed to decrypt SMTP password for IMAP testing', {
              error: decryptError instanceof Error ? decryptError.message : 'Unknown error',
            });
            return res.status(500).json({ error: 'Failed to decrypt SMTP password' });
          }
        }

        // If enabling IMAP, test the connection first
        if (imapEnabled) {
          try {
            await testImapConnection({
              host: imapHost,
              port: imapPort,
              user: imapUsername || emailAccount.email,
              password: testPassword,
            });
          } catch (error) {
            return res.status(400).json({
              error: 'Failed to connect to IMAP server',
              details: error instanceof Error ? error.message : 'Unknown error',
            });
          }
        }

        // Prepare update data
        const updateData: any = {
          imapHost: imapHost || null,
          imapPort: imapPort ? parseInt(imapPort) : null,
          imapUsername: imapUsername || null,
          imapEnabled: imapEnabled || false,
        };

        // Encrypt password if provided
        if (imapPassword) {
          try {
            updateData.imapPassword = await encrypt(imapPassword);
            logger.info('IMAP password encrypted successfully for update');
          } catch (encryptError) {
            logger.error('Failed to encrypt IMAP password for update:', encryptError);
            return res.status(500).json({ error: 'Failed to encrypt IMAP password' });
          }
        }

        // Update the email account
        const updatedAccount = await prisma.emailAccount.update({
          where: { id },
          data: updateData,
        });

        // If IMAP is enabled, trigger an initial sync
        if (updatedAccount.imapEnabled) {
          // Don't await this to avoid blocking the response
          ImapService.fetchEmails(id).catch((error) => {
            logger.error('Error during initial IMAP sync', {
              emailAccountId: id,
              error: error instanceof Error ? error.message : 'Unknown error',
            });
          });
        }

        return res.status(200).json({
          imapHost: updatedAccount.imapHost,
          imapPort: updatedAccount.imapPort,
          imapUsername: updatedAccount.imapUsername,
          imapEnabled: updatedAccount.imapEnabled,
          lastImapSync: updatedAccount.lastImapSync,
        });

      case 'DELETE':
        // Disable IMAP
        await prisma.emailAccount.update({
          where: { id },
          data: {
            imapEnabled: false,
          },
        });

        return res.status(200).json({ success: true });

      default:
        res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in IMAP settings endpoint', {
      method,
      userId: req.user.id,
      emailAccountId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});

/**
 * Test IMAP connection
 */
async function testImapConnection(config: {
  host: string;
  port: number;
  user: string;
  password: string;
}): Promise<void> {
  return new Promise((resolve, reject) => {
    logger.info('Testing IMAP connection', {
      host: config.host,
      port: config.port,
      user: config.user,
      passwordLength: config.password?.length || 0,
    });

    const imap = new Imap({
      user: config.user,
      password: config.password,
      host: config.host,
      port: config.port,
      tls: true,
      tlsOptions: { rejectUnauthorized: false }, // For development, consider removing in production
      authTimeout: 15000, // 15 seconds timeout
      debug: (info) => logger.debug('IMAP debug', { info }),
    });

    imap.once('ready', () => {
      logger.info('IMAP connection successful');
      imap.end();
      resolve();
    });

    imap.once('error', (err) => {
      logger.error('IMAP connection error', {
        error: err.message,
        source: err.source,
        stack: err.stack,
      });
      reject(err);
    });

    imap.once('end', () => {
      logger.info('IMAP connection ended');
    });

    imap.connect();
  });
}
