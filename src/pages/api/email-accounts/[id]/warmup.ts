import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { WarmupService } from '@/services/warmup.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Email account ID is required' });
  }

  try {
    // Check if the email account belongs to the user
    const emailAccount = await prisma.emailAccount.findFirst({
      where: {
        id,
        userId: req.user.id,
      },
    });

    if (!emailAccount) {
      return res.status(404).json({ error: 'Email account not found' });
    }

    switch (method) {
      case 'GET':
        // Get warmup status and statistics
        const warmupActivities = await prisma.warmupActivity.findMany({
          where: { emailAccountId: id },
        });

        const emailsSent = warmupActivities.length;
        const emailsOpened = warmupActivities.filter(a => a.openedAt).length;
        const emailsReplied = warmupActivities.filter(a => a.repliedAt).length;

        // Calculate progress (assuming a 30-day warmup)
        let progress = 0;
        if (emailAccount.warmupStatus === 'IN_PROGRESS') {
          const startDate = warmupActivities[0]?.sentAt;
          if (startDate) {
            const daysPassed = Math.floor((Date.now() - startDate.getTime()) / (1000 * 60 * 60 * 24));
            progress = Math.min(Math.round((daysPassed / 30) * 100), 100);
          }
        } else if (emailAccount.warmupStatus === 'COMPLETED') {
          progress = 100;
        }

        return res.status(200).json({
          status: emailAccount.warmupStatus || null,
          emailsSent,
          emailsOpened,
          emailsReplied,
          progress,
          startedAt: warmupActivities[0]?.sentAt || null,
          completedAt: emailAccount.warmupStatus === 'COMPLETED' ? 
            warmupActivities[warmupActivities.length - 1]?.sentAt || null : null,
        });

      case 'POST':
        // Start or update warmup
        const { enabled, dailyIncrement, maxDailyEmails, warmupDuration, includeWeekends } = req.body;

        if (enabled === false) {
          // Stop warmup
          await prisma.emailAccount.update({
            where: { id },
            data: {
              warmupStatus: null,
            },
          });

          return res.status(200).json({ status: 'stopped' });
        } else {
          // Start or update warmup
          const warmupConfig = {
            dailyIncrement: dailyIncrement || 5,
            maxDailyEmails: maxDailyEmails || 40,
            duration: warmupDuration || 30,
            includeWeekends: includeWeekends !== false,
          };

          // Start the warmup process
          await WarmupService.startWarmup(id, warmupConfig);

          return res.status(200).json({ 
            status: 'started',
            config: warmupConfig,
          });
        }

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in email warmup endpoint', {
      method,
      userId: req.user.id,
      emailAccountId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
