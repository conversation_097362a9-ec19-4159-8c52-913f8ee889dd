import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { ImapService } from '@/services/imap.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Email account ID is required' });
  }

  try {
    // Check if the email account belongs to the user
    const emailAccount = await prisma.emailAccount.findFirst({
      where: {
        id,
        userId: req.user.id,
      },
    });

    if (!emailAccount) {
      return res.status(404).json({ error: 'Email account not found' });
    }

    if (!emailAccount.imapEnabled) {
      return res.status(400).json({ error: 'IMAP is not enabled for this account' });
    }

    switch (method) {
      case 'POST':
        // Trigger IMAP sync
        await ImapService.fetchEmails(id);

        // Get the updated account
        const updatedAccount = await prisma.emailAccount.findUnique({
          where: { id },
        });

        return res.status(200).json({
          success: true,
          lastImapSync: updatedAccount?.lastImapSync,
        });

      default:
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in IMAP sync endpoint', {
      method,
      userId: req.user.id,
      emailAccountId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({
      error: 'Failed to sync emails',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}, {
  requireAuth: true,
});
