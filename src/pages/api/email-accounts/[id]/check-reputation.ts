import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { EmailReputationService } from '@/services/email-reputation.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Email account ID is required' });
  }

  try {
    // Check if the email account exists and belongs to the user
    const emailAccount = await prisma.emailAccount.findFirst({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!emailAccount) {
      return res.status(404).json({ error: 'Email account not found' });
    }

    switch (method) {
      case 'POST':
        // Extract domain from email
        const domain = emailAccount.email.split('@')[1];
        
        // Check domain reputation
        const reputationResult = await EmailReputationService.checkDomainReputation(domain);
        
        // Save the results
        await EmailReputationService.saveReputationCheck(
          emailAccount.id,
          reputationResult.score,
          reputationResult.provider,
          reputationResult.details
        );

        // Get the updated account with the latest reputation check
        const updatedAccount = await prisma.emailAccount.findUnique({
          where: { id },
          include: {
            reputationChecks: {
              orderBy: {
                createdAt: 'desc'
              },
              take: 5
            }
          }
        });

        return res.status(200).json({
          success: true,
          reputationScore: reputationResult.score,
          provider: reputationResult.provider,
          details: reputationResult.details,
          account: updatedAccount
        });

      default:
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in email reputation check endpoint', {
      method,
      userId: req.user.id,
      emailAccountId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({
      error: 'Failed to check email reputation',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}, {
  requireAuth: true,
});
