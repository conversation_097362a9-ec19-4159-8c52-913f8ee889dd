import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;
  const { page = '1', limit = '20', isRepliedTo, campaignId } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Email account ID is required' });
  }

  try {
    // Check if the email account belongs to the user
    const emailAccount = await prisma.emailAccount.findFirst({
      where: {
        id,
        userId: req.user.id,
      },
    });

    if (!emailAccount) {
      return res.status(404).json({ error: 'Email account not found' });
    }

    switch (method) {
      case 'GET':
        // Build the query
        const where: any = { emailAccountId: id };

        // Add filters
        if (isRepliedTo !== undefined) {
          where.isRepliedTo = isRepliedTo === 'true';
        }

        if (campaignId) {
          where.campaignId = campaignId;
        }

        // Get total count
        const totalCount = await prisma.receivedEmail.count({ where });

        // Parse pagination params
        const pageNum = parseInt(page as string, 10);
        const limitNum = parseInt(limit as string, 10);
        const skip = (pageNum - 1) * limitNum;

        // Get emails
        const emails = await prisma.receivedEmail.findMany({
          where,
          include: {
            campaign: {
              select: {
                id: true,
                name: true,
              },
            },
            lead: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
            agentReply: {
              select: {
                id: true,
                subject: true,
                textContent: true,
                sentAt: true,
                status: true,
              },
            },
          },
          orderBy: { receivedAt: 'desc' },
          skip,
          take: limitNum,
        });

        return res.status(200).json({
          emails,
          pagination: {
            page: pageNum,
            limit: limitNum,
            totalCount,
            totalPages: Math.ceil(totalCount / limitNum),
          },
        });

      default:
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in received emails endpoint', {
      method,
      userId: req.user.id,
      emailAccountId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
