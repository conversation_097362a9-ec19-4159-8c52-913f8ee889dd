import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Email account ID is required' });
  }

  try {
    // Check if the email account belongs to the user
    const emailAccount = await prisma.emailAccount.findFirst({
      where: {
        id,
        userId: req.user.id,
      },
    });

    if (!emailAccount) {
      return res.status(404).json({ error: 'Email account not found' });
    }

    switch (method) {
      case 'GET':
        try {
          // Get the reputation data
          const reputationData: {
            score: number | null;
            lastChecked: Date | null;
            provider: string;
            details?: any;
          } = {
            score: emailAccount.reputationScore || null,
            lastChecked: emailAccount.lastChecked || null,
            provider: 'Composite', // Default provider
          };

          // Get the latest reputation check
          const latestCheck = await prisma.reputationCheck.findFirst({
            where: { emailAccountId: id },
            orderBy: { createdAt: 'desc' },
          });

          if (latestCheck) {
            reputationData.score = latestCheck.score;
            reputationData.lastChecked = latestCheck.createdAt;
            reputationData.provider = latestCheck.provider;
            reputationData.details = latestCheck.details;
          }

          // Get recommendations
          const { recommendations } = await import('@/services/reputation-monitoring.service')
            .then(module => module.ReputationMonitoringService.getReputationRecommendations(id));

          return res.status(200).json({
            ...reputationData,
            recommendations
          });
        } catch (error) {
          logger.error('Error getting reputation data', {
            emailAccountId: id,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
          return res.status(500).json({ error: 'Failed to get reputation data' });
        }

      default:
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in email reputation endpoint', {
      method,
      userId: req.user.id,
      emailAccountId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
