import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { InboxAgentService } from '@/services/inbox-agent.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Email account ID is required' });
  }

  try {
    // Verify the email account exists and belongs to the user
    const emailAccount = await prisma.emailAccount.findFirst({
      where: {
        id,
        userId: req.user.id,
      },
    });

    if (!emailAccount) {
      return res.status(404).json({ error: 'Email account not found' });
    }

    switch (method) {
      case 'GET':
        // Get AI agent configuration for the email account
        const agentConfig = await InboxAgentService.getAgentConfiguration(id);
        return res.status(200).json(agentConfig);

      case 'POST':
        // Enable/configure AI agent for the email account
        const { agentId, aiReplyEnabled, aiReplyDelay, aiReplyPrompt } = req.body;

        if (!agentId) {
          return res.status(400).json({ error: 'Agent ID is required' });
        }

        // Verify the agent exists and belongs to the user's organization
        const agent = await prisma.agent.findFirst({
          where: {
            id: agentId,
            organization: {
              ownerId: req.user.id,
            },
          },
        });

        if (!agent) {
          return res.status(404).json({ error: 'Agent not found or not accessible' });
        }

        const updatedAccount = await InboxAgentService.enableAgentForInbox(id, agentId, {
          aiReplyEnabled: aiReplyEnabled !== undefined ? aiReplyEnabled : true,
          aiReplyDelay: aiReplyDelay || 0,
          aiReplyPrompt: aiReplyPrompt || null,
        });

        return res.status(200).json({
          success: true,
          emailAccount: updatedAccount,
        });

      case 'PUT':
        // Update AI agent configuration
        const updateData = req.body;
        
        // If agentId is being changed, verify the new agent
        if (updateData.agentId) {
          const newAgent = await prisma.agent.findFirst({
            where: {
              id: updateData.agentId,
              organization: {
                ownerId: req.user.id,
              },
            },
          });

          if (!newAgent) {
            return res.status(404).json({ error: 'New agent not found or not accessible' });
          }
        }

        const updatedConfig = await prisma.emailAccount.update({
          where: { id },
          data: {
            aiAgentEnabled: updateData.aiAgentEnabled,
            aiAgentId: updateData.agentId,
            aiReplyEnabled: updateData.aiReplyEnabled,
            aiReplyDelay: updateData.aiReplyDelay,
            aiReplyPrompt: updateData.aiReplyPrompt,
          },
          include: {
            agent: {
              include: {
                knowledgeBases: {
                  include: {
                    knowledgeBase: true,
                  },
                },
              },
            },
          },
        });

        return res.status(200).json({
          success: true,
          emailAccount: updatedConfig,
        });

      case 'DELETE':
        // Disable AI agent for the email account
        const disabledAccount = await InboxAgentService.disableAgentForInbox(id);
        
        return res.status(200).json({
          success: true,
          emailAccount: disabledAccount,
        });

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in email account agent endpoint', {
      method,
      emailAccountId: id,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}, {
  requireAuth: true,
});
