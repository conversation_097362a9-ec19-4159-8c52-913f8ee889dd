import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { encrypt } from '@/lib/crypto';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Email account ID is required' });
  }

  try {
    // Check if the email account belongs to the user
    const emailAccount = await prisma.emailAccount.findFirst({
      where: {
        id,
        userId: req.user.id,
      },
    });

    if (!emailAccount) {
      return res.status(404).json({ error: 'Email account not found' });
    }

    switch (method) {
      case 'GET':
        return res.status(200).json(emailAccount);

      case 'PUT':
        const {
          name,
          smtpHost,
          smtpPort,
          smtpUsername,
          smtpPassword,
          // Signature fields
          signatureEnabled,
          signatureName,
          signatureTitle,
          signatureCompany,
          signaturePhone,
          signatureEmail,
          signatureWebsite,
          signatureCustom
        } = req.body;

        // Prepare update data
        const updateData: any = {
          name: name || undefined,
          smtpHost: smtpHost || undefined,
          smtpPort: smtpPort ? parseInt(smtpPort) : undefined,
          smtpUsername: smtpUsername || undefined,
        };

        // Handle signature fields
        if (signatureEnabled !== undefined) {
          updateData.signatureEnabled = signatureEnabled;
        }
        if (signatureName !== undefined) {
          updateData.signatureName = signatureName || null;
        }
        if (signatureTitle !== undefined) {
          updateData.signatureTitle = signatureTitle || null;
        }
        if (signatureCompany !== undefined) {
          updateData.signatureCompany = signatureCompany || null;
        }
        if (signaturePhone !== undefined) {
          updateData.signaturePhone = signaturePhone || null;
        }
        if (signatureEmail !== undefined) {
          updateData.signatureEmail = signatureEmail || null;
        }
        if (signatureWebsite !== undefined) {
          updateData.signatureWebsite = signatureWebsite || null;
        }
        if (signatureCustom !== undefined) {
          updateData.signatureCustom = signatureCustom || null;
        }

        // Encrypt password if provided
        if (smtpPassword) {
          try {
            updateData.smtpPassword = await encrypt(smtpPassword);
            logger.info('SMTP password encrypted successfully for update');
          } catch (encryptError) {
            logger.error('Failed to encrypt SMTP password for update:', encryptError);
            return res.status(500).json({ error: 'Failed to encrypt SMTP password' });
          }
        }

        const updatedAccount = await prisma.emailAccount.update({
          where: { id },
          data: updateData,
        });

        return res.status(200).json(updatedAccount);

      case 'DELETE':
        await prisma.emailAccount.delete({
          where: { id },
        });

        return res.status(200).json({ success: true });

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in email account endpoint', {
      method,
      userId: req.user.id,
      emailAccountId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
