import { api<PERSON><PERSON><PERSON> } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import type { ExtendedNextApiRequest } from '@/lib/apiHandler';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { EmailVerificationService } from '@/services/email-verification.service';
import { logger } from '@/lib/logger';
import { encrypt } from '@/lib/crypto';

const createEmailAccountSchema = z.object({
  email: z.string().email(),
  name: z.string().optional(),
  provider: z.enum(['smtp', 'gmail']),
  smtpHost: z.string().optional(),
  smtpPort: z.number().optional(),
  smtpUsername: z.string().optional(),
  smtpPassword: z.string().optional(),
});

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  if (req.method === 'GET') {
    try {
      const emailAccounts = await prisma.emailAccount.findMany({
        where: {
          userId: req.user.id
        },
        select: {
          id: true,
          email: true,
          name: true,
          provider: true,
          status: true,
          lastUsed: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return res.status(200).json(emailAccounts);
    } catch (error) {
      console.error('Failed to fetch email accounts:', error);
      return res.status(500).json({ error: 'Failed to fetch email accounts' });
    }
  }

  if (req.method === 'POST') {
    try {
      const data = createEmailAccountSchema.parse(req.body);

      // Encrypt the password if provided
      let processedData = { ...data };
      if (processedData.smtpPassword) {
        try {
          processedData.smtpPassword = await encrypt(processedData.smtpPassword);
          logger.info('SMTP password encrypted successfully');
        } catch (encryptError) {
          logger.error('Failed to encrypt SMTP password:', encryptError);
          return res.status(500).json({ error: 'Failed to encrypt SMTP password' });
        }
      }

      // Make sure required fields are present
      if (!processedData.email || !processedData.provider) {
        return res.status(400).json({ error: 'Email and provider are required' });
      }

      const emailAccount = await prisma.emailAccount.create({
        data: {
          email: processedData.email,
          provider: processedData.provider,
          name: processedData.name,
          smtpHost: processedData.smtpHost,
          smtpPort: processedData.smtpPort,
          smtpUsername: processedData.smtpUsername,
          smtpPassword: processedData.smtpPassword,
          userId: req.user.id,
          status: 'pending',
        }
      });

      // Send verification email
      try {
        await EmailVerificationService.sendVerificationEmail(emailAccount.id);
      } catch (emailError) {
        logger.error('Failed to send verification email:', emailError);
        // Continue even if sending fails
      }

      return res.status(201).json(emailAccount);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Invalid input data', details: error.errors });
      }
      console.error('Failed to create email account:', error);
      return res.status(500).json({ error: 'Failed to create email account' });
    }
  }

  return res.status(405).json({ error: 'Method not allowed' });
}, {
  requireAuth: true
});
