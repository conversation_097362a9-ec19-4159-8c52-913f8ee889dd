import type { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '@/lib/logger';

/**
 * Debug endpoint to check environment variables in production
 * This helps diagnose configuration issues
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check environment variables with detailed analysis
    const envCheck = {
      NODE_ENV: process.env.NODE_ENV,
      AI_PROVIDER: {
        value: process.env.AI_PROVIDER,
        isSet: !!process.env.AI_PROVIDER,
        raw: JSON.stringify(process.env.AI_PROVIDER),
      },
      OPENAI_API_KEY: {
        isSet: !!process.env.OPENAI_API_KEY,
        length: process.env.OPENAI_API_KEY?.length || 0,
        prefix: process.env.OPENAI_API_KEY?.substring(0, 15) || 'none',
        suffix: process.env.OPENAI_API_KEY?.substring(-10) || 'none',
        isDefault: process.env.OPENAI_API_KEY === 'your-openai-api-key-here',
        hasQuotes: process.env.OPENAI_API_KEY?.includes('"') || false,
        raw: process.env.OPENAI_API_KEY ? `"${process.env.OPENAI_API_KEY.substring(0, 20)}..."` : 'undefined',
      },
      OPENAI_MODEL: {
        value: process.env.OPENAI_MODEL,
        isSet: !!process.env.OPENAI_MODEL,
        raw: JSON.stringify(process.env.OPENAI_MODEL),
      },
      OLLAMA_BASE_URL: {
        value: process.env.OLLAMA_BASE_URL,
        isSet: !!process.env.OLLAMA_BASE_URL,
        raw: JSON.stringify(process.env.OLLAMA_BASE_URL),
      },
      OLLAMA_MODEL: {
        value: process.env.OLLAMA_MODEL,
        isSet: !!process.env.OLLAMA_MODEL,
        raw: JSON.stringify(process.env.OLLAMA_MODEL),
      },
      OLLAMA_EMBEDDING_MODEL: {
        value: process.env.OLLAMA_EMBEDDING_MODEL,
        isSet: !!process.env.OLLAMA_EMBEDDING_MODEL,
        raw: JSON.stringify(process.env.OLLAMA_EMBEDDING_MODEL),
      },
      LANGCHAIN_API_KEY: {
        isSet: !!process.env.LANGCHAIN_API_KEY,
        length: process.env.LANGCHAIN_API_KEY?.length || 0,
        prefix: process.env.LANGCHAIN_API_KEY?.substring(0, 15) || 'none',
        raw: JSON.stringify(process.env.LANGCHAIN_API_KEY),
      },
      LANGCHAIN_PROJECT: {
        value: process.env.LANGCHAIN_PROJECT,
        isSet: !!process.env.LANGCHAIN_PROJECT,
        raw: JSON.stringify(process.env.LANGCHAIN_PROJECT),
      },
      LANGCHAIN_TRACING_V2: {
        value: process.env.LANGCHAIN_TRACING_V2,
        isSet: !!process.env.LANGCHAIN_TRACING_V2,
        raw: JSON.stringify(process.env.LANGCHAIN_TRACING_V2),
      },
      // Check all environment variables that start with certain prefixes
      allEnvKeys: Object.keys(process.env).filter(key =>
        key.startsWith('OPENAI_') ||
        key.startsWith('AI_') ||
        key.startsWith('OLLAMA_') ||
        key.startsWith('LANGCHAIN_')
      ).sort(),
    };

    logger.info('Environment variables check', envCheck);

    // Test AI provider service
    const { AIProviderService } = await import('@/services/ai-provider.service');

    const providerInfo = {
      currentProvider: AIProviderService.getProvider(),
      modelName: AIProviderService.getModelName(),
      embeddingModelName: AIProviderService.getEmbeddingModelName(),
      providerInfo: AIProviderService.getProviderInfo(),
    };

    logger.info('AI Provider service info', providerInfo);

    // Test OpenAI API key directly if it's set
    let openaiTest = null;
    if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your-openai-api-key-here') {
      try {
        const OpenAI = (await import('openai')).default;
        const openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });

        // Test with a simple completion
        const testResponse = await openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: 'Say "test successful"' }],
          max_tokens: 10,
        });

        openaiTest = {
          success: true,
          response: testResponse.choices[0]?.message?.content || 'No response',
        };
      } catch (error) {
        openaiTest = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }

    return res.status(200).json({
      success: true,
      environment: envCheck,
      aiProvider: providerInfo,
      openaiTest,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    logger.error('Environment check failed', {
      error: errorMessage,
      timestamp: new Date().toISOString(),
    });

    return res.status(500).json({
      success: false,
      error: errorMessage,
      timestamp: new Date().toISOString(),
    });
  }
}
