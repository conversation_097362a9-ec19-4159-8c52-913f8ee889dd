import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { logger } from '@/lib/logger';
import { ChromaService } from '@/services/chroma.service';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ error: `Method ${method} Not Allowed` });
  }

  try {
    const baseUrl = process.env.CHROMA_API_URL || 'https://chroma.wattlesol.com';

    logger.info('Testing Chroma API endpoints', { baseUrl });

    const results: any = {};

    // Test different API versions and endpoints
    const testEndpoints = [
      // Root endpoints
      { name: 'root', url: `${baseUrl}` },
      { name: 'api', url: `${baseUrl}/api` },

      // Version endpoints
      { name: 'v1-heartbeat', url: `${baseUrl}/api/v1/heartbeat` },
      { name: 'v1-version', url: `${baseUrl}/api/v1/version` },
      { name: 'v1-collections', url: `${baseUrl}/api/v1/collections` },

      // Try without /api prefix
      { name: 'direct-heartbeat', url: `${baseUrl}/heartbeat` },
      { name: 'direct-version', url: `${baseUrl}/version` },
      { name: 'direct-collections', url: `${baseUrl}/collections` },

      // Try different versions
      { name: 'v2-heartbeat', url: `${baseUrl}/api/v2/heartbeat` },
      { name: 'v2-version', url: `${baseUrl}/api/v2/version` },
      { name: 'v2-collections', url: `${baseUrl}/api/v2/collections` },
    ];

    for (const endpoint of testEndpoints) {
      try {
        logger.info(`Testing endpoint: ${endpoint.name}`, { url: endpoint.url });

        const response = await fetch(endpoint.url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        let data;
        const contentType = response.headers.get('content-type');

        if (contentType && contentType.includes('application/json')) {
          try {
            data = await response.json();
          } catch (e) {
            data = { error: 'Failed to parse JSON' };
          }
        } else {
          data = await response.text();
        }

        results[endpoint.name] = {
          status: response.status,
          statusText: response.statusText,
          contentType,
          data,
          success: response.ok,
        };

        logger.info(`Endpoint ${endpoint.name} result`, {
          status: response.status,
          success: response.ok,
          dataType: typeof data,
        });
      } catch (error) {
        results[endpoint.name] = {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        };

        logger.error(`Error testing endpoint ${endpoint.name}`, {
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Test collections endpoint with different methods
    const collectionsEndpoints = [
      { name: 'collections-get', url: `${baseUrl}/collections`, method: 'GET' },
      { name: 'collections-list', url: `${baseUrl}/api/v1/collections`, method: 'GET' },
    ];

    for (const endpoint of collectionsEndpoints) {
      try {
        const response = await fetch(endpoint.url, {
          method: endpoint.method,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        let data;
        try {
          data = await response.json();
        } catch (e) {
          data = await response.text();
        }

        results[endpoint.name] = {
          status: response.status,
          statusText: response.statusText,
          data,
          success: response.ok,
        };
      } catch (error) {
        results[endpoint.name] = {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        };
      }
    }

    // Test creating a collection with different API versions
    const testCollectionName = 'test_collection_' + Date.now();

    const createEndpoints = [
      { name: 'direct-create', url: `${baseUrl}/collections`, method: 'POST' },
      { name: 'v1-create', url: `${baseUrl}/api/v1/collections`, method: 'POST' },
    ];

    for (const endpoint of createEndpoints) {
      try {
        const response = await fetch(endpoint.url, {
          method: endpoint.method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: testCollectionName,
            metadata: { test: true },
          }),
        });

        let data;
        try {
          data = await response.json();
        } catch (e) {
          data = await response.text();
        }

        results[endpoint.name] = {
          status: response.status,
          statusText: response.statusText,
          data,
          success: response.ok,
        };
      } catch (error) {
        results[endpoint.name] = {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        };
      }
    }

    // Test the new Chroma service with proper hierarchy
    const testOrgId = 'test_org_debug_123';
    let chromaServiceTest = null;

    try {
      logger.info('Testing ChromaService with tenant/database/collection hierarchy');

      // Test creating tenant, database, and collection
      const collection = await ChromaService.createOrGetCollection(testOrgId);

      logger.info('Successfully created/got collection', { collection });

      // Test adding a document
      await ChromaService.addDocuments(testOrgId, [
        {
          id: 'test_doc_debug_1',
          content: 'This is a test document for debugging the Chroma service.',
          metadata: {
            type: 'test',
            title: 'Debug Test Document',
            timestamp: new Date().toISOString(),
          },
        },
      ]);

      logger.info('Successfully added test document');

      // Test querying
      const queryResults = await ChromaService.queryCollection(testOrgId, 'test document debug', 1);

      logger.info('Successfully queried collection', {
        resultsCount: queryResults.documents?.[0]?.length || 0,
      });

      chromaServiceTest = {
        success: true,
        collection,
        documentsAdded: 1,
        queryResults: queryResults.documents?.[0]?.length || 0,
        queryData: queryResults,
      };
    } catch (chromaError) {
      logger.error('Error testing ChromaService', {
        error: chromaError instanceof Error ? chromaError.message : 'Unknown error',
      });

      chromaServiceTest = {
        success: false,
        error: chromaError instanceof Error ? chromaError.message : 'Unknown error',
      };
    }

    return res.status(200).json({
      success: true,
      baseUrl,
      results,
      chromaServiceTest,
      summary: {
        workingEndpoints: Object.entries(results)
          .filter(([_, result]: [string, any]) => result.success)
          .map(([name]) => name),
        failedEndpoints: Object.entries(results)
          .filter(([_, result]: [string, any]) => !result.success)
          .map(([name]) => name),
      },
    });
  } catch (error) {
    logger.error('Error debugging Chroma API', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({
      error: 'Failed to debug Chroma API',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}, {
  requireAuth: false,
});
