import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    switch (method) {
      case 'GET':
        // Get all agents for the organization with knowledge base information
        const agents = await prisma.agent.findMany({
          where: { organizationId },
          include: {
            knowledgeBases: {
              include: {
                knowledgeBase: {
                  select: {
                    id: true,
                    name: true,
                    vectorCount: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        });

        return res.status(200).json(agents);

      case 'POST':
        // Create a new agent
        const { name, description, type, config, capabilities, isActive, systemPrompt } = req.body;

        if (!name) {
          return res.status(400).json({ error: 'Agent name is required' });
        }

        // Create the agent
        const agent = await prisma.agent.create({
          data: {
            name,
            description,
            type: type || 'email',
            config: config || {},
            systemPrompt,
            capabilities: capabilities || [],
            isActive: isActive !== undefined ? isActive : true,
            organizationId,
          },
        });

        return res.status(201).json(agent);

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in agents endpoint', {
      method,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
