import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method, query } = req;
  const { id } = query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Agent ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Check if the agent exists and belongs to the user's organization
    const agent = await prisma.agent.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    switch (method) {
      case 'GET':
        // Return the agent
        return res.status(200).json(agent);

      case 'PUT':
        // Update the agent
        const { name, description, type, config, capabilities, isActive, systemPrompt } = req.body;

        // Validate required fields
        if (!name) {
          return res.status(400).json({ error: 'Agent name is required' });
        }

        // Update the agent
        const updatedAgent = await prisma.agent.update({
          where: { id },
          data: {
            name,
            description,
            type: type || 'email',
            config: config || {},
            systemPrompt,
            capabilities: capabilities || [],
            isActive: isActive !== undefined ? isActive : true,
          },
        });

        return res.status(200).json(updatedAgent);

      case 'DELETE':
        // Check if the agent is used in any campaigns
        const campaigns = await prisma.campaign.findMany({
          where: {
            agentId: id,
          },
          select: {
            id: true,
            name: true,
          },
        });

        if (campaigns.length > 0) {
          return res.status(400).json({
            error: 'Cannot delete agent that is used in campaigns',
            campaigns,
          });
        }

        // Delete the agent
        await prisma.agent.delete({
          where: { id },
        });

        return res.status(200).json({ message: 'Agent deleted successfully' });

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in agent endpoint', {
      method,
      agentId: id,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({ error: 'Internal server error' });
  }
}, {
  requireAuth: true,
});
