import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method, query } = req;
  const { id: agentId } = query;

  if (!req.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (!agentId || typeof agentId !== 'string') {
    return res.status(400).json({ error: 'Agent ID is required' });
  }

  try {
    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user?.organizations[0]?.organization.id) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const organizationId = user.organizations[0].organization.id;

    // Verify the agent belongs to the user's organization
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        organizationId,
      },
    });

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    // Get the organization's knowledge base
    const knowledgeBase = await prisma.knowledgeBase.findUnique({
      where: { organizationId },
    });

    if (!knowledgeBase) {
      return res.status(404).json({ error: 'Knowledge base not found for this organization' });
    }

    switch (method) {
      case 'POST':
        // Link agent to knowledge base
        try {
          const existingLink = await prisma.knowledgeBaseAgent.findUnique({
            where: {
              knowledgeBaseId_agentId: {
                knowledgeBaseId: knowledgeBase.id,
                agentId: agent.id,
              },
            },
          });

          if (existingLink) {
            return res.status(200).json({ 
              message: 'Agent is already linked to knowledge base',
              link: existingLink,
            });
          }

          const link = await prisma.knowledgeBaseAgent.create({
            data: {
              knowledgeBaseId: knowledgeBase.id,
              agentId: agent.id,
            },
            include: {
              knowledgeBase: {
                select: {
                  id: true,
                  name: true,
                  vectorCount: true,
                },
              },
              agent: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          });

          logger.info('Agent linked to knowledge base', {
            agentId: agent.id,
            agentName: agent.name,
            knowledgeBaseId: knowledgeBase.id,
            organizationId,
          });

          return res.status(201).json({
            message: 'Agent linked to knowledge base successfully',
            link,
          });
        } catch (error) {
          if (error instanceof Error && error.message.includes('Unique constraint')) {
            return res.status(200).json({ 
              message: 'Agent is already linked to knowledge base',
            });
          }
          throw error;
        }

      case 'DELETE':
        // Unlink agent from knowledge base
        const existingLink = await prisma.knowledgeBaseAgent.findUnique({
          where: {
            knowledgeBaseId_agentId: {
              knowledgeBaseId: knowledgeBase.id,
              agentId: agent.id,
            },
          },
        });

        if (!existingLink) {
          return res.status(404).json({ error: 'Agent is not linked to knowledge base' });
        }

        await prisma.knowledgeBaseAgent.delete({
          where: {
            id: existingLink.id,
          },
        });

        logger.info('Agent unlinked from knowledge base', {
          agentId: agent.id,
          agentName: agent.name,
          knowledgeBaseId: knowledgeBase.id,
          organizationId,
        });

        return res.status(200).json({
          message: 'Agent unlinked from knowledge base successfully',
        });

      default:
        res.setHeader('Allow', ['POST', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in agent knowledge base endpoint', {
      method,
      agentId,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    return res.status(500).json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}, {
  requireAuth: true,
});
