import { api<PERSON><PERSON><PERSON>, ExtendedNextApiRequest } from '@/lib/apiHandler';
import { NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export default apiHandler(async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
  const { method } = req;
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Agent ID is required' });
  }

  try {
    // Verify the agent exists and belongs to the user's organization
    const agent = await prisma.agent.findFirst({
      where: {
        id,
        organization: {
          ownerId: req.user.id,
        },
      },
    });

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    switch (method) {
      case 'GET':
        // Get calendar configuration for the agent
        return res.status(200).json({
          calendarEnabled: agent.calendarEnabled,
          calendarProvider: agent.calendarProvider,
          calendarConfig: agent.calendarConfig,
          defaultMeetingDuration: agent.defaultMeetingDuration,
          availableTimeSlots: agent.availableTimeSlots,
        });

      case 'POST':
        // Enable/configure calendar for the agent
        const {
          calendarProvider,
          calendarConfig,
          defaultMeetingDuration,
          availableTimeSlots,
        } = req.body;

        if (!calendarProvider || !calendarConfig) {
          return res.status(400).json({ 
            error: 'Calendar provider and configuration are required' 
          });
        }

        // Validate calendar provider
        const validProviders = ['google', 'cal_com', 'outlook'];
        if (!validProviders.includes(calendarProvider)) {
          return res.status(400).json({ 
            error: 'Invalid calendar provider. Must be one of: ' + validProviders.join(', ')
          });
        }

        // Update agent with calendar configuration
        const updatedAgent = await prisma.agent.update({
          where: { id },
          data: {
            calendarEnabled: true,
            calendarProvider,
            calendarConfig,
            defaultMeetingDuration: defaultMeetingDuration || 30,
            availableTimeSlots,
            capabilities: {
              set: [...(agent.capabilities || []), 'appointment_scheduling'].filter(
                (cap, index, arr) => arr.indexOf(cap) === index
              ),
            },
          },
        });

        logger.info('Calendar enabled for agent', {
          agentId: id,
          calendarProvider,
          defaultMeetingDuration,
        });

        return res.status(200).json({
          success: true,
          agent: updatedAgent,
        });

      case 'PUT':
        // Update calendar configuration
        const updateData = req.body;

        const updatedCalendarAgent = await prisma.agent.update({
          where: { id },
          data: {
            calendarEnabled: updateData.calendarEnabled,
            calendarProvider: updateData.calendarProvider,
            calendarConfig: updateData.calendarConfig,
            defaultMeetingDuration: updateData.defaultMeetingDuration,
            availableTimeSlots: updateData.availableTimeSlots,
          },
        });

        return res.status(200).json({
          success: true,
          agent: updatedCalendarAgent,
        });

      case 'DELETE':
        // Disable calendar for the agent
        const disabledAgent = await prisma.agent.update({
          where: { id },
          data: {
            calendarEnabled: false,
            calendarProvider: null,
            calendarConfig: null,
            defaultMeetingDuration: null,
            availableTimeSlots: null,
            capabilities: {
              set: (agent.capabilities || []).filter(cap => cap !== 'appointment_scheduling'),
            },
          },
        });

        logger.info('Calendar disabled for agent', { agentId: id });

        return res.status(200).json({
          success: true,
          agent: disabledAgent,
        });

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    logger.error('Error in agent calendar endpoint', {
      method,
      agentId: id,
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}, {
  requireAuth: true,
});
