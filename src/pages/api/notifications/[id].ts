import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { NotificationService } from '@/services/notification.service';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);

  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const id = req.query.id as string;

  if (!id) {
    return res.status(400).json({ error: 'Notification ID is required' });
  }

  switch (req.method) {
    case 'PATCH':
      try {
        const notification = await NotificationService.markAsRead(id);
        return res.status(200).json(notification);
      } catch (error) {
        console.error('Failed to update notification:', error);
        return res.status(500).json({ error: 'Failed to update notification' });
      }

    case 'DELETE':
      try {
        await NotificationService.deleteNotification(id);
        return res.status(204).end();
      } catch (error) {
        console.error('Failed to delete notification:', error);
        return res.status(500).json({ error: 'Failed to delete notification' });
      }

    default:
      res.setHeader('Allow', ['PATCH', 'DELETE']);
      return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }
}
