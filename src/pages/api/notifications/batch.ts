import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { prisma } from '@/lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);

  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Get the current organization ID from the session or query
  const organizationId = req.query.organizationId as string || 
    session.user.currentOrganization?.id;

  if (!organizationId) {
    return res.status(400).json({ error: 'Organization ID is required' });
  }

  // Get the notification IDs from the request body
  const { ids } = req.body;

  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ error: 'Notification IDs are required' });
  }

  // Verify that all notifications belong to the current organization
  const notifications = await prisma.notification.findMany({
    where: {
      id: { in: ids },
      organizationId,
    },
  });

  if (notifications.length !== ids.length) {
    return res.status(403).json({ error: 'Some notifications do not belong to the current organization' });
  }

  try {
    switch (req.method) {
      case 'PATCH':
        const { action } = req.body;

        if (action === 'markAsRead') {
          // Mark multiple notifications as read
          await prisma.notification.updateMany({
            where: {
              id: { in: ids },
              organizationId,
            },
            data: {
              isRead: true,
            },
          });

          return res.status(200).json({ success: true, count: ids.length });
        } else {
          return res.status(400).json({ error: 'Invalid action' });
        }

      case 'DELETE':
        // Delete multiple notifications
        await prisma.notification.deleteMany({
          where: {
            id: { in: ids },
            organizationId,
          },
        });

        return res.status(200).json({ success: true, count: ids.length });

      default:
        res.setHeader('Allow', ['PATCH', 'DELETE']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error('Error processing batch operation:', error);
    return res.status(500).json({ error: 'Failed to process batch operation' });
  }
}
