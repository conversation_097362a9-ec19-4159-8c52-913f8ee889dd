import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { NotificationService } from '@/services/notification.service';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);

  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Get the current organization ID from the session or query
  const organizationId = req.query.organizationId as string || 
    session.user.currentOrganization?.id;

  if (!organizationId) {
    return res.status(400).json({ error: 'Organization ID is required' });
  }

  switch (req.method) {
    case 'GET':
      try {
        const notifications = await NotificationService.listNotifications(organizationId);
        return res.status(200).json(notifications);
      } catch (error) {
        console.error('Failed to fetch notifications:', error);
        return res.status(500).json({ error: 'Failed to fetch notifications' });
      }

    case 'POST':
      try {
        const { title, message, type } = req.body;
        
        if (!title || !message) {
          return res.status(400).json({ error: 'Title and message are required' });
        }

        const notification = await NotificationService.createNotification({
          title,
          message,
          type: type || 'info',
          organizationId,
          userId: session.user.id,
        });

        return res.status(201).json(notification);
      } catch (error) {
        console.error('Failed to create notification:', error);
        return res.status(500).json({ error: 'Failed to create notification' });
      }

    default:
      res.setHeader('Allow', ['GET', 'POST']);
      return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }
}
