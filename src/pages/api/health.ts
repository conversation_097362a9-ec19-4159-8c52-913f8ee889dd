import { NextApiRequest, NextApiResponse } from 'next';
import { HealthService } from '@/services/health.service';
import { apiHandler } from '@/lib/apiHandler';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const healthCheck = await HealthService.performHealthCheck();

  const statusCode = healthCheck.status === 'healthy' 
    ? 200 
    : healthCheck.status === 'degraded' 
    ? 200 
    : 503;

  res.status(statusCode).json(healthCheck);
}

export default apiHandler(handler);