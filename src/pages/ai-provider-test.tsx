import { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Alert, Spin, Tag, Descriptions, message } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';

const { Title, Text } = Typography;

interface ProviderInfo {
  provider: string;
  chatModel: string;
  embeddingModel: string;
  baseUrl?: string;
}

interface ProviderStatus {
  success: boolean;
  currentProvider: string;
  chatModel: string;
  embeddingModel: string;
  baseUrl?: string;
  availableProviders: string[];
  configuration: {
    openai: {
      hasApiKey: boolean;
      model: string;
    };
    ollama: {
      hasBaseUrl: boolean;
      baseUrl: string;
      model: string;
      embeddingModel: string;
    };
  };
}

interface TestResult {
  success: boolean;
  connected: boolean;
  provider: ProviderInfo;
  message: string;
}

export default function AIProviderTest() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [providerStatus, setProviderStatus] = useState<ProviderStatus | null>(null);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (status === 'authenticated') {
      fetchProviderStatus();
    }
  }, [status, router]);

  const fetchProviderStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ai-provider/status');
      const data = await response.json();
      
      if (data.success) {
        setProviderStatus(data);
      } else {
        message.error('Failed to fetch provider status');
      }
    } catch (error) {
      message.error('Error fetching provider status');
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    setTesting(true);
    try {
      const response = await fetch('/api/ai-provider/test', {
        method: 'POST',
      });
      const data = await response.json();
      
      setTestResult(data);
      
      if (data.success && data.connected) {
        message.success(data.message);
      } else {
        message.error(data.message || 'Connection test failed');
      }
    } catch (error) {
      message.error('Error testing connection');
      console.error('Error:', error);
    } finally {
      setTesting(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>AI Provider Configuration & Testing</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Current Provider Status */}
        <Card title="Current AI Provider Status" extra={
          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchProviderStatus}
            loading={loading}
          >
            Refresh
          </Button>
        }>
          {providerStatus && (
            <Descriptions column={2} bordered>
              <Descriptions.Item label="Active Provider">
                <Tag color={providerStatus.currentProvider === 'openai' ? 'blue' : 'green'}>
                  {providerStatus.currentProvider.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Chat Model">
                {providerStatus.chatModel}
              </Descriptions.Item>
              <Descriptions.Item label="Embedding Model">
                {providerStatus.embeddingModel}
              </Descriptions.Item>
              {providerStatus.baseUrl && (
                <Descriptions.Item label="Base URL">
                  {providerStatus.baseUrl}
                </Descriptions.Item>
              )}
            </Descriptions>
          )}
        </Card>

        {/* Provider Configurations */}
        <Card title="Provider Configurations">
          {providerStatus && (
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              {/* OpenAI Configuration */}
              <Card size="small" title="OpenAI Configuration">
                <Descriptions column={2} size="small">
                  <Descriptions.Item label="API Key">
                    {providerStatus.configuration.openai.hasApiKey ? (
                      <Tag color="green" icon={<CheckCircleOutlined />}>Configured</Tag>
                    ) : (
                      <Tag color="red" icon={<CloseCircleOutlined />}>Not Configured</Tag>
                    )}
                  </Descriptions.Item>
                  <Descriptions.Item label="Model">
                    {providerStatus.configuration.openai.model}
                  </Descriptions.Item>
                </Descriptions>
              </Card>

              {/* Ollama Configuration */}
              <Card size="small" title="Ollama Configuration">
                <Descriptions column={2} size="small">
                  <Descriptions.Item label="Base URL">
                    {providerStatus.configuration.ollama.hasBaseUrl ? (
                      <Tag color="green" icon={<CheckCircleOutlined />}>
                        {providerStatus.configuration.ollama.baseUrl}
                      </Tag>
                    ) : (
                      <Tag color="red" icon={<CloseCircleOutlined />}>Not Configured</Tag>
                    )}
                  </Descriptions.Item>
                  <Descriptions.Item label="Chat Model">
                    {providerStatus.configuration.ollama.model}
                  </Descriptions.Item>
                  <Descriptions.Item label="Embedding Model">
                    {providerStatus.configuration.ollama.embeddingModel}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Space>
          )}
        </Card>

        {/* Connection Test */}
        <Card title="Connection Test">
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <Text>
              Test the connection to the current AI provider to ensure it's working properly.
            </Text>
            
            <Button 
              type="primary" 
              onClick={testConnection}
              loading={testing}
              disabled={!providerStatus}
            >
              Test Connection
            </Button>

            {testResult && (
              <Alert
                type={testResult.connected ? 'success' : 'error'}
                message={testResult.message}
                description={
                  <Descriptions size="small" column={1}>
                    <Descriptions.Item label="Provider">
                      {testResult.provider.provider}
                    </Descriptions.Item>
                    <Descriptions.Item label="Model">
                      {testResult.provider.chatModel}
                    </Descriptions.Item>
                    {testResult.provider.baseUrl && (
                      <Descriptions.Item label="Base URL">
                        {testResult.provider.baseUrl}
                      </Descriptions.Item>
                    )}
                  </Descriptions>
                }
                showIcon
              />
            )}
          </Space>
        </Card>

        {/* Instructions */}
        <Card title="How to Switch Providers">
          <Space direction="vertical" size="small">
            <Text strong>To switch between OpenAI and Ollama:</Text>
            <Text>1. Update the <code>AI_PROVIDER</code> environment variable in your .env file</Text>
            <Text>2. Set it to either <code>"openai"</code> or <code>"ollama"</code></Text>
            <Text>3. Restart the application</Text>
            <Text>4. Use this page to test the connection</Text>
            
            <Alert
              type="info"
              message="Environment Variables"
              description={
                <div>
                  <Text strong>For OpenAI:</Text>
                  <br />
                  <code>AI_PROVIDER="openai"</code>
                  <br />
                  <code>OPENAI_API_KEY="your-api-key"</code>
                  <br />
                  <code>OPENAI_MODEL="gpt-4"</code>
                  <br />
                  <br />
                  <Text strong>For Ollama:</Text>
                  <br />
                  <code>AI_PROVIDER="ollama"</code>
                  <br />
                  <code>OLLAMA_BASE_URL="http://ollama.wattlesol.com"</code>
                  <br />
                  <code>OLLAMA_MODEL="deepseek-r1"</code>
                  <br />
                  <code>OLLAMA_EMBEDDING_MODEL="nomic-embed-text"</code>
                </div>
              }
            />
          </Space>
        </Card>
      </Space>
    </div>
  );
}
