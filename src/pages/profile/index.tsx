import { useSession } from 'next-auth/react';
import Layout from '@/components/Layout';

export default function ProfilePage() {
  const { data: session } = useSession({
    required: true,
  });

  return (
    <Layout title="Profile Settings">
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-6">Profile Settings</h2>
        {/* Add profile management components here */}
      </div>
    </Layout>
  );
}