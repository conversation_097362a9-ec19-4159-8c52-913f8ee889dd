import { useState } from 'react';
import { Form, Input, Button, Select, InputNumber, Card, Table } from 'antd';
import { useMutation, useQuery } from '@tanstack/react-query';

interface TestValues {
  name: string;
  variants: Array<{
    templateId: string;
    weight: number;
  }>;
}

interface Template {
  id: string;
  name: string;
}

interface ABTest {
  id: string;
  name: string;
  status: string;
  winner?: {
    name: string;
  };
}

export default function ABTestingPage() {
  const [form] = Form.useForm();

  const { data: templates } = useQuery<Template[]>({
    queryKey: ['templates'],
    queryFn: () => fetch('/api/templates').then(res => res.json())
  });

  const createTestMutation = useMutation({
    mutationFn: (values: TestValues) => 
      fetch('/api/ab-testing', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      }).then(res => res.json()),
  });

  const { data: activeTests } = useQuery<ABTest[]>({
    queryKey: ['abTests'],
    queryFn: () => fetch('/api/ab-testing').then(res => res.json())
  });

  const columns = [
    {
      title: 'Test Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: 'Winner',
      dataIndex: 'winner',
      key: 'winner',
      render: (winner: ABTest['winner']) => winner?.name || 'Not determined',
    },
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">A/B Testing</h1>

      <Card title="Create New Test" className="mb-6">
        <Form
          form={form}
          onFinish={(values) => createTestMutation.mutate(values)}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="Test Name"
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>

          <Form.List name="variants">
            {(fields, { add, remove }) => (
              <>
                {fields.map((field, index) => (
                  <Card key={field.key} className="mb-4">
                    <Form.Item
                      {...field}
                      label={`Variant ${index + 1}`}
                      required
                    >
                      <Select placeholder="Select template">
                        {templates?.map((template) => (
                          <Select.Option key={template.id} value={template.id}>
                            {template.name}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item
                      {...field}
                      label="Weight (%)"
                    >
                      <InputNumber min={1} max={100} />
                    </Form.Item>
                  </Card>
                ))}
                <Button type="dashed" onClick={() => add()} block>
                  Add Variant
                </Button>
              </>
            )}
          </Form.List>

          <Button type="primary" htmlType="submit" className="mt-4">
            Create Test
          </Button>
        </Form>
      </Card>

      <Card title="Active Tests">
        <Table
          columns={columns}
          dataSource={activeTests}
          rowKey="id"
        />
      </Card>
    </div>
  );
}
