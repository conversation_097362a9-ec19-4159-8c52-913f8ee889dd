import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useState } from 'react';
import Layout from '@/components/Layout';
import { CampaignList } from '@/components/CampaignList';
import { EmailAccountsList } from '@/components/EmailAccountsList';
import { Stats } from '@/components/Stats';
import { useQuery } from '@tanstack/react-query';
import api from '@/lib/api';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { format } from 'date-fns';


interface Campaign {
  id: string;
  name: string;
  status: 'draft' | 'scheduled' | 'sending' | 'completed';
  sentCount?: number;
  totalRecipients?: number;
  createdAt: string;
}

interface EmailMetrics {
  date: string;
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
}

export default function Dashboard() {
  const router = useRouter();
  const [timeRange, setTimeRange] = useState('7d');
  const [filterType, setFilterType] = useState('all');
  const [selectedCampaign, setSelectedCampaign] = useState<string>('all');

  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const { data: emailAccounts = [], isLoading: isLoadingEmails } = useQuery({
    queryKey: ['emailAccounts'],
    queryFn: () => api.get('/email-accounts').then(res => res.data),
    enabled: status === 'authenticated',
    staleTime: 30000,
  });

  const { data: campaigns = [], isLoading: isLoadingCampaigns } = useQuery<Campaign[]>({
    queryKey: ['campaigns'],
    queryFn: () => api.get('/campaigns').then(res => res.data),
    enabled: status === 'authenticated',
    staleTime: 30000,
  });

  const { data: emailMetrics = [], isLoading: isLoadingMetrics } = useQuery<EmailMetrics[]>({
    queryKey: ['emailMetrics', timeRange, filterType, selectedCampaign],
    queryFn: () => api.get(`/analytics/email-metrics`, {
      params: { timeRange, filterType, campaignId: selectedCampaign !== 'all' ? selectedCampaign : undefined }
    }).then(res => res.data),
    enabled: status === 'authenticated',
    staleTime: 30000,
  });

  const isLoading = status === 'loading' || isLoadingEmails || isLoadingCampaigns || isLoadingMetrics;

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <Layout title="Dashboard">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Stats campaigns={campaigns} />

        {/* Analytics Controls */}
        <div className="mb-6 flex space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          >
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>

          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          >
            <option value="all">All Emails</option>
            <option value="campaign">By Campaign</option>
          </select>

          {filterType === 'campaign' && (
            <select
              value={selectedCampaign}
              onChange={(e) => setSelectedCampaign(e.target.value)}
              className="rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option value="all">All Campaigns</option>
              {Array.isArray(campaigns) && campaigns.map(campaign => (
                <option key={campaign.id} value={campaign.id}>{campaign.name}</option>
              ))}
            </select>
          )}
        </div>

        {/* Email Metrics Chart */}
        <div className="bg-white dark:bg-dark-card p-6 rounded-lg shadow mb-6 transition-colors duration-200">
          <h2 className="text-lg font-medium mb-4 dark:text-white">Email Performance</h2>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={emailMetrics}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(date) => format(new Date(date), 'MMM d')}
                />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey="sent" stackId="1" stroke="#8884d8" fill="#8884d8" />
                <Area type="monotone" dataKey="delivered" stackId="1" stroke="#82ca9d" fill="#82ca9d" />
                <Area type="monotone" dataKey="opened" stackId="1" stroke="#ffc658" fill="#ffc658" />
                <Area type="monotone" dataKey="clicked" stackId="1" stroke="#ff7300" fill="#ff7300" />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Email Status Table */}
        <div className="bg-white dark:bg-dark-card p-6 rounded-lg shadow mb-6 transition-colors duration-200">
          <h2 className="text-lg font-medium mb-4 dark:text-white">Email Status</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-dark-header">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Campaign
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Sent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Delivered
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Opened
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Clicked
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Bounced
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                {Array.isArray(campaigns) && campaigns.length > 0 ? campaigns.map((campaign) => (
                  <tr key={campaign.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {campaign.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {campaign.sentCount || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {campaign.totalRecipients || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {/* Add actual metrics */}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {/* Add actual metrics */}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {/* Add actual metrics */}
                    </td>
                  </tr>
                )) : (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                      No campaigns found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div>
            <h2 className="text-lg font-medium mb-4">Email Accounts</h2>
            <EmailAccountsList accounts={emailAccounts} />
          </div>
          <div>
            <h2 className="text-lg font-medium mb-4">Recent Campaigns</h2>
            <CampaignList campaigns={campaigns} />
          </div>
        </div>
      </div>
    </Layout>
  );
}
