import { useState } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Table,
  Button,
  Card,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  TimePicker,
  message,
  Space,
  Tag,
  Typography,
  Tooltip,
  Empty,
  Spin,
  Badge,
  Tabs
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  UserOutlined,
  RobotOutlined,
  MailOutlined
} from '@ant-design/icons';
import Layout from '@/components/Layout';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface Appointment {
  id: string;
  title: string;
  description: string | null;
  startTime: string;
  endTime: string;
  status: string;
  leadId: string;
  campaignId: string;
  agentId: string;
  createdAt: string;
  lead: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  agent: {
    id: string;
    name: string;
  };
  campaign: {
    id: string;
    name: string;
  };
  reminders: {
    id: string;
    status: string;
    timeBeforeAppointment: number;
  }[];
}

export default function AppointmentsPage() {
  const router = useRouter();
  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const queryClient = useQueryClient();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAppointment, setEditingAppointment] = useState<Appointment | null>(null);
  const [activeTab, setActiveTab] = useState('upcoming');
  const [form] = Form.useForm();

  // Fetch appointments
  const { data: appointments = [], isPending: isLoading } = useQuery({
    queryKey: ['appointments', activeTab],
    queryFn: () => fetch(`/api/appointments?upcoming=${activeTab === 'upcoming'}`).then(res => res.json()),
    enabled: !!session,
  });

  // Fetch leads for dropdown
  const { data: leads = [] } = useQuery({
    queryKey: ['leads'],
    queryFn: () => fetch('/api/leads').then(res => res.json()),
    enabled: !!session,
  });

  // Fetch agents for dropdown
  const { data: agents = [] } = useQuery({
    queryKey: ['agents'],
    queryFn: () => fetch('/api/agents').then(res => res.json()),
    enabled: !!session,
  });

  // Fetch campaigns for dropdown
  const { data: campaigns = [] } = useQuery({
    queryKey: ['campaigns'],
    queryFn: () => fetch('/api/campaigns').then(res => res.json()),
    enabled: !!session,
  });

  // Create/update appointment mutation
  const appointmentMutation = useMutation({
    mutationFn: async (values: any) => {
      const url = editingAppointment ? `/api/appointments/${editingAppointment.id}` : '/api/appointments';
      const method = editingAppointment ? 'PUT' : 'POST';

      // Format dates
      const formattedValues = {
        ...values,
        startTime: values.startTime.toISOString(),
        endTime: values.endTime.toISOString(),
      };

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formattedValues),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save appointment');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      message.success(`Appointment ${editingAppointment ? 'updated' : 'created'} successfully`);
      setIsModalVisible(false);
      setEditingAppointment(null);
      form.resetFields();
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to save appointment');
    },
  });

  // Delete appointment mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/appointments/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete appointment');
      }

      return id;
    },
    onSuccess: (id) => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      message.success('Appointment deleted successfully');
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to delete appointment');
    },
  });

  const handleAddEdit = (appointment?: Appointment) => {
    setEditingAppointment(appointment || null);

    if (appointment) {
      form.setFieldsValue({
        title: appointment.title,
        description: appointment.description,
        leadId: appointment.leadId,
        agentId: appointment.agentId,
        campaignId: appointment.campaignId,
        startTime: dayjs(appointment.startTime),
        endTime: dayjs(appointment.endTime),
        status: appointment.status,
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        status: 'scheduled',
        startTime: dayjs().add(1, 'day').hour(9).minute(0).second(0),
        endTime: dayjs().add(1, 'day').hour(10).minute(0).second(0),
      });
    }

    setIsModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: 'Are you sure you want to delete this appointment?',
      content: 'This action cannot be undone.',
      onOk: () => deleteMutation.mutate(id),
    });
  };

  const handleSubmit = (values: any) => {
    appointmentMutation.mutate(values);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'blue';
      case 'completed':
        return 'green';
      case 'cancelled':
        return 'red';
      case 'rescheduled':
        return 'orange';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: Appointment) => (
        <div className="flex items-center">
          <CalendarOutlined className="mr-2 text-blue-500" />
          <span>{text}</span>
        </div>
      ),
    },
    {
      title: 'Lead',
      dataIndex: 'lead',
      key: 'lead',
      render: (lead: Appointment['lead']) => (
        <div className="flex items-center">
          <UserOutlined className="mr-2" />
          <span>{lead.firstName} {lead.lastName}</span>
          <br />
          <small>{lead.email}</small>
        </div>
      ),
    },
    {
      title: 'Agent',
      dataIndex: 'agent',
      key: 'agent',
      render: (agent: Appointment['agent']) => (
        <div className="flex items-center">
          <RobotOutlined className="mr-2" />
          <span>{agent.name}</span>
        </div>
      ),
    },
    {
      title: 'Date & Time',
      key: 'datetime',
      render: (_, record: Appointment) => {
        const start = dayjs(record.startTime);
        const end = dayjs(record.endTime);
        return (
          <div>
            <div><ClockCircleOutlined /> {start.format('MMM D, YYYY')}</div>
            <div>{start.format('h:mm A')} - {end.format('h:mm A')}</div>
          </div>
        );
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Reminders',
      dataIndex: 'reminders',
      key: 'reminders',
      render: (reminders: Appointment['reminders']) => (
        <div>
          {reminders && reminders.length > 0 ? (
            <Badge count={reminders.length} style={{ backgroundColor: '#52c41a' }}>
              <Button size="small" icon={<MailOutlined />}>Reminders</Button>
            </Badge>
          ) : (
            <Text type="secondary">No reminders</Text>
          )}
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Appointment) => (
        <Space>
          <Tooltip title="Edit">
            <Button
              icon={<EditOutlined />}
              onClick={() => handleAddEdit(record)}
              type="text"
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.id)}
              type="text"
              danger
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Layout title="Appointments">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <Title level={2}>Appointments</Title>
            <Text type="secondary">
              Manage appointments scheduled by AI agents
            </Text>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAddEdit()}
          >
            Create Appointment
          </Button>
        </div>

        <Card>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="Upcoming Appointments" key="upcoming" />
            <TabPane tab="Past Appointments" key="past" />
          </Tabs>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <Spin size="large" />
            </div>
          ) : appointments.length > 0 ? (
            <Table
              dataSource={appointments}
              columns={columns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <span>
                  No {activeTab} appointments found.
                </span>
              }
            />
          )}
        </Card>

        <Modal
          title={`${editingAppointment ? 'Edit' : 'Create'} Appointment`}
          open={isModalVisible}
          onCancel={() => {
            setIsModalVisible(false);
            setEditingAppointment(null);
            form.resetFields();
          }}
          footer={null}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              status: 'scheduled',
              startTime: dayjs().add(1, 'day').hour(9).minute(0).second(0),
              endTime: dayjs().add(1, 'day').hour(10).minute(0).second(0),
            }}
          >
            <Form.Item
              name="title"
              label="Title"
              rules={[{ required: true, message: 'Please enter a title' }]}
            >
              <Input placeholder="Enter appointment title" />
            </Form.Item>

            <Form.Item
              name="description"
              label="Description"
            >
              <TextArea
                placeholder="Enter appointment description"
                rows={3}
              />
            </Form.Item>

            <div className="grid grid-cols-2 gap-4">
              <Form.Item
                name="leadId"
                label="Lead"
                rules={[{ required: true, message: 'Please select a lead' }]}
              >
                <Select
                  placeholder="Select lead"
                  showSearch
                  optionFilterProp="children"
                >
                  {leads.map((lead: any) => (
                    <Option key={lead.id} value={lead.id}>
                      {lead.firstName} {lead.lastName} ({lead.email})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="agentId"
                label="Agent"
                rules={[{ required: true, message: 'Please select an agent' }]}
              >
                <Select
                  placeholder="Select agent"
                  showSearch
                  optionFilterProp="children"
                >
                  {agents.map((agent: any) => (
                    <Option key={agent.id} value={agent.id}>
                      {agent.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>

            <Form.Item
              name="campaignId"
              label="Campaign"
              rules={[{ required: true, message: 'Please select a campaign' }]}
            >
              <Select
                placeholder="Select campaign"
                showSearch
                optionFilterProp="children"
              >
                {campaigns.map((campaign: any) => (
                  <Option key={campaign.id} value={campaign.id}>
                    {campaign.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <div className="grid grid-cols-2 gap-4">
              <Form.Item
                name="startTime"
                label="Start Time"
                rules={[{ required: true, message: 'Please select a start time' }]}
              >
                <DatePicker
                  showTime
                  format="YYYY-MM-DD HH:mm"
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item
                name="endTime"
                label="End Time"
                rules={[{ required: true, message: 'Please select an end time' }]}
              >
                <DatePicker
                  showTime
                  format="YYYY-MM-DD HH:mm"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </div>

            <Form.Item
              name="status"
              label="Status"
              rules={[{ required: true, message: 'Please select a status' }]}
            >
              <Select placeholder="Select status">
                <Option value="scheduled">Scheduled</Option>
                <Option value="completed">Completed</Option>
                <Option value="cancelled">Cancelled</Option>
                <Option value="rescheduled">Rescheduled</Option>
              </Select>
            </Form.Item>

            <div className="flex justify-end">
              <Button
                type="default"
                onClick={() => {
                  setIsModalVisible(false);
                  setEditingAppointment(null);
                  form.resetFields();
                }}
                className="mr-2"
              >
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={appointmentMutation.isPending}
              >
                {editingAppointment ? 'Update' : 'Create'}
              </Button>
            </div>
          </Form>
        </Modal>
      </div>
    </Layout>
  );
}
