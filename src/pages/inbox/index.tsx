import { useState, useEffect, useCallback } from 'react';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/pages/api/auth/[...nextauth]';
import { useRouter } from 'next/router';
import axios from 'axios';
import Layout from '@/components/Layout';
import Sidebar from '@/components/Sidebar';
import { EmailAccountSidebar } from '@/components/inbox/EmailAccountSidebar';
import { EmailList } from '@/components/inbox/EmailList';
import { SimpleEmailDetail } from '@/components/inbox/SimpleEmailDetail';
import { Spin, Alert, Button, message } from 'antd';
import { SyncOutlined, CloudDownloadOutlined } from '@ant-design/icons';
import ThemeToggle from '@/components/ThemeToggle';

export const getServerSideProps: GetServerSideProps = async (context) => {
  const session = await getServerSession(context.req, context.res, authOptions);

  if (!session) {
    return {
      redirect: {
        destination: '/auth/signin',
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};

export default function InboxPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [emailAccounts, setEmailAccounts] = useState<any[]>([]);
  const [emails, setEmails] = useState<any[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<string | null>(null);
  const [selectedEmail, setSelectedEmail] = useState<any | null>(null);
  const [campaignFilter, setCampaignFilter] = useState<string>('all'); // 'all', 'only', 'exclude'
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    totalCount: 0,
    totalPages: 0,
  });
  const [syncing, setSyncing] = useState(false);

  // Function to fetch email accounts
  const fetchEmailAccounts = useCallback(async () => {
    try {
      const response = await axios.get('/api/inbox/accounts');
      setEmailAccounts(response.data.emailAccounts);

      // Select the first account with IMAP enabled (only if no account is currently selected)
      if (!selectedAccount) {
        const imapAccount = response.data.emailAccounts.find((account: any) => account.imapEnabled);
        if (imapAccount) {
          setSelectedAccount(imapAccount.id);
        }
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching email accounts:', error);
      setError('Failed to load email accounts');
      setLoading(false);
    }
  }, [selectedAccount]);

  // Get email accounts on component mount
  useEffect(() => {
    fetchEmailAccounts();
  }, [fetchEmailAccounts]);

  // Function to get cache key for emails
  const getEmailsCacheKey = useCallback((accountId: string, page: number, limit: number, filter: string) => {
    return `emails_${accountId}_${page}_${limit}_${filter}`;
  }, []);

  // Function to get cache key for email details
  const getEmailDetailCacheKey = useCallback((emailId: string) => {
    return `email_detail_${emailId}`;
  }, []);

  // Function to save emails to local storage
  const saveEmailsToCache = useCallback((accountId: string, page: number, limit: number, filter: string, data: any) => {
    try {
      const cacheKey = getEmailsCacheKey(accountId, page, limit, filter);
      localStorage.setItem(cacheKey, JSON.stringify({
        timestamp: Date.now(),
        data
      }));
    } catch (error) {
      console.error('Error saving emails to cache:', error);
    }
  }, [getEmailsCacheKey]);

  // Function to get emails from local storage
  const getEmailsFromCache = useCallback((accountId: string, page: number, limit: number, filter: string) => {
    try {
      const cacheKey = getEmailsCacheKey(accountId, page, limit, filter);
      const cachedData = localStorage.getItem(cacheKey);

      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        const cacheAge = Date.now() - parsed.timestamp;

        // Cache is valid for 5 minutes (300000 ms)
        if (cacheAge < 300000) {
          return parsed.data;
        }
      }

      return null;
    } catch (error) {
      console.error('Error getting emails from cache:', error);
      return null;
    }
  }, [getEmailsCacheKey]);

  const fetchEmails = useCallback(async (skipCache = false) => {
    if (!selectedAccount) return;

    try {
      setLoading(true);

      // Try to get emails from cache first (unless skipCache is true)
      if (!skipCache) {
        const cachedData = getEmailsFromCache(selectedAccount, pagination.page, pagination.limit, campaignFilter);
        if (cachedData) {
          setEmails(cachedData.emails);
          setPagination(cachedData.pagination);
          setLoading(false);
          return;
        }
      }

      // If no cache or skipCache is true, fetch from API
      const params = {
        emailAccountId: selectedAccount,
        page: pagination.page,
        limit: pagination.limit,
        showCampaignReplies: campaignFilter !== 'all' ? campaignFilter : undefined,
      };

      const response = await axios.get('/api/inbox', { params });

      setEmails(response.data.emails);
      setPagination(response.data.pagination);

      // Save to cache
      saveEmailsToCache(selectedAccount, pagination.page, pagination.limit, campaignFilter, response.data);

      setLoading(false);
    } catch (error) {
      console.error('Error fetching emails:', error);
      setError('Failed to load emails');
      setLoading(false);
    }
  }, [selectedAccount, pagination.page, pagination.limit, campaignFilter, getEmailsFromCache, saveEmailsToCache]);

  // Get emails for the selected account
  useEffect(() => {
    if (selectedAccount) {
      fetchEmails(false); // Try to use cache first
    }
  }, [selectedAccount, pagination.page, campaignFilter, fetchEmails]);

  const handleAccountSelect = (accountId: string) => {
    setSelectedAccount(accountId);
    setSelectedEmail(null);
    setPagination({ ...pagination, page: 1 });

    // Trigger a background sync when an account is selected
    setTimeout(() => {
      if (accountId) {
        // First try to load from cache
        fetchEmails(false);

        // Then trigger a background sync
        axios.post('/api/inbox/sync', { emailAccountId: accountId })
          .then(() => {
            // After sync completes, refresh the emails
            fetchEmails(true);
            message.success('Emails synced successfully');
          })
          .catch((error) => {
            console.error('Background sync error:', error);
            // Don't show error to user since this is a background operation
          });
      }
    }, 100); // Small delay to ensure UI updates first
  };

  // Function to save email details to local storage
  const saveEmailDetailToCache = useCallback((emailId: string, data: any) => {
    try {
      const cacheKey = getEmailDetailCacheKey(emailId);
      localStorage.setItem(cacheKey, JSON.stringify({
        timestamp: Date.now(),
        data
      }));
    } catch (error) {
      console.error('Error saving email details to cache:', error);
    }
  }, [getEmailDetailCacheKey]);

  // Function to get email details from local storage
  const getEmailDetailFromCache = useCallback((emailId: string) => {
    try {
      const cacheKey = getEmailDetailCacheKey(emailId);
      const cachedData = localStorage.getItem(cacheKey);

      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        const cacheAge = Date.now() - parsed.timestamp;

        // Cache is valid for 10 minutes (600000 ms)
        if (cacheAge < 600000) {
          return parsed.data;
        }
      }

      return null;
    } catch (error) {
      console.error('Error getting email details from cache:', error);
      return null;
    }
  }, [getEmailDetailCacheKey]);

  // Function to clear email detail cache
  const clearEmailDetailCache = useCallback((emailId?: string) => {
    try {
      if (emailId) {
        // Clear specific email cache
        const cacheKey = getEmailDetailCacheKey(emailId);
        localStorage.removeItem(cacheKey);
      } else {
        // Clear all email detail cache
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith('email_detail_')) {
            localStorage.removeItem(key);
          }
        });
      }
    } catch (error) {
      console.error('Error clearing email detail cache:', error);
    }
  }, [getEmailDetailCacheKey]);

  const handleEmailSelect = async (emailId: string) => {
    try {
      // Try to get email details from cache first
      const cachedData = getEmailDetailFromCache(emailId);
      if (cachedData && cachedData.email) {
        setSelectedEmail(cachedData.email);
      }

      // Check if the email was previously unread
      const currentEmail = emails.find(e => e.id === emailId);
      const wasUnread = currentEmail && !currentEmail.isRead;

      // Fetch from API (even if we have cache, to ensure we have the latest data)
      const response = await axios.get(`/api/inbox/${emailId}`, {
        timeout: 10000 // 10 second timeout
      });
      console.log('Email details response:', response.data);

      if (response.data && response.data.email) {
        setSelectedEmail(response.data.email);
        // Save to cache
        saveEmailDetailToCache(emailId, response.data);

        // If the email was marked as read, refresh the email list and account counts
        if (wasUnread && response.data.email.isRead) {
          // Update the email in the current list to reflect read status
          setEmails(prevEmails =>
            prevEmails.map(email =>
              email.id === emailId
                ? { ...email, isRead: true, readAt: response.data.email.readAt }
                : email
            )
          );

          // Refresh email accounts to update unread counts
          fetchEmailAccounts();
        }
      } else {
        console.error('Invalid email data format:', response.data);
        if (!cachedData) { // Only show error if we don't have cached data
          setError('Invalid email data format received from server');
        }
      }
    } catch (error) {
      console.error('Error fetching email details:', error);

      // Check if it's a network error or timeout
      const isNetworkError = error.code === 'NETWORK_ERROR' ||
                            error.code === 'ECONNABORTED' ||
                            error.message?.includes('timeout') ||
                            error.message?.includes('Network Error') ||
                            error.response?.status >= 500 ||
                            !navigator.onLine;

      // If we have cached data and it's a network error, use cache
      const cachedData = getEmailDetailFromCache(emailId);
      if (cachedData && cachedData.email && isNetworkError) {
        setSelectedEmail(cachedData.email);
        message.warning('Using cached email data. Network connection issue.');
      } else if (cachedData && cachedData.email) {
        // Use cache for other errors but don't show warning unless it's clearly a network issue
        setSelectedEmail(cachedData.email);
      } else {
        setError('Failed to load email details');
      }
    }
  };

  const handlePageChange = (page: number) => {
    setPagination({ ...pagination, page });
  };

  const handleSync = async () => {
    if (!selectedAccount) return;

    try {
      setSyncing(true);
      await axios.post('/api/inbox/sync', { emailAccountId: selectedAccount });
      await fetchEmails(true); // Skip cache to get fresh data
      setSyncing(false);
      message.success('Emails synced successfully');
    } catch (error) {
      console.error('Error syncing emails:', error);
      setError('Failed to sync emails');
      setSyncing(false);
    }
  };

  return (
    <Layout title="Inbox">
      <div className="flex h-[calc(100vh-4rem)]">
          {/* Email Account Sidebar */}
          <div className="w-64 h-full border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 overflow-y-auto">
            <EmailAccountSidebar
              accounts={emailAccounts}
              selectedAccountId={selectedAccount}
              onSelectAccount={handleAccountSelect}
            />
          </div>

          {/* Email List */}
          <div className="w-96 h-full border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 overflow-y-auto">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <h2 className="text-lg font-semibold">Inbox</h2>
              <Button
                icon={<SyncOutlined spin={syncing} />}
                onClick={handleSync}
                disabled={syncing || !selectedAccount}
              >
                {syncing ? 'Syncing...' : 'Sync'}
              </Button>
            </div>

            {/* Filter Controls */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Filter:</span>
                <select
                  value={campaignFilter}
                  onChange={(e) => {
                    setCampaignFilter(e.target.value);
                    setPagination({ ...pagination, page: 1 });
                  }}
                  className="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="all">All Emails</option>
                  <option value="only">Campaign Replies Only</option>
                  <option value="exclude">Exclude Campaign Replies</option>
                </select>
              </div>
            </div>

            {error && (
              <Alert
                message="Error"
                description={error}
                type="error"
                showIcon
                className="m-4"
                closable
                onClose={() => setError(null)}
              />
            )}

            {loading ? (
              <div className="flex justify-center items-center h-64">
                <Spin size="large" />
              </div>
            ) : (
              <EmailList
                emails={emails}
                selectedEmailId={selectedEmail?.id}
                onSelectEmail={handleEmailSelect}
                pagination={pagination}
                onPageChange={handlePageChange}
              />
            )}
          </div>

          {/* Email Detail */}
          <div className="flex-1 h-full bg-white dark:bg-gray-800 overflow-y-auto">
            {selectedEmail && selectedEmail.id ? (
              <>
                {console.log('Rendering EmailDetail with data:', selectedEmail)}
                <SimpleEmailDetail key={selectedEmail.id} email={selectedEmail} />
              </>
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                <p>Select an email to view</p>
              </div>
            )}
          </div>
      </div>
    </Layout>
  );
}
