import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Form, Input, Button, Select, Card, message, Spin, Tag, Space } from 'antd';
import Layout from '@/components/Layout';
import api from '@/lib/api';

const { Option } = Select;

export default function NewLeadPage() {
  const router = useRouter();
  const { edit: leadId } = router.query;
  const isEditMode = Boolean(leadId);

  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Fetch lead data if in edit mode
  const { data: leadData, isLoading: isLoadingLead } = useQuery({
    queryKey: ['lead', leadId],
    queryFn: () => api.get(`/leads/${leadId}`).then(res => res.data),
    enabled: isEditMode && !!leadId,
  });

  // Fetch lead lists
  const { data: leadLists = [], isLoading: isLoadingLists } = useQuery({
    queryKey: ['leadLists'],
    queryFn: () => api.get('/lead-lists').then(res => res.data),
  });

  // Set form values when lead data is loaded
  useEffect(() => {
    if (leadData) {
      form.setFieldsValue({
        email: leadData.email,
        firstName: leadData.firstName,
        lastName: leadData.lastName,
        company: leadData.company,
        phone: leadData.phone,
        status: leadData.status,
        source: leadData.source,
        listId: leadData.listId,
      });
    }
  }, [leadData, form]);

  const createLeadMutation = useMutation({
    mutationFn: async (values: any) => {
      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create lead');
      }

      return response.json();
    },
    onSuccess: () => {
      message.success('Lead created successfully');
      router.push('/leads');
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to create lead');
    },
  });

  const updateLeadMutation = useMutation({
    mutationFn: async (values: any) => {
      const response = await fetch(`/api/leads/${leadId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update lead');
      }

      return response.json();
    },
    onSuccess: () => {
      message.success('Lead updated successfully');
      router.push('/leads');
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to update lead');
    },
  });

  const handleSubmit = async (values: any) => {
    if (isEditMode) {
      updateLeadMutation.mutate(values);
    } else {
      createLeadMutation.mutate(values);
    }
  };

  return (
    <Layout title={isEditMode ? 'Edit Lead' : 'Add New Lead'}>
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            {isEditMode ? 'Edit Lead' : 'Add New Lead'}
            {isEditMode && leadData && (
              <Tag color="blue" className="ml-2">{leadData.email}</Tag>
            )}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {isEditMode ? 'Update lead information.' : 'Create a new lead in your database.'}
          </p>
        </div>

        {isLoadingLead && isEditMode ? (
          <div className="flex justify-center py-8 dark:text-white">
            <Spin size="large" />
          </div>
        ) : (
        <Card className="dark:bg-dark-card dark:border-dark-border">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              status: 'active',
              source: 'manual',
              listId: null,
            }}
          >
            <Form.Item
              name="email"
              label={<span className="dark:text-white">Email</span>}
              rules={[
                { required: true, message: 'Please enter an email address' },
                { type: 'email', message: 'Please enter a valid email address' },
              ]}
            >
              <Input placeholder="<EMAIL>" className="dark:bg-dark-card dark:border-gray-600 dark:text-white" />
            </Form.Item>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <Form.Item
                name="firstName"
                label={<span className="dark:text-white">First Name</span>}
              >
                <Input placeholder="First Name" className="dark:bg-dark-card dark:border-gray-600 dark:text-white" />
              </Form.Item>

              <Form.Item
                name="lastName"
                label={<span className="dark:text-white">Last Name</span>}
              >
                <Input placeholder="Last Name" className="dark:bg-dark-card dark:border-gray-600 dark:text-white" />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <Form.Item
                name="company"
                label={<span className="dark:text-white">Company</span>}
              >
                <Input placeholder="Company Name" className="dark:bg-dark-card dark:border-gray-600 dark:text-white" />
              </Form.Item>

              <Form.Item
                name="phone"
                label={<span className="dark:text-white">Phone</span>}
              >
                <Input placeholder="Phone Number" className="dark:bg-dark-card dark:border-gray-600 dark:text-white" />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <Form.Item
                name="status"
                label={<span className="dark:text-white">Status</span>}
              >
                <Select className="dark:bg-dark-card dark:text-white">
                  <Option value="active">Active</Option>
                  <Option value="inactive">Inactive</Option>
                  <Option value="unsubscribed">Unsubscribed</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="source"
                label={<span className="dark:text-white">Source</span>}
              >
                <Select className="dark:bg-dark-card dark:text-white">
                  <Option value="manual">Manual</Option>
                  <Option value="import">Import</Option>
                  <Option value="api">API</Option>
                  <Option value="website">Website</Option>
                </Select>
              </Form.Item>
            </div>

            <div className="mt-4">
              <Form.Item
                name="listId"
                label={<span className="dark:text-white">Lead List</span>}
              >
                <Select
                  placeholder="Select a list"
                  allowClear
                  loading={isLoadingLists}
                  className="dark:bg-dark-card dark:text-white"
                  dropdownRender={menu => (
                    <>
                      {menu}
                      <div className="p-2 border-t border-gray-200 dark:border-gray-700 dark:bg-dark-card">
                        <Button
                          type="link"
                          block
                          onClick={() => router.push('/lead-lists/new')}
                        >
                          + Create New List
                        </Button>
                      </div>
                    </>
                  )}
                >
                  {leadLists.map((list: any) => (
                    <Option key={list.id} value={list.id}>
                      {list.name} {list._count?.leads > 0 && `(${list._count.leads})`}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <Button onClick={() => router.push('/leads')}>
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={isEditMode ? updateLeadMutation.isPending : createLeadMutation.isPending}
              >
                {isEditMode ? 'Update Lead' : 'Create Lead'}
              </Button>
            </div>
          </Form>
        </Card>
        )}
      </div>
    </Layout>
  );
}
