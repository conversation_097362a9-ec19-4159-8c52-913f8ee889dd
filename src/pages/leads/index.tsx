import { useState, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import Layout from '@/components/Layout';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import { useCommonData } from '@/hooks/useCommonData';
import { Button, Modal, Upload, message, Spin, Tooltip, Table, Checkbox, Space, Drawer, Typography, Divider, Tag, Select, Form } from 'antd';
import { UploadOutlined, DownloadOutlined, PlusOutlined, DeleteOutlined, ExclamationCircleOutlined, CloseOutlined, MailOutlined, PhoneOutlined, BuildOutlined, TagOutlined } from '@ant-design/icons';

import type { UploadProps } from 'antd';

const { Option } = Select;

const { Title, Text, Paragraph } = Typography;

interface Lead {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  company?: string;
  phone?: string;
  status: 'active' | 'inactive' | 'unsubscribed';
  source: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  customFields?: Record<string, any>;
}

export default function LeadsPage() {
  const router = useRouter();
  const { listId } = router.query;
  const queryClient = useQueryClient();
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  // Fetch the current list if listId is provided
  const { data: currentList } = useQuery({
    queryKey: ['leadList', listId],
    queryFn: () => api.get(`/lead-lists/${listId}`).then(res => res.data),
    enabled: !!listId && typeof listId === 'string',
  });

  // Get lead lists from global state
  const { leadLists = [], isLoadingLeadLists: isLoadingLists } = useCommonData();

  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [selectedLeadIds, setSelectedLeadIds] = useState<string[]>([]);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [importListName, setImportListName] = useState<string>('');
  const [pageSize, setPageSize] = useState<number>(() => {
    // Try to get the page size from local storage, default to 10
    if (typeof window !== 'undefined') {
      const savedPageSize = localStorage.getItem('leadsPageSize');
      return savedPageSize ? parseInt(savedPageSize, 10) : 10;
    }
    return 10;
  });

  // State for lead detail drawer
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);

  const { data: leads = [], isLoading } = useQuery<Lead[]>({
    queryKey: ['leads', listId],
    queryFn: () => {
      const url = listId ? `/lead-lists/${listId}/leads` : '/leads';
      return api.get(url).then(res => res.data);
    },
    enabled: status === 'authenticated',
  });

  // Import leads mutation
  const importMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await fetch('/api/leads/import', {
        method: 'POST',
        body: formData,
      });
      if (!response.ok) {
        throw new Error('Failed to import leads');
      }
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['leads'] });
      queryClient.invalidateQueries({ queryKey: ['commonData'] });

      if (data.errors && data.errors.length > 0) {
        // Show warning with errors
        Modal.warning({
          title: 'Import completed with warnings',
          content: (
            <div>
              <p>{`Successfully imported ${data.imported} leads. ${data.duplicates} duplicates were skipped.`}</p>
              <p>The following errors were encountered:</p>
              <ul className="mt-2 max-h-60 overflow-y-auto">
                {data.errors.map((error, index) => (
                  <li key={index} className="text-red-500">{error}</li>
                ))}
              </ul>
            </div>
          ),
        });
      } else {
        message.success(
          `Successfully imported ${data.imported} leads. ${data.duplicates} duplicates were skipped.`
        );
      }

      setIsImportModalVisible(false);
      setFileList([]);
      setImportListName('');
    },
    onError: (error: any) => {
      if (error.response && error.response.data && error.response.data.details) {
        // Show detailed error message
        Modal.error({
          title: 'Import failed',
          content: (
            <div>
              <p>{error.response.data.error || 'Failed to import leads'}</p>
              <p>The following errors were encountered:</p>
              <ul className="mt-2 max-h-60 overflow-y-auto">
                {error.response.data.details.map((error: string, index: number) => (
                  <li key={index} className="text-red-500">{error}</li>
                ))}
              </ul>
            </div>
          ),
        });
      } else {
        message.error('Failed to import leads');
      }
    },
  });

  const uploadProps: UploadProps = {
    onRemove: (file) => {
      setFileList((prev) => prev.filter((item) => item.uid !== file.uid));
    },
    beforeUpload: (file) => {
      setFileList([file]);
      return false;
    },
    fileList,
    accept: '.csv',
  };

  const handleImport = () => {
    if (fileList.length === 0) {
      message.error('Please select a file to import');
      return;
    }

    const formData = new FormData();
    formData.append('file', fileList[0]);

    // Add list name if provided
    if (importListName) {
      formData.append('listName', importListName);
    }

    importMutation.mutate(formData);
  };

  const handleExport = () => {
    fetch('/api/leads/export')
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'leads.csv';
        document.body.appendChild(a);
        a.click();
        a.remove();
      })
      .catch(() => {
        message.error('Failed to export leads');
      });
  };

  // Bulk delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (ids: string[]) => {
      return api.delete('/leads/batch', { data: { ids } });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leads'] });
      queryClient.invalidateQueries({ queryKey: ['commonData'] });
      message.success(`Successfully deleted ${selectedLeadIds.length} leads`);
      setSelectedLeadIds([]);
      setIsDeleteModalVisible(false);
    },
    onError: () => {
      message.error('Failed to delete leads');
    },
  });

  const handleDeleteSelected = () => {
    if (selectedLeadIds.length === 0) {
      message.warning('Please select leads to delete');
      return;
    }
    setIsDeleteModalVisible(true);
  };

  const confirmDelete = () => {
    deleteMutation.mutate(selectedLeadIds);
  };

  const rowSelection = {
    selectedRowKeys: selectedLeadIds,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedLeadIds(selectedRowKeys as string[]);
    },
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-8 dark:text-white">Loading...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Leads">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <div className="flex items-center">
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Leads</h1>
              {currentList && (
                <Tag color="blue" className="ml-2 text-base">
                  {currentList.name}
                </Tag>
              )}
            </div>
            <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
              {currentList
                ? `Showing ${leads.length} leads in list "${currentList.name}"`
                : `A list of all ${leads.length} leads in your account.`
              }
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-2">
            {/* List selection dropdown */}
            {!selectedLeadIds.length && (
              <Select
                className="w-48 mr-2"
                placeholder="Select a list"
                value={listId as string || undefined}
                onChange={(value) => {
                  if (value) {
                    router.push(`/leads?listId=${value}`);
                  } else {
                    router.push('/leads');
                  }
                }}
                allowClear
                dropdownRender={menu => (
                  <>
                    {menu}
                    <div className="p-2 border-t border-gray-200">
                      <Button
                        type="link"
                        block
                        onClick={() => router.push('/lead-lists')}
                      >
                        Manage Lists
                      </Button>
                    </div>
                  </>
                )}
              >
                <Option value="">All Leads</Option>
                {leadLists.map((list: any) => (
                  <Option key={list.id} value={list.id}>
                    {list.name} {list._count?.leads > 0 && `(${list._count.leads})`}
                  </Option>
                ))}
              </Select>
            )}
            {selectedLeadIds.length > 0 ? (
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={handleDeleteSelected}
              >
                Delete Selected ({selectedLeadIds.length})
              </Button>
            ) : (
              <>
                <Button icon={<DownloadOutlined />} onClick={handleExport}>
                  Export
                </Button>
                <Button
                  icon={<UploadOutlined />}
                  onClick={() => setIsImportModalVisible(true)}
                >
                  Import
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => router.push('/leads/new')}
                >
                  Add Lead
                </Button>
              </>
            )}
          </div>
        </div>

        <div className="mt-8">
          <Table
            rowSelection={rowSelection}
            dataSource={leads.map(lead => ({ ...lead, key: lead.id }))}
            columns={[
              {
                title: 'First Name',
                dataIndex: 'firstName',
                key: 'firstName',
                render: (text) => (
                  <span className="whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {text || '-'}
                  </span>
                ),
              },
              {
                title: 'Last Name',
                dataIndex: 'lastName',
                key: 'lastName',
                render: (text) => (
                  <span className="whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {text || '-'}
                  </span>
                ),
              },
              {
                title: 'Email',
                dataIndex: 'email',
                key: 'email',
                render: (text) => <span className="whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{text}</span>,
              },
              {
                title: 'Company',
                dataIndex: 'company',
                key: 'company',
                render: (text) => <span className="whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{text}</span>,
              },
              {
                title: 'Status',
                dataIndex: 'status',
                key: 'status',
                render: (status) => (
                  <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                    status === 'new' ? 'bg-blue-100 text-blue-800' :
                    status === 'contacted' ? 'bg-yellow-100 text-yellow-800' :
                    status === 'qualified' ? 'bg-green-100 text-green-800' :
                    'bg-purple-100 text-purple-800'
                  }`}>
                    {status}
                  </span>
                ),
              },
              {
                title: 'Source',
                dataIndex: 'source',
                key: 'source',
                render: (text) => <span className="whitespace-nowrap text-sm text-gray-500">{text}</span>,
              },
              {
                title: 'Actions',
                key: 'actions',
                render: (_, record) => (
                  <Space>
                    <Button
                      type="link"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedLead(record);
                        setDrawerVisible(true);
                      }}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      View
                    </Button>
                    <Button
                      type="link"
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/leads/new?edit=${record.id}`);
                      }}
                      className="text-green-600 hover:text-green-900"
                    >
                      Edit
                    </Button>
                  </Space>
                ),
              },
            ]}
            pagination={{
              pageSize: pageSize,
              showSizeChanger: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              onChange: (page, size) => {
                setPageSize(size);
                // Save the page size to local storage
                if (typeof window !== 'undefined') {
                  localStorage.setItem('leadsPageSize', size.toString());
                }
              }
            }}
            className="shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
            onRow={(record) => ({
              onClick: () => {
                setSelectedLead(record);
                setDrawerVisible(true);
              },
              style: { cursor: 'pointer' }
            })}
          />
        </div>
      </div>

      {/* Import Modal */}
      <Modal
        title="Import Leads"
        open={isImportModalVisible}
        onOk={handleImport}
        onCancel={() => {
          setIsImportModalVisible(false);
          setFileList([]);
          setImportListName('');
        }}
        confirmLoading={importMutation.isPending}
      >

        <div className="mb-4">
          <p className="mb-2">
            Upload a CSV file with your leads. The file must include email addresses.
          </p>

          <Form.Item
            label="List Name (Optional)"
            help="If not provided, a list will be created using the CSV filename"
          >
            <Select
              placeholder="Select or create a list"
              allowClear
              loading={isLoadingLists}
              onChange={(value) => setImportListName(value)}
              dropdownRender={menu => (
                <>
                  {menu}
                  <div className="p-2 border-t border-gray-200">
                    <Button
                      type="link"
                      block
                      onClick={() => {
                        setIsImportModalVisible(false);
                        router.push('/lead-lists/new');
                      }}
                    >
                      + Create New List
                    </Button>
                  </div>
                </>
              )}
            >
              {leadLists.map((list: any) => (
                <Option key={list.id} value={list.name}>
                  {list.name} {list._count?.leads > 0 && `(${list._count.leads})`}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <p className="text-sm text-gray-500 mb-2">
            The system will automatically detect the following fields:
          </p>
          <ul className="list-disc list-inside text-sm text-gray-500 mb-4 pl-4">
            <li><strong>Email</strong> (required) - email, Email</li>
            <li><strong>First Name</strong> - firstName, FirstName, first_name, "First Name", "first name"</li>
            <li><strong>Last Name</strong> - lastName, LastName, last_name, "Last Name", "last name"</li>
            <li><strong>Full Name</strong> - name, Name, fullName, "Full Name" (will be split into First Name and Last Name)</li>
            <li><strong>Company</strong> - company, Company</li>
            <li><strong>Phone</strong> - phone, Phone</li>
          </ul>
          <p className="text-sm text-gray-500 mb-2">
            Name handling rules:
          </p>
          <ul className="list-disc list-inside text-sm text-gray-500 mb-4 pl-4">
            <li>If both first name and last name columns are present, they will be used as is</li>
            <li>If only a single "name" column is provided, it will be treated as the first name</li>
            <li>If a full name is provided with spaces, the first word will be used as the first name and the rest as the last name</li>
          </ul>
        </div>
        <Upload {...uploadProps}>
          <Button icon={<UploadOutlined />}>Select CSV File</Button>
        </Upload>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        title="Delete Leads"
        open={isDeleteModalVisible}
        onOk={confirmDelete}
        onCancel={() => setIsDeleteModalVisible(false)}
        confirmLoading={deleteMutation.isPending}
        okText="Delete"
        okButtonProps={{ danger: true }}
      >
        <div className="flex items-center">
          <ExclamationCircleOutlined className="text-yellow-500 text-xl mr-2" />
          <span>
            Are you sure you want to delete {selectedLeadIds.length} selected leads? This action cannot be undone.
          </span>
        </div>
      </Modal>

      {/* Lead Detail Drawer */}
      <Drawer
        title={
          <div className="flex justify-between items-center">
            <span>Lead Details</span>
            {selectedLead && (
              <Tag color={selectedLead.status === 'active' ? 'green' :
                    selectedLead.status === 'inactive' ? 'orange' : 'red'}>
                {selectedLead.status.toUpperCase()}
              </Tag>
            )}
          </div>
        }
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        width={400}
        extra={
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={() => setDrawerVisible(false)}
          />
        }
      >
        {selectedLead && (
          <div>
            <div className="mb-4">
              <Tag color={selectedLead.source === 'manual' ? 'blue' :
                     selectedLead.source === 'import' ? 'purple' : 'cyan'}>
                {selectedLead.source.toUpperCase()}
              </Tag>
              <Text type="secondary" className="ml-2">
                Created: {new Date(selectedLead.createdAt).toLocaleDateString()}
              </Text>
            </div>

            <Title level={4}>
              {selectedLead.firstName} {selectedLead.lastName}
            </Title>

            <Divider />

            <div className="space-y-4">
              <div className="flex items-center">
                <MailOutlined className="text-gray-400 mr-2" />
                <Text copyable>{selectedLead.email}</Text>
              </div>

              {selectedLead.phone && (
                <div className="flex items-center">
                  <PhoneOutlined className="text-gray-400 mr-2" />
                  <Text copyable>{selectedLead.phone}</Text>
                </div>
              )}

              {selectedLead.company && (
                <div className="flex items-center">
                  <BuildOutlined className="text-gray-400 mr-2" />
                  <Text>{selectedLead.company}</Text>
                </div>
              )}
            </div>

            {selectedLead.tags && selectedLead.tags.length > 0 && (
              <>
                <Divider orientation="left">
                  <div className="flex items-center">
                    <TagOutlined className="mr-1" /> Tags
                  </div>
                </Divider>
                <div className="flex flex-wrap gap-1 mb-4">
                  {selectedLead.tags.map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </div>
              </>
            )}

            {selectedLead.customFields && Object.keys(selectedLead.customFields).length > 0 && (
              <>
                <Divider orientation="left">Custom Fields</Divider>
                <div className="space-y-2">
                  {Object.entries(selectedLead.customFields).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <Text type="secondary">{key}:</Text>
                      <Text>{String(value)}</Text>
                    </div>
                  ))}
                </div>
              </>
            )}

            <Divider />

            <div className="flex justify-between mt-4">
              <Button
                type="primary"
                onClick={() => router.push(`/leads/new?edit=${selectedLead.id}`)}
              >
                Edit Lead
              </Button>
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={() => {
                  setSelectedLeadIds([selectedLead.id]);
                  setIsDeleteModalVisible(true);
                  setDrawerVisible(false);
                }}
              >
                Delete
              </Button>
            </div>
          </div>
        )}
      </Drawer>
    </Layout>
  );
}