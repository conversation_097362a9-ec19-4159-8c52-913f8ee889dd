import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button, Table, Modal, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useSession } from 'next-auth/react';
import Layout from '@/components/Layout';

export default function TemplatesPage() {
  const { data: session } = useSession({
    required: true,
  });
  const queryClient = useQueryClient();

  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  const { data: templates, isLoading } = useQuery({
    queryKey: ['templates'],
    queryFn: () => fetch('/api/templates').then(res => res.json())
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => fetch(`/api/templates/${id}`, { method: 'DELETE' }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['templates'] });
      message.success('Template deleted successfully');
      setDeleteModalVisible(false);
    },
  });

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <span className="text-gray-900 dark:text-white">{text}</span>,
    },
    {
      title: 'Subject',
      dataIndex: 'subject',
      key: 'subject',
      render: (text: string) => <span className="text-gray-500 dark:text-gray-300">{text}</span>,
    },
    {
      title: 'Last Modified',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date: string) => <span className="text-gray-500 dark:text-gray-300">{new Date(date).toLocaleDateString()}</span>,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <>
          <Button
            icon={<EditOutlined />}
            onClick={() => window.location.href = `/template-builder?id=${record.id}`}
          />
          <Button
            icon={<DeleteOutlined />}
            danger
            onClick={() => {
              setSelectedTemplate(record);
              setDeleteModalVisible(true);
            }}
          />
        </>
      ),
    },
  ];

  return (
    <Layout title="Email Templates">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center mb-6">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Email Templates</h1>
            <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
              Create and manage email templates for your campaigns.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => window.location.href = '/template-builder'}
            >
              Create New
            </Button>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-card shadow rounded-lg p-6 transition-colors duration-200">

        <Table
          columns={columns}
          dataSource={templates}
          loading={isLoading}
          rowKey="id"
          className="dark:text-white"
          rowClassName="dark:hover:bg-gray-700 transition-colors duration-200"
        />

        <Modal
          title="Delete Template"
          open={deleteModalVisible}
          onOk={() => deleteMutation.mutate(selectedTemplate?.id)}
          onCancel={() => setDeleteModalVisible(false)}
          confirmLoading={deleteMutation.isPending}
        >
          <p className="dark:text-gray-300">Are you sure you want to delete this template?</p>
        </Modal>
        </div>
      </div>
    </Layout>
  );
}
