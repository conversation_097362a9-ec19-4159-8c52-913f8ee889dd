import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Tabs,
  Card,
  Button,
  Form,
  Input,
  Select,
  Spin,
  message,
  Typography,
  Divider,
  Space,
  Alert
} from 'antd';
import {
  MailOutlined,
  EditOutlined,
  SaveOutlined,
  DeleteOutlined,
  BarChartOutlined,
  RocketOutlined,
  InboxOutlined,
  SettingOutlined,
  UserOutlined,
  SendOutlined
} from '@ant-design/icons';
import Layout from '@/components/Layout';
import ReputationPanel from '@/components/email-accounts/ReputationPanel';
import ImapSettings from '@/components/email-accounts/ImapSettings';
import AgentSettings from '@/components/email-accounts/AgentSettings';
import SignatureSettings from '@/components/email-accounts/SignatureSettings';
import ReceivedEmails from '@/components/email-accounts/ReceivedEmails';
import ResendVerificationButton from '@/components/email-accounts/ResendVerificationButton';
import TestEmailSection from '@/components/email-accounts/TestEmailSection';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

export default function EmailAccountDetailPage() {
  const router = useRouter();
  const { id, tab } = router.query;
  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('details');

  // Set active tab based on URL parameter
  useEffect(() => {
    if (tab && typeof tab === 'string') {
      setActiveTab(tab);
    }
  }, [tab]);

  // Fetch email account details
  const { data: emailAccount, isPending: isLoading } = useQuery({
    queryKey: ['emailAccount', id],
    queryFn: () => fetch(`/api/email-accounts/${id}`).then(res => res.json()),
    enabled: !!id && !!session,

  });

  // Set form values when email account data is loaded
  useEffect(() => {
    if (emailAccount) {
      form.setFieldsValue({
        email: emailAccount.email,
        name: emailAccount.name,
        provider: emailAccount.provider,
        smtpHost: emailAccount.smtpHost,
        smtpPort: emailAccount.smtpPort,
        smtpUsername: emailAccount.smtpUsername,
      });
    }
  }, [emailAccount, form]);

  // Update email account mutation
  const updateMutation = useMutation({
    mutationFn: async (values: any) => {
      const response = await fetch(`/api/email-accounts/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        throw new Error('Failed to update email account');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['emailAccount', id] });
      message.success('Email account updated successfully');
      setIsEditing(false);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to update email account');
    },
  });

  // Delete email account mutation
  const deleteMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/email-accounts/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete email account');
      }

      return response.json();
    },
    onSuccess: () => {
      message.success('Email account deleted successfully');
      router.push('/email-accounts');
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to delete email account');
    },
  });

  const handleSubmit = (values: any) => {
    updateMutation.mutate(values);
  };

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this email account? This action cannot be undone.')) {
      deleteMutation.mutate();
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  if (!emailAccount) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card>
            <div className="text-center py-8">
              <Title level={4}>Email account not found</Title>
              <Button type="primary" onClick={() => router.push('/email-accounts')}>
                Back to Email Accounts
              </Button>
            </div>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <MailOutlined className="text-2xl mr-2 text-blue-500" />
            <Title level={2} className="m-0">
              {emailAccount.name || emailAccount.email}
            </Title>
          </div>
          <Space>
            {!isEditing ? (
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={() => setIsEditing(true)}
              >
                Edit
              </Button>
            ) : (
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={() => form.submit()}
                loading={updateMutation.isPending}
              >
                Save
              </Button>
            )}
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleDelete}
              loading={deleteMutation.isPending}
            >
              Delete
            </Button>
          </Space>
        </div>

        {emailAccount.status === 'pending' && (
          <Alert
            message="Email Account Not Verified"
            description={
              <div>
                <p>This email account has not been verified yet. Please check your inbox for a verification email.</p>
                <p>If you didn't receive the email, you can resend it:</p>
                <div className="mt-2">
                  <ResendVerificationButton emailAccountId={id as string} />
                </div>
              </div>
            }
            type="warning"
            showIcon
            className="mb-6"
          />
        )}

        <Tabs activeKey={activeTab} onChange={(key) => {
          setActiveTab(key);
          router.push(`/email-accounts/${id}?tab=${key}`, undefined, { shallow: true });
        }}>
          <TabPane
            tab={
              <span>
                <MailOutlined />
                Account Details
              </span>
            }
            key="details"
          >
            <Card>
              {isEditing ? (
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleSubmit}
                  initialValues={{
                    email: emailAccount.email,
                    name: emailAccount.name,
                    provider: emailAccount.provider,
                    smtpHost: emailAccount.smtpHost,
                    smtpPort: emailAccount.smtpPort,
                    smtpUsername: emailAccount.smtpUsername,
                  }}
                >
                  <Form.Item
                    name="email"
                    label="Email Address"
                    rules={[{ required: true, message: 'Please enter an email address' }]}
                  >
                    <Input disabled />
                  </Form.Item>

                  <Form.Item
                    name="name"
                    label="Display Name"
                  >
                    <Input placeholder="Enter a display name" />
                  </Form.Item>

                  <Form.Item
                    name="provider"
                    label="Provider"
                    rules={[{ required: true, message: 'Please select a provider' }]}
                  >
                    <Select disabled>
                      <Option value="smtp">SMTP</Option>
                      <Option value="gmail">Gmail</Option>
                    </Select>
                  </Form.Item>

                  {emailAccount.provider === 'smtp' && (
                    <>
                      <Form.Item
                        name="smtpHost"
                        label="SMTP Host"
                        rules={[{ required: true, message: 'Please enter SMTP host' }]}
                      >
                        <Input placeholder="e.g., smtp.gmail.com" />
                      </Form.Item>

                      <Form.Item
                        name="smtpPort"
                        label="SMTP Port"
                        rules={[{ required: true, message: 'Please enter SMTP port' }]}
                      >
                        <Input type="number" placeholder="e.g., 587" />
                      </Form.Item>

                      <Form.Item
                        name="smtpUsername"
                        label="SMTP Username"
                        rules={[{ required: true, message: 'Please enter SMTP username' }]}
                      >
                        <Input placeholder="Usually your email address" />
                      </Form.Item>

                      <Form.Item
                        name="smtpPassword"
                        label="SMTP Password"
                        extra="Leave blank to keep the current password"
                      >
                        <Input.Password placeholder="Enter new password" />
                      </Form.Item>
                    </>
                  )}

                  <div className="flex justify-end space-x-3 mt-4">
                    <Button onClick={() => setIsEditing(false)}>
                      Cancel
                    </Button>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={updateMutation.isPending}
                    >
                      Save Changes
                    </Button>
                  </div>
                </Form>
              ) : (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Text type="secondary">Email Address</Text>
                      <div className="text-lg">{emailAccount.email}</div>
                    </div>
                    <div>
                      <Text type="secondary">Display Name</Text>
                      <div className="text-lg">{emailAccount.name || '-'}</div>
                    </div>
                    <div>
                      <Text type="secondary">Provider</Text>
                      <div className="text-lg capitalize">{emailAccount.provider}</div>
                    </div>
                    <div>
                      <Text type="secondary">Status</Text>
                      <div className="text-lg capitalize">{emailAccount.status}</div>
                    </div>
                    {emailAccount.provider === 'smtp' && (
                      <>
                        <div>
                          <Text type="secondary">SMTP Host</Text>
                          <div className="text-lg">{emailAccount.smtpHost}</div>
                        </div>
                        <div>
                          <Text type="secondary">SMTP Port</Text>
                          <div className="text-lg">{emailAccount.smtpPort}</div>
                        </div>
                        <div>
                          <Text type="secondary">SMTP Username</Text>
                          <div className="text-lg">{emailAccount.smtpUsername}</div>
                        </div>
                      </>
                    )}
                    <div>
                      <Text type="secondary">Last Used</Text>
                      <div className="text-lg">
                        {emailAccount.lastUsed ? new Date(emailAccount.lastUsed).toLocaleString() : 'Never'}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </Card>
          </TabPane>

          <TabPane
            tab={
              <span>
                <BarChartOutlined />
                Reputation & Warmup
              </span>
            }
            key="reputation"
          >
            <ReputationPanel
              emailAccountId={id as string}
              emailAddress={emailAccount.email}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <SettingOutlined />
                IMAP Settings
              </span>
            }
            key="imap"
          >
            <ImapSettings
              emailAccountId={id as string}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <RocketOutlined />
                AI Agent
              </span>
            }
            key="agent"
          >
            <AgentSettings
              emailAccountId={id as string}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <UserOutlined />
                Signature
              </span>
            }
            key="signature"
          >
            <SignatureSettings
              emailAccountId={id as string}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <InboxOutlined />
                Received Emails
              </span>
            }
            key="emails"
          >
            <ReceivedEmails
              emailAccountId={id as string}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <SendOutlined />
                Test Email
              </span>
            }
            key="test"
          >
            <TestEmailSection
              emailAccountId={id as string}
            />
          </TabPane>
        </Tabs>
      </div>
    </Layout>
  );
}
