import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { Resul<PERSON>, <PERSON><PERSON>, Spin } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, MailOutlined } from '@ant-design/icons';
import Layout from '@/components/Layout';
import api from '@/lib/api';

export default function VerifyEmailAccount() {
  const router = useRouter();
  const [status, setStatus] = useState<'verifying' | 'success' | 'error'>('verifying');
  const [error, setError] = useState('');
  const [emailAccountId, setEmailAccountId] = useState<string | null>(null);

  useEffect(() => {
    const { token } = router.query;

    if (token && typeof token === 'string') {
      console.log('Verifying token:', token.substring(0, 10) + '...');

      api.post('/email-accounts/verify-token', { token })
        .then((response) => {
          console.log('Verification successful:', response.data);
          setStatus('success');
          setEmailAccountId(response.data.emailAccountId);
        })
        .catch((err) => {
          console.error('Verification failed:', err.response?.data || err.message);
          setStatus('error');
          setError(err.response?.data?.error || 'Verification failed');
        });
    }
  }, [router.query]);

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {status === 'verifying' && (
          <div className="flex flex-col items-center justify-center py-12">
            <Spin size="large" />
            <p className="mt-4 text-lg">Verifying your email account...</p>
          </div>
        )}

        {status === 'success' && (
          <Result
            icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            title="Email Account Verified Successfully!"
            subTitle="Your email account has been verified and is now ready to use."
            extra={[
              <Button
                type="primary"
                key="dashboard"
                onClick={() => router.push('/email-accounts')}
              >
                Go to Email Accounts
              </Button>,
              emailAccountId && (
                <Button
                  key="details"
                  onClick={() => router.push(`/email-accounts/${emailAccountId}`)}
                >
                  View Account Details
                </Button>
              )
            ]}
          />
        )}

        {status === 'error' && (
          <Result
            icon={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
            title="Verification Failed"
            subTitle={error || 'There was a problem verifying your email account.'}
            extra={[
              <Button
                type="primary"
                key="dashboard"
                onClick={() => router.push('/email-accounts')}
              >
                Go to Email Accounts
              </Button>,
              <Link href="/support" key="support">
                <Button>Contact Support</Button>
              </Link>
            ]}
          />
        )}
      </div>
    </Layout>
  );
}
