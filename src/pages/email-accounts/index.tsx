import { NextPage } from 'next';
import { useState } from 'react';
import { EmailAccountsList } from '@/components/EmailAccountsList';
import { Modal } from '@/components/ui/Modal';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import Layout from '@/components/Layout';

const createEmailAccountSchema = z.object({
  email: z.string().email(),
  name: z.string().optional(),
  provider: z.enum(['smtp', 'gmail']),
  smtpHost: z.string().optional(),
  smtpPort: z.number().optional(),
  smtpUsername: z.string().optional(),
  smtpPassword: z.string().optional(),
});

type CreateEmailAccountInput = z.infer<typeof createEmailAccountSchema>;

const EmailAccountsPage: NextPage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formData, setFormData] = useState<CreateEmailAccountInput>({
    email: '',
    provider: 'smtp',
  });
  const queryClient = useQueryClient();

  const { data: emailAccounts, isLoading } = useQuery({
    queryKey: ['emailAccounts'],
    queryFn: () => fetch('/api/email-accounts').then(res => res.json()),
  });

  const createMutation = useMutation({
    mutationFn: (data: CreateEmailAccountInput) =>
      fetch('/api/email-accounts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }).then(res => res.json()),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['emailAccounts'] });
      setIsModalOpen(false);
      setFormData({ email: '', provider: 'smtp' });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createEmailAccountSchema.parseAsync(formData);
      createMutation.mutate(formData);
    } catch (error) {
      console.error('Validation error:', error);
    }
  };

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center mb-6">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Email Accounts</h1>
            <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
              Manage your email accounts for sending campaigns.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button
              onClick={() => setIsModalOpen(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600"
            >
              Add Email Account
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-card shadow rounded-lg transition-colors duration-200">

          <div className="px-4 py-5 sm:p-6">
            <EmailAccountsList accounts={emailAccounts} isLoading={isLoading} />
          </div>
        </div>

        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title="Add Email Account"
        >
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email Address
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Display Name (Optional)
              </label>
              <input
                type="text"
                value={formData.name || ''}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Provider
              </label>
              <select
                value={formData.provider}
                onChange={(e) => setFormData({ ...formData, provider: e.target.value as 'smtp' | 'gmail' })}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="smtp">SMTP</option>
                <option value="gmail">Gmail</option>
              </select>
            </div>

            {formData.provider === 'smtp' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    SMTP Host
                  </label>
                  <input
                    type="text"
                    value={formData.smtpHost || ''}
                    onChange={(e) => setFormData({ ...formData, smtpHost: e.target.value })}
                    className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    SMTP Port
                  </label>
                  <input
                    type="number"
                    value={formData.smtpPort || ''}
                    onChange={(e) => setFormData({ ...formData, smtpPort: parseInt(e.target.value) })}
                    className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    SMTP Username
                  </label>
                  <input
                    type="text"
                    value={formData.smtpUsername || ''}
                    onChange={(e) => setFormData({ ...formData, smtpUsername: e.target.value })}
                    className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    SMTP Password
                  </label>
                  <input
                    type="password"
                    value={formData.smtpPassword || ''}
                    onChange={(e) => setFormData({ ...formData, smtpPassword: e.target.value })}
                    className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-dark-card dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    required
                  />
                </div>
              </>
            )}

            <div className="mt-5 sm:mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={createMutation.isPending}
                className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2"
              >
                {createMutation.isPending ? 'Adding...' : 'Add Account'}
              </button>
            </div>
          </form>
        </Modal>
      </div>
    </Layout>
  );
};

export default EmailAccountsPage;
