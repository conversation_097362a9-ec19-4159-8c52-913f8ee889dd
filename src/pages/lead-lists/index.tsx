import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import Layout from '@/components/Layout';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import { Button, Modal, message, Spin, Card, Typography, Divider, Empty, Space, Tag, Tooltip } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, ExclamationCircleOutlined, TeamOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface LeadList {
  id: string;
  name: string;
  description?: string;
  source: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    leads: number;
  };
}

export default function LeadListsPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [selectedList, setSelectedList] = useState<LeadList | null>(null);

  const { data: leadLists = [], isLoading } = useQuery<LeadList[]>({
    queryKey: ['leadLists'],
    queryFn: () => api.get('/lead-lists').then(res => res.data),
    enabled: status === 'authenticated',
  });

  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      return api.delete(`/lead-lists/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadLists'] });
      message.success('List deleted successfully');
      setIsDeleteModalVisible(false);
    },
    onError: () => {
      message.error('Failed to delete list');
    },
  });

  const handleDelete = (list: LeadList) => {
    setSelectedList(list);
    setIsDeleteModalVisible(true);
  };

  const confirmDelete = () => {
    if (selectedList) {
      deleteMutation.mutate(selectedList.id);
    }
  };

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'manual':
        return 'blue';
      case 'import':
        return 'purple';
      case 'api':
        return 'cyan';
      case 'website':
        return 'green';
      default:
        return 'default';
    }
  };

  return (
    <Layout title="Lead Lists">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Lead Lists</h1>
            <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
              Organize your leads into lists for better management and targeting.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => router.push('/lead-lists/new')}
            >
              Create List
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8 dark:text-white">
            <Spin size="large" />
          </div>
        ) : leadLists.length > 0 ? (
          <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {leadLists.map((list) => (
              <Card
                key={list.id}
                hoverable
                className="shadow-sm hover:shadow-md transition-shadow duration-200 dark:bg-dark-card dark:border-dark-border"
                onClick={() => router.push(`/leads?listId=${list.id}`)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <Title level={4} className="mb-1 dark:text-white">{list.name}</Title>
                    <Tag color={getSourceColor(list.source)}>
                      {list.source.toUpperCase()}
                    </Tag>
                    <div className="flex items-center mt-2">
                      <TeamOutlined className="mr-1 text-gray-500 dark:text-gray-400" />
                      <Text className="dark:text-gray-300">{list._count?.leads || 0} leads</Text>
                    </div>
                  </div>
                  <Space>
                    <Tooltip title="Edit List">
                      <Button
                        type="text"
                        icon={<EditOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/lead-lists/${list.id}`);
                        }}
                      />
                    </Tooltip>
                    <Tooltip title="Delete List">
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(list);
                        }}
                      />
                    </Tooltip>
                  </Space>
                </div>
                {list.description && (
                  <>
                    <Divider className="my-3 dark:border-gray-700" />
                    <Text type="secondary" className="line-clamp-2 dark:text-gray-400">
                      {list.description}
                    </Text>
                  </>
                )}
                <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
                  Created: {new Date(list.createdAt).toLocaleDateString()}
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <div className="mt-8">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <span className="dark:text-gray-300">
                  No lead lists yet
                </span>
              }
            >
              <Button
                type="primary"
                onClick={() => router.push('/lead-lists/new')}
              >
                Create your first list
              </Button>
            </Empty>
          </div>
        )}

        <Modal
          title="Delete List"
          open={isDeleteModalVisible}
          onOk={confirmDelete}
          onCancel={() => setIsDeleteModalVisible(false)}
          confirmLoading={deleteMutation.isPending}
          okButtonProps={{ danger: true }}
          okText="Delete"
        >
          <div className="flex items-center">
            <ExclamationCircleOutlined className="text-yellow-500 text-xl mr-2" />
            <span className="dark:text-gray-300">
              Are you sure you want to delete the list "{selectedList?.name}"?
              {selectedList?._count?.leads ? (
                <Text strong className="block mt-2 dark:text-gray-200">
                  This list contains {selectedList._count.leads} leads. They will not be deleted, but they will be removed from this list.
                </Text>
              ) : null}
            </span>
          </div>
        </Modal>
      </div>
    </Layout>
  );
}
