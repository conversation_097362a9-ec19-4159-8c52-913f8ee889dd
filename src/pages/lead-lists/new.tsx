import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Form, Input, Button, Select, Card, message, Spin, Typography } from 'antd';
import Layout from '@/components/Layout';
import api from '@/lib/api';

const { Option } = Select;
const { Title, Text } = Typography;

export default function NewLeadListPage() {
  const router = useRouter();
  const { id: listId } = router.query;
  const isEditMode = Boolean(listId);

  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const [form] = Form.useForm();

  // Fetch list data if in edit mode
  const { data: listData, isLoading: isLoadingList } = useQuery({
    queryKey: ['leadList', listId],
    queryFn: () => api.get(`/lead-lists/${listId}`).then(res => res.data),
    enabled: isEditMode && !!listId,
  });

  // Set form values when list data is loaded
  useEffect(() => {
    if (listData) {
      form.setFieldsValue({
        name: listData.name,
        description: listData.description,
        source: listData.source,
      });
    }
  }, [listData, form]);

  const createListMutation = useMutation({
    mutationFn: async (values: any) => {
      return api.post('/lead-lists', values).then(res => res.data);
    },
    onSuccess: () => {
      message.success('List created successfully');
      router.push('/lead-lists');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.error || 'Failed to create list');
    },
  });

  const updateListMutation = useMutation({
    mutationFn: async (values: any) => {
      return api.put(`/lead-lists/${listId}`, values).then(res => res.data);
    },
    onSuccess: () => {
      message.success('List updated successfully');
      router.push('/lead-lists');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.error || 'Failed to update list');
    },
  });

  const handleSubmit = (values: any) => {
    if (isEditMode) {
      updateListMutation.mutate(values);
    } else {
      createListMutation.mutate(values);
    }
  };

  return (
    <Layout>
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Title level={2} className="dark:text-white">
            {isEditMode ? 'Edit Lead List' : 'Create Lead List'}
          </Title>
          <Text type="secondary" className="dark:text-gray-400">
            {isEditMode
              ? 'Update your lead list details.'
              : 'Create a new list to organize your leads.'}
          </Text>
        </div>

        {isLoadingList && isEditMode ? (
          <div className="flex justify-center py-8 dark:text-white">
            <Spin size="large" />
          </div>
        ) : (
          <Card className="dark:bg-dark-card dark:border-dark-border">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              initialValues={{
                source: 'manual',
              }}
            >
              <Form.Item
                name="name"
                label={<span className="dark:text-white">List Name</span>}
                rules={[{ required: true, message: 'Please enter a list name' }]}
              >
                <Input placeholder="My Lead List" className="dark:bg-dark-card dark:border-gray-600 dark:text-white" />
              </Form.Item>

              <Form.Item
                name="description"
                label={<span className="dark:text-white">Description</span>}
              >
                <Input.TextArea
                  placeholder="Optional description of this list"
                  rows={3}
                  className="dark:bg-dark-card dark:border-gray-600 dark:text-white"
                />
              </Form.Item>

              <Form.Item
                name="source"
                label={<span className="dark:text-white">Source</span>}
              >
                <Select className="dark:bg-dark-card dark:text-white">
                  <Option value="manual">Manual</Option>
                  <Option value="import">Import</Option>
                  <Option value="api">API</Option>
                  <Option value="website">Website</Option>
                </Select>
              </Form.Item>

              <div className="flex justify-end space-x-3 mt-4">
                <Button onClick={() => router.push('/lead-lists')}>
                  Cancel
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={isEditMode ? updateListMutation.isPending : createListMutation.isPending}
                >
                  {isEditMode ? 'Update List' : 'Create List'}
                </Button>
              </div>
            </Form>
          </Card>
        )}
      </div>
    </Layout>
  );
}
