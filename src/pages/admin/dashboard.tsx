import { useSession } from 'next-auth/react';
import { AdminDashboard } from '@/components/AdminDashboard';
import AdminLayout from '@/components/AdminLayout';

export default function AdminDashboardPage() {
  const { data: session } = useSession({
    required: true,
  });

  return (
    <AdminLayout>
      <div className="px-4 py-6">
        <h1 className="text-2xl font-semibold mb-6">Admin Dashboard</h1>
        <AdminDashboard />
      </div>
    </AdminLayout>
  );
}