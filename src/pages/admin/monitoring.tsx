import { useQuery } from '@tanstack/react-query';
import AdminLayout from '@/components/AdminLayout';
import { PerformanceMetrics } from '@/services/monitoring.service';
import { HealthCheckResult } from '@/services/health.service';

export default function MonitoringPage() {
  const { data: health } = useQuery<HealthCheckResult>({
    queryKey: ['health'],
    queryFn: () => fetch('/api/health').then(res => res.json()),
    refetchInterval: 30000,
  });

  const { data: metrics } = useQuery<PerformanceMetrics>({
    queryKey: ['metrics'],
    queryFn: () => fetch('/api/metrics').then(res => res.json()),
    refetchInterval: 30000,
  });

  return (
    <AdminLayout>
      <h1 className="text-2xl font-semibold mb-6">System Monitoring</h1>
      
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">System Health</h2>
          {health && (
            <div className="space-y-4">
              <div className={`text-lg font-medium ${health.status === 'healthy' ? 'text-green-600' : health.status === 'degraded' ? 'text-yellow-600' : 'text-red-600'}`}>
                Status: {health.status}
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>Database: {health.checks.database ? '✅' : '❌'}</div>
                <div>Redis: {health.checks.redis ? '✅' : '❌'}</div>
                <div>Email Service: {health.checks.emailService ? '✅' : '❌'}</div>
              </div>
            </div>
          )}
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Performance Metrics</h2>
          {metrics && (
            <div className="space-y-4">
              <div>API Latency: {metrics.apiLatency}ms</div>
              <div>Error Rate: {metrics.errorRate}%</div>
              <div>Successful Requests: {metrics.successfulRequests}</div>
              <div>Failed Requests: {metrics.failedRequests}</div>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}