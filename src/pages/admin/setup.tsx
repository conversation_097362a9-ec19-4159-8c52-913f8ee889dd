import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import {
  Card,
  Button,
  Typography,
  Space,
  Alert,
  Spin,
  message,
  Table,
  Input,
  Modal
} from 'antd';
import {
  CrownOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import Layout from '@/components/Layout';

const { Title, Text, Paragraph } = Typography;

interface AdminStatus {
  hasGlobalAdmins: boolean;
  isAdmin: boolean;
  adminLevel: 'global' | 'organization' | 'none';
  user: {
    id: string;
    name: string | null;
    email: string;
    isAdmin: boolean;
    ownedOrganization?: {
      id: string;
      name: string;
    };
  };
  globalAdmins: Array<{
    id: string;
    name: string | null;
    email: string;
    createdAt: string;
  }>;
  canInitialize: boolean;
}

export default function AdminSetupPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [adminStatus, setAdminStatus] = useState<AdminStatus | null>(null);
  const [initializing, setInitializing] = useState(false);
  const [newAdminEmail, setNewAdminEmail] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
      return;
    }
    
    fetchAdminStatus();
  }, [session, status]);

  const fetchAdminStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/setup');
      if (response.ok) {
        const data = await response.json();
        setAdminStatus(data);
      } else {
        message.error('Failed to load admin status');
      }
    } catch (error) {
      console.error('Error fetching admin status:', error);
      message.error('Failed to load admin status');
    } finally {
      setLoading(false);
    }
  };

  const handleInitializeAdmin = async () => {
    try {
      setInitializing(true);
      const response = await fetch('/api/admin/setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'initialize'
        })
      });

      if (response.ok) {
        message.success('Successfully initialized as admin!');
        fetchAdminStatus();
        // Redirect to admin dashboard
        setTimeout(() => {
          router.push('/admin/packages');
        }, 1500);
      } else {
        const error = await response.json();
        message.error(error.error || 'Failed to initialize admin');
      }
    } catch (error) {
      console.error('Error initializing admin:', error);
      message.error('Failed to initialize admin');
    } finally {
      setInitializing(false);
    }
  };

  const handleAddAdmin = async () => {
    if (!newAdminEmail) {
      message.error('Please enter an email address');
      return;
    }

    try {
      const response = await fetch('/api/admin/setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'makeAdmin',
          targetEmail: newAdminEmail
        })
      });

      if (response.ok) {
        message.success(`Successfully made ${newAdminEmail} an admin`);
        setNewAdminEmail('');
        setShowAddModal(false);
        fetchAdminStatus();
      } else {
        const error = await response.json();
        message.error(error.error || 'Failed to add admin');
      }
    } catch (error) {
      console.error('Error adding admin:', error);
      message.error('Failed to add admin');
    }
  };

  const handleRemoveAdmin = async (email: string) => {
    Modal.confirm({
      title: 'Remove Admin Access',
      content: `Are you sure you want to remove admin access from ${email}?`,
      onOk: async () => {
        try {
          const response = await fetch('/api/admin/setup', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'removeAdmin',
              targetEmail: email
            })
          });

          if (response.ok) {
            message.success(`Successfully removed admin access from ${email}`);
            fetchAdminStatus();
          } else {
            const error = await response.json();
            message.error(error.error || 'Failed to remove admin');
          }
        } catch (error) {
          console.error('Error removing admin:', error);
          message.error('Failed to remove admin');
        }
      }
    });
  };

  const adminColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name: string | null) => name || 'N/A'
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: any) => (
        adminStatus?.user.email !== record.email && (
          <Button
            size="small"
            danger
            icon={<UserDeleteOutlined />}
            onClick={() => handleRemoveAdmin(record.email)}
          >
            Remove
          </Button>
        )
      ),
    },
  ];

  if (loading) {
    return (
      <Layout>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
        <Title level={2}>
          <CrownOutlined /> Admin Setup
        </Title>

        {adminStatus?.canInitialize && (
          <Card style={{ marginBottom: '24px' }}>
            <Alert
              message="No Admin Users Found"
              description="This appears to be the first time accessing the admin panel. You can initialize yourself as the first admin user."
              type="warning"
              showIcon
              style={{ marginBottom: '16px' }}
            />
            
            <Space direction="vertical" style={{ width: '100%' }}>
              <Paragraph>
                <strong>Current User:</strong> {adminStatus.user.name} ({adminStatus.user.email})
              </Paragraph>
              
              <Button
                type="primary"
                size="large"
                icon={<CheckCircleOutlined />}
                loading={initializing}
                onClick={handleInitializeAdmin}
              >
                Initialize as First Admin
              </Button>
            </Space>
          </Card>
        )}

        {adminStatus?.isAdmin && (
          <Card>
            <Title level={4}>Admin Management</Title>
            
            <div style={{ marginBottom: '16px' }}>
              <Text strong>Your Admin Level:</Text>{' '}
              <Text type={adminStatus.adminLevel === 'global' ? 'success' : 'warning'}>
                {adminStatus.adminLevel === 'global' ? 'Global Admin' : 'Organization Admin'}
              </Text>
            </div>

            {adminStatus.adminLevel === 'global' && (
              <>
                <div style={{ marginBottom: '16px' }}>
                  <Button
                    type="primary"
                    icon={<UserAddOutlined />}
                    onClick={() => setShowAddModal(true)}
                  >
                    Add Admin User
                  </Button>
                </div>

                <Title level={5}>Global Admins</Title>
                <Table
                  dataSource={adminStatus.globalAdmins}
                  columns={adminColumns}
                  rowKey="id"
                  pagination={false}
                  size="small"
                />
              </>
            )}

            <div style={{ marginTop: '24px' }}>
              <Button
                type="default"
                onClick={() => router.push('/admin/packages')}
              >
                Go to Admin Dashboard
              </Button>
            </div>
          </Card>
        )}

        {!adminStatus?.isAdmin && !adminStatus?.canInitialize && (
          <Card>
            <Alert
              message="Access Denied"
              description="You don't have admin access. Please contact an administrator to grant you admin privileges."
              type="error"
              showIcon
            />
          </Card>
        )}

        <Modal
          title="Add Admin User"
          visible={showAddModal}
          onOk={handleAddAdmin}
          onCancel={() => {
            setShowAddModal(false);
            setNewAdminEmail('');
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text>Enter the email address of the user you want to make an admin:</Text>
            <Input
              placeholder="<EMAIL>"
              value={newAdminEmail}
              onChange={(e) => setNewAdminEmail(e.target.value)}
              onPressEnter={handleAddAdmin}
            />
          </Space>
        </Modal>
      </div>
    </Layout>
  );
}
