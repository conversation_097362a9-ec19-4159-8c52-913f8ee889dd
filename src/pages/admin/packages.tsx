import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Spin,
  message,
  Tag,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import Layout from '@/components/Layout';

const { Title } = Typography;
const { Option } = Select;

interface Package {
  id: string;
  name: string;
  description?: string;
  price: number;
  billingCycle: string;
  status: string;
  dailyEmailLimit?: number;
  monthlyEmailLimit?: number;
  aiAgentsEnabled: boolean;
  knowledgeBaseEnabled: boolean;
  maxAiAgents?: number;
  maxKnowledgeBases?: number;
  maxEmailAccounts?: number;
  maxLeads?: number;
  maxCampaigns?: number;
  isDefault: boolean;
  _count: {
    organizations: number;
    subscriptions: number;
  };
}

export default function PackagesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [packages, setPackages] = useState<Package[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPackage, setEditingPackage] = useState<Package | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
      return;
    }
    
    fetchPackages();
  }, [session, status]);

  const fetchPackages = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/packages');
      if (response.ok) {
        const data = await response.json();
        setPackages(data.packages);
      } else {
        message.error('Failed to fetch packages');
      }
    } catch (error) {
      console.error('Error fetching packages:', error);
      message.error('Failed to fetch packages');
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePackage = () => {
    setEditingPackage(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditPackage = (pkg: Package) => {
    setEditingPackage(pkg);
    form.setFieldsValue(pkg);
    setIsModalVisible(true);
  };

  const handleDeletePackage = async (packageId: string) => {
    try {
      const response = await fetch(`/api/admin/packages/${packageId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        message.success('Package deleted successfully');
        fetchPackages();
      } else {
        const error = await response.json();
        message.error(error.error || 'Failed to delete package');
      }
    } catch (error) {
      console.error('Error deleting package:', error);
      message.error('Failed to delete package');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const url = editingPackage 
        ? `/api/admin/packages/${editingPackage.id}`
        : '/api/admin/packages';
      
      const method = editingPackage ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(values)
      });
      
      if (response.ok) {
        message.success(`Package ${editingPackage ? 'updated' : 'created'} successfully`);
        setIsModalVisible(false);
        fetchPackages();
      } else {
        const error = await response.json();
        message.error(error.error || `Failed to ${editingPackage ? 'update' : 'create'} package`);
      }
    } catch (error) {
      console.error('Error saving package:', error);
      message.error(`Failed to ${editingPackage ? 'update' : 'create'} package`);
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Package) => (
        <div>
          <strong>{name}</strong>
          {record.isDefault && <Tag color="blue" style={{ marginLeft: 8 }}>Default</Tag>}
          <br />
          <small style={{ color: '#666' }}>{record.description}</small>
        </div>
      )
    },
    {
      title: 'Price',
      dataIndex: 'price',
      key: 'price',
      render: (price: number, record: Package) => 
        `$${price}/${record.billingCycle.toLowerCase()}`
    },
    {
      title: 'Email Limits',
      key: 'emailLimits',
      render: (record: Package) => (
        <div>
          <div>Daily: {record.dailyEmailLimit || 'Unlimited'}</div>
          <div>Monthly: {record.monthlyEmailLimit || 'Unlimited'}</div>
        </div>
      )
    },
    {
      title: 'AI Features',
      key: 'aiFeatures',
      render: (record: Package) => (
        <div>
          <Tag color={record.aiAgentsEnabled ? 'green' : 'red'}>
            AI Agents: {record.aiAgentsEnabled ? 'Yes' : 'No'}
          </Tag>
          <br />
          <Tag color={record.knowledgeBaseEnabled ? 'green' : 'red'}>
            Knowledge Base: {record.knowledgeBaseEnabled ? 'Yes' : 'No'}
          </Tag>
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'ACTIVE' ? 'green' : 'red'}>
          {status}
        </Tag>
      )
    },
    {
      title: 'Organizations',
      dataIndex: ['_count', 'organizations'],
      key: 'organizations',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: Package) => (
        <Space>
          <Button 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => handleEditPackage(record)}
          >
            Edit
          </Button>
          <Button 
            size="small" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              Modal.confirm({
                title: 'Delete Package',
                content: `Are you sure you want to delete "${record.name}"?`,
                onOk: () => handleDeletePackage(record.id)
              });
            }}
          >
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  if (loading) {
    return (
      <Layout>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div style={{ padding: '24px' }}>
        <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2}>Package Management</Title>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={handleCreatePackage}
          >
            Create Package
          </Button>
        </div>

        <Card>
          <Table
            dataSource={packages}
            columns={columns}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true
            }}
          />
        </Card>

        <Modal
          title={editingPackage ? 'Edit Package' : 'Create Package'}
          visible={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          footer={null}
          width={800}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="Package Name"
                  rules={[{ required: true, message: 'Please enter package name' }]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="price"
                  label="Price"
                  rules={[{ required: true, message: 'Please enter price' }]}
                >
                  <InputNumber min={0} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="description"
              label="Description"
            >
              <Input.TextArea rows={3} />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="billingCycle"
                  label="Billing Cycle"
                  rules={[{ required: true, message: 'Please select billing cycle' }]}
                >
                  <Select>
                    <Option value="DAILY">Daily</Option>
                    <Option value="WEEKLY">Weekly</Option>
                    <Option value="BIWEEKLY">Bi-weekly</Option>
                    <Option value="MONTHLY">Monthly</Option>
                    <Option value="QUARTERLY">Quarterly</Option>
                    <Option value="YEARLY">Yearly</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="status"
                  label="Status"
                  rules={[{ required: true, message: 'Please select status' }]}
                >
                  <Select>
                    <Option value="ACTIVE">Active</Option>
                    <Option value="INACTIVE">Inactive</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Divider>Email Limits</Divider>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="dailyEmailLimit"
                  label="Daily Email Limit"
                >
                  <InputNumber min={0} placeholder="Leave empty for unlimited" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="monthlyEmailLimit"
                  label="Monthly Email Limit"
                >
                  <InputNumber min={0} placeholder="Leave empty for unlimited" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Divider>AI Features</Divider>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="aiAgentsEnabled"
                  label="AI Agents Enabled"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="knowledgeBaseEnabled"
                  label="Knowledge Base Enabled"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="maxAiAgents"
                  label="Max AI Agents"
                >
                  <InputNumber min={0} placeholder="Leave empty for unlimited" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="maxKnowledgeBases"
                  label="Max Knowledge Bases"
                >
                  <InputNumber min={0} placeholder="Leave empty for unlimited" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Divider>Other Limits</Divider>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="maxEmailAccounts"
                  label="Max Email Accounts"
                >
                  <InputNumber min={0} placeholder="Unlimited" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="maxLeads"
                  label="Max Leads"
                >
                  <InputNumber min={0} placeholder="Unlimited" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="maxCampaigns"
                  label="Max Campaigns"
                >
                  <InputNumber min={0} placeholder="Unlimited" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="isDefault"
                  label="Default Package"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="sortOrder"
                  label="Sort Order"
                >
                  <InputNumber min={0} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  {editingPackage ? 'Update' : 'Create'} Package
                </Button>
                <Button onClick={() => setIsModalVisible(false)}>
                  Cancel
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </Layout>
  );
}
