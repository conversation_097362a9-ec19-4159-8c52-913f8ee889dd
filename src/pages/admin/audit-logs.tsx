import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import AdminLayout from '@/components/AdminLayout';

interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resourceType: string;
  resourceId: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export default function AuditLogsPage() {
  const [filters, setFilters] = useState({
    action: '',
    resourceType: '',
    startDate: '',
    endDate: '',
  });

  const { data: logs } = useQuery<AuditLog[]>({
    queryKey: ['audit-logs', filters],
    queryFn: () => 
      fetch(`/api/audit-logs?${new URLSearchParams(filters as any)}`).then(res => 
        res.json()
      ),
  });

  return (
    <AdminLayout>
      <h1 className="text-2xl font-semibold mb-6">Audit Logs</h1>
      
      <div className="mb-6 grid grid-cols-4 gap-4">
        <input
          type="text"
          placeholder="Filter by action"
          className="rounded-md border-gray-300"
          value={filters.action}
          onChange={(e) => setFilters(f => ({ ...f, action: e.target.value }))}
        />
        <input
          type="text"
          placeholder="Filter by resource type"
          className="rounded-md border-gray-300"
          value={filters.resourceType}
          onChange={(e) => setFilters(f => ({ ...f, resourceType: e.target.value }))}
        />
        <input
          type="date"
          className="rounded-md border-gray-300"
          value={filters.startDate}
          onChange={(e) => setFilters(f => ({ ...f, startDate: e.target.value }))}
        />
        <input
          type="date"
          className="rounded-md border-gray-300"
          value={filters.endDate}
          onChange={(e) => setFilters(f => ({ ...f, endDate: e.target.value }))}
        />
      </div>

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User ID</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resource</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {logs?.map((log) => (
              <tr key={log.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(log.timestamp).toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.userId}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.action}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {log.resourceType}/{log.resourceId}
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  {log.metadata && (
                    <pre className="text-xs">{JSON.stringify(log.metadata, null, 2)}</pre>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </AdminLayout>
  );
}