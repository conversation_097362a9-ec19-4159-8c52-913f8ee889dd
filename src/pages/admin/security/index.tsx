import { useSession } from 'next-auth/react';
import AdminLayout from '@/components/AdminLayout';

export default function SecurityPage() {
  const { data: session } = useSession({
    required: true,
  });

  return (
    <AdminLayout>
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-6">Security Settings</h2>
        {/* Add security management components here */}
      </div>
    </AdminLayout>
  );
}