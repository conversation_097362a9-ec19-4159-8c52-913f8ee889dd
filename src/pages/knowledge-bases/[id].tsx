import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  Select,
  message,
  Descriptions,
  Table,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Spin,
} from 'antd';
import {
  ArrowLeftOutlined,
  LinkOutlined,
  DisconnectOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  GlobalOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import Layout from '@/components/Layout';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface Agent {
  id: string;
  name: string;
  description: string | null;
  type: string;
  isActive: boolean;
}

interface KnowledgeBase {
  id: string;
  name: string;
  description: string | null;
  type: 'TEXT' | 'PDF' | 'IMAGE' | 'WEB_URL' | 'DOCUMENT';
  sourceUrl: string | null;
  fileName: string | null;
  fileSize: number | null;
  vectorCount: number;
  createdAt: string;
  content: string | null;
  agents: Array<{
    agent: Agent;
  }>;
  documents: Array<{
    id: string;
    title: string | null;
    content: string;
    chunkIndex: number | null;
    createdAt: string;
  }>;
}

const typeIcons = {
  TEXT: <FileTextOutlined />,
  PDF: <FilePdfOutlined />,
  IMAGE: <FileImageOutlined />,
  WEB_URL: <GlobalOutlined />,
  DOCUMENT: <FileTextOutlined />,
};

const typeColors = {
  TEXT: 'blue',
  PDF: 'red',
  IMAGE: 'green',
  WEB_URL: 'orange',
  DOCUMENT: 'purple',
};

export default function KnowledgeBaseDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const queryClient = useQueryClient();
  const [isLinkModalVisible, setIsLinkModalVisible] = useState(false);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);

  // Fetch knowledge base details
  const { data: knowledgeBase, isLoading } = useQuery<KnowledgeBase>({
    queryKey: ['knowledge-base', id],
    queryFn: async () => {
      const response = await fetch(`/api/knowledge-bases/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch knowledge base');
      }
      return response.json();
    },
    enabled: !!id,
  });

  // Fetch available agents
  const { data: agents = [] } = useQuery<Agent[]>({
    queryKey: ['agents'],
    queryFn: async () => {
      const response = await fetch('/api/agents');
      if (!response.ok) {
        throw new Error('Failed to fetch agents');
      }
      return response.json();
    },
  });

  // Link agent mutation
  const linkAgentMutation = useMutation({
    mutationFn: async (agentId: string) => {
      const response = await fetch(`/api/knowledge-bases/${id}/agents`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ agentId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to link agent');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledge-base', id] });
      message.success('Agent linked successfully');
      setIsLinkModalVisible(false);
      setSelectedAgentId(null);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to link agent');
    },
  });

  // Unlink agent mutation
  const unlinkAgentMutation = useMutation({
    mutationFn: async (agentId: string) => {
      const response = await fetch(`/api/knowledge-bases/${id}/agents`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ agentId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to unlink agent');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledge-base', id] });
      message.success('Agent unlinked successfully');
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to unlink agent');
    },
  });

  const handleLinkAgent = () => {
    if (selectedAgentId) {
      linkAgentMutation.mutate(selectedAgentId);
    }
  };

  const handleUnlinkAgent = (agentId: string) => {
    unlinkAgentMutation.mutate(agentId);
  };

  const formatFileSize = (bytes: number | null) => {
    if (!bytes) return '-';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  // Get available agents for linking (exclude already linked ones)
  const availableAgents = agents.filter(
    agent => !knowledgeBase?.agents.some(link => link.agent.id === agent.id)
  );

  const agentColumns = [
    {
      title: 'Name',
      dataIndex: ['agent', 'name'],
      key: 'name',
      render: (text: string, record: any) => (
        <Space>
          <RobotOutlined />
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: 'Type',
      dataIndex: ['agent', 'type'],
      key: 'type',
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: ['agent', 'isActive'],
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: any) => (
        <Popconfirm
          title="Are you sure you want to unlink this agent?"
          onConfirm={() => handleUnlinkAgent(record.agent.id)}
          okText="Yes"
          cancelText="No"
        >
          <Button
            type="text"
            danger
            icon={<DisconnectOutlined />}
            loading={unlinkAgentMutation.isPending}
          >
            Unlink
          </Button>
        </Popconfirm>
      ),
    },
  ];

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  if (!knowledgeBase) {
    return (
      <Layout>
        <div className="p-6">
          <Text>Knowledge base not found</Text>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="p-6">
        <div className="flex items-center mb-6">
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => router.push('/knowledge-bases')}
            className="mr-4"
          >
            Back to Knowledge Bases
          </Button>
          <div>
            <Title level={2} className="!mb-2">
              <Space>
                {typeIcons[knowledgeBase.type]}
                {knowledgeBase.name}
              </Space>
            </Title>
            <Text type="secondary">{knowledgeBase.description}</Text>
          </div>
        </div>

        {/* Statistics */}
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic
                title="Vector Count"
                value={knowledgeBase.vectorCount}
                prefix={<GlobalOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Linked Agents"
                value={knowledgeBase.agents.length}
                prefix={<RobotOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Documents"
                value={knowledgeBase.documents.length}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="File Size"
                value={formatFileSize(knowledgeBase.fileSize)}
                valueStyle={{ fontSize: '16px' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            {/* Knowledge Base Details */}
            <Card title="Details" className="mb-6">
              <Descriptions column={1} bordered>
                <Descriptions.Item label="Type">
                  <Tag color={typeColors[knowledgeBase.type]} icon={typeIcons[knowledgeBase.type]}>
                    {knowledgeBase.type}
                  </Tag>
                </Descriptions.Item>
                {knowledgeBase.sourceUrl && (
                  <Descriptions.Item label="Source URL">
                    <a href={knowledgeBase.sourceUrl} target="_blank" rel="noopener noreferrer">
                      {knowledgeBase.sourceUrl}
                    </a>
                  </Descriptions.Item>
                )}
                {knowledgeBase.fileName && (
                  <Descriptions.Item label="File Name">
                    {knowledgeBase.fileName}
                  </Descriptions.Item>
                )}
                <Descriptions.Item label="Created">
                  {new Date(knowledgeBase.createdAt).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            </Card>

            {/* Content Preview */}
            {knowledgeBase.content && (
              <Card title="Content Preview">
                <Paragraph
                  ellipsis={{ rows: 10, expandable: true, symbol: 'Show more' }}
                  style={{ whiteSpace: 'pre-wrap' }}
                >
                  {knowledgeBase.content}
                </Paragraph>
              </Card>
            )}
          </Col>

          <Col span={12}>
            {/* Linked Agents */}
            <Card
              title="Linked Agents"
              extra={
                <Button
                  type="primary"
                  icon={<LinkOutlined />}
                  onClick={() => setIsLinkModalVisible(true)}
                  disabled={availableAgents.length === 0}
                >
                  Link Agent
                </Button>
              }
            >
              {knowledgeBase.agents.length > 0 ? (
                <Table
                  columns={agentColumns}
                  dataSource={knowledgeBase.agents}
                  rowKey={record => record.agent.id}
                  pagination={false}
                  size="small"
                />
              ) : (
                <Text type="secondary">No agents linked to this knowledge base</Text>
              )}
            </Card>
          </Col>
        </Row>

        {/* Link Agent Modal */}
        <Modal
          title="Link Agent to Knowledge Base"
          open={isLinkModalVisible}
          onCancel={() => {
            setIsLinkModalVisible(false);
            setSelectedAgentId(null);
          }}
          footer={[
            <Button key="cancel" onClick={() => setIsLinkModalVisible(false)}>
              Cancel
            </Button>,
            <Button
              key="link"
              type="primary"
              onClick={handleLinkAgent}
              loading={linkAgentMutation.isPending}
              disabled={!selectedAgentId}
            >
              Link Agent
            </Button>,
          ]}
        >
          <div className="mb-4">
            <Text>Select an agent to link to this knowledge base:</Text>
          </div>
          <Select
            style={{ width: '100%' }}
            placeholder="Select an agent"
            value={selectedAgentId}
            onChange={setSelectedAgentId}
          >
            {availableAgents.map(agent => (
              <Option key={agent.id} value={agent.id}>
                <Space>
                  <RobotOutlined />
                  {agent.name}
                  <Tag color={agent.isActive ? 'green' : 'red'}>
                    {agent.isActive ? 'Active' : 'Inactive'}
                  </Tag>
                </Space>
              </Option>
            ))}
          </Select>
          {availableAgents.length === 0 && (
            <Text type="secondary">All available agents are already linked to this knowledge base.</Text>
          )}
        </Modal>
      </div>
    </Layout>
  );
}
