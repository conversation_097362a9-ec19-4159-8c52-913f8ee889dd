import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  message,
  Popconfirm,
  Tooltip,
  Row,
  Col,
  Statistic,
  Divider,
  Spin,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  GlobalOutlined,
  UploadOutlined,
  LinkOutlined,
  BlockOutlined,
  EyeOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import Layout from '@/components/Layout';
import { useSession } from 'next-auth/react';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface KnowledgeBase {
  id: string;
  name: string;
  description: string | null;
  vectorCount: number;
  createdAt: string;
  agents: Array<{
    agent: {
      id: string;
      name: string;
    };
  }>;
  documents: Array<{
    id: string;
    title: string | null;
    type: 'TEXT' | 'PDF' | 'IMAGE' | 'WEB_URL' | 'DOCUMENT';
    sourceUrl: string | null;
    fileName: string | null;
    fileSize: number | null;
    createdAt: string;
  }>;
}

interface Document {
  id: string;
  title: string | null;
  type: 'TEXT' | 'PDF' | 'IMAGE' | 'WEB_URL' | 'DOCUMENT';
  sourceUrl: string | null;
  fileName: string | null;
  fileSize: number | null;
  chunkCount: number;
  createdAt: string;
}

const typeIcons = {
  TEXT: <FileTextOutlined />,
  PDF: <FilePdfOutlined />,
  IMAGE: <FileImageOutlined />,
  WEB_URL: <GlobalOutlined />,
  DOCUMENT: <FileTextOutlined />,
};

const typeColors = {
  TEXT: 'blue',
  PDF: 'red',
  IMAGE: 'green',
  WEB_URL: 'orange',
  DOCUMENT: 'purple',
};

export default function KnowledgeBasesPage() {
  const { data: session } = useSession();
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [isSearchModalVisible, setIsSearchModalVisible] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);

  // Fetch knowledge base
  const { data: knowledgeBase, isLoading } = useQuery<KnowledgeBase>({
    queryKey: ['knowledge-base'],
    queryFn: async () => {
      const response = await fetch('/api/knowledge-bases');
      if (!response.ok) {
        throw new Error('Failed to fetch knowledge base');
      }
      return response.json();
    },
  });

  // Fetch documents
  const { data: documents = [], isLoading: documentsLoading } = useQuery<Document[]>({
    queryKey: ['knowledge-base-documents'],
    queryFn: async () => {
      const response = await fetch('/api/knowledge-bases/documents');
      if (!response.ok) {
        throw new Error('Failed to fetch documents');
      }
      return response.json();
    },
  });

  // Add document mutation
  const addDocumentMutation = useMutation({
    mutationFn: async (values: any) => {
      const formData = new FormData();

      // Add form fields
      Object.keys(values).forEach(key => {
        if (key !== 'file' && values[key] !== undefined) {
          formData.append(key, values[key]);
        }
      });

      // Add file if present
      if (fileList.length > 0 && fileList[0].originFileObj) {
        formData.append('file', fileList[0].originFileObj);
      }

      const response = await fetch('/api/knowledge-bases', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add document');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledge-base'] });
      queryClient.invalidateQueries({ queryKey: ['knowledge-base-documents'] });
      message.success('Document added successfully');
      handleCancel();
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to add document');
    },
  });

  // Delete document mutation
  const deleteDocumentMutation = useMutation({
    mutationFn: async (documentId: string) => {
      const response = await fetch('/api/knowledge-bases/documents', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ documentId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete document');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledge-base'] });
      queryClient.invalidateQueries({ queryKey: ['knowledge-base-documents'] });
      message.success('Document deleted successfully');
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to delete document');
    },
  });

  // Edit document mutation
  const editDocumentMutation = useMutation({
    mutationFn: async ({ id, title, content }: { id: string; title: string; content: string }) => {
      const response = await fetch(`/api/knowledge-bases/documents/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ title, content }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update document');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledge-base'] });
      queryClient.invalidateQueries({ queryKey: ['knowledge-base-documents'] });
      message.success('Document updated successfully');
      setIsViewModalVisible(false);
      setSelectedDocument(null);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to update document');
    },
  });

  // Search knowledge base mutation
  const searchMutation = useMutation({
    mutationFn: async ({ query, nResults }: { query: string; nResults?: number }) => {
      const response = await fetch('/api/knowledge-bases/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query, nResults: nResults || 10 }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to search knowledge base');
      }

      return response.json();
    },
    onSuccess: (data) => {
      setSearchResults(data.results || []);
      setIsSearching(false);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to search knowledge base');
      setIsSearching(false);
    },
  });

  const handleAddDocument = () => {
    form.resetFields();
    form.setFieldsValue({
      type: 'TEXT',
    });
    setFileList([]);
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setFileList([]);
  };

  const handleSubmit = (values: any) => {
    addDocumentMutation.mutate(values);
  };

  const handleDeleteDocument = (documentId: string) => {
    deleteDocumentMutation.mutate(documentId);
  };

  const handleFileUpload = ({ fileList: newFileList }: any) => {
    setFileList(newFileList);
  };

  const handleViewDocument = async (documentId: string) => {
    try {
      const response = await fetch(`/api/knowledge-bases/documents/${documentId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch document');
      }
      const document = await response.json();
      setSelectedDocument(document);
      editForm.setFieldsValue({
        title: document.title || '',
        content: document.content || '',
      });
      setIsViewModalVisible(true);
    } catch (error: any) {
      message.error(error.message || 'Failed to load document');
    }
  };

  const handleEditDocument = (values: any) => {
    if (!selectedDocument) return;
    editDocumentMutation.mutate({
      id: selectedDocument.id,
      title: values.title,
      content: values.content,
    });
  };

  const handleSearch = (values: any) => {
    setIsSearching(true);
    searchMutation.mutate({
      query: values.query,
      nResults: values.nResults || 10,
    });
  };

  const handleOpenSearch = () => {
    setSearchResults([]);
    searchForm.resetFields();
    setIsSearchModalVisible(true);
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: Document) => (
        <Space>
          {typeIcons[record.type]}
          <Text strong>{text || record.fileName || 'Untitled'}</Text>
        </Space>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={typeColors[type as keyof typeof typeColors]} icon={typeIcons[type as keyof typeof typeIcons]}>
          {type}
        </Tag>
      ),
    },
    {
      title: 'Source',
      key: 'source',
      render: (record: Document) => {
        if (record.sourceUrl) {
          return (
            <a href={record.sourceUrl} target="_blank" rel="noopener noreferrer">
              {record.sourceUrl.length > 50 ? `${record.sourceUrl.substring(0, 50)}...` : record.sourceUrl}
            </a>
          );
        }
        if (record.fileName) {
          return <Text>{record.fileName}</Text>;
        }
        return <Text type="secondary">Direct input</Text>;
      },
    },
    {
      title: 'Chunks',
      dataIndex: 'chunkCount',
      key: 'chunkCount',
      render: (count: number) => (
        <Tag color="blue">{count.toLocaleString()}</Tag>
      ),
    },
    {
      title: 'Size',
      dataIndex: 'fileSize',
      key: 'fileSize',
      render: (size: number | null) => {
        if (!size) return '-';
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(size) / Math.log(1024));
        return Math.round(size / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
      },
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: Document) => (
        <Space>
          <Tooltip title="View/Edit">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDocument(record.id)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this document?"
            description="This action cannot be undone and will remove all associated vectors."
            onConfirm={() => handleDeleteDocument(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                loading={deleteDocumentMutation.isPending}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // Calculate statistics
  const totalDocuments = documents.length;
  const totalVectors = knowledgeBase?.vectorCount || 0;
  const totalChunks = documents.reduce((sum: number, doc: Document) => sum + doc.chunkCount, 0);
  const linkedAgents = knowledgeBase?.agents.length || 0;

  return (
    <Layout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <Title level={2} className="!mb-2">Knowledge Base</Title>
            <Text type="secondary">
              Manage your organization's knowledge base with vector storage and AI agent integration
            </Text>
          </div>
          <Space>
            <Button
              icon={<SearchOutlined />}
              onClick={handleOpenSearch}
              size="large"
            >
              Search Knowledge Base
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddDocument}
              size="large"
            >
              Add Document
            </Button>
          </Space>
        </div>

        {/* Statistics */}
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Documents"
                value={totalDocuments}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Vectors"
                value={totalVectors}
                prefix={<GlobalOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Chunks"
                value={totalChunks}
                prefix={<BlockOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Linked Agents"
                value={linkedAgents}
                prefix={<LinkOutlined />}
              />
              {knowledgeBase?.agents && knowledgeBase.agents.length > 0 && (
                <div className="mt-2">
                  <Text type="secondary" className="text-xs">
                    {knowledgeBase.agents.map(a => a.agent.name).join(', ')}
                  </Text>
                </div>
              )}
            </Card>
          </Col>
        </Row>

        <Card title="Documents">
          <Table
            columns={columns}
            dataSource={documents}
            rowKey="id"
            loading={documentsLoading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} documents`,
            }}
          />
        </Card>

        {/* Add Document Modal */}
        <Modal
          title="Add Document to Knowledge Base"
          open={isModalVisible}
          onCancel={handleCancel}
          footer={null}
          width={800}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            <Form.Item
              name="title"
              label="Title"
              rules={[{ required: true, message: 'Please enter a title' }]}
            >
              <Input placeholder="Enter document title" />
            </Form.Item>

            <Form.Item
              name="type"
              label="Type"
              rules={[{ required: true, message: 'Please select a type' }]}
            >
              <Select placeholder="Select knowledge base type">
                <Option value="TEXT">Text Content</Option>
                <Option value="PDF">PDF Document</Option>
                <Option value="IMAGE">Image (OCR)</Option>
                <Option value="WEB_URL">Web URL</Option>
                <Option value="DOCUMENT">Document Upload</Option>
              </Select>
            </Form.Item>

            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
            >
              {({ getFieldValue }) => {
                const type = getFieldValue('type');

                if (type === 'WEB_URL') {
                  return (
                    <Form.Item
                      name="sourceUrl"
                      label="URL"
                      rules={[
                        { required: true, message: 'Please enter a URL' },
                        { type: 'url', message: 'Please enter a valid URL' },
                      ]}
                    >
                      <Input placeholder="https://example.com" />
                    </Form.Item>
                  );
                }

                if (type === 'TEXT') {
                  return (
                    <Form.Item
                      name="content"
                      label="Content"
                      rules={[{ required: true, message: 'Please enter content' }]}
                    >
                      <TextArea
                        placeholder="Enter text content"
                        rows={8}
                      />
                    </Form.Item>
                  );
                }

                if (['PDF', 'IMAGE', 'DOCUMENT'].includes(type)) {
                  return (
                    <Form.Item
                      label="Upload File"
                      rules={[{ required: true, message: 'Please upload a file' }]}
                    >
                      <Upload
                        beforeUpload={() => false}
                        fileList={fileList}
                        onChange={handleFileUpload}
                        maxCount={1}
                        accept={type === 'PDF' ? '.pdf' : type === 'IMAGE' ? 'image/*' : '*'}
                      >
                        <Button icon={<UploadOutlined />}>Select File</Button>
                      </Upload>
                    </Form.Item>
                  );
                }

                return null;
              }}
            </Form.Item>

            <div className="flex justify-end space-x-2">
              <Button onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={addDocumentMutation.isPending}
              >
                Add Document
              </Button>
            </div>
          </Form>
        </Modal>

        {/* View/Edit Document Modal */}
        <Modal
          title={`${selectedDocument?.title || 'Untitled'} - View/Edit`}
          open={isViewModalVisible}
          onCancel={() => {
            setIsViewModalVisible(false);
            setSelectedDocument(null);
          }}
          footer={null}
          width={1000}
        >
          {selectedDocument && (
            <Form
              form={editForm}
              layout="vertical"
              onFinish={handleEditDocument}
            >
              <Form.Item
                name="title"
                label="Title"
                rules={[{ required: true, message: 'Please enter a title' }]}
              >
                <Input placeholder="Enter document title" />
              </Form.Item>

              <Form.Item
                name="content"
                label="Content"
                rules={[{ required: true, message: 'Please enter content' }]}
              >
                <TextArea
                  placeholder="Enter document content"
                  rows={20}
                  style={{ fontFamily: 'monospace' }}
                />
              </Form.Item>

              <div className="flex justify-between items-center">
                <Space>
                  <Text type="secondary">Type: </Text>
                  <Tag color={typeColors[selectedDocument.type as keyof typeof typeColors]}>
                    {selectedDocument.type}
                  </Tag>
                  <Text type="secondary">Created: </Text>
                  <Text>{new Date(selectedDocument.createdAt).toLocaleString()}</Text>
                </Space>
                <Space>
                  <Button onClick={() => {
                    setIsViewModalVisible(false);
                    setSelectedDocument(null);
                  }}>
                    Cancel
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={editDocumentMutation.isPending}
                  >
                    Save Changes
                  </Button>
                </Space>
              </div>
            </Form>
          )}
        </Modal>

        {/* Search Knowledge Base Modal */}
        <Modal
          title="Search Knowledge Base"
          open={isSearchModalVisible}
          onCancel={() => setIsSearchModalVisible(false)}
          footer={null}
          width={1000}
        >
          <Form
            form={searchForm}
            layout="vertical"
            onFinish={handleSearch}
          >
            <Form.Item
              name="query"
              label="Search Query"
              rules={[{ required: true, message: 'Please enter a search query' }]}
            >
              <Input.Search
                placeholder="Enter keywords to search your knowledge base..."
                enterButton="Search"
                size="large"
                onSearch={(value) => {
                  if (value.trim()) {
                    handleSearch({ query: value, nResults: 10 });
                  }
                }}
                loading={isSearching}
              />
            </Form.Item>

            <Form.Item
              name="nResults"
              label="Number of Results"
              initialValue={10}
            >
              <Select style={{ width: 120 }}>
                <Option value={5}>5</Option>
                <Option value={10}>10</Option>
                <Option value={20}>20</Option>
                <Option value={50}>50</Option>
              </Select>
            </Form.Item>
          </Form>

          <Divider />

          {isSearching && (
            <div className="text-center py-8">
              <Spin size="large" />
              <div className="mt-4">
                <Text>Searching knowledge base...</Text>
              </div>
            </div>
          )}

          {searchResults.length > 0 && (
            <div>
              <Title level={4}>Search Results ({searchResults.length})</Title>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {searchResults.map((result, index) => (
                  <Card key={index} size="small">
                    <div className="space-y-2">
                      <div className="flex justify-between items-start">
                        <Text strong>
                          {result.metadata?.title || `Document ${result.metadata?.documentId || 'Unknown'}`}
                        </Text>
                        <Tag color="blue">
                          Score: {(result.score * 100).toFixed(1)}%
                        </Tag>
                      </div>
                      <Text type="secondary" className="text-sm">
                        Type: {result.metadata?.type || 'Unknown'} |
                        Chunk: {result.metadata?.chunkIndex + 1 || 'Unknown'}
                      </Text>
                      <div className="bg-gray-50 p-3 rounded border-l-4 border-blue-500">
                        <Text className="text-sm">
                          {result.content?.substring(0, 300)}
                          {result.content?.length > 300 && '...'}
                        </Text>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {!isSearching && searchResults.length === 0 && searchForm.getFieldValue('query') && (
            <div className="text-center py-8">
              <Text type="secondary">No results found for your search query.</Text>
            </div>
          )}
        </Modal>
      </div>
    </Layout>
  );
}
