import '@/styles/globals.css';
import '@/styles/email-preview.css';
import '@/styles/email-content.css';
import { SessionProvider } from 'next-auth/react';
import { QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider, App as AntdApp } from 'antd';
import { ThemeProvider, useTheme } from '@/contexts/ThemeContext';
import { lightTheme, darkTheme } from '@/styles/theme';
import { queryClient } from '@/lib/queryClient';
import { DataProvider } from '@/components/DataProvider';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { initLangChain } from '@/lib/langchain';

// Initialize LangChain tracing
if (typeof window === 'undefined') {
  // Only initialize on the server side
  initLangChain();
}

// Wrapper component to access theme context
function AppContent({ Component, pageProps }) {
  const { isDarkMode } = useTheme();
  const currentTheme = isDarkMode ? darkTheme : lightTheme;

  return (
    <ConfigProvider theme={currentTheme}>
      <AntdApp>
        <Component {...pageProps} />
      </AntdApp>
    </ConfigProvider>
  );
}

export default function App({
  Component,
  pageProps: { session, ...pageProps },
}) {
  return (
    <SessionProvider session={session}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <DataProvider>
            <AppContent Component={Component} pageProps={pageProps} />
            <SpeedInsights />
          </DataProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </SessionProvider>
  );
}
