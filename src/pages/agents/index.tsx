import { useState } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Table,
  Button,
  Card,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Space,
  Tag,
  Typography,
  Tooltip,
  Empty,
  Spin
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  RobotOutlined,
  SettingOutlined
} from '@ant-design/icons';
import Layout from '@/components/Layout';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface Agent {
  id: string;
  name: string;
  description: string | null;
  type: string;
  isActive: boolean;
  capabilities: string[];
  systemPrompt: string | null;
  createdAt: string;
  knowledgeBases?: Array<{
    knowledgeBase: {
      id: string;
      name: string;
    };
  }>;
}

export default function AgentsPage() {
  const router = useRouter();
  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const queryClient = useQueryClient();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);
  const [form] = Form.useForm();

  // Fetch agents
  const { data: agents = [], isPending: isLoading } = useQuery({
    queryKey: ['agents'],
    queryFn: () => fetch('/api/agents').then(res => res.json()),
    enabled: !!session,
  });

  // Fetch knowledge base for linking
  const { data: knowledgeBase } = useQuery({
    queryKey: ['knowledge-base'],
    queryFn: () => fetch('/api/knowledge-bases').then(res => res.json()),
    enabled: !!session,
  });

  // Create/update agent mutation
  const agentMutation = useMutation({
    mutationFn: async (values: any) => {
      const url = editingAgent ? `/api/agents/${editingAgent.id}` : '/api/agents';
      const method = editingAgent ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save agent');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      message.success(`Agent ${editingAgent ? 'updated' : 'created'} successfully`);
      setIsModalVisible(false);
      setEditingAgent(null);
      form.resetFields();
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to save agent');
    },
  });

  // Delete agent mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/agents/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete agent');
      }

      return id;
    },
    onSuccess: (id) => {
      queryClient.setQueryData(['agents'], (oldData: Agent[] | undefined) =>
        oldData ? oldData.filter(agent => agent.id !== id) : []
      );
      message.success('Agent deleted successfully');
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to delete agent');
    },
  });

  // Toggle agent active status
  const toggleActiveMutation = useMutation({
    mutationFn: async ({ id, isActive }: { id: string; isActive: boolean }) => {
      const response = await fetch(`/api/agents/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update agent status');
      }

      return { id, isActive };
    },
    onSuccess: ({ id, isActive }) => {
      queryClient.setQueryData(['agents'], (oldData: Agent[] | undefined) =>
        oldData ? oldData.map(agent => agent.id === id ? { ...agent, isActive } : agent) : []
      );
      message.success(`Agent ${isActive ? 'activated' : 'deactivated'} successfully`);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to update agent status');
    },
  });

  // Link/unlink knowledge base mutation
  const linkKnowledgeBaseMutation = useMutation({
    mutationFn: async ({ agentId, link }: { agentId: string; link: boolean }) => {
      const response = await fetch(`/api/agents/${agentId}/knowledge-base`, {
        method: link ? 'POST' : 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${link ? 'link' : 'unlink'} knowledge base`);
      }

      return response.json();
    },
    onSuccess: (_, { link }) => {
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      queryClient.invalidateQueries({ queryKey: ['knowledge-base'] });
      message.success(`Knowledge base ${link ? 'linked' : 'unlinked'} successfully`);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to update knowledge base link');
    },
  });

  const handleAddEdit = (agent?: Agent) => {
    setEditingAgent(agent || null);

    if (agent) {
      form.setFieldsValue({
        name: agent.name,
        description: agent.description,
        type: agent.type,
        isActive: agent.isActive,
        capabilities: agent.capabilities || [],
        systemPrompt: agent.systemPrompt || '',
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        type: 'email',
        isActive: true,
        capabilities: [],
        systemPrompt: `You are an AI email assistant that writes personalized, engaging emails for our company.
Your task is to create emails that are:
1. Highly personalized based on the recipient's information
2. Engaging and likely to get a response
3. Professional but conversational in tone
4. Concise (3-5 short paragraphs)
5. Focused on providing value to the recipient by explaining OUR services
6. Ending with a clear call to action

IMPORTANT RULES:
- DO NOT make assumptions about the recipient's industry based on their company name
- DO NOT ask questions about their business needs - we are TELLING them about OUR services
- ONLY use information from the knowledge base to describe our services
- If the knowledge base is empty, focus on a generic introduction without specifics about services
- NEVER make up information about our company or services that isn't in the knowledge base

Use the recipient's name, company, and other details to create a truly personalized message.
Reference information from the knowledge base when relevant to demonstrate expertise.
Each email should be unique and tailored specifically to the individual recipient.`,
      });
    }

    setIsModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: 'Are you sure you want to delete this agent?',
      content: 'This action cannot be undone.',
      onOk: () => deleteMutation.mutate(id),
    });
  };

  const handleToggleActive = (id: string, isActive: boolean) => {
    toggleActiveMutation.mutate({ id, isActive: !isActive });
  };

  const handleSubmit = (values: any) => {
    agentMutation.mutate(values);
  };

  const handleToggleKnowledgeBase = (agentId: string, isLinked: boolean) => {
    linkKnowledgeBaseMutation.mutate({ agentId, link: !isLinked });
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Agent) => (
        <div className="flex items-center">
          <RobotOutlined className="mr-2 text-blue-500" />
          <span>{text}</span>
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === 'email' ? 'blue' : 'green'}>
          {type.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Capabilities',
      dataIndex: 'capabilities',
      key: 'capabilities',
      render: (capabilities: string[]) => (
        <div>
          {capabilities && capabilities.length > 0 ? (
            <Space wrap>
              {capabilities.map(capability => {
                let color = 'default';
                if (capability === 'web_scraping') color = 'purple';
                if (capability === 'linkedin_research') color = 'blue';
                if (capability === 'personalization') color = 'green';
                if (capability === 'appointment_scheduling') color = 'orange';
                if (capability === 'knowledge_base') color = 'cyan';
                if (capability === 'web_search') color = 'magenta';

                return (
                  <Tag color={color} key={capability}>
                    {capability.replace(/_/g, ' ').toUpperCase()}
                  </Tag>
                );
              })}
            </Space>
          ) : (
            <Text type="secondary">None</Text>
          )}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean, record: Agent) => (
        <Switch
          checked={isActive}
          onChange={() => handleToggleActive(record.id, isActive)}
          checkedChildren="Active"
          unCheckedChildren="Inactive"
        />
      ),
    },
    {
      title: 'Knowledge Base',
      key: 'knowledgeBase',
      render: (record: Agent) => {
        const isLinked = record.knowledgeBases && record.knowledgeBases.length > 0;
        const hasKnowledgeBase = knowledgeBase && knowledgeBase.vectorCount > 0;

        if (!hasKnowledgeBase) {
          return (
            <Tooltip title="No knowledge base available. Create documents in the Knowledge Base section first.">
              <Tag color="default">No KB Available</Tag>
            </Tooltip>
          );
        }

        return (
          <Switch
            checked={isLinked}
            onChange={() => handleToggleKnowledgeBase(record.id, isLinked)}
            checkedChildren="Linked"
            unCheckedChildren="Not Linked"
            loading={linkKnowledgeBaseMutation.isPending}
          />
        );
      },
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Agent) => (
        <Space>
          <Tooltip title="Edit">
            <Button
              icon={<EditOutlined />}
              onClick={() => handleAddEdit(record)}
              type="text"
            />
          </Tooltip>
          <Tooltip title="Configure">
            <Button
              icon={<SettingOutlined />}
              onClick={() => router.push(`/agents/${record.id}/configure`)}
              type="text"
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.id)}
              type="text"
              danger
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Layout title="AI Agents">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <Title level={2}>AI Agents</Title>
            <Text type="secondary">
              Create and manage AI agents to automate email conversations
            </Text>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAddEdit()}
          >
            Create Agent
          </Button>
        </div>

        <Card>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Spin size="large" />
            </div>
          ) : agents.length > 0 ? (
            <Table
              dataSource={agents}
              columns={columns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <span>
                  No agents yet. Create your first AI agent to automate email conversations.
                </span>
              }
            />
          )}
        </Card>

        <Modal
          title={`${editingAgent ? 'Edit' : 'Create'} Agent`}
          open={isModalVisible}
          onCancel={() => {
            setIsModalVisible(false);
            setEditingAgent(null);
            form.resetFields();
          }}
          footer={null}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              type: 'email',
              isActive: true,
            }}
          >
            <Form.Item
              name="name"
              label="Name"
              rules={[{ required: true, message: 'Please enter a name' }]}
            >
              <Input placeholder="Enter agent name" />
            </Form.Item>

            <Form.Item
              name="description"
              label="Description"
            >
              <TextArea
                placeholder="Enter agent description"
                rows={3}
              />
            </Form.Item>

            <Form.Item
              name="systemPrompt"
              label="System Prompt"
              help="Customize how the AI agent generates emails. This prompt guides the AI's behavior and style."
            >
              <TextArea
                rows={6}
                placeholder="Enter system prompt for the AI agent..."
              />
            </Form.Item>

            <Form.Item
              name="type"
              label="Type"
              rules={[{ required: true, message: 'Please select a type' }]}
            >
              <Select>
                <Option value="email">Email</Option>
                <Option value="chat">Chat</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="capabilities"
              label="Capabilities"
              help="Select the capabilities this agent will have"
            >
              <Select
                mode="multiple"
                placeholder="Select capabilities"
                style={{ width: '100%' }}
                optionLabelProp="label"
              >
                <Option value="web_scraping" label="Web Scraping">
                  <div className="flex items-center">
                    <span role="img" aria-label="web scraping">🕸️</span> Web Scraping
                  </div>
                </Option>
                <Option value="linkedin_research" label="LinkedIn Research">
                  <div className="flex items-center">
                    <span role="img" aria-label="linkedin">👔</span> LinkedIn Research
                  </div>
                </Option>
                <Option value="personalization" label="Personalization">
                  <div className="flex items-center">
                    <span role="img" aria-label="personalization">✨</span> Personalization
                  </div>
                </Option>
                <Option value="appointment_scheduling" label="Appointment Scheduling">
                  <div className="flex items-center">
                    <span role="img" aria-label="appointment">📅</span> Appointment Scheduling
                  </div>
                </Option>
                <Option value="knowledge_base" label="Knowledge Base">
                  <div className="flex items-center">
                    <span role="img" aria-label="knowledge">📚</span> Knowledge Base
                  </div>
                </Option>
                <Option value="web_search" label="Web Search">
                  <div className="flex items-center">
                    <span role="img" aria-label="search">🔍</span> Web Search
                  </div>
                </Option>
              </Select>
            </Form.Item>

            {knowledgeBase && knowledgeBase.vectorCount > 0 && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <Text type="secondary" className="text-sm">
                  💡 <strong>Knowledge Base Available:</strong> After creating this agent, you can link it to your knowledge base
                  ({knowledgeBase.vectorCount} vectors) to enable AI-powered responses using your organization's information.
                </Text>
              </div>
            )}

            <Form.Item
              name="isActive"
              label="Status"
              valuePropName="checked"
            >
              <Switch
                checkedChildren="Active"
                unCheckedChildren="Inactive"
              />
            </Form.Item>

            <div className="flex justify-end">
              <Button
                type="default"
                onClick={() => {
                  setIsModalVisible(false);
                  setEditingAgent(null);
                  form.resetFields();
                }}
                className="mr-2"
              >
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={agentMutation.isPending}
              >
                {editingAgent ? 'Update' : 'Create'}
              </Button>
            </div>
          </Form>
        </Modal>
      </div>
    </Layout>
  );
}
