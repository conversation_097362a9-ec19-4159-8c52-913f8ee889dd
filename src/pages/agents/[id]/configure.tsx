import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Button,
  Card,
  Form,
  Input,
  Select,
  Switch,
  message,
  Tabs,
  Typography,
  Spin,
  Divider,
  Space,
  Tag,
  List,
  Modal,
  Upload,
  Alert
} from 'antd';
import {
  SaveOutlined,
  RobotOutlined,
  DatabaseOutlined,
  SearchOutlined,
  UploadOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import Layout from '@/components/Layout';
import { UploadFile } from 'antd/lib/upload/interface';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface Agent {
  id: string;
  name: string;
  description: string | null;
  type: string;
  isActive: boolean;
  capabilities: string[];
  config: any;
  systemPrompt: string | null;
  createdAt: string;
}

interface KnowledgeBase {
  id: string;
  name: string;
  description: string | null;
  content: string;
  agentId: string;
  createdAt: string;
}

export default function AgentConfigurePage() {
  const router = useRouter();
  const { id } = router.query;
  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('general');
  const [form] = Form.useForm();
  const [knowledgeBaseForm] = Form.useForm();
  const [isKnowledgeBaseModalVisible, setIsKnowledgeBaseModalVisible] = useState(false);
  const [editingKnowledgeBase, setEditingKnowledgeBase] = useState<KnowledgeBase | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [researchTopic, setResearchTopic] = useState('');
  const [researchResults, setResearchResults] = useState('');
  const [isResearching, setIsResearching] = useState(false);

  // Fetch agent
  const { data: agent, isPending: isAgentLoading } = useQuery({
    queryKey: ['agent', id],
    queryFn: () => fetch(`/api/agents/${id}`).then(res => res.json()),
    enabled: !!id && !!session,
  });

  // Fetch knowledge bases
  const { data: knowledgeBases = [], isPending: isKnowledgeBasesLoading } = useQuery({
    queryKey: ['knowledgeBases', id],
    queryFn: () => fetch(`/api/knowledge-bases?agentId=${id}`).then(res => res.json()),
    enabled: !!id && !!session,
  });

  // Update agent config mutation
  const updateConfigMutation = useMutation({
    mutationFn: async (values: any) => {
      // Extract systemPrompt from values and remove it from config
      const { systemPrompt, ...configValues } = values;

      const response = await fetch(`/api/agents/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...agent,
          systemPrompt: systemPrompt,
          config: configValues,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update agent configuration');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agent', id] });
      message.success('Agent configuration updated successfully');
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to update agent configuration');
    },
  });

  // Create/update knowledge base mutation
  const knowledgeBaseMutation = useMutation({
    mutationFn: async (values: any) => {
      const url = editingKnowledgeBase
        ? `/api/knowledge-bases/${editingKnowledgeBase.id}`
        : '/api/knowledge-bases';
      const method = editingKnowledgeBase ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...values,
          agentId: id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save knowledge base');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledgeBases', id] });
      message.success(`Knowledge base ${editingKnowledgeBase ? 'updated' : 'created'} successfully`);
      setIsKnowledgeBaseModalVisible(false);
      setEditingKnowledgeBase(null);
      knowledgeBaseForm.resetFields();
      setFileList([]);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to save knowledge base');
    },
  });

  // Delete knowledge base mutation
  const deleteKnowledgeBaseMutation = useMutation({
    mutationFn: async (knowledgeBaseId: string) => {
      const response = await fetch(`/api/knowledge-bases/${knowledgeBaseId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete knowledge base');
      }

      return knowledgeBaseId;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledgeBases', id] });
      message.success('Knowledge base deleted successfully');
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to delete knowledge base');
    },
  });

  // Web search mutation
  const webSearchMutation = useMutation({
    mutationFn: async (query: string) => {
      const response = await fetch('/api/agents/web-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to perform web search');
      }

      return response.json();
    },
    onSuccess: (data) => {
      setSearchResults(data.results);
      setIsSearching(false);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to perform web search');
      setIsSearching(false);
    },
  });

  // Research topic mutation
  const researchMutation = useMutation({
    mutationFn: async (topic: string) => {
      const response = await fetch('/api/agents/research', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ topic }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to research topic');
      }

      return response.json();
    },
    onSuccess: (data) => {
      setResearchResults(data.summary);
      setIsResearching(false);
    },
    onError: (error: any) => {
      message.error(error.message || 'Failed to research topic');
      setIsResearching(false);
    },
  });

  useEffect(() => {
    if (agent) {
      // Set form values from agent config and systemPrompt
      form.setFieldsValue({
        ...agent.config,
        systemPrompt: agent.systemPrompt || '',
      });
    }
  }, [agent, form]);

  const handleConfigSubmit = (values: any) => {
    updateConfigMutation.mutate(values);
  };

  const handleAddEditKnowledgeBase = (kb?: KnowledgeBase) => {
    setEditingKnowledgeBase(kb || null);

    if (kb) {
      knowledgeBaseForm.setFieldsValue({
        name: kb.name,
        description: kb.description,
        content: kb.content,
      });
    } else {
      knowledgeBaseForm.resetFields();
    }

    setIsKnowledgeBaseModalVisible(true);
  };

  const handleDeleteKnowledgeBase = (knowledgeBaseId: string) => {
    Modal.confirm({
      title: 'Are you sure you want to delete this knowledge base?',
      content: 'This action cannot be undone.',
      onOk: () => deleteKnowledgeBaseMutation.mutate(knowledgeBaseId),
    });
  };

  const handleKnowledgeBaseSubmit = (values: any) => {
    knowledgeBaseMutation.mutate(values);
  };

  const handleFileUpload = (info: any) => {
    const { fileList } = info;
    setFileList(fileList);

    if (info.file.status === 'done') {
      // Read the file content
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        knowledgeBaseForm.setFieldsValue({
          content: content,
        });
      };
      reader.readAsText(info.file.originFileObj);
      message.success(`${info.file.name} file uploaded successfully`);
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} file upload failed.`);
    }
  };

  const handleSearch = () => {
    if (!searchQuery.trim()) {
      message.warning('Please enter a search query');
      return;
    }

    setIsSearching(true);
    setSearchResults([]);
    webSearchMutation.mutate(searchQuery);
  };

  const handleResearch = () => {
    if (!researchTopic.trim()) {
      message.warning('Please enter a research topic');
      return;
    }

    setIsResearching(true);
    setResearchResults('');
    researchMutation.mutate(researchTopic);
  };

  if (isAgentLoading) {
    return (
      <Layout title="Configure Agent">
        <div className="flex justify-center items-center h-screen">
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  if (!agent) {
    return (
      <Layout title="Configure Agent">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Alert
            message="Agent not found"
            description="The agent you are trying to configure does not exist."
            type="error"
            showIcon
          />
        </div>
      </Layout>
    );
  }

  return (
    <Layout title={`Configure ${agent.name}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <Title level={2}>
              <RobotOutlined className="mr-2" /> Configure {agent.name}
            </Title>
            <Text type="secondary">
              Configure your AI agent's behavior and capabilities
            </Text>
          </div>
          <Space>
            <Button
              onClick={() => router.push('/agents')}
            >
              Back to Agents
            </Button>
          </Space>
        </div>

        <Card>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="General Configuration" key="general" />
            {agent.capabilities.includes('knowledge_base') && (
              <TabPane tab="Knowledge Bases" key="knowledge" />
            )}
            {agent.capabilities.includes('web_search') && (
              <TabPane tab="Web Search" key="search" />
            )}
          </Tabs>

          {activeTab === 'general' && (
            <div className="mt-4">
              <Form
                form={form}
                layout="vertical"
                onFinish={handleConfigSubmit}
                initialValues={{
                  tone: 'professional',
                  emailTemplate: '',
                }}
              >
                <Form.Item
                  name="systemPrompt"
                  label="System Prompt"
                  help="Customize how the AI agent generates emails. This prompt guides the AI's behavior and style."
                >
                  <TextArea
                    rows={8}
                    placeholder="Enter system prompt for the AI agent..."
                    defaultValue={`You are an AI email assistant that writes personalized, engaging emails.
Your task is to create emails that are:
1. Highly personalized based on the recipient's information
2. Engaging and likely to get a response
3. Professional but conversational in tone
4. Concise (3-5 short paragraphs)
5. Focused on providing value to the recipient
6. Ending with a clear call to action

Use the recipient's name, company, and other details to create a truly personalized message.
Reference information from the knowledge base when relevant to demonstrate expertise.
Each email should be unique and tailored specifically to the individual recipient.`}
                  />
                </Form.Item>

                <Form.Item
                  name="tone"
                  label="Communication Tone"
                  help="Select the tone the agent should use in communications"
                >
                  <Select>
                    <Option value="professional">Professional</Option>
                    <Option value="friendly">Friendly</Option>
                    <Option value="casual">Casual</Option>
                    <Option value="formal">Formal</Option>
                    <Option value="enthusiastic">Enthusiastic</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="emailTemplate"
                  label="Email Template"
                  help="Provide a template for emails (optional). Use variables like {{name}}, {{company}}, etc."
                >
                  <TextArea rows={6} placeholder="Enter email template..." />
                </Form.Item>

                <Form.Item
                  name="followUpDays"
                  label="Follow-up Days"
                  help="Number of days to wait before following up"
                >
                  <Input type="number" min={1} max={30} />
                </Form.Item>

                <Form.Item
                  name="maxFollowUps"
                  label="Maximum Follow-ups"
                  help="Maximum number of follow-up emails to send"
                >
                  <Input type="number" min={0} max={10} />
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={updateConfigMutation.isPending}
                  >
                    Save Configuration
                  </Button>
                </Form.Item>
              </Form>
            </div>
          )}

          {activeTab === 'knowledge' && (
            <div className="mt-4">
              <div className="flex justify-between items-center mb-4">
                <Title level={4}>
                  <DatabaseOutlined className="mr-2" /> Knowledge Bases
                </Title>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => handleAddEditKnowledgeBase()}
                >
                  Add Knowledge Base
                </Button>
              </div>

              {isKnowledgeBasesLoading ? (
                <div className="flex justify-center py-8">
                  <Spin size="large" />
                </div>
              ) : knowledgeBases.length > 0 ? (
                <List
                  itemLayout="horizontal"
                  dataSource={knowledgeBases}
                  renderItem={(kb: KnowledgeBase) => (
                    <List.Item
                      actions={[
                        <Button
                          key="edit"
                          icon={<SaveOutlined />}
                          onClick={() => handleAddEditKnowledgeBase(kb)}
                        >
                          Edit
                        </Button>,
                        <Button
                          key="delete"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeleteKnowledgeBase(kb.id)}
                        >
                          Delete
                        </Button>,
                      ]}
                    >
                      <List.Item.Meta
                        title={kb.name}
                        description={kb.description || 'No description'}
                      />
                      <div>
                        <Text type="secondary">
                          Created: {new Date(kb.createdAt).toLocaleDateString()}
                        </Text>
                      </div>
                    </List.Item>
                  )}
                />
              ) : (
                <div className="text-center py-8">
                  <Text type="secondary">No knowledge bases yet. Add one to get started.</Text>
                </div>
              )}
            </div>
          )}

          {activeTab === 'search' && (
            <div className="mt-4">
              <Title level={4}>
                <SearchOutlined className="mr-2" /> Web Search
              </Title>
              <Paragraph>
                Test the agent's web search capabilities by searching for information.
              </Paragraph>

              <Divider />

              <div className="mb-8">
                <Title level={5}>Quick Search</Title>
                <div className="flex mb-4">
                  <Input
                    placeholder="Enter search query..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onPressEnter={handleSearch}
                    style={{ width: '80%' }}
                  />
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                    loading={isSearching}
                    style={{ marginLeft: 8 }}
                  >
                    Search
                  </Button>
                </div>

                {searchResults.length > 0 && (
                  <List
                    bordered
                    dataSource={searchResults}
                    renderItem={(result, index) => (
                      <List.Item>
                        <List.Item.Meta
                          title={<a href={result.link} target="_blank" rel="noopener noreferrer">{result.title}</a>}
                          description={result.snippet}
                        />
                      </List.Item>
                    )}
                  />
                )}
              </div>

              <Divider />

              <div>
                <Title level={5}>Research Topic</Title>
                <Paragraph>
                  Research a topic in-depth and get a comprehensive summary.
                </Paragraph>
                <div className="flex mb-4">
                  <Input
                    placeholder="Enter research topic..."
                    value={researchTopic}
                    onChange={(e) => setResearchTopic(e.target.value)}
                    onPressEnter={handleResearch}
                    style={{ width: '80%' }}
                  />
                  <Button
                    type="primary"
                    onClick={handleResearch}
                    loading={isResearching}
                    style={{ marginLeft: 8 }}
                  >
                    Research
                  </Button>
                </div>

                {researchResults && (
                  <Card title="Research Results">
                    <div dangerouslySetInnerHTML={{ __html: researchResults.replace(/\n/g, '<br>') }} />
                  </Card>
                )}
              </div>
            </div>
          )}
        </Card>

        <Modal
          title={`${editingKnowledgeBase ? 'Edit' : 'Add'} Knowledge Base`}
          open={isKnowledgeBaseModalVisible}
          onCancel={() => {
            setIsKnowledgeBaseModalVisible(false);
            setEditingKnowledgeBase(null);
            knowledgeBaseForm.resetFields();
            setFileList([]);
          }}
          footer={null}
          width={800}
        >
          <Form
            form={knowledgeBaseForm}
            layout="vertical"
            onFinish={handleKnowledgeBaseSubmit}
          >
            <Form.Item
              name="name"
              label="Name"
              rules={[{ required: true, message: 'Please enter a name' }]}
            >
              <Input placeholder="Enter knowledge base name" />
            </Form.Item>

            <Form.Item
              name="description"
              label="Description"
            >
              <TextArea
                placeholder="Enter description"
                rows={2}
              />
            </Form.Item>

            <Form.Item
              label="Upload File (Optional)"
              help="Upload a text file to populate the knowledge base content"
            >
              <Upload
                beforeUpload={() => false}
                fileList={fileList}
                onChange={handleFileUpload}
                maxCount={1}
              >
                <Button icon={<UploadOutlined />}>Select File</Button>
              </Upload>
            </Form.Item>

            <Form.Item
              name="content"
              label="Content"
              rules={[{ required: true, message: 'Please enter content' }]}
            >
              <TextArea
                placeholder="Enter knowledge base content"
                rows={10}
              />
            </Form.Item>

            <div className="flex justify-end">
              <Button
                type="default"
                onClick={() => {
                  setIsKnowledgeBaseModalVisible(false);
                  setEditingKnowledgeBase(null);
                  knowledgeBaseForm.resetFields();
                  setFileList([]);
                }}
                className="mr-2"
              >
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={knowledgeBaseMutation.isPending}
              >
                {editingKnowledgeBase ? 'Update' : 'Create'}
              </Button>
            </div>
          </Form>
        </Modal>
      </div>
    </Layout>
  );
}
