import { useState, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button, Table, Empty, Badge, message, Modal, Spin, Space, Tag, Input, Tooltip, Drawer, Typography, Divider } from 'antd';
import { CheckOutlined, DeleteOutlined, SearchOutlined, EyeOutlined, CloseOutlined } from '@ant-design/icons';
import { useSession } from 'next-auth/react';
import Layout from '@/components/Layout';
import type { TableProps, InputRef } from 'antd';
import type { FilterConfirmProps } from 'antd/es/table/interface';

const { Title, Text, Paragraph } = Typography;

interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function NotificationsPage() {
  const { data: session } = useSession({
    required: true,
  });
  const queryClient = useQueryClient();

  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [detailNotification, setDetailNotification] = useState<Notification | null>(null);

  const { data: notifications, isLoading } = useQuery({
    queryKey: ['notifications'],
    queryFn: () => fetch('/api/notifications').then(res => res.json()),
  });

  const markAsReadMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/notifications/${id}`, { method: 'PATCH' });
      if (!response.ok) {
        throw new Error('Failed to mark notification as read');
      }
      const updatedNotification = await response.json();
      return updatedNotification;
    },
    onSuccess: (updatedNotification) => {
      // Update the local state immediately
      if (notifications) {
        const updatedNotifications = notifications.map((notification: Notification) =>
          notification.id === updatedNotification.id ? { ...notification, isRead: true } : notification
        );
        queryClient.setQueryData(['notifications'], updatedNotifications);
      }

      // Then invalidate the query to refetch from server
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      message.success('Notification marked as read');
    },
    onError: (error) => {
      console.error('Error marking notification as read:', error);
      message.error('Failed to mark notification as read');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/notifications/${id}`, { method: 'DELETE' });
      if (!response.ok) {
        throw new Error('Failed to delete notification');
      }
      return id;
    },
    onSuccess: (deletedId) => {
      // Update the local state immediately
      if (notifications) {
        const updatedNotifications = notifications.filter((notification: Notification) => notification.id !== deletedId);
        queryClient.setQueryData(['notifications'], updatedNotifications);
      }

      // Then invalidate the query to refetch from server
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      message.success('Notification deleted successfully');
      setDeleteModalVisible(false);
    },
    onError: (error) => {
      console.error('Error deleting notification:', error);
      message.error('Failed to delete notification');
    },
  });

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'green';
      case 'warning':
        return 'orange';
      case 'error':
        return 'red';
      default:
        return 'blue';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Add state for selected row keys and search text
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchText, setSearchText] = useState('');
  const [searchedColumn, setSearchedColumn] = useState('');
  const searchInput = useRef<InputRef>(null);

  // Handle batch actions
  const handleBatchMarkAsRead = () => {
    if (selectedRowKeys.length === 0) return;

    // Call the API to mark multiple notifications as read
    fetch('/api/notifications/batch', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ids: selectedRowKeys,
        action: 'markAsRead',
      }),
    })
      .then(response => {
        if (!response.ok) throw new Error('Failed to mark notifications as read');
        return response.json();
      })
      .then(() => {
        // Update local state
        if (notifications) {
          const updatedNotifications = notifications.map((notification: Notification) =>
            selectedRowKeys.includes(notification.id) ? { ...notification, isRead: true } : notification
          );
          queryClient.setQueryData(['notifications'], updatedNotifications);
        }

        // Invalidate query and show success message
        queryClient.invalidateQueries({ queryKey: ['notifications'] });
        message.success(`${selectedRowKeys.length} notifications marked as read`);
        setSelectedRowKeys([]);
      })
      .catch(error => {
        console.error('Error marking notifications as read:', error);
        message.error('Failed to mark notifications as read');
      });
  };

  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) return;

    Modal.confirm({
      title: 'Delete Notifications',
      content: `Are you sure you want to delete ${selectedRowKeys.length} notifications?`,
      onOk: () => {
        // Call the API to delete multiple notifications
        fetch('/api/notifications/batch', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ids: selectedRowKeys,
          }),
        })
          .then(response => {
            if (!response.ok) throw new Error('Failed to delete notifications');
            return response.json();
          })
          .then(() => {
            // Update local state
            if (notifications) {
              const updatedNotifications = notifications.filter(
                (notification: Notification) => !selectedRowKeys.includes(notification.id)
              );
              queryClient.setQueryData(['notifications'], updatedNotifications);
            }

            // Invalidate query and show success message
            queryClient.invalidateQueries({ queryKey: ['notifications'] });
            message.success(`${selectedRowKeys.length} notifications deleted`);
            setSelectedRowKeys([]);
          })
          .catch(error => {
            console.error('Error deleting notifications:', error);
            message.error('Failed to delete notifications');
          });
      },
    });
  };

  // Table row selection config
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  // Search handler
  const handleSearch = (
    selectedKeys: string[],
    confirm: (param?: FilterConfirmProps) => void,
    dataIndex: keyof Notification,
  ) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters: () => void) => {
    clearFilters();
    setSearchText('');
  };

  const getColumnSearchProps = (dataIndex: keyof Notification) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }: any) => (
      <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
        <Input
          ref={searchInput}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
          <Button
            onClick={() => clearFilters && handleReset(clearFilters)}
            size="small"
            style={{ width: 90 }}
          >
            Reset
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              confirm({ closeDropdown: false });
              setSearchText((selectedKeys as string[])[0]);
              setSearchedColumn(dataIndex);
            }}
          >
            Filter
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              close();
            }}
          >
            Close
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: boolean) => (
      <SearchOutlined style={{ color: filtered ? '#1677ff' : undefined }} />
    ),
    onFilter: (value: string, record: Notification) =>
      record[dataIndex]
        .toString()
        .toLowerCase()
        .includes((value as string).toLowerCase()),
    onFilterDropdownOpenChange: (visible: boolean) => {
      if (visible) {
        setTimeout(() => searchInput.current?.select(), 100);
      }
    },
  });

  // Table columns
  const columns = [
    {
      title: 'Status',
      key: 'status',
      width: 90,
      filters: [
        { text: 'Read', value: true },
        { text: 'Unread', value: false },
      ],
      onFilter: (value: boolean, record: Notification) => record.isRead === value,
      render: (_, record: Notification) => (
        record.isRead ?
          <Tag color="default">Read</Tag> :
          <Tag color="blue">Unread</Tag>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      filters: [
        { text: 'Info', value: 'info' },
        { text: 'Success', value: 'success' },
        { text: 'Warning', value: 'warning' },
        { text: 'Error', value: 'error' },
      ],
      onFilter: (value: string, record: Notification) => record.type === value,
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>{type.toUpperCase()}</Tag>
      ),
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      sorter: (a: Notification, b: Notification) => a.title.localeCompare(b.title),
      ...getColumnSearchProps('title'),
      render: (text: string) => <span className="text-gray-900 dark:text-white">{text}</span>,
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
      ...getColumnSearchProps('message'),
      render: (text: string) => <span className="text-gray-500 dark:text-gray-300">{text}</span>,
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      sorter: (a: Notification, b: Notification) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      defaultSortOrder: 'descend' as const,
      render: (date: string) => <span className="text-gray-500 dark:text-gray-300">{formatDate(date)}</span>,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      align: 'center' as const,
      render: (_, record: Notification) => (
        <Space size="middle">
          <Tooltip title="View details">
            <Button
              type="text"
              icon={<EyeOutlined style={{ color: '#52c41a' }} />}
              onClick={(e) => {
                e.stopPropagation();
                setDetailNotification(record);
                setDrawerVisible(true);

                // If notification is unread, mark it as read when viewed
                if (!record.isRead) {
                  markAsReadMutation.mutate(record.id);
                }
              }}
            />
          </Tooltip>
          {!record.isRead && (
            <Tooltip title="Mark as read">
              <Button
                type="text"
                icon={<CheckOutlined style={{ color: '#1677ff' }} />}
                onClick={(e) => {
                  e.stopPropagation();
                  markAsReadMutation.mutate(record.id);
                }}
              />
            </Tooltip>
          )}
          <Tooltip title="Delete notification">
            <Button
              type="text"
              icon={<DeleteOutlined style={{ color: '#ff4d4f' }} />}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedNotification(record);
                setDeleteModalVisible(true);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center mb-6">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Notifications</h1>
            <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
              View and manage your system notifications.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">

            {selectedRowKeys.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-gray-500 dark:text-gray-400">{selectedRowKeys.length} selected</span>
                <Space>
                  <Button
                    type="primary"
                    onClick={handleBatchMarkAsRead}
                    icon={<CheckOutlined />}
                    size="middle"
                  >
                    Mark as Read
                  </Button>
                  <Button
                    danger
                    onClick={handleBatchDelete}
                    icon={<DeleteOutlined />}
                    size="middle"
                  >
                    Delete
                  </Button>
                </Space>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-dark-card shadow rounded-lg p-6 transition-colors duration-200">
          {isLoading ? (
            <div className="flex justify-center py-8 dark:text-white">
              <Spin size="large" />
            </div>
          ) : notifications?.length > 0 ? (
            <div className="overflow-x-auto">
            <Table
              rowSelection={rowSelection}
              columns={columns}
              dataSource={notifications}
              rowKey="id"
              className="dark:text-white"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'],
                showTotal: (total) => <span className="dark:text-gray-300">Total {total} notifications</span>
              }}
              rowClassName={(record: Notification) => record.isRead ? 'dark:hover:bg-gray-700' : 'bg-blue-50 dark:bg-blue-900/30 dark:hover:bg-blue-900/40'}
              size="middle"
              bordered
              onRow={(record) => ({
                onClick: () => {
                  setDetailNotification(record);
                  setDrawerVisible(true);

                  // If notification is unread, mark it as read when viewed
                  if (!record.isRead) {
                    markAsReadMutation.mutate(record.id);
                  }
                },
                style: { cursor: 'pointer' }
              })}
            />
          </div>
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <span className="dark:text-gray-300">
                  No notifications yet
                </span>
              }
            />
          )}
        </div>

        <Modal
          title="Delete Notification"
          open={deleteModalVisible}
          onOk={() => selectedNotification && deleteMutation.mutate(selectedNotification.id)}
          onCancel={() => setDeleteModalVisible(false)}
          confirmLoading={deleteMutation.isPending}
        >
          <p className="dark:text-gray-300">Are you sure you want to delete this notification?</p>
          {selectedNotification && (
            <div className="mt-4 dark:text-gray-300">
              <p><strong>Title:</strong> {selectedNotification.title}</p>
              <p><strong>Type:</strong> {selectedNotification.type}</p>
              <p><strong>Date:</strong> {formatDate(selectedNotification.createdAt)}</p>
            </div>
          )}
        </Modal>

        {/* Notification Details Drawer */}
        <Drawer
          title={
            <div className="flex justify-between items-center">
              <span className="dark:text-white">Notification Details</span>
              {detailNotification && !detailNotification.isRead && (
                <Tag color="blue">Unread</Tag>
              )}
            </div>
          }
          className="dark:bg-dark-card"
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          extra={
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={() => setDrawerVisible(false)}
            />
          }
        >
          {detailNotification && (
            <div>
              <div className="mb-4">
                <Tag color={getTypeColor(detailNotification.type)}>
                  {detailNotification.type.toUpperCase()}
                </Tag>
                <Text type="secondary" className="ml-2 dark:text-gray-400">
                  {formatDate(detailNotification.createdAt)}
                </Text>
              </div>

              <Title level={4} className="dark:text-white">{detailNotification.title}</Title>

              <Divider className="dark:border-gray-700" />

              <Paragraph className="dark:text-gray-300">
                {detailNotification.message}
              </Paragraph>

              <Divider className="dark:border-gray-700" />

              <div className="flex justify-end mt-4">
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => {
                    setSelectedNotification(detailNotification);
                    setDeleteModalVisible(true);
                    setDrawerVisible(false);
                  }}
                >
                  Delete
                </Button>
              </div>
            </div>
          )}
        </Drawer>
      </div>
    </Layout>
  );
}