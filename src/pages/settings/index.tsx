import { useState } from 'react';
import { useSession } from 'next-auth/react';
import Layout from '@/components/Layout';
import { Tabs, Card, Typography, Divider, Button, Form, Input, Switch, Alert, Space, Modal } from 'antd';
import ThemeSettings from '@/components/ThemeSettings';
import { PlanSelectionModal } from '@/components/PlanSelectionModal';
import {
  SettingOutlined,
  UserOutlined,
  BellOutlined,
  LockOutlined,
  CreditCardOutlined,
  ExportOutlined,
  DeleteOutlined,
  WarningOutlined,
  GlobalOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

export default function SettingsPage() {
  const { data: session } = useSession({
    required: true,
  });

  const [activeTab, setActiveTab] = useState('appearance');
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');
  const [planModalVisible, setPlanModalVisible] = useState(false);
  const [trackingEnabled, setTrackingEnabled] = useState(false);

  const handleDeleteAccount = () => {
    // Implement account deletion logic here
    setDeleteModalVisible(false);
    setDeleteConfirmText('');
  };

  const handlePlanSelection = (planId: string) => {
    // Implement plan selection logic here
    console.log('Selected plan:', planId);
    // In a real implementation, this would call an API to update the subscription
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center mb-6">
          <SettingOutlined className="text-2xl mr-2 text-gray-700 dark:text-gray-300" />
          <Title level={2} className="m-0 dark:text-white">Settings</Title>
        </div>

        <Divider className="dark:border-gray-700" />

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="md:col-span-1">
            <Card className="dark:bg-dark-card dark:border-dark-border">
              <Tabs
                activeKey={activeTab}
                onChange={setActiveTab}
                tabPosition="left"
                className="settings-tabs"
              >
                <TabPane
                  tab={
                    <span className="flex items-center dark:text-gray-300">
                      <SettingOutlined className="mr-2" />
                      Appearance
                    </span>
                  }
                  key="appearance"
                />
                <TabPane
                  tab={
                    <span className="flex items-center dark:text-gray-300">
                      <UserOutlined className="mr-2" />
                      Account
                    </span>
                  }
                  key="account"
                />
                <TabPane
                  tab={
                    <span className="flex items-center dark:text-gray-300">
                      <CreditCardOutlined className="mr-2" />
                      Billing
                    </span>
                  }
                  key="billing"
                />
                <TabPane
                  tab={
                    <span className="flex items-center dark:text-gray-300">
                      <BellOutlined className="mr-2" />
                      Notifications
                    </span>
                  }
                  key="notifications"
                />
                <TabPane
                  tab={
                    <span className="flex items-center dark:text-gray-300">
                      <LockOutlined className="mr-2" />
                      Privacy & GDPR
                    </span>
                  }
                  key="privacy"
                />
                <TabPane
                  tab={
                    <span className="flex items-center dark:text-gray-300">
                      <GlobalOutlined className="mr-2" />
                      DNS Tracking
                    </span>
                  }
                  key="dns-tracking"
                />
              </Tabs>
            </Card>
          </div>

          <div className="md:col-span-3">
            {activeTab === 'appearance' && (
              <ThemeSettings />
            )}

            {activeTab === 'account' && (
              <div className="space-y-6">
                <Card className="dark:bg-dark-card dark:border-dark-border">
                  <Title level={4} className="dark:text-gray-200">Account Information</Title>
                  <Form layout="vertical" className="mt-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Form.Item label="Name" name="name" initialValue={session?.user?.name}>
                        <Input placeholder="Your name" className="dark:bg-dark-card dark:border-dark-border dark:text-white" />
                      </Form.Item>
                      <Form.Item label="Email" name="email" initialValue={session?.user?.email}>
                        <Input disabled placeholder="Your email" className="dark:bg-dark-card dark:border-dark-border dark:text-white" />
                      </Form.Item>
                    </div>
                    <Button type="primary">Save Changes</Button>
                  </Form>
                  <Divider className="dark:border-gray-700" />
                  <div className="flex items-center justify-between mt-4">
                    <div>
                      <Text strong className="dark:text-white">Email Tracking</Text>
                      <div className="text-gray-500 dark:text-gray-400">Enable or disable open and click tracking for your emails</div>
                    </div>
                    <Switch
                      checked={trackingEnabled}
                      onChange={setTrackingEnabled}
                      data-testid="tracking-toggle"
                    />
                  </div>
                </Card>

                <Card className="dark:bg-dark-card dark:border-dark-border">
                  <Title level={4} className="dark:text-gray-200 text-red-600">Danger Zone</Title>
                  <Alert
                    message="Warning: This action cannot be undone"
                    description="Deleting your account will permanently remove all your data, including campaigns, leads, and templates."
                    type="error"
                    showIcon
                    className="mb-4"
                  />
                  <Button
                    danger
                    type="primary"
                    icon={<DeleteOutlined />}
                    onClick={() => setDeleteModalVisible(true)}
                  >
                    Delete Account
                  </Button>
                </Card>
              </div>
            )}

            {activeTab === 'billing' && (
              <div className="space-y-6">
                <Card className="dark:bg-dark-card dark:border-dark-border">
                  <Title level={4} className="dark:text-gray-200">Current Plan</Title>
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg mb-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <Text strong className="text-lg dark:text-white">Free Plan</Text>
                        <div className="text-gray-500 dark:text-gray-400">Basic features for small teams</div>
                      </div>
                      <Button type="primary" onClick={() => setPlanModalVisible(true)}>Upgrade Plan</Button>
                    </div>
                  </div>
                  <Divider className="dark:border-gray-700" />
                  <Title level={5} className="dark:text-gray-200">Payment Methods</Title>
                  <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg mb-4">
                    <div className="flex items-center">
                      <CreditCardOutlined className="text-2xl mr-3 text-gray-500 dark:text-gray-400" />
                      <div>
                        <div className="font-medium dark:text-white">No payment method</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">Add a payment method to upgrade your plan</div>
                      </div>
                    </div>
                    <Button>Add Payment Method</Button>
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'notifications' && (
              <Card className="dark:bg-dark-card dark:border-dark-border">
                <Title level={4} className="dark:text-gray-200">Notification Settings</Title>
                <div className="space-y-4 mt-4">
                  <div className="flex justify-between items-center p-4 border-b dark:border-gray-700">
                    <div>
                      <Text strong className="dark:text-white">Email Notifications</Text>
                      <div className="text-gray-500 dark:text-gray-400">Receive email notifications about campaign activity</div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex justify-between items-center p-4 border-b dark:border-gray-700">
                    <div>
                      <Text strong className="dark:text-white">Campaign Completion</Text>
                      <div className="text-gray-500 dark:text-gray-400">Get notified when a campaign is completed</div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex justify-between items-center p-4 border-b dark:border-gray-700">
                    <div>
                      <Text strong className="dark:text-white">Lead Activity</Text>
                      <div className="text-gray-500 dark:text-gray-400">Get notified about important lead activities</div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex justify-between items-center p-4">
                    <div>
                      <Text strong className="dark:text-white">System Updates</Text>
                      <div className="text-gray-500 dark:text-gray-400">Receive notifications about system updates and maintenance</div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 'dns-tracking' && (
              <Card className="dark:bg-dark-card dark:border-dark-border">
                <Title level={4} className="dark:text-gray-200">DNS Tracking Configuration</Title>
                <Text className="dark:text-gray-400 block mb-4">
                  Configure custom domains for email tracking to improve deliverability and brand consistency.
                </Text>
                <Button type="primary" onClick={() => window.location.href = '/settings/dns-tracking'}>
                  Configure DNS Tracking
                </Button>
              </Card>
            )}

            {activeTab === 'privacy' && (
              <div className="space-y-6">
                <Card className="dark:bg-dark-card dark:border-dark-border">
                  <Title level={4} className="dark:text-gray-200">Privacy Settings</Title>
                  <div className="space-y-4 mt-4">
                    <div className="flex justify-between items-center p-4 border-b dark:border-gray-700">
                      <div>
                        <Text strong className="dark:text-white">Data Collection</Text>
                        <div className="text-gray-500 dark:text-gray-400">Allow us to collect usage data to improve your experience</div>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex justify-between items-center p-4">
                      <div>
                        <Text strong className="dark:text-white">Marketing Communications</Text>
                        <div className="text-gray-500 dark:text-gray-400">Receive marketing emails about new features and offers</div>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </Card>

                <Card className="dark:bg-dark-card dark:border-dark-border">
                  <Title level={4} className="dark:text-gray-200">GDPR Data</Title>
                  <Text className="dark:text-gray-400 block mb-4">
                    Under GDPR, you have the right to access and export your personal data.
                  </Text>
                  <Space>
                    <Button
                      icon={<ExportOutlined />}
                      onClick={async () => {
                        const response = await fetch('/api/user/export-data');
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'user-data-export.json';
                        a.click();
                      }}
                    >
                      Export My Data
                    </Button>
                  </Space>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Delete Account Confirmation Modal */}
      <Modal
        title={
          <div className="flex items-center text-red-600">
            <WarningOutlined className="mr-2" />
            Delete Account
          </div>
        }
        open={deleteModalVisible}
        onCancel={() => setDeleteModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setDeleteModalVisible(false)}>
            Cancel
          </Button>,
          <Button
            key="delete"
            danger
            type="primary"
            disabled={deleteConfirmText !== 'DELETE'}
            onClick={handleDeleteAccount}
          >
            Delete Account
          </Button>,
        ]}
      >
        <div className="space-y-4">
          <Alert
            message="This action cannot be undone"
            description="All your data will be permanently deleted. This includes campaigns, leads, templates, and all other account information."
            type="error"
            showIcon
          />
          <div>
            <Text>To confirm, type <Text strong>DELETE</Text> in the field below:</Text>
            <Input
              className="mt-2 dark:bg-dark-card dark:border-dark-border dark:text-white"
              value={deleteConfirmText}
              onChange={(e) => setDeleteConfirmText(e.target.value)}
              placeholder="Type DELETE to confirm"
            />
          </div>
        </div>
      </Modal>

      <PlanSelectionModal
        isOpen={planModalVisible}
        onClose={() => setPlanModalVisible(false)}
        currentPlanId="price_basic" // Replace with actual current plan ID from user data
        onSelectPlan={handlePlanSelection}
      />
    </Layout>
  );
}
