import { useEffect, useState } from 'react'
import { fetchCsrfToken } from '@/lib/api-client'
import { useSession, signOut } from 'next-auth/react'
import Layout from '@/components/Layout'
import { Container } from '@/components/Container'
import { Button } from '@/components/Button'
import { Switch } from '@/components/Switch'
import { Alert } from '@/components/Alert'
import { useRouter } from 'next/router'

export default function PrivacySettings() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [consents, setConsents] = useState({
    marketingEmails: false,
    analyticsTracking: false,
    thirdPartySharing: false
  })
  const [csrfToken, setCsrfToken] = useState('')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/settings/privacy')
    }
  }, [status, router])

  useEffect(() => {
    const initializePage = async () => {
      if (status !== 'authenticated') return

      try {
        // Get CSRF token
        const token = await fetchCsrfToken()
        setCsrfToken(token)

        // Fetch current consents
        const response = await fetch('/api/user/consents', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          throw new Error('Failed to fetch consents')
        }

        const data = await response.json()
        setConsents(data)
      } catch (err) {
        setError('Failed to load settings')
      }
    }

    initializePage()
  }, [status])

  if (status === 'loading') {
    return (
      <Layout>
        <Container>
          <div className="flex items-center justify-center min-h-screen">
            <div>Loading...</div>
          </div>
        </Container>
      </Layout>
    )
  }

  if (!session) {
    return null
  }

  const handleConsentChange = async (key: string, value: boolean) => {
    try {
      setLoading(true)
      const response = await fetch('/api/user/consents', {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken
        },
        body: JSON.stringify({ [key]: value })
      })

      if (!response.ok) throw new Error('Failed to update settings')

      setConsents(prev => ({ ...prev, [key]: value }))
      setSuccess('Settings updated successfully')
    } catch (err) {
      setError('Failed to update settings')
    } finally {
      setLoading(false)
    }
  }

  const handleExportData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/user/export-data', {
        credentials: 'include'
      })
      const blob = await response.blob()

      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'my-data-export.json'
      document.body.appendChild(a)
      a.click()
      a.remove()

      setSuccess('Your data has been exported')
    } catch (err) {
      setError('Failed to export data')
    } finally {
      setLoading(false)
    }
  }



  return (
    <Layout>
      <Container>
        <div className="max-w-2xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Privacy Settings</h1>

          {success && (
            <Alert type="success" onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}

          {error && (
            <Alert type="error" onClose={() => setError('')}>
              {error}
            </Alert>
          )}

          <div className="space-y-6">
            <section className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">Consent Preferences</h2>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Marketing Emails</h3>
                    <p className="text-sm text-gray-500">Receive updates about new features and promotions</p>
                  </div>
                  <Switch
                    checked={consents.marketingEmails}
                    onChange={(value) => handleConsentChange('marketingEmails', value)}
                    disabled={loading}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Analytics Tracking</h3>
                    <p className="text-sm text-gray-500">Allow us to collect usage data to improve our service</p>
                  </div>
                  <Switch
                    checked={consents.analyticsTracking}
                    onChange={(value) => handleConsentChange('analyticsTracking', value)}
                    disabled={loading}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Third-party Data Sharing</h3>
                    <p className="text-sm text-gray-500">Allow sharing of non-personal data with trusted partners</p>
                  </div>
                  <Switch
                    checked={consents.thirdPartySharing}
                    onChange={(value) => handleConsentChange('thirdPartySharing', value)}
                    disabled={loading}
                  />
                </div>
              </div>
            </section>

            <section className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">Your Data</h2>

              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Export Your Data</h3>
                  <p className="text-sm text-gray-500 mb-2">
                    Download a copy of your personal data
                  </p>
                  <Button
                    onClick={handleExportData}
                    disabled={loading}
                    variant="secondary"
                  >
                    {loading ? 'Exporting...' : 'Export Data'}
                  </Button>
                </div>


              </div>
            </section>
          </div>
        </div>
      </Container>
    </Layout>
  )
}
