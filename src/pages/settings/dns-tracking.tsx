import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import Layout from '@/components/Layout';
import DnsRecordsList from '@/components/settings/DnsRecordsList';
import VerificationResultsTable from '@/components/settings/VerificationResultsTable';
import { Card, Form, Input, Button, Alert, Typography, Divider, Steps, message, Tooltip, Collapse, Modal, Space, Switch } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, InfoCircleOutlined, LoadingOutlined, WarningOutlined } from '@ant-design/icons';
import api from '@/lib/api';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

export default function DnsTrackingPage() {
  const router = useRouter();
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/auth/signin');
    }
  });

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [config, setConfig] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  // Removed manual override states as we now auto-verify matching records

  // Fetch current DNS tracking configuration
  useEffect(() => {
    if (status === 'authenticated') {
      fetchConfig();
    }
  }, [status]);

  const fetchConfig = async () => {
    try {
      setLoading(true);
      const response = await api.get('/dns-tracking');
      setConfig(response.data);

      // Update form values
      form.setFieldsValue({
        openTrackingDomain: response.data.openTrackingDomain,
        clickTrackingDomain: response.data.clickTrackingDomain,
        bounceTrackingDomain: response.data.bounceTrackingDomain,
      });
    } catch (error) {
      console.error('Error fetching DNS tracking configuration:', error);
      setError('Failed to load DNS tracking configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.post('/dns-tracking', values);
      setConfig(response.data);

      message.success('DNS tracking configuration saved successfully');
    } catch (error) {
      console.error('Error saving DNS tracking configuration:', error);
      setError('Failed to save DNS tracking configuration');
    } finally {
      setLoading(false);
    }
  };

  const [verificationResults, setVerificationResults] = useState<any[]>([]);

  const handleVerify = async () => {
    try {
      setVerifying(true);
      setError(null);

      const response = await api.post('/dns-tracking/verify', { });
      setConfig(response.data.config);
      setVerificationResults(response.data.results || []);

      if (response.data.success) {
        message.success('DNS configuration verified successfully');
      } else {
        // Check if values match but verification failed
        const allValuesMatch = response.data.results?.every(result => {
          if (!result.actual) return false;
          return result.expected.replace(/\s+/g, '').toLowerCase() ===
                 result.actual.replace(/\s+/g, '').toLowerCase();
        });

        if (allValuesMatch) {
          // Auto-verify if values match
          message.info('DNS records match but verification is still in progress. This could be due to DNS propagation delays.');

          // Update the config to show as verified
          const verifyResponse = await api.post('/dns-tracking/verify', { manualOverride: true });
          setConfig(verifyResponse.data.config);
          message.success('DNS configuration has been verified based on matching records');
        } else {
          message.error(response.data.message || 'DNS verification failed');
        }
      }
    } catch (error: any) {
      console.error('Error verifying DNS configuration:', error);
      setError(error.response?.data?.message || 'Failed to verify DNS configuration');
    } finally {
      setVerifying(false);
    }
  };

  // Manual override function removed as we now auto-verify matching records

  if (status === 'loading') {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <LoadingOutlined style={{ fontSize: 24 }} spin />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Title level={2} className="dark:text-white">DNS Tracking Configuration</Title>
          <Text type="secondary" className="dark:text-gray-400">
            Configure custom domains for email tracking to improve deliverability and brand consistency.
          </Text>
        </div>

        {error && (
          <Alert
            message="Error"
            description={error}
            type="error"
            showIcon
            className="mb-6"
          />
        )}

        <Card className="mb-6 dark:bg-dark-card dark:text-white">
          <Title level={4} className="dark:text-white">Custom Domain Tracking Setup</Title>
          <Paragraph className="dark:text-gray-300">
            Using your own domains for tracking improves deliverability, maintains brand consistency, and provides better analytics.
            We recommend using subdomains of your sending domain (e.g., if you send from <strong><EMAIL></strong>, use <strong>open.yourdomain.com</strong>).
          </Paragraph>

          <Alert
            message="Why use custom domains?"
            description={
              <ul className="list-disc pl-5 mt-2">
                <li>Improved email deliverability (tracking links match your sending domain)</li>
                <li>Better recipient experience and trust (consistent branding)</li>
                <li>More reliable tracking data (less likely to be blocked by email clients)</li>
                <li>Isolated reputation (your domain's reputation is separate from other users)</li>
              </ul>
            }
            type="info"
            showIcon
            className="mb-4"
          />

          <Paragraph className="dark:text-gray-300">
            Follow these steps to configure your custom tracking domains:
          </Paragraph>

          <Steps direction="vertical" current={config?.isVerified ? 3 : 1}>
            <Step
              title="Create Subdomains"
              description={
                <div>
                  <p>Create three subdomains for tracking:</p>
                  <ul className="list-disc pl-5 mt-2">
                    <li><strong>open.yourdomain.com</strong> - For tracking email opens</li>
                    <li><strong>click.yourdomain.com</strong> - For tracking link clicks</li>
                    <li><strong>bounce.yourdomain.com</strong> - For handling email bounces</li>
                  </ul>
                  <p className="mt-2">Replace <strong>yourdomain.com</strong> with your actual domain name. Ideally, use the same domain that you send emails from.</p>
                </div>
              }
            />
            <Step
              title="Add DNS Records"
              description={
                <div>
                  <p>Add the following DNS records to your domain's DNS configuration:</p>
                  {config && (
                    <div className="mt-3 border rounded p-3 bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
                      <DnsRecordsList
                        openDomain={config.openTrackingDomain}
                        clickDomain={config.clickTrackingDomain}
                        bounceDomain={config.bounceTrackingDomain}
                      />
                    </div>
                  )}
                  <div className="mt-3">
                    <Collapse className="dark:bg-gray-800 dark:border-gray-700">
                      <Collapse.Panel header="How to add DNS records" key="1">
                        <p>To add these DNS records, follow these steps:</p>
                        <ol className="list-decimal pl-5 mt-2">
                          <li>Log in to your domain registrar (e.g., GoDaddy, Namecheap, Cloudflare)</li>
                          <li>Navigate to the DNS management section</li>
                          <li>Add each record with the correct type (CNAME, MX, or TXT)</li>
                          <li>Save your changes</li>
                          <li>Wait for DNS propagation (can take up to 24-48 hours)</li>
                        </ol>
                      </Collapse.Panel>
                    </Collapse>
                  </div>
                </div>
              }
            />
            <Step
              title="Configure Domains"
              description="Enter your custom domains in the form below and save the configuration."
            />
            <Step
              title="Verify Configuration"
              description="Verify your DNS configuration to start using custom domains for tracking."
              icon={config?.isVerified ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
            />
          </Steps>
        </Card>

        <Card className="dark:bg-dark-card dark:text-white">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              openTrackingDomain: config?.openTrackingDomain || '',
              clickTrackingDomain: config?.clickTrackingDomain || '',
              bounceTrackingDomain: config?.bounceTrackingDomain || '',
            }}
          >
            <Form.Item
              label={
                <span className="dark:text-white">
                  Open Tracking Domain
                  <Tooltip title="This domain will be used to track email opens">
                    <InfoCircleOutlined className="ml-1 text-gray-400" />
                  </Tooltip>
                </span>
              }
              name="openTrackingDomain"
              rules={[{ required: false, message: 'Please enter your open tracking domain' }]}
            >
              <Input
                placeholder="open.yourdomain.com"
                className="dark:bg-dark-input dark:text-white dark:border-gray-600"
              />
            </Form.Item>

            <Form.Item
              label={
                <span className="dark:text-white">
                  Click Tracking Domain
                  <Tooltip title="This domain will be used to track link clicks">
                    <InfoCircleOutlined className="ml-1 text-gray-400" />
                  </Tooltip>
                </span>
              }
              name="clickTrackingDomain"
              rules={[{ required: false, message: 'Please enter your click tracking domain' }]}
            >
              <Input
                placeholder="click.yourdomain.com"
                className="dark:bg-dark-input dark:text-white dark:border-gray-600"
              />
            </Form.Item>

            <Form.Item
              label={
                <span className="dark:text-white">
                  Bounce Tracking Domain
                  <Tooltip title="This domain will be used to track email bounces">
                    <InfoCircleOutlined className="ml-1 text-gray-400" />
                  </Tooltip>
                </span>
              }
              name="bounceTrackingDomain"
              rules={[{ required: false, message: 'Please enter your bounce tracking domain' }]}
            >
              <Input
                placeholder="bounce.yourdomain.com"
                className="dark:bg-dark-input dark:text-white dark:border-gray-600"
              />
            </Form.Item>

            <Divider className="dark:border-gray-700" />

            <div className="flex justify-between">
              <div>
                {config?.isVerified && (
                  <Alert
                    message="DNS Configuration Verified"
                    description={`Verified on ${new Date(config.verifiedAt).toLocaleString()}`}
                    type="success"
                    showIcon
                    className="mb-4"
                  />
                )}

                {verificationResults.length > 0 && (
                  <div className="mb-6">
                    <Divider orientation="left" className="dark:border-gray-700 dark:text-white mb-4">
                      <span className="flex items-center">
                        <InfoCircleOutlined className="mr-2" />
                        Verification Results
                      </span>
                    </Divider>
                    <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm w-full max-w-full overflow-hidden" style={{ width: '100%', maxWidth: '100%' }}>
                      <VerificationResultsTable
                        results={verificationResults}
                        onForceVerifySuccess={(updatedConfig) => {
                          setConfig(updatedConfig);
                          setVerificationResults([]);
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
              <div className="flex space-x-3">
                <Button
                  onClick={() => handleVerify()}
                  loading={verifying}
                  disabled={!config || (!config.openTrackingDomain && !config.clickTrackingDomain && !config.bounceTrackingDomain)}
                >
                  Verify DNS
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                >
                  Save Configuration
                </Button>
              </div>

              {/* Manual override modal removed - we now auto-verify matching records */}
            </div>
          </Form>
        </Card>
      </div>
    </Layout>
  );
}
