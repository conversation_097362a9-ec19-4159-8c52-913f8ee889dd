import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import Layout from '@/components/Layout'
import { Container } from '@/components/Container'
import { Button } from '@/components/Button'
import { Alert } from '@/components/Alert'
import { BillingPortal } from '@/components/BillingPortal'
import { PaymentMethods } from '@/components/PaymentMethods'
import { PlanSelectionModal } from '@/components/PlanSelectionModal'
import { PLANS } from '@/config/stripe'

export default function AccountSettings() {
  const router = useRouter()
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState('')
  const [error, setError] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deleteConfirmText, setDeleteConfirmText] = useState('')
  const [showPlanModal, setShowPlanModal] = useState(false)
  const [billingAddress, setBillingAddress] = useState({
    line1: '',
    line2: '',
    city: '',
    state: '',
    postalCode: '',
    country: '',
  })
  const [subscription, setSubscription] = useState({
    plan: '',
    status: '',
    currentPeriodEnd: null as Date | null,
  })

  useEffect(() => {
    if (session?.user) {
      // Fetch billing address
      fetch('/api/user/billing-address')
        .then(res => res.json())
        .then(data => {
          if (data && !data.error) {
            setBillingAddress(data)
          }
        })
        .catch(err => console.error('Failed to fetch billing address:', err))

      // Fetch subscription details
      fetch('/api/user/subscription')
        .then(res => res.json())
        .then(data => {
          if (data && !data.error) {
            setSubscription({
              plan: data.planId || '',
              status: data.status || '',
              currentPeriodEnd: data.currentPeriodEnd ? new Date(data.currentPeriodEnd) : null,
            })
          }
        })
        .catch(err => console.error('Failed to fetch subscription details:', err))
    }
  }, [session])

  const handleBillingAddressSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    try {
      const response = await fetch('/api/user/billing-address', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(billingAddress),
      })

      if (!response.ok) throw new Error('Failed to update billing address')

      setSuccess('Billing address updated successfully')
    } catch (err) {
      setError('Failed to update billing address')
    } finally {
      setLoading(false)
    }
  }

  const handleUpdatePlan = async (planId: string) => {
    setLoading(true)
    try {
      const response = await fetch('/api/subscription/update-plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ planId }),
      })

      if (!response.ok) throw new Error('Failed to update subscription plan')

      // Update the local state
      setSubscription(prev => ({
        ...prev,
        plan: planId,
      }))

      setSuccess('Subscription plan updated successfully')
    } catch (err) {
      setError('Failed to update subscription plan')
    } finally {
      setLoading(false)
    }
  }

  const handleExportData = async () => {
    try {
      const response = await fetch('/api/user/export-data')
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'user-data-export.json'
      a.click()
    } catch (err) {
      setError('Failed to export data')
    }
  }

  const handleDeleteAccount = async () => {
    if (deleteConfirmText !== 'DELETE') {
      setError('Please type DELETE to confirm account deletion')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/user/delete-account', {
        method: 'DELETE',
      })

      if (!response.ok) throw new Error('Failed to delete account')

      // Redirect to sign out
      router.push('/api/auth/signout')
    } catch (err) {
      setError('Failed to delete account')
      setLoading(false)
    }
  }

  if (!session) {
    return <div>Please sign in to access account settings.</div>
  }

  return (
    <Layout>
      <Container>
        <div className="max-w-3xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Account Settings</h1>

          {success && (
            <Alert type="success" onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}

          {error && (
            <Alert type="error" onClose={() => setError('')}>
              {error}
            </Alert>
          )}

          {/* Subscription Information */}
          <div className="bg-white shadow rounded-lg mb-8">
            <div className="px-4 py-5 sm:p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Subscription</h2>

              {subscription.plan ? (
                <div className="mb-4">
                  <div className="flex justify-between mb-2">
                    <span className="text-sm font-medium text-gray-500">Current Plan:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {Object.values(PLANS).find(p => p.id === subscription.plan)?.name || subscription.plan}
                    </span>
                  </div>

                  <div className="flex justify-between mb-2">
                    <span className="text-sm font-medium text-gray-500">Status:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {subscription.status}
                    </span>
                  </div>

                  {subscription.currentPeriodEnd && (
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium text-gray-500">Current Period Ends:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {subscription.currentPeriodEnd.toLocaleDateString()}
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-sm text-gray-500 mb-4">No active subscription found.</p>
              )}

              <div className="mt-4 flex space-x-4">
                <Button onClick={() => setShowPlanModal(true)}>
                  Change Plan
                </Button>
                <BillingPortal />
              </div>

              <PlanSelectionModal
                isOpen={showPlanModal}
                onClose={() => setShowPlanModal(false)}
                currentPlanId={subscription.plan}
                onSelectPlan={handleUpdatePlan}
              />
            </div>
          </div>

          {/* Payment Methods */}
          <div className="bg-white shadow rounded-lg mb-8">
            <PaymentMethods />
          </div>

          {/* Billing Address */}
          <div className="bg-white shadow rounded-lg mb-8">
            <div className="px-4 py-5 sm:p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Billing Address</h2>

              <form onSubmit={handleBillingAddressSubmit} className="space-y-4">
                <div>
                  <label htmlFor="line1" className="block text-sm font-medium text-gray-700">
                    Address Line 1
                  </label>
                  <input
                    type="text"
                    id="line1"
                    value={billingAddress.line1}
                    onChange={(e) => setBillingAddress(prev => ({ ...prev, line1: e.target.value }))}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="line2" className="block text-sm font-medium text-gray-700">
                    Address Line 2 (Optional)
                  </label>
                  <input
                    type="text"
                    id="line2"
                    value={billingAddress.line2}
                    onChange={(e) => setBillingAddress(prev => ({ ...prev, line2: e.target.value }))}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="city" className="block text-sm font-medium text-gray-700">
                      City
                    </label>
                    <input
                      type="text"
                      id="city"
                      value={billingAddress.city}
                      onChange={(e) => setBillingAddress(prev => ({ ...prev, city: e.target.value }))}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label htmlFor="state" className="block text-sm font-medium text-gray-700">
                      State / Province
                    </label>
                    <input
                      type="text"
                      id="state"
                      value={billingAddress.state}
                      onChange={(e) => setBillingAddress(prev => ({ ...prev, state: e.target.value }))}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="postalCode" className="block text-sm font-medium text-gray-700">
                      Postal Code
                    </label>
                    <input
                      type="text"
                      id="postalCode"
                      value={billingAddress.postalCode}
                      onChange={(e) => setBillingAddress(prev => ({ ...prev, postalCode: e.target.value }))}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label htmlFor="country" className="block text-sm font-medium text-gray-700">
                      Country
                    </label>
                    <input
                      type="text"
                      id="country"
                      value={billingAddress.country}
                      onChange={(e) => setBillingAddress(prev => ({ ...prev, country: e.target.value }))}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Saving...' : 'Save Address'}
                  </Button>
                </div>
              </form>
            </div>
          </div>

          {/* Data & Privacy */}
          <div className="bg-white shadow rounded-lg mb-8">
            <div className="px-4 py-5 sm:p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Data & Privacy</h2>

              <div className="space-y-4">
                <div>
                  <Button variant="outline" onClick={handleExportData}>
                    Export My Data
                  </Button>
                  <p className="mt-1 text-sm text-gray-500">
                    Download a copy of your personal data in JSON format.
                  </p>
                </div>

                <div>
                  <Button variant="outline" onClick={() => router.push('/settings/privacy')}>
                    Privacy Settings
                  </Button>
                  <p className="mt-1 text-sm text-gray-500">
                    Manage your privacy and consent settings.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Danger Zone */}
          <div className="bg-white shadow rounded-lg border border-red-200">
            <div className="px-4 py-5 sm:p-6">
              <h2 className="text-lg font-medium text-red-600 mb-4">Danger Zone</h2>

              {!showDeleteConfirm ? (
                <div>
                  <Button
                    variant="danger"
                    onClick={() => setShowDeleteConfirm(true)}
                  >
                    Delete Account
                  </Button>
                  <p className="mt-1 text-sm text-gray-500">
                    Permanently delete your account and all associated data. This action cannot be undone.
                  </p>
                </div>
              ) : (
                <div className="bg-red-50 p-4 rounded-md">
                  <h3 className="text-sm font-medium text-red-800 mb-2">Are you sure you want to delete your account?</h3>
                  <p className="text-sm text-red-700 mb-4">
                    This will permanently delete your account, all your data, campaigns, and email accounts.
                    This action cannot be undone.
                  </p>

                  <div className="mb-4">
                    <label htmlFor="confirmDelete" className="block text-sm font-medium text-red-700 mb-1">
                      Type DELETE to confirm
                    </label>
                    <input
                      type="text"
                      id="confirmDelete"
                      value={deleteConfirmText}
                      onChange={(e) => setDeleteConfirmText(e.target.value)}
                      className="block w-full rounded-md border-red-300 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm"
                    />
                  </div>

                  <div className="flex space-x-3">
                    <Button
                      variant="danger"
                      onClick={handleDeleteAccount}
                      disabled={loading || deleteConfirmText !== 'DELETE'}
                    >
                      {loading ? 'Deleting...' : 'Confirm Delete'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowDeleteConfirm(false);
                        setDeleteConfirmText('');
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </Container>
    </Layout>
  )
}