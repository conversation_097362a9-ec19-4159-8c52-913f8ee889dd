import 'next-auth';
import { Organization } from '@prisma/client';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role: string;
      organizations: {
        id: string;
        name: string;
        role: string;
        isPersonal: boolean;
      }[];
      currentOrganization?: Organization | null;
    }
  }

  interface User {
    role?: string;
  }
}
