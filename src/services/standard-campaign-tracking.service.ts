import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { addTrackingToEmail } from '@/lib/email-tracking';

/**
 * Service for handling tracking functionality for standard campaigns (single-email campaigns)
 */
export class StandardCampaignTrackingService {
  /**
   * Add tracking to an email for a standard campaign
   * @param html The HTML content of the email
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param baseUrl The base URL for tracking
   * @param trackingDomains Optional custom tracking domains
   * @returns The HTML with tracking added
   */
  static async addTracking(
    html: string,
    campaignId: string,
    leadId: string,
    baseUrl?: string,
    trackingDomains?: {
      openDomain?: string;
      clickDomain?: string;
      bounceDomain?: string;
      source?: 'custom' | 'sender' | 'default';
    }
  ): Promise<string> {
    try {
      // If no baseUrl is provided, get it from environment
      const trackingBaseUrl = baseUrl || process.env.NEXTAUTH_URL || 'http://localhost:3000';

      // Use the existing addTrackingToEmail function
      return addTrackingToEmail(html, campaignId, leadId, trackingBaseUrl, trackingDomains);
    } catch (error) {
      logger.error('[STANDARD_TRACKING] Error adding tracking to email', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId
      });
      return html; // Return original HTML if there's an error
    }
  }

  /**
   * Initialize tracking for a lead in a standard campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @returns Success status
   */
  static async initializeTracking(campaignId: string, leadId: string): Promise<boolean> {
    try {
      // Create or update the tracking record
      await prisma.standardCampaignTracking.upsert({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        },
        update: {}, // No updates needed if it already exists
        create: {
          campaignId,
          leadId,
          emailSent: false,
          emailOpened: false,
          linkClicked: false,
          replied: false,
          unsubscribed: false,
          bounced: false
        }
      });

      logger.info('[STANDARD_TRACKING] Initialized tracking for lead', {
        campaignId,
        leadId
      });

      return true;
    } catch (error) {
      logger.error('[STANDARD_TRACKING] Error initializing tracking', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId
      });
      return false;
    }
  }

  /**
   * Track an email being sent in a standard campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @returns Success status
   */
  static async trackEmailSent(campaignId: string, leadId: string): Promise<boolean> {
    try {
      // Update the tracking record
      await prisma.standardCampaignTracking.upsert({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        },
        update: {
          emailSent: true,
          sentAt: new Date()
        },
        create: {
          campaignId,
          leadId,
          emailSent: true,
          sentAt: new Date()
        }
      });

      // Update campaign metrics
      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          sentCount: {
            increment: 1
          }
        }
      });

      logger.info('[STANDARD_TRACKING] Email sent tracked', {
        campaignId,
        leadId,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[STANDARD_TRACKING] Error tracking email sent', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId
      });
      return false;
    }
  }

  /**
   * Track an email open in a standard campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param metadata Additional metadata about the open
   * @returns Success status
   */
  static async trackOpen(
    campaignId: string,
    leadId: string,
    metadata: {
      userAgent?: string;
      ip?: string;
      referrer?: string;
      timestamp?: string;
    }
  ): Promise<boolean> {
    try {
      // Check if this is the first open
      const tracking = await prisma.standardCampaignTracking.findUnique({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        }
      });

      const isFirstOpen = !tracking?.emailOpened;

      // Update the tracking record
      await prisma.standardCampaignTracking.upsert({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        },
        update: {
          emailOpened: true,
          openedAt: tracking?.openedAt || new Date(), // Keep the first open time if already opened
          metadata: {
            ...tracking?.metadata as any || {},
            lastOpenUserAgent: metadata.userAgent,
            lastOpenIp: metadata.ip,
            lastOpenReferrer: metadata.referrer,
            lastOpenTimestamp: metadata.timestamp || new Date().toISOString(),
            openCount: ((tracking?.metadata as any)?.openCount || 0) + 1
          }
        },
        create: {
          campaignId,
          leadId,
          emailSent: true, // Assume it was sent if we're tracking an open
          emailOpened: true,
          openedAt: new Date(),
          metadata: {
            lastOpenUserAgent: metadata.userAgent,
            lastOpenIp: metadata.ip,
            lastOpenReferrer: metadata.referrer,
            lastOpenTimestamp: metadata.timestamp || new Date().toISOString(),
            openCount: 1
          }
        }
      });

      // Record the open event for analytics
      await prisma.emailOpenEvent.create({
        data: {
          campaignId,
          leadId,
          campaignType: 'standard',
          userAgent: metadata.userAgent || '',
          ipAddress: metadata.ip || '',
          metadata: {
            referrer: metadata.referrer || '',
            timestamp: metadata.timestamp || new Date().toISOString()
          }
        }
      });

      // Update campaign metrics only for first opens
      if (isFirstOpen) {
        await prisma.campaign.update({
          where: { id: campaignId },
          data: {
            openedCount: {
              increment: 1
            }
          }
        });
      }

      logger.info('[STANDARD_TRACKING] Email open tracked', {
        campaignId,
        leadId,
        isFirstOpen,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[STANDARD_TRACKING] Error tracking email open', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId
      });
      return false;
    }
  }

  /**
   * Track a link click in a standard campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param url The URL that was clicked
   * @param metadata Additional metadata about the click
   * @returns Success status
   */
  static async trackClick(
    campaignId: string,
    leadId: string,
    url: string,
    metadata: {
      userAgent?: string;
      ip?: string;
      referrer?: string;
      timestamp?: string;
    }
  ): Promise<boolean> {
    try {
      // Check if this is the first click
      const tracking = await prisma.standardCampaignTracking.findUnique({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        }
      });

      const isFirstClick = !tracking?.linkClicked;

      // Update the tracking record
      await prisma.standardCampaignTracking.upsert({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        },
        update: {
          linkClicked: true,
          clickedAt: tracking?.clickedAt || new Date(), // Keep the first click time if already clicked
          lastUrl: url,
          metadata: {
            ...tracking?.metadata as any || {},
            lastClickUserAgent: metadata.userAgent,
            lastClickIp: metadata.ip,
            lastClickReferrer: metadata.referrer,
            lastClickTimestamp: metadata.timestamp || new Date().toISOString(),
            clickCount: ((tracking?.metadata as any)?.clickCount || 0) + 1,
            clickedUrls: [...((tracking?.metadata as any)?.clickedUrls || []), url]
          }
        },
        create: {
          campaignId,
          leadId,
          emailSent: true, // Assume it was sent if we're tracking a click
          emailOpened: true, // Assume it was opened if it was clicked
          linkClicked: true,
          openedAt: new Date(), // Set open time to now if this is the first interaction
          clickedAt: new Date(),
          lastUrl: url,
          metadata: {
            lastClickUserAgent: metadata.userAgent,
            lastClickIp: metadata.ip,
            lastClickReferrer: metadata.referrer,
            lastClickTimestamp: metadata.timestamp || new Date().toISOString(),
            clickCount: 1,
            clickedUrls: [url]
          }
        }
      });

      // Record the click event for analytics
      await prisma.linkClickEvent.create({
        data: {
          campaignId,
          leadId,
          campaignType: 'standard',
          url,
          userAgent: metadata.userAgent || '',
          ipAddress: metadata.ip || '',
          metadata: {
            referrer: metadata.referrer || '',
            timestamp: metadata.timestamp || new Date().toISOString()
          }
        }
      });

      // Update campaign metrics only for first clicks
      if (isFirstClick) {
        await prisma.campaign.update({
          where: { id: campaignId },
          data: {
            clickedCount: {
              increment: 1
            }
          }
        });
      }

      logger.info('[STANDARD_TRACKING] Link click tracked', {
        campaignId,
        leadId,
        url,
        isFirstClick,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[STANDARD_TRACKING] Error tracking link click', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        url
      });
      return false;
    }
  }

  /**
   * Track an unsubscribe event in a standard campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param metadata Additional metadata about the unsubscribe
   * @returns Success status
   */
  static async trackUnsubscribe(
    campaignId: string,
    leadId: string,
    metadata: {
      userAgent?: string;
      ip?: string;
      timestamp?: string;
      reason?: string;
    }
  ): Promise<boolean> {
    try {
      // Update the tracking record
      await prisma.standardCampaignTracking.upsert({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        },
        update: {
          unsubscribed: true,
          unsubscribedAt: new Date(),
          metadata: {
            ...(await prisma.standardCampaignTracking.findUnique({
              where: { campaignId_leadId: { campaignId, leadId } }
            }))?.metadata as any || {},
            unsubscribeUserAgent: metadata.userAgent,
            unsubscribeIp: metadata.ip,
            unsubscribeTimestamp: metadata.timestamp || new Date().toISOString(),
            unsubscribeReason: metadata.reason
          }
        },
        create: {
          campaignId,
          leadId,
          emailSent: true, // Assume it was sent if we're tracking an unsubscribe
          unsubscribed: true,
          unsubscribedAt: new Date(),
          metadata: {
            unsubscribeUserAgent: metadata.userAgent,
            unsubscribeIp: metadata.ip,
            unsubscribeTimestamp: metadata.timestamp || new Date().toISOString(),
            unsubscribeReason: metadata.reason
          }
        }
      });

      // Update the lead status to unsubscribed
      await prisma.lead.update({
        where: { id: leadId },
        data: { status: 'unsubscribed' }
      });

      logger.info('[STANDARD_TRACKING] Unsubscribe tracked', {
        campaignId,
        leadId,
        reason: metadata.reason,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[STANDARD_TRACKING] Error tracking unsubscribe', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId
      });
      return false;
    }
  }

  /**
   * Track a bounce event in a standard campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param bounceType The type of bounce (hard or soft)
   * @param bounceReason The reason for the bounce
   * @returns Success status
   */
  static async trackBounce(
    campaignId: string,
    leadId: string,
    bounceType: 'hard' | 'soft',
    bounceReason?: string
  ): Promise<boolean> {
    try {
      // Get the lead email
      const lead = await prisma.lead.findUnique({
        where: { id: leadId },
        select: { email: true }
      });

      if (!lead) {
        logger.warn('[STANDARD_TRACKING] Lead not found for bounce tracking', { campaignId, leadId });
        return false;
      }

      // Update the tracking record
      await prisma.standardCampaignTracking.upsert({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        },
        update: {
          bounced: true,
          bouncedAt: new Date(),
          metadata: {
            ...(await prisma.standardCampaignTracking.findUnique({
              where: { campaignId_leadId: { campaignId, leadId } }
            }))?.metadata as any || {},
            bounceType,
            bounceReason,
            bounceTimestamp: new Date().toISOString()
          }
        },
        create: {
          campaignId,
          leadId,
          emailSent: true, // Assume it was sent if we're tracking a bounce
          bounced: true,
          bouncedAt: new Date(),
          metadata: {
            bounceType,
            bounceReason,
            bounceTimestamp: new Date().toISOString()
          }
        }
      });

      // Update campaign metrics
      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          errorCount: {
            increment: 1
          }
        }
      });

      // Add to suppression list for hard bounces
      if (bounceType === 'hard' && lead.email) {
        await prisma.suppressionList.upsert({
          where: { email: lead.email },
          update: {
            reason: `Bounce: ${bounceReason || bounceType || 'Unknown'}`
          },
          create: {
            email: lead.email,
            reason: `Bounce: ${bounceReason || bounceType || 'Unknown'}`
          }
        });
      }

      logger.info('[STANDARD_TRACKING] Bounce tracked', {
        campaignId,
        leadId,
        email: lead.email,
        bounceType,
        bounceReason,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[STANDARD_TRACKING] Error tracking bounce', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        bounceType,
        bounceReason
      });
      return false;
    }
  }

  /**
   * Track a reply event in a standard campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param messageId The message ID of the reply
   * @param replyContent The content of the reply
   * @returns Success status
   */
  static async trackReply(
    campaignId: string,
    leadId: string,
    messageId: string,
    replyContent: {
      subject?: string;
      text?: string;
      html?: string;
      from?: string;
      inReplyTo?: string;
    }
  ): Promise<boolean> {
    try {
      // Get the lead email
      const lead = await prisma.lead.findUnique({
        where: { id: leadId },
        select: { email: true }
      });

      if (!lead) {
        logger.warn('[STANDARD_TRACKING] Lead not found for reply tracking', { campaignId, leadId });
        return false;
      }

      // Update the tracking record
      await prisma.standardCampaignTracking.upsert({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        },
        update: {
          replied: true,
          repliedAt: new Date(),
          metadata: {
            ...(await prisma.standardCampaignTracking.findUnique({
              where: { campaignId_leadId: { campaignId, leadId } }
            }))?.metadata as any || {},
            replyMessageId: messageId,
            replySubject: replyContent.subject,
            replyFrom: replyContent.from,
            replyTimestamp: new Date().toISOString(),
            replyCount: ((await prisma.standardCampaignTracking.findUnique({
              where: { campaignId_leadId: { campaignId, leadId } }
            }))?.metadata as any)?.replyCount ? ((await prisma.standardCampaignTracking.findUnique({
              where: { campaignId_leadId: { campaignId, leadId } }
            }))?.metadata as any)?.replyCount + 1 : 1
          }
        },
        create: {
          campaignId,
          leadId,
          emailSent: true, // Assume it was sent if we're tracking a reply
          replied: true,
          repliedAt: new Date(),
          metadata: {
            replyMessageId: messageId,
            replySubject: replyContent.subject,
            replyFrom: replyContent.from,
            replyTimestamp: new Date().toISOString(),
            replyCount: 1
          }
        }
      });

      // Update campaign metrics
      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          repliedCount: {
            increment: 1
          }
        }
      });

      logger.info('[STANDARD_TRACKING] Reply tracked', {
        campaignId,
        leadId,
        email: lead.email,
        messageId,
        subject: replyContent.subject,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[STANDARD_TRACKING] Error tracking reply', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        messageId
      });
      return false;
    }
  }
}
