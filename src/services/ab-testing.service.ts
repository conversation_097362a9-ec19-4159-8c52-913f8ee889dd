import { prisma } from '@/lib/prisma';

export class ABTestingService {
  static async createTest(data: {
    name: string;
    campaignId: string;
    variants: Array<{
      templateId: string;
      weight: number;
    }>;
  }) {
    return prisma.aBTest.create({
      data: {
        name: data.name,
        campaignId: data.campaignId,
        variants: {
          create: data.variants.map((variant, index) => ({
            name: `Variant ${String.fromCharCode(65 + index)}`, // A, B, C, etc.
            templateId: variant.templateId,
            weight: variant.weight,
          })),
        },
      },
    });
  }

  static async getTestResults(testId: string) {
    const variants = await prisma.aBTestVariant.findMany({
      where: { testId },
      // No related models to include
    });

    return variants.map(variant => ({
      id: variant.id,
      name: variant.name,
      templateId: variant.templateId,
      opens: variant.opens,
      clicks: variant.clicks,
      conversions: variant.conversions,
      bounces: variant.bounces,
    }));
  }
}