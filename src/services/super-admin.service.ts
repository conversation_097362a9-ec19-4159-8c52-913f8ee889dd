import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

/**
 * Super Admin Service - Handles secure admin operations
 * This is for application owners only, not regular organization users
 */
export class SuperAdminService {
  private static readonly ADMIN_SECRET = process.env.SUPER_ADMIN_SECRET || 'super-secret-admin-key-change-in-production';
  private static readonly JWT_SECRET = process.env.SUPER_ADMIN_JWT_SECRET || 'super-admin-jwt-secret-change-in-production';
  private static readonly ALLOWED_IPS = process.env.SUPER_ADMIN_ALLOWED_IPS?.split(',') || [];

  /**
   * Authenticate super admin with email and password
   */
  static async authenticateAdmin(email: string, password: string, clientIP?: string): Promise<{ token: string; admin: any } | null> {
    try {
      // IP whitelist check (if configured)
      if (this.ALLOWED_IPS.length > 0 && clientIP && !this.ALLOWED_IPS.includes(clientIP)) {
        logger.warn('Super admin access denied - IP not whitelisted', {
          email,
          clientIP,
          allowedIPs: this.ALLOWED_IPS
        });
        return null;
      }

      // Find admin user
      const admin = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          name: true,
          password: true,
          isAdmin: true
        }
      });

      if (!admin || !admin.isAdmin) {
        logger.warn('Super admin login attempt with invalid credentials', { email, clientIP });
        return null;
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, admin.password);
      if (!isValidPassword) {
        logger.warn('Super admin login attempt with wrong password', { email, clientIP });
        return null;
      }

      // Generate JWT token
      const token = jwt.sign(
        {
          adminId: admin.id,
          email: admin.email,
          role: 'super_admin',
          iat: Date.now()
        },
        this.JWT_SECRET,
        { expiresIn: '24h' }
      );

      logger.info('Super admin authenticated successfully', {
        adminId: admin.id,
        email: admin.email,
        clientIP
      });

      return {
        token,
        admin: {
          id: admin.id,
          email: admin.email,
          name: admin.name,
          role: 'super_admin'
        }
      };
    } catch (error) {
      logger.error('Error authenticating super admin', {
        error: error instanceof Error ? error.message : 'Unknown error',
        email,
        clientIP
      });
      return null;
    }
  }

  /**
   * Verify JWT token and return admin info
   */
  static async verifyAdminToken(token: string): Promise<any | null> {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as any;

      // Verify admin still exists and is active
      const admin = await prisma.user.findUnique({
        where: { id: decoded.adminId },
        select: {
          id: true,
          email: true,
          name: true,
          isAdmin: true
        }
      });

      if (!admin || !admin.isAdmin) {
        return null;
      }

      return {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: 'super_admin'
      };
    } catch (error) {
      logger.warn('Invalid super admin token', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return null;
    }
  }

  /**
   * Create the first super admin user
   */
  static async createFirstSuperAdmin(email: string, password: string, name?: string): Promise<boolean> {
    try {
      // Check if any super admin already exists
      try {
        const existingAdmin = await prisma.user.findFirst({
          where: { isAdmin: true }
        });

        if (existingAdmin) {
          logger.warn('Attempt to create super admin when one already exists', { email });
          return false;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        if (errorMessage.includes('column') && errorMessage.includes('isAdmin') && errorMessage.includes('does not exist')) {
          // Column doesn't exist, we'll try to create the user anyway and add the column
          logger.warn('isAdmin column missing, will attempt to add it during user creation');
        } else {
          throw error;
        }
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      try {
        // Try to create super admin user with isAdmin column
        const admin = await prisma.user.create({
          data: {
            email,
            password: hashedPassword,
            name: name || 'Super Admin',
            isAdmin: true,
            emailVerified: new Date() // Auto-verify super admin
          }
        });

        logger.info('First super admin created successfully', {
          adminId: admin.id,
          email: admin.email
        });

        return true;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';

        if (errorMessage.includes('column') && errorMessage.includes('isAdmin') && errorMessage.includes('does not exist')) {
          // Try to add the column first
          try {
            await prisma.$executeRaw`ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "isAdmin" BOOLEAN DEFAULT false;`;

            // Now try to create the user again
            const admin = await prisma.user.create({
              data: {
                email,
                password: hashedPassword,
                name: name || 'Super Admin',
                isAdmin: true,
                emailVerified: new Date()
              }
            });

            logger.info('First super admin created successfully after adding column', {
              adminId: admin.id,
              email: admin.email
            });

            return true;
          } catch (alterError) {
            logger.error('Failed to add isAdmin column or create user', {
              error: alterError instanceof Error ? alterError.message : 'Unknown error'
            });
            throw alterError;
          }
        } else {
          throw error;
        }
      }
    } catch (error) {
      logger.error('Error creating first super admin', {
        error: error instanceof Error ? error.message : 'Unknown error',
        email
      });
      return false;
    }
  }

  /**
   * Check if any super admin exists
   */
  static async hasSuperAdmin(): Promise<boolean> {
    try {
      // First check if the isAdmin column exists
      const count = await prisma.user.count({
        where: { isAdmin: true }
      });
      return count > 0;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // If the column doesn't exist, return false (no super admin exists)
      if (errorMessage.includes('column') && errorMessage.includes('isAdmin') && errorMessage.includes('does not exist')) {
        logger.warn('isAdmin column does not exist in database - database migration required');
        return false;
      }

      logger.error('Error checking super admin existence', {
        error: errorMessage
      });
      return false;
    }
  }

  /**
   * Get all super admins (for management)
   */
  static async getAllSuperAdmins(): Promise<Array<{ id: string; email: string; name: string | null; createdAt: Date }>> {
    try {
      const admins = await prisma.user.findMany({
        where: { isAdmin: true },
        select: {
          id: true,
          email: true,
          name: true,
          createdAt: true
        },
        orderBy: { createdAt: 'asc' }
      });

      return admins;
    } catch (error) {
      logger.error('Error getting super admins', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }

  /**
   * Create additional super admin (only by existing super admin)
   */
  static async createSuperAdmin(email: string, password: string, name: string, createdByAdminId: string): Promise<boolean> {
    try {
      // Verify the creating admin is a super admin
      const creatingAdmin = await prisma.user.findUnique({
        where: { id: createdByAdminId },
        select: { isAdmin: true }
      });

      if (!creatingAdmin?.isAdmin) {
        logger.warn('Non-super admin attempted to create super admin', {
          createdByAdminId,
          targetEmail: email
        });
        return false;
      }

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        logger.warn('Attempt to create super admin with existing email', { email });
        return false;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create super admin user
      const admin = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          name,
          isAdmin: true,
          emailVerified: new Date()
        }
      });

      logger.info('Super admin created successfully', {
        adminId: admin.id,
        email: admin.email,
        createdBy: createdByAdminId
      });

      return true;
    } catch (error) {
      logger.error('Error creating super admin', {
        error: error instanceof Error ? error.message : 'Unknown error',
        email,
        createdByAdminId
      });
      return false;
    }
  }

  /**
   * Remove super admin (cannot remove yourself)
   */
  static async removeSuperAdmin(targetAdminId: string, removedByAdminId: string): Promise<boolean> {
    try {
      // Cannot remove yourself
      if (targetAdminId === removedByAdminId) {
        logger.warn('Super admin attempted to remove themselves', { adminId: targetAdminId });
        return false;
      }

      // Verify the removing admin is a super admin
      const removingAdmin = await prisma.user.findUnique({
        where: { id: removedByAdminId },
        select: { isAdmin: true }
      });

      if (!removingAdmin?.isAdmin) {
        logger.warn('Non-super admin attempted to remove super admin', {
          removedByAdminId,
          targetAdminId
        });
        return false;
      }

      // Remove super admin privileges
      await prisma.user.update({
        where: { id: targetAdminId },
        data: { isAdmin: false }
      });

      logger.info('Super admin removed successfully', {
        targetAdminId,
        removedBy: removedByAdminId
      });

      return true;
    } catch (error) {
      logger.error('Error removing super admin', {
        error: error instanceof Error ? error.message : 'Unknown error',
        targetAdminId,
        removedByAdminId
      });
      return false;
    }
  }

  /**
   * Validate admin secret for initial setup
   */
  static validateAdminSecret(secret: string): boolean {
    return secret === this.ADMIN_SECRET;
  }
}
