import { logger } from '@/lib/logger';
import axios from 'axios';
import { PromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { RunnableSequence } from '@langchain/core/runnables';
import * as cheerio from 'cheerio';
import * as puppeteer from 'puppeteer';
import { AIProviderService } from './ai-provider.service';

/**
 * Service for web search and research capabilities
 */
export class WebSearchService {
  /**
   * Initialize the LLM model using the AI provider service
   */
  private static getLLM() {
    try {
      const providerInfo = AIProviderService.getProviderInfo();

      logger.info('Initializing LLM for web search', {
        provider: providerInfo.provider,
        chatModel: providerInfo.chatModel,
        baseUrl: providerInfo.baseUrl,
      });

      return AIProviderService.getChatModel();
    } catch (error) {
      logger.error('Error initializing LLM for web search', {
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: AIProviderService.getProvider(),
      });
      throw error;
    }
  }

  /**
   * Scrape a website using Puppeteer
   * @param url The URL to scrape
   * @returns The scraped content
   */
  private static async scrapeWebsite(url: string): Promise<string> {
    let browser: puppeteer.Browser | undefined;
    try {
      logger.info(`Scraping website with Puppeteer: ${url}`);

      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

      // Wait for content to load
      await page.waitForSelector('body', { timeout: 5000 });

      const content = await page.content();
      const $ = cheerio.load(content);

      // Remove scripts, styles, and other non-content elements
      $('script, style, meta, link, noscript').remove();

      return $('body').text();
    } catch (error) {
      logger.error(`Error scraping website with Puppeteer ${url}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return '';
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Perform a web search
   * @param query Search query
   * @param numResults Number of results to return
   * @returns Search results
   */
  static async webSearch(query: string, numResults: number = 5): Promise<any[]> {
    try {
      logger.info(`Performing web search for: ${query}`);

      // Use Google Search API or another search API
      // For this implementation, we'll use the Serper API (you'll need to add SERPER_API_KEY to your .env)
      const response = await axios.post(
        'https://google.serper.dev/search',
        {
          q: query,
          num: numResults,
        },
        {
          headers: {
            'X-API-KEY': process.env.SERPER_API_KEY || '',
            'Content-Type': 'application/json',
          },
        }
      );

      // Extract organic search results
      const results = response.data.organic || [];

      return results.map((result: any) => ({
        title: result.title,
        link: result.link,
        snippet: result.snippet,
        position: result.position,
      }));
    } catch (error) {
      logger.error(`Error performing web search for "${query}"`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return [];
    }
  }

  /**
   * Research a topic using web search and summarize the results
   * @param topic Topic to research
   * @returns Summarized research
   */
  static async researchTopic(topic: string): Promise<string> {
    try {
      logger.info(`Researching topic: ${topic}`);

      // Perform web search
      const searchResults = await this.webSearch(topic, 5);

      if (searchResults.length === 0) {
        return `No search results found for "${topic}".`;
      }

      // Scrape content from top results
      const contentPromises = searchResults.slice(0, 3).map(async (result) => {
        try {
          const content = await this.scrapeWebsite(result.link);
          return {
            title: result.title,
            link: result.link,
            content: content.substring(0, 3000), // Limit content length
          };
        } catch (error) {
          logger.error(`Error scraping content from ${result.link}`, {
            error: error instanceof Error ? error.message : 'Unknown error',
          });
          return {
            title: result.title,
            link: result.link,
            content: result.snippet || '',
          };
        }
      });

      const scrapedContents = await Promise.all(contentPromises);

      // Use LLM to summarize the research
      const llm = this.getLLM();

      const prompt = PromptTemplate.fromTemplate(`
        You are a research assistant. Summarize the following information about "${topic}".

        Search results:
        ${searchResults.map((result, index) => `${index + 1}. ${result.title} - ${result.link}\n${result.snippet}`).join('\n\n')}

        Scraped content:
        ${scrapedContents.map((content, index) => `${index + 1}. ${content.title} - ${content.link}\n${content.content.substring(0, 1000)}`).join('\n\n')}

        Provide a comprehensive summary of the topic based on these sources. Include key facts, different perspectives, and any important details.
        Format your response in markdown with sections and bullet points where appropriate.
        Include a "Sources" section at the end listing the URLs used.
      `);

      const chain = RunnableSequence.from([
        prompt,
        llm,
        new StringOutputParser(),
      ]);

      const summary = await chain.invoke({});

      return summary;
    } catch (error) {
      logger.error(`Error researching topic "${topic}"`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return `Error researching topic "${topic}": ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}
