import { prisma } from '@/lib/prisma';
import { EmailTemplate } from '@prisma/client';

interface CreateTemplateData {
  name: string;
  subject: string;
  content: string;
  organizationId: string;
}

export class TemplateService {
  static async listTemplates(organizationId: string) {
    return prisma.emailTemplate.findMany({
      where: { organizationId },
      orderBy: { updatedAt: 'desc' },
    });
  }

  static async getTemplate(id: string) {
    return prisma.emailTemplate.findUnique({
      where: { id },
    });
  }

  static async createTemplate(data: CreateTemplateData) {
    return prisma.emailTemplate.create({
      data,
    });
  }

  static async updateTemplate(id: string, data: Partial<EmailTemplate>) {
    return prisma.emailTemplate.update({
      where: { id },
      data,
    });
  }

  static async deleteTemplate(id: string) {
    return prisma.emailTemplate.delete({
      where: { id },
    });
  }
}