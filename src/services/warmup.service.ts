import { prisma } from '@/lib/prisma';
import { EmailService } from './email.service';
import { Queue, Worker } from 'bullmq';
import { logger } from '@/lib/logger';

interface WarmupJob {
  emailAccountId: string;
  day: number;
  targetEmails: string[];
}

interface WarmupConfig {
  dailyIncrement: number;
  maxDailyEmails: number;
  duration: number;
  includeWeekends: boolean;
}

export class WarmupService {
  // Parse Redis URL to get connection options
  static getRedisConnection() {
    const redisUrl = process.env.REDIS_URL ? new URL(process.env.REDIS_URL) : null;

    const connectionOptions = redisUrl ? {
      host: redisUrl.hostname,
      port: parseInt(redisUrl.port),
      username: redisUrl.username || undefined,
      password: redisUrl.password || undefined,
      // Always enable TLS for cloud Redis
      tls: {},
      // BullMQ requires maxRetriesPerRequest to be null
      maxRetriesPerRequest: null
    } : {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      // BullMQ requires maxRetriesPerRequest to be null
      maxRetriesPerRequest: null
    };

    logger.info('Warmup service Redis connection options configured for BullMQ', {
      host: connectionOptions.host,
      port: connectionOptions.port,
      tls: !!connectionOptions.tls,
      maxRetriesPerRequest: connectionOptions.maxRetriesPerRequest
    });

    return connectionOptions;
  }

  static warmupQueue = new Queue<WarmupJob>('warmup-queue', {
    connection: WarmupService.getRedisConnection()
  });

  static async startWarmup(emailAccountId: string, config?: WarmupConfig) {
    const warmupPlan = await this.generateWarmupPlan(emailAccountId, config);

    for (const day of warmupPlan) {
      await this.warmupQueue.add(
        'warmup-email',
        {
          emailAccountId,
          day: day.day,
          targetEmails: day.targetEmails,
        },
        {
          delay: day.day * 24 * 60 * 60 * 1000, // Convert days to milliseconds
        }
      );
    }

    await prisma.emailAccount.update({
      where: { id: emailAccountId },
      data: { warmupStatus: 'IN_PROGRESS' },
    });
  }

  private static async generateWarmupPlan(emailAccountId: string, config?: WarmupConfig) {
    // Default config if not provided
    const {
      dailyIncrement = 5,
      maxDailyEmails = 40,
      duration = 30,
      includeWeekends = true
    } = config || {};

    // Example warmup plan
    const warmupPlan = [];
    const baseEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      // Add more warm-up email addresses as needed
    ];

    // Generate enough email addresses to cover the max daily emails
    while (baseEmails.length < maxDailyEmails) {
      baseEmails.push(`warmup${baseEmails.length + 1}@example.com`);
    }

    for (let day = 1; day <= duration; day++) {
      // Skip weekends if configured
      if (!includeWeekends) {
        const date = new Date();
        date.setDate(date.getDate() + day);
        const dayOfWeek = date.getDay();
        if (dayOfWeek === 0 || dayOfWeek === 6) { // 0 = Sunday, 6 = Saturday
          continue;
        }
      }

      const emailCount = Math.min(day * dailyIncrement, maxDailyEmails);
      const targetEmails = baseEmails.slice(0, emailCount);

      warmupPlan.push({
        day,
        targetEmails,
        volume: emailCount,
      });
    }

    return warmupPlan;
  }

  static async processWarmupEmail(job: WarmupJob) {
    const { emailAccountId, targetEmails } = job;

    const emailAccount = await prisma.emailAccount.findUnique({
      where: { id: emailAccountId },
    });

    if (!emailAccount) {
      throw new Error('Email account not found');
    }

    for (const targetEmail of targetEmails) {
      try {
        await EmailService.sendEmail({
          emailAccountId,
          to: targetEmail,
          subject: 'Warm-up Test Email',
          html: this.generateWarmupEmailContent()
        });

        await prisma.warmupActivity.create({
          data: {
            emailAccountId,
            targetEmail,
            status: 'SUCCESS',
          },
        });
      } catch (error) {
        await prisma.warmupActivity.create({
          data: {
            emailAccountId,
            targetEmail,
            status: 'FAILED',
            error: error.message,
          },
        });
      }
    }
  }

  private static generateWarmupEmailContent() {
    const templates = [
      'Hi there, Just checking in!',
      'Hello, Hope you\'re having a great day!',
      'Quick update from our team',
      // Add more templates
    ];

    return templates[Math.floor(Math.random() * templates.length)];
  }
}

// Create warm-up worker
const warmupWorker = new Worker<WarmupJob>(
  'warmup-queue',
  async (job) => {
    await WarmupService.processWarmupEmail(job.data);
  },
  {
    connection: WarmupService.getRedisConnection()
  }
);

warmupWorker.on('completed', (job) => {
  console.log(`Warm-up job ${job.id} completed`);
});

warmupWorker.on('failed', (job, error) => {
  logger.error(`Warm-up job ${job?.id} failed:`, {
    jobId: job?.id,
    error: error.message
  });
});

// Additional methods for the enhanced warmup functionality
export const enhancedWarmupService = {
  /**
   * Stop the warmup process for an email account
   */
  async stopWarmup(emailAccountId: string): Promise<void> {
    try {
      // Update the email account status
      await prisma.emailAccount.update({
        where: { id: emailAccountId },
        data: {
          warmupStatus: null,
        },
      });

      // Remove any pending jobs from the queue
      const jobs = await WarmupService.warmupQueue.getJobs(['waiting', 'delayed']);
      for (const job of jobs) {
        if (job.data.emailAccountId === emailAccountId) {
          await job.remove();
        }
      }

      logger.info('Stopped email warmup process', {
        emailAccountId,
      });
    } catch (error) {
      logger.error('Error stopping email warmup', {
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    }
  },

  /**
   * Complete the warmup process for an email account
   */
  async completeWarmup(emailAccountId: string): Promise<void> {
    try {
      // Update the email account status
      await prisma.emailAccount.update({
        where: { id: emailAccountId },
        data: {
          warmupStatus: 'COMPLETED',
        },
      });

      logger.info('Completed email warmup process', {
        emailAccountId,
      });
    } catch (error) {
      logger.error('Error completing email warmup', {
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    }
  },

  /**
   * Get warmup statistics for an email account
   */
  async getWarmupStats(emailAccountId: string): Promise<any> {
    try {
      const activities = await prisma.warmupActivity.findMany({
        where: { emailAccountId },
      });

      const emailsSent = activities.length;
      const emailsOpened = activities.filter(a => a.openedAt).length;
      const emailsReplied = activities.filter(a => a.repliedAt).length;
      const successRate = emailsSent > 0 ? (activities.filter(a => a.status === 'SUCCESS').length / emailsSent) * 100 : 0;

      return {
        emailsSent,
        emailsOpened,
        emailsReplied,
        successRate,
        startedAt: activities[0]?.sentAt || null,
        lastActivity: activities[activities.length - 1]?.sentAt || null,
      };
    } catch (error) {
      logger.error('Error getting warmup stats', {
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    }
  }
};