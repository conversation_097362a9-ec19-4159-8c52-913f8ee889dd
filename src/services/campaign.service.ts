import { prisma } from '@/lib/prisma';
import { emailQueue } from './queue.service';
import { logger } from '@/lib/logger';
import { AgentEnhancementService } from './agent-enhancement.service';

export class CampaignService {
  /**
   * Process active campaigns and send emails to recipients
   */
  static async processActiveCampaigns() {
    try {
      const startTime = Date.now();
      logger.info(`[CAMPAIGN_DEBUG] Starting to process active campaigns`, {
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString(),
        vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
        requestId: `campaign-processor-${Date.now()}`,
        source: 'vercel-cron'
      });

      // Find all active campaigns
      logger.info(`[CAMPAIGN_DEBUG] Querying database for active campaigns`);
      const activeCampaigns = await prisma.campaign.findMany({
        where: {
          status: 'active',
        },
        include: {
          steps: {
            orderBy: {
              position: 'asc',
            },
            include: {
              emailAccount: true,
            },
          },
          leads: {
            include: {
              lead: true,
              stepActivities: true,
            },
          },
        },
      });

      logger.info(`[CAMPAIGN_DEBUG] Found ${activeCampaigns.length} active campaigns`, {
        campaignIds: activeCampaigns.map(c => c.id),
        campaignNames: activeCampaigns.map(c => c.name)
      });

      if (activeCampaigns.length === 0) {
        logger.info(`[CAMPAIGN_DEBUG] No active campaigns found to process`);
        return;
      }

      for (const campaign of activeCampaigns) {
        logger.info(`[CAMPAIGN_DEBUG] Processing campaign: ${campaign.id} - ${campaign.name}`, {
          campaignId: campaign.id,
          campaignName: campaign.name,
          stepCount: campaign.steps.length,
          leadCount: campaign.leads.length,
          timestamp: new Date().toISOString()
        });

        try {
          await this.processCampaign(campaign);
          logger.info(`[CAMPAIGN_DEBUG] Successfully processed campaign: ${campaign.id}`);
        } catch (campaignError) {
          logger.error(`[CAMPAIGN_DEBUG] Error processing individual campaign ${campaign.id}`, {
            error: campaignError instanceof Error ? campaignError.message : 'Unknown error',
            stack: campaignError instanceof Error ? campaignError.stack : 'No stack trace',
            campaignId: campaign.id,
            campaignName: campaign.name,
            timestamp: new Date().toISOString()
          });

          // Continue processing other campaigns even if one fails
          continue;
        }
      }

      const processingTime = Date.now() - startTime;
      logger.info(`[CAMPAIGN_DEBUG] Completed processing all campaigns`, {
        processingTimeMs: processingTime,
        campaignCount: activeCampaigns.length,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('[CAMPAIGN_DEBUG] Error processing active campaigns', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Process a single campaign
   */
  static async processCampaign(campaign: any) {
    try {
      logger.info(`[CAMPAIGN_DEBUG] Processing campaign details: ${campaign.id} - ${campaign.name}`, {
        campaignId: campaign.id,
        campaignName: campaign.name,
        campaignStatus: campaign.status,
        stepCount: campaign.steps.length,
        leadCount: campaign.leads.length,
        organizationId: campaign.organizationId,
        userId: campaign.userId
      });

      // Get the initial step (for now, we only support single-step campaigns)
      const initialStep = campaign.steps[0];
      if (!initialStep) {
        logger.error(`[CAMPAIGN_DEBUG] Campaign ${campaign.id} has no steps`, {
          campaignId: campaign.id,
          campaignName: campaign.name
        });
        return;
      }

      logger.info(`[CAMPAIGN_DEBUG] Campaign step details:`, {
        stepId: initialStep.id,
        stepType: initialStep.type,
        stepPosition: initialStep.position,
        emailAccountId: initialStep.emailAccountId,
        useMultipleSenders: initialStep.useMultipleSenders,
        hasSubject: !!initialStep.subject,
        hasContent: !!initialStep.content,
        subjectPreview: initialStep.subject ? initialStep.subject.substring(0, 30) + '...' : 'No subject'
      });

      // Check if the step is an email step
      if (initialStep.type !== 'email') {
        logger.error(`[CAMPAIGN_DEBUG] Campaign ${campaign.id} initial step is not an email step`, {
          stepType: initialStep.type
        });
        return;
      }

      // For backward compatibility, check if the step has a primary email account
      if (!initialStep.emailAccountId && !initialStep.useMultipleSenders) {
        logger.error(`[CAMPAIGN_DEBUG] Campaign ${campaign.id} has no email account for the initial step`, {
          useMultipleSenders: initialStep.useMultipleSenders,
          emailAccountId: initialStep.emailAccountId
        });
        return;
      }

      // Log email account details if available
      if (initialStep.emailAccount) {
        logger.info(`[CAMPAIGN_DEBUG] Email account details:`, {
          emailAccountId: initialStep.emailAccount.id,
          emailAddress: initialStep.emailAccount.email,
          status: initialStep.emailAccount.status,
          reputationScore: initialStep.emailAccount.reputationScore
        });
      }

      // Process each lead that hasn't received an email yet
      logger.info(`[CAMPAIGN_DEBUG] Processing ${campaign.leads.length} leads for campaign ${campaign.id}`);
      let processedLeadCount = 0;
      let skippedLeadCount = 0;
      let queuedLeadCount = 0;

      // Prepare arrays for batch operations
      const leadsToProcess = [];
      const stepActivitiesToCreate = [];

      // First pass: identify leads to process
      for (const campaignLead of campaign.leads) {
        // Skip leads that have opted out
        if (campaignLead.lead.optOut) {
          logger.info(`[CAMPAIGN_DEBUG] Skipping lead ${campaignLead.lead.email} for campaign ${campaign.id} - lead has opted out`);
          skippedLeadCount++;
          continue;
        }

        // Skip leads that have already received an email for this step
        const hasReceivedEmail = campaignLead.stepActivities.some(
          (activity: any) =>
            activity.stepId === initialStep.id &&
            ['pending', 'sent', 'opened', 'clicked', 'replied'].includes(activity.status)
        );

        if (hasReceivedEmail) {
          logger.info(`[CAMPAIGN_DEBUG] Skipping lead ${campaignLead.lead.email} - already received email`);
          skippedLeadCount++;
          continue;
        }

        // Add to the list of leads to process
        leadsToProcess.push(campaignLead);
      }

      // Select the best email account for all leads at once
      logger.info(`[CAMPAIGN_DEBUG] Selecting email account for ${leadsToProcess.length} leads`);
      const selectedEmailAccountId = await this.selectEmailAccount(initialStep.id);

      if (!selectedEmailAccountId) {
        logger.error(`[CAMPAIGN_DEBUG] No valid email account found for campaign ${campaign.id}, step ${initialStep.id}`, {
          campaignId: campaign.id,
          stepId: initialStep.id,
          emailAccountId: initialStep.emailAccountId,
          useMultipleSenders: initialStep.useMultipleSenders
        });

        // Update campaign status to paused
        logger.info(`[CAMPAIGN_DEBUG] Pausing campaign ${campaign.id} due to no valid email accounts`);
        await prisma.campaign.update({
          where: { id: campaign.id },
          data: {
            status: 'paused',
            errorMessage: `No valid email accounts available for sending. Please check your email account settings.`
          }
        });

        // Create a notification
        await prisma.notification.create({
          data: {
            userId: campaign.userId,
            organizationId: campaign.organizationId,
            title: 'Campaign Paused',
            message: `Campaign "${campaign.name}" has been paused because no valid email accounts are available.`,
            type: 'warning',
            isRead: false,
          },
        });

        return;
      }

      logger.info(`[CAMPAIGN_DEBUG] Selected email account ${selectedEmailAccountId} for all leads`);

      // Prepare batch step activities
      for (const campaignLead of leadsToProcess) {
        stepActivitiesToCreate.push({
          campaignLeadId: campaignLead.id,
          stepId: initialStep.id,
          status: 'pending',
        });
      }

      // Batch create all step activities at once
      if (stepActivitiesToCreate.length > 0) {
        logger.info(`[CAMPAIGN_DEBUG] Creating ${stepActivitiesToCreate.length} step activities in batch`);
        await prisma.$transaction(
          stepActivitiesToCreate.map(activity =>
            prisma.stepActivity.create({
              data: activity
            })
          )
        );
        logger.info(`[CAMPAIGN_DEBUG] Successfully created ${stepActivitiesToCreate.length} step activities`);
      }

      // Now process each lead for queueing
      for (const campaignLead of leadsToProcess) {
        // Add email to the queue with a delay
        // For manual processing, we'll use a minimal delay to make it feel immediate
        // For automated processing, we'll use a moderate delay to prevent spam flags
        const isManualProcessing = process.env.MANUAL_PROCESSING === 'true' || false;
        const delaySeconds = isManualProcessing
          ? Math.floor(Math.random() * 2) + 1 // 1-3 seconds for manual processing (ultra-fast)
          : Math.floor(Math.random() * 15) + 15; // 15-30 seconds for automated processing (reduced from 30-60)

        try {
          await this.queueEmail(
            campaign.id,
            campaignLead.id,
            campaignLead.lead.email,
            initialStep.subject || '',
            initialStep.content || '',
            selectedEmailAccountId,
            delaySeconds
          );

          logger.info(`[CAMPAIGN_DEBUG] Queued email for campaign ${campaign.id}, lead ${campaignLead.lead.email} with ${delaySeconds}s delay`);
          queuedLeadCount++;

          // Add a minimal delay between queueing emails to avoid overwhelming the system
          // Reduced from 500ms to 100ms for faster processing
          await new Promise(resolve => setTimeout(resolve, 50)); // Further reduced to 50ms
        } catch (queueError) {
          logger.error(`[CAMPAIGN_DEBUG] Error queueing email for lead ${campaignLead.lead.email}`, {
            error: queueError instanceof Error ? queueError.message : 'Unknown error',
            stack: queueError instanceof Error ? queueError.stack : 'No stack trace',
            campaignId: campaign.id,
            leadId: campaignLead.id,
            emailAccountId: selectedEmailAccountId
          });
        }

        processedLeadCount++;
      }

      logger.info(`[CAMPAIGN_DEBUG] Campaign ${campaign.id} processing summary:`, {
        totalLeads: campaign.leads.length,
        processedLeads: processedLeadCount,
        skippedLeads: skippedLeadCount,
        queuedLeads: queuedLeadCount
      });

      // Update campaign status if all leads have been processed
      // We only consider a lead processed if it has a 'sent', 'opened', 'clicked', or 'replied' status
      // NOT just 'pending' - this ensures we don't mark campaigns as completed prematurely
      const allLeadsProcessed = campaign.leads.every((campaignLead: any) => {
        return campaignLead.stepActivities.some(
          (activity: any) =>
            activity.stepId === initialStep.id &&
            ['sent', 'opened', 'clicked', 'replied'].includes(activity.status)
        );
      });

      // Only mark as completed if we've actually sent emails (not just queued them)
      if (allLeadsProcessed && queuedLeadCount > 0) {
        // If all leads have been processed, mark the campaign as completed
        logger.info(`[CAMPAIGN_DEBUG] All leads processed for campaign ${campaign.id}, marking as completed`);
        await prisma.campaign.update({
          where: { id: campaign.id },
          data: {
            status: 'completed',
            endDate: new Date(),
          },
        });

        logger.info(`[CAMPAIGN_DEBUG] Campaign ${campaign.id} completed`);
      } else {
        // If we've queued emails but not all have been sent yet, mark as in_progress
        if (queuedLeadCount > 0) {
          logger.info(`[CAMPAIGN_DEBUG] Emails queued for campaign ${campaign.id}, marking as in_progress`);
          await prisma.campaign.update({
            where: { id: campaign.id },
            data: {
              status: 'in_progress',
            },
          });
        } else {
          logger.info(`[CAMPAIGN_DEBUG] Not all leads processed for campaign ${campaign.id}, keeping status as active`);
        }
      }
    } catch (error) {
      logger.error(`[CAMPAIGN_DEBUG] Error processing campaign ${campaign.id}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId: campaign.id,
        campaignName: campaign.name
      });
    }
  }

  /**
   * Process a campaign that was just activated
   */
  static async processActivatedCampaign(campaignId: string) {
    try {
      // Get the campaign with all necessary data
      const campaign = await prisma.campaign.findUnique({
        where: {
          id: campaignId,
          status: 'active',
        },
        include: {
          steps: {
            orderBy: {
              position: 'asc',
            },
            include: {
              emailAccount: true,
              emailAccounts: {
                include: {
                  emailAccount: true
                }
              }
            },
          },
          leads: {
            include: {
              lead: true,
              stepActivities: true,
            },
          },
        },
      });

      if (!campaign) {
        logger.error(`Campaign ${campaignId} not found or not active`);
        return;
      }

      await this.processCampaign(campaign);
    } catch (error) {
      logger.error(`Error processing activated campaign ${campaignId}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Queue an email to be sent (public version for use by other services)
   */
  static async queueCampaignEmail(
    campaignId: string,
    campaignLeadId: string,
    to: string,
    subject: string,
    html: string,
    emailAccountId: string,
    delaySeconds: number = 0
  ) {
    return this.queueEmail(campaignId, campaignLeadId, to, subject, html, emailAccountId, delaySeconds);
  }

  /**
   * Queue an email to be sent (private implementation)
   */
  private static async queueEmail(
    campaignId: string,
    campaignLeadId: string,
    to: string,
    subject: string,
    html: string,
    emailAccountId: string,
    delaySeconds: number = 0
  ) {
    try {
      // Get the campaign lead to access the lead data
      const campaignLead = await prisma.campaignLead.findUnique({
        where: { id: campaignLeadId },
        include: {
          lead: true,
          campaign: {
            include: {
              steps: {
                where: { position: 0 }, // Get the first step
                include: {
                  agent: true
                }
              }
            }
          }
        }
      });

      if (!campaignLead) {
        throw new Error(`Campaign lead not found: ${campaignLeadId}`);
      }

      // Check if the campaign has an AI agent
      const initialStep = campaignLead.campaign.steps[0];
      let finalSubject = subject;
      let finalHtml = html;

      if (initialStep && initialStep.agentId) {
        logger.info(`Campaign ${campaignId} has an AI agent (${initialStep.agentId}). Generating personalized content...`);

        try {
          // Prepare scraped data (in a real implementation, this would come from actual scraping)
          const scrapedData = {
            linkedInProfile: {
              name: campaignLead.lead.firstName ? `${campaignLead.lead.firstName} ${campaignLead.lead.lastName || ''}` : '',
              company: campaignLead.lead.company || '',
            },
            website: campaignLead.lead.company ? `Information about ${campaignLead.lead.company}` : '',
          };

          // Generate personalized email using the agent
          const generatedEmail = await AgentEnhancementService.generatePersonalizedEmail(
            campaignLead.lead.id,
            initialStep.agentId,
            scrapedData
          );

          // Use the generated content
          finalSubject = generatedEmail.subject || subject;
          finalHtml = generatedEmail.html || html;

          logger.info(`Successfully generated personalized content for campaign ${campaignId}, lead ${to}`);
        } catch (agentError) {
          logger.error(`Error generating personalized content with AI agent`, {
            campaignId,
            leadId: campaignLead.lead.id,
            agentId: initialStep.agentId,
            error: agentError instanceof Error ? agentError.message : 'Unknown error',
          });
          // Continue with the original content if agent generation fails
        }
      }

      // Generate a unique job ID for tracking
      const jobId = `campaign-${campaignId}-lead-${campaignLeadId}-${Date.now()}`;

      // In development mode with queue worker disabled, send email directly
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_QUEUE_WORKER !== 'true') {
        logger.info('[CAMPAIGN_DEBUG] Queue worker disabled in development, sending email directly', {
          campaignId,
          campaignLeadId,
          to,
          delaySeconds
        });

        // Add a delay if specified
        if (delaySeconds > 0) {
          await new Promise(resolve => setTimeout(resolve, delaySeconds * 1000));
        }

        // Send email directly without queue
        try {
          await this.sendEmailDirectly(campaignId, campaignLeadId, to, finalSubject, finalHtml, emailAccountId);
          logger.info('[CAMPAIGN_DEBUG] Email sent directly in development mode', {
            campaignId,
            campaignLeadId,
            to
          });
        } catch (error) {
          logger.error('[CAMPAIGN_DEBUG] Error sending email directly in development mode', {
            error: error instanceof Error ? error.message : 'Unknown error',
            campaignId,
            campaignLeadId,
            to
          });
          throw error;
        }
      } else {
        // Check if queue is available
        if (!emailQueue) {
          logger.error('[CAMPAIGN_DEBUG] Email queue not available - falling back to direct sending', {
            campaignId,
            campaignLeadId,
            to
          });

          // Fall back to direct sending if queue is not available
          try {
            await this.sendEmailDirectly(campaignId, campaignLeadId, to, finalSubject, finalHtml, emailAccountId);
            logger.info('[CAMPAIGN_DEBUG] Email sent directly as fallback', {
              campaignId,
              campaignLeadId,
              to
            });
          } catch (error) {
            logger.error('[CAMPAIGN_DEBUG] Error sending email directly as fallback', {
              error: error instanceof Error ? error.message : 'Unknown error',
              campaignId,
              campaignLeadId,
              to
            });
            throw error;
          }
        } else {
          // Add the email to the queue with a delay and retry options
          await emailQueue.add('send-email', {
            campaignId,
            recipientId: campaignLeadId,
            to,
            subject: finalSubject,
            html: finalHtml,
            emailAccountId,
          }, {
            jobId,
            delay: delaySeconds * 1000, // Convert seconds to milliseconds
            attempts: 3, // Retry up to 3 times
            backoff: {
              type: 'exponential',
              delay: 5000 // Start with 5 seconds delay
            },
            removeOnComplete: false, // Keep completed jobs for 24 hours
            removeOnFail: false, // Keep failed jobs for 24 hours
          });
        }
      }

      logger.info(`Added email job ${jobId} to queue for campaign ${campaignId}, lead ${to}`, {
        delay: delaySeconds,
        emailAccountId,
        hasCustomContent: finalSubject !== subject || finalHtml !== html
      });
    } catch (error) {
      logger.error(`Error queueing email for campaign ${campaignId}, lead ${to}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Send email directly without queue (for development mode)
   */
  private static async sendEmailDirectly(
    campaignId: string,
    campaignLeadId: string,
    to: string,
    subject: string,
    html: string,
    emailAccountId: string
  ) {
    const { EmailService } = await import('./email.service');
    const { UsageService } = await import('./usage.service');
    const { DnsTrackingService } = await import('./dns-tracking.service');
    const optimizedTrackingModule = await import('@/lib/optimized-tracking');

    // Get email account
    const emailAccount = await prisma.emailAccount.findUnique({
      where: { id: emailAccountId },
      include: { user: true },
    });

    if (!emailAccount) {
      throw new Error('Email account not found');
    }

    // Get organization
    const organizationMember = await prisma.organizationMember.findFirst({
      where: { userId: emailAccount.userId },
    });

    if (!organizationMember) {
      throw new Error('User has no organization');
    }

    const organizationId = organizationMember.organizationId;

    // Check usage limits
    const withinLimits = await UsageService.checkUsageLimits(organizationId);
    if (!withinLimits) {
      throw new Error('Usage limits exceeded');
    }

    // Get tracking domains
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const trackingDomains = await DnsTrackingService.getTrackingDomains(organizationId, emailAccount.email);

    // Add tracking
    const trackedHtml = optimizedTrackingModule.addTrackingToEmail(
      html,
      campaignId,
      campaignLeadId,
      baseUrl,
      {
        openDomain: trackingDomains.openDomain,
        clickDomain: trackingDomains.clickDomain
      }
    );

    // Send email
    const emailResult = await EmailService.sendEmail({
      emailAccountId,
      to,
      subject,
      html: trackedHtml,
      from: emailAccount.email,
      maxRetries: 3,
      campaignId
    });

    // Track usage
    await UsageService.trackEmailSent(organizationId);

    // Update step activity
    const campaignLead = await prisma.campaignLead.findUnique({
      where: { id: campaignLeadId },
      include: { currentStep: true },
    });

    if (campaignLead && campaignLead.currentStepId) {
      await prisma.stepActivity.create({
        data: {
          campaignLeadId,
          stepId: campaignLead.currentStepId,
          status: 'sent',
          type: 'email_sent',
          sentAt: new Date(),
          metadata: {
            senderEmail: emailAccount.email,
            senderName: emailAccount.name || '',
            subject: subject,
            messageId: emailResult?.messageId || 'unknown'
          },
        },
      });
    }

    return emailResult;
  }

  /**
   * Select the best email account for sending based on reputation
   * If multiple senders are configured, this will distribute leads among them
   * based on their reputation scores
   */
  static async selectEmailAccount(campaignStepId: string): Promise<string | null> {
    try {
      logger.info(`[CAMPAIGN_DEBUG] Selecting email account for campaign step ${campaignStepId}`);

      // Get the campaign step with its email accounts
      const step = await prisma.campaignStep.findUnique({
        where: { id: campaignStepId },
        include: {
          emailAccount: true,
          emailAccounts: {
            include: {
              emailAccount: true
            }
          }
        }
      });

      if (!step) {
        logger.error(`[CAMPAIGN_DEBUG] Campaign step ${campaignStepId} not found`);
        return null;
      }

      logger.info(`[CAMPAIGN_DEBUG] Campaign step ${campaignStepId} found`, {
        stepId: step.id,
        campaignId: step.campaignId,
        primaryEmailAccountId: step.emailAccountId,
        useMultipleSenders: step.useMultipleSenders,
        multipleAccountsCount: step.emailAccounts.length
      });

      // If not using multiple senders, return the primary email account
      if (!step.useMultipleSenders || step.emailAccounts.length === 0) {
        logger.info(`[CAMPAIGN_DEBUG] Using primary email account for step ${campaignStepId}`);

        // Check if the primary email account is valid
        if (!step.emailAccountId) {
          logger.error(`[CAMPAIGN_DEBUG] Campaign step ${campaignStepId} has no email account`);
          return null;
        }

        // Log the email account details
        if (step.emailAccount) {
          logger.info(`[CAMPAIGN_DEBUG] Primary email account details:`, {
            emailAccountId: step.emailAccount.id,
            email: step.emailAccount.email,
            status: step.emailAccount.status,
            reputationScore: step.emailAccount.reputationScore
          });
        }

        // Check if the primary email account is verified and active
        if (step.emailAccount?.status !== 'active' && step.emailAccount?.status !== 'verified') {
          logger.error(`[CAMPAIGN_DEBUG] Email account ${step.emailAccount?.id} has invalid status: ${step.emailAccount?.status}`, {
            emailAccountId: step.emailAccount?.id,
            email: step.emailAccount?.email,
            status: step.emailAccount?.status
          });

          // Create a detailed error message based on the status
          let errorMessage = '';
          if (step.emailAccount?.status === 'suspended') {
            errorMessage = `Email account ${step.emailAccount.email} is suspended and cannot be used for sending emails.`;
          } else if (step.emailAccount?.status === 'pending') {
            errorMessage = `Email account ${step.emailAccount.email} is not verified. Please verify the account before using it for campaigns.`;
          } else {
            errorMessage = `Email account ${step.emailAccount?.email} has invalid status: ${step.emailAccount?.status}.`;
          }

          logger.error(`[CAMPAIGN_DEBUG] Email account error: ${errorMessage}`);

          // Store the error message for the campaign
          try {
            const campaignStep = await prisma.campaignStep.findUnique({
              where: { id: campaignStepId },
              include: { campaign: true }
            });

            if (campaignStep?.campaign) {
              logger.info(`[CAMPAIGN_DEBUG] Updating campaign ${campaignStep.campaignId} status to failed due to email account issues`);

              await prisma.campaign.update({
                where: { id: campaignStep.campaignId },
                data: {
                  status: 'failed',
                  errorMessage: errorMessage,
                  endDate: new Date()
                }
              });

              // Create a notification about the failed campaign
              await prisma.notification.create({
                data: {
                  userId: campaignStep.campaign.userId,
                  organizationId: campaignStep.campaign.organizationId,
                  title: 'Campaign Failed',
                  message: `Campaign "${campaignStep.campaign.name}" has failed because ${errorMessage}`,
                  type: 'error',
                  isRead: false,
                },
              });

              logger.info(`[CAMPAIGN_DEBUG] Created notification for campaign ${campaignStep.campaignId} failure`);
            }
          } catch (err) {
            logger.error('[CAMPAIGN_DEBUG] Failed to update campaign with error message', {
              error: err instanceof Error ? err.message : 'Unknown error',
              stack: err instanceof Error ? err.stack : 'No stack trace',
              campaignStepId
            });
          }

          return null;
        }

        // Check if the primary email account has good reputation
        if (step.emailAccount?.reputationScore !== null && step.emailAccount.reputationScore < 40) {
          logger.warn(`[CAMPAIGN_DEBUG] Email account ${step.emailAccount.id} has poor reputation score ${step.emailAccount.reputationScore}`);
        }

        logger.info(`[CAMPAIGN_DEBUG] Selected primary email account ${step.emailAccountId} for step ${campaignStepId}`);
        return step.emailAccountId;
      }

      // Using multiple senders
      logger.info(`[CAMPAIGN_DEBUG] Using multiple senders for step ${campaignStepId}. Found ${step.emailAccounts.length} accounts`);

      // Filter out accounts that aren't verified/active and those with poor reputation
      const validAccounts = step.emailAccounts.filter(account => {
        const emailAccount = account.emailAccount;
        const isValidStatus = emailAccount.status === 'active' || emailAccount.status === 'verified';
        const hasGoodReputation = emailAccount.reputationScore === null || emailAccount.reputationScore >= 40;

        // Log account status for debugging
        logger.info(`[CAMPAIGN_DEBUG] Checking email account ${emailAccount.id} (${emailAccount.email})`, {
          status: emailAccount.status,
          isValidStatus,
          reputationScore: emailAccount.reputationScore,
          hasGoodReputation
        });

        // Log invalid accounts for debugging
        if (!isValidStatus) {
          logger.warn(`[CAMPAIGN_DEBUG] Email account ${emailAccount.id} (${emailAccount.email}) has invalid status: ${emailAccount.status}`);
        }

        if (!hasGoodReputation && emailAccount.reputationScore !== null) {
          logger.warn(`[CAMPAIGN_DEBUG] Email account ${emailAccount.id} (${emailAccount.email}) has poor reputation score: ${emailAccount.reputationScore}`);
        }

        return isValidStatus && hasGoodReputation;
      });

      logger.info(`[CAMPAIGN_DEBUG] Found ${validAccounts.length} valid email accounts out of ${step.emailAccounts.length} total`);

      if (validAccounts.length === 0) {
        logger.error(`[CAMPAIGN_DEBUG] No valid email accounts found for campaign step ${campaignStepId}`);
        return null;
      }

      // Calculate total weight based on reputation scores
      let totalWeight = 0;
      const accountsWithWeights = validAccounts.map(account => {
        // Base weight is the configured weight
        let weight = account.weight;

        // Adjust weight based on reputation score if available
        if (account.emailAccount.reputationScore !== null) {
          // Reputation bonus: accounts with higher reputation get more weight
          const reputationBonus = Math.max(0, account.emailAccount.reputationScore - 40) / 60; // 0 to 1 scale
          weight = weight * (1 + reputationBonus);
        }

        totalWeight += weight;

        logger.info(`[CAMPAIGN_DEBUG] Email account ${account.emailAccountId} (${account.emailAccount.email}) weight: ${weight}`);

        return {
          id: account.emailAccountId,
          email: account.emailAccount.email,
          weight,
        };
      });

      // Select an account based on weighted probability
      const random = Math.random() * totalWeight;
      let cumulativeWeight = 0;

      logger.info(`[CAMPAIGN_DEBUG] Selecting account with random value ${random} out of total weight ${totalWeight}`);

      for (const account of accountsWithWeights) {
        cumulativeWeight += account.weight;
        if (random <= cumulativeWeight) {
          logger.info(`[CAMPAIGN_DEBUG] Selected email account ${account.id} (${account.email}) with cumulative weight ${cumulativeWeight}`);
          return account.id;
        }
      }

      // Fallback to the first account if something goes wrong
      logger.info(`[CAMPAIGN_DEBUG] Fallback to first account ${accountsWithWeights[0].id} (${accountsWithWeights[0].email})`);
      return accountsWithWeights[0].id;
    } catch (error) {
      logger.error('[CAMPAIGN_DEBUG] Error selecting email account', {
        campaignStepId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      return null;
    }
  }
}
