import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { AIProviderService } from './ai-provider.service';
import { PromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { RunnableSequence } from '@langchain/core/runnables';

export class AgentService {
  /**
   * Generate an email reply using an AI agent with knowledge base context
   */
  static async generateEmailReply(
    agentId: string,
    emailContent: string,
    emailSubject: string,
    threadContext?: string,
    knowledgeBaseContent?: string,
    emailAccountId?: string
  ): Promise<{ text: string; html: string }> {
    try {
      // Get the agent with knowledge base
      const agent = await prisma.agent.findUnique({
        where: { id: agentId },
        include: {
          organization: {
            select: {
              name: true,
            },
          },
          knowledgeBases: {
            include: {
              knowledgeBase: {
                include: {
                  organization: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!agent) {
        throw new Error(`Agent not found: ${agentId}`);
      }

      if (!agent.isActive) {
        throw new Error(`Agent is not active: ${agentId}`);
      }

      // Get organization information
      const organization = agent.organization || agent.knowledgeBases[0]?.knowledgeBase?.organization;
      const companyName = organization?.name || 'WattleSol';

      // Get agent configuration
      const config = agent.config as any || {};
      const systemPrompt = agent.systemPrompt || this.getDefaultEmailPrompt();
      const temperature = config.temperature || 0.7;
      const maxTokens = config.maxTokens || 500;

      // Get custom signature if email account is provided
      let customSignature = '';
      let signatureOptions: string[] = [];

      if (emailAccountId) {
        try {
          const emailAccount = await prisma.emailAccount.findUnique({
            where: { id: emailAccountId },
            select: {
              signatureEnabled: true,
              signatureName: true,
              signatureTitle: true,
              signatureCompany: true,
              signaturePhone: true,
              signatureEmail: true,
              signatureWebsite: true,
              signatureCustom: true,
              email: true,
            },
          });

          if (emailAccount?.signatureEnabled) {
            if (emailAccount.signatureCustom) {
              // Use custom signature as-is
              customSignature = emailAccount.signatureCustom;
            } else {
              // Build signature from individual fields
              const signatureParts = [];

              if (emailAccount.signatureName) {
                signatureParts.push(emailAccount.signatureName);
              }

              if (emailAccount.signatureTitle) {
                signatureParts.push(emailAccount.signatureTitle);
              }

              if (emailAccount.signatureCompany) {
                signatureParts.push(emailAccount.signatureCompany);
              }

              if (emailAccount.signaturePhone) {
                signatureParts.push(`Phone: ${emailAccount.signaturePhone}`);
              }

              if (emailAccount.signatureEmail) {
                signatureParts.push(`Email: ${emailAccount.signatureEmail}`);
              }

              if (emailAccount.signatureWebsite) {
                signatureParts.push(`Website: ${emailAccount.signatureWebsite}`);
              }

              if (signatureParts.length > 0) {
                customSignature = signatureParts.join('\n');
              }
            }
          }
        } catch (signatureError) {
          logger.error('Error fetching custom signature', {
            emailAccountId,
            error: signatureError instanceof Error ? signatureError.message : 'Unknown error',
          });
        }
      }

      // Use custom signature or fallback to default options
      if (customSignature) {
        signatureOptions = [customSignature];
      } else {
        // Create professional signature options
        signatureOptions = [
          `Best regards,\nSales Team\n${companyName}`,
          `Best regards,\nTeam ${companyName}`,
          `Looking forward to connecting!\n\nBest,\nSales Team\n${companyName} LLC`,
          `Best regards,\n${companyName} Team`,
          `Thank you,\nSales Department\n${companyName}`
        ];
      }

      // Query knowledge base for company-specific information
      let companyInfo = '';

      if (knowledgeBaseContent) {
        // Extract company information from knowledge base
        const companyInfoMatch = knowledgeBaseContent.match(/company|about|business|services|solutions/i);
        if (companyInfoMatch) {
          companyInfo = knowledgeBaseContent;
        }
      }

      // Build context with knowledge base and company information
      let contextualPrompt = `${systemPrompt}

COMPANY INFORMATION:
Company Name: ${companyName}
${companyInfo ? `Company Details: ${companyInfo}` : ''}

${knowledgeBaseContent ? `KNOWLEDGE BASE CONTEXT:\n${knowledgeBaseContent}\n` : ''}

${threadContext ? `PREVIOUS CONVERSATION:\n${threadContext}\n` : ''}

CUSTOMER EMAIL:
Subject: ${emailSubject}
Content: ${emailContent}

${customSignature ? 'CUSTOM SIGNATURE (use exactly as provided):' : 'SIGNATURE OPTIONS (choose one that fits the tone):'}
${signatureOptions.join('\n\n')}

INSTRUCTIONS:
- Reply as a professional representative of ${companyName}
- Do NOT include a subject line in your response
- Reference the customer's message naturally and professionally
- Use information from the knowledge base when relevant
- Be helpful, engaging, and personalized
- ${customSignature ? 'End with the provided custom signature exactly as shown' : 'End with one of the provided signature options (choose the most appropriate)'}
- Keep the response concise but comprehensive
- Maintain a professional yet friendly tone

Generate your email reply (content only, no subject):`;

      // Check if we're using OpenAI provider for direct API calls
      const provider = AIProviderService.getProvider();

      let generatedText = '';

      if (provider === 'openai') {
        // Use direct OpenAI API for backward compatibility
        const openai = AIProviderService.getOpenAIClient();
        const response = await openai.chat.completions.create({
          model: AIProviderService.getModelName(),
          messages: [
            {
              role: 'system',
              content: `You are a professional sales representative for ${companyName}. Write email replies without subject lines. Always use one of the provided signature options. Be helpful, professional, and engaging.`,
            },
            {
              role: 'user',
              content: contextualPrompt,
            },
          ],
          temperature,
          max_tokens: maxTokens,
        });

        generatedText = response.choices[0]?.message?.content?.trim() || '';
      } else {
        // Use optimized LangChain for Ollama and other providers (R1 optimization)
        const optimizedPrompt = AIProviderService.optimizePromptForR1(contextualPrompt, 'email');
        const llm = AIProviderService.getOptimizedChatModel('email');

        const prompt = PromptTemplate.fromTemplate(`
System: You are a professional sales representative for ${companyName}. Write email replies without subject lines. Always use one of the provided signature options. Be helpful, professional, and engaging.

User: {content}
        `);

        const chain = RunnableSequence.from([
          prompt,
          llm,
          new StringOutputParser(),
        ]);

        generatedText = await chain.invoke({ content: optimizedPrompt });
      }

      // Clean up the response - remove any subject lines that might have been included
      generatedText = this.cleanEmailResponse(generatedText);

      // Convert to HTML (simple conversion)
      const htmlContent = this.textToHtml(generatedText);

      return {
        text: generatedText,
        html: htmlContent,
      };
    } catch (error) {
      logger.error('Error generating email reply', {
        agentId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Get the default email reply prompt
   */
  private static getDefaultEmailPrompt(): string {
    return `
You are an AI assistant helping to respond to customer emails. Your task is to generate a professional,
helpful, and friendly reply to the email below. The reply should:

1. Address the sender by name if available
2. Acknowledge their email and thank them for reaching out
3. Answer their questions or address their concerns directly
4. Provide any relevant additional information
5. End with a friendly closing and your name

Keep the tone professional but conversational. Be concise but thorough.
`;
  }

  /**
   * Clean email response to remove unwanted elements
   */
  private static cleanEmailResponse(text: string): string {
    // Remove any subject lines that might have been included
    let cleaned = text.replace(/^Subject:\s*.*$/gim, '');

    // Remove "Re:" prefixes
    cleaned = cleaned.replace(/^Re:\s*/gim, '');

    // Remove extra whitespace and empty lines at the beginning
    cleaned = cleaned.replace(/^\s*\n+/, '');

    // Fix placeholder signatures
    cleaned = cleaned.replace(/\[Your Name\]/g, 'Sales Team');
    cleaned = cleaned.replace(/\[Your Job Title\]/g, '');
    cleaned = cleaned.replace(/\[Your Contact Information\]/g, '');
    cleaned = cleaned.replace(/Your Name/g, 'Sales Team');
    cleaned = cleaned.replace(/\[Company Name\]/g, 'WattleSol');

    // Remove empty lines after signature fixes
    cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');

    // Trim whitespace
    cleaned = cleaned.trim();

    return cleaned;
  }

  /**
   * Convert plain text to simple HTML
   */
  private static textToHtml(text: string): string {
    // Replace newlines with <br> tags
    let html = text.replace(/\n/g, '<br>');

    // Wrap paragraphs
    html = html.replace(/(.+?)(\n\n|$)/g, '<p>$1</p>');

    // Remove <br> tags inside paragraphs
    html = html.replace(/<p>(.*?)<br>(.*?)<\/p>/g, '<p>$1 $2</p>');

    return html;
  }

  /**
   * Process all pending agent replies
   */
  static async processPendingReplies(): Promise<void> {
    try {
      // Get all pending agent replies
      const pendingReplies = await prisma.agentReply.findMany({
        where: { status: 'PENDING' },
        include: {
          receivedEmail: {
            include: {
              emailAccount: true,
            },
          },
          lead: true,
        },
      });

      logger.info(`Processing ${pendingReplies.length} pending agent replies`);

      // Process each reply
      for (const reply of pendingReplies) {
        try {
          // Send the email
          // This would typically call the EmailService.sendEmail method
          // For now, we'll just update the status
          await prisma.agentReply.update({
            where: { id: reply.id },
            data: {
              status: 'SENT',
              sentAt: new Date(),
            },
          });

          logger.info('Agent reply processed', { replyId: reply.id });
        } catch (error) {
          logger.error('Error processing agent reply', {
            replyId: reply.id,
            error: error instanceof Error ? error.message : 'Unknown error',
          });

          // Update the reply with the error
          await prisma.agentReply.update({
            where: { id: reply.id },
            data: {
              status: 'FAILED',
              error: error instanceof Error ? error.message : 'Unknown error',
            },
          });
        }
      }
    } catch (error) {
      logger.error('Error processing pending agent replies', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}
