import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { addTrackingToEmail } from '@/lib/email-tracking';

/**
 * Service for handling tracking functionality for sequence campaigns (multi-step campaigns)
 */
export class SequenceCampaignTrackingService {
  /**
   * Add tracking to an email for a sequence campaign
   * @param html The HTML content of the email
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param stepId The step ID
   * @param baseUrl The base URL for tracking
   * @param trackingDomains Optional custom tracking domains
   * @returns The HTML with tracking added
   */
  static async addTracking(
    html: string,
    campaignId: string,
    leadId: string,
    stepId: string,
    baseUrl?: string,
    trackingDomains?: {
      openDomain?: string;
      clickDomain?: string;
      bounceDomain?: string;
      source?: 'custom' | 'sender' | 'default';
    }
  ): Promise<string> {
    try {
      // If no baseUrl is provided, get it from environment
      const trackingBaseUrl = baseUrl || process.env.NEXTAUTH_URL || 'http://localhost:3000';

      // Add step ID to the metadata for tracking
      const htmlWithStepMetadata = html.replace('</body>', `<meta name="sequence-step-id" content="${stepId}" /></body>`);

      // Use the existing addTrackingToEmail function
      return addTrackingToEmail(htmlWithStepMetadata, campaignId, leadId, trackingBaseUrl, trackingDomains);
    } catch (error) {
      logger.error('[SEQUENCE_TRACKING] Error adding tracking to email', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        stepId
      });
      return html; // Return original HTML if there's an error
    }
  }

  /**
   * Initialize tracking for a lead in a sequence campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param initialStepId The initial step ID
   * @returns Success status
   */
  static async initializeTracking(
    campaignId: string,
    leadId: string,
    initialStepId: string
  ): Promise<boolean> {
    try {
      // Create or update the tracking record
      await prisma.sequenceCampaignTracking.upsert({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        },
        update: {
          currentStepId: initialStepId,
          status: 'active'
        },
        create: {
          campaignId,
          leadId,
          currentStepId: initialStepId,
          status: 'active',
          completedStepIds: []
        }
      });

      // Create a step event for entering the initial step
      await prisma.sequenceStepEvent.create({
        data: {
          sequenceCampaignTrackingId: (await prisma.sequenceCampaignTracking.findUnique({
            where: { campaignId_leadId: { campaignId, leadId } }
          }))!.id,
          stepId: initialStepId,
          eventType: 'step_entered',
          status: 'active',
          metadata: {
            timestamp: new Date().toISOString()
          }
        }
      });

      logger.info('[SEQUENCE_TRACKING] Initialized tracking for lead', {
        campaignId,
        leadId,
        initialStepId
      });

      return true;
    } catch (error) {
      logger.error('[SEQUENCE_TRACKING] Error initializing tracking', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        initialStepId
      });
      return false;
    }
  }

  /**
   * Track an email being sent in a sequence campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param stepId The step ID
   * @returns Success status
   */
  static async trackEmailSent(
    campaignId: string,
    leadId: string,
    stepId: string
  ): Promise<boolean> {
    try {
      // Get the tracking record
      const tracking = await prisma.sequenceCampaignTracking.findUnique({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        }
      });

      if (!tracking) {
        logger.warn('[SEQUENCE_TRACKING] Tracking record not found for email sent', {
          campaignId,
          leadId,
          stepId
        });
        return false;
      }

      // Create a step event for the email being sent
      await prisma.sequenceStepEvent.create({
        data: {
          sequenceCampaignTrackingId: tracking.id,
          stepId,
          eventType: 'email_sent',
          status: 'sent',
          metadata: {
            timestamp: new Date().toISOString()
          }
        }
      });

      // Update campaign metrics
      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          sentCount: {
            increment: 1
          }
        }
      });

      logger.info('[SEQUENCE_TRACKING] Email sent tracked', {
        campaignId,
        leadId,
        stepId,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[SEQUENCE_TRACKING] Error tracking email sent', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        stepId
      });
      return false;
    }
  }

  /**
   * Track an email open in a sequence campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param stepId The step ID (optional, will use current step if not provided)
   * @param metadata Additional metadata about the open
   * @returns Success status
   */
  static async trackOpen(
    campaignId: string,
    leadId: string,
    stepId: string | null,
    metadata: {
      userAgent?: string;
      ip?: string;
      referrer?: string;
      timestamp?: string;
    }
  ): Promise<boolean> {
    try {
      // Get the tracking record
      const tracking = await prisma.sequenceCampaignTracking.findUnique({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        }
      });

      if (!tracking) {
        logger.warn('[SEQUENCE_TRACKING] Tracking record not found for email open', {
          campaignId,
          leadId,
          stepId
        });
        return false;
      }

      // Use the provided step ID or the current step ID
      const actualStepId = stepId || tracking.currentStepId;

      if (!actualStepId) {
        logger.warn('[SEQUENCE_TRACKING] No step ID available for email open', {
          campaignId,
          leadId
        });
        return false;
      }

      // Check if this is the first open for this step
      const existingOpen = await prisma.sequenceStepEvent.findFirst({
        where: {
          sequenceCampaignTrackingId: tracking.id,
          stepId: actualStepId,
          eventType: 'email_opened'
        }
      });

      const isFirstOpen = !existingOpen;

      // Create a step event for the email being opened
      await prisma.sequenceStepEvent.create({
        data: {
          sequenceCampaignTrackingId: tracking.id,
          stepId: actualStepId,
          eventType: 'email_opened',
          status: 'opened',
          metadata: {
            userAgent: metadata.userAgent,
            ip: metadata.ip,
            referrer: metadata.referrer,
            timestamp: metadata.timestamp || new Date().toISOString(),
            isFirstOpen
          }
        }
      });

      // Update the tracking record's last interaction time
      await prisma.sequenceCampaignTracking.update({
        where: { id: tracking.id },
        data: {
          lastInteractionAt: new Date()
        }
      });

      // Record the open event for analytics
      await prisma.emailOpenEvent.create({
        data: {
          campaignId,
          leadId,
          campaignType: 'sequence',
          userAgent: metadata.userAgent || '',
          ipAddress: metadata.ip || '',
          metadata: {
            stepId: actualStepId,
            referrer: metadata.referrer || '',
            timestamp: metadata.timestamp || new Date().toISOString(),
            isFirstOpen
          }
        }
      });

      // Update campaign metrics only for first opens
      if (isFirstOpen) {
        await prisma.campaign.update({
          where: { id: campaignId },
          data: {
            openedCount: {
              increment: 1
            }
          }
        });
      }

      logger.info('[SEQUENCE_TRACKING] Email open tracked', {
        campaignId,
        leadId,
        stepId: actualStepId,
        isFirstOpen,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[SEQUENCE_TRACKING] Error tracking email open', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        stepId
      });
      return false;
    }
  }

  /**
   * Track a link click in a sequence campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param stepId The step ID (optional, will use current step if not provided)
   * @param url The URL that was clicked
   * @param metadata Additional metadata about the click
   * @returns Success status
   */
  static async trackClick(
    campaignId: string,
    leadId: string,
    stepId: string | null,
    url: string,
    metadata: {
      userAgent?: string;
      ip?: string;
      referrer?: string;
      timestamp?: string;
    }
  ): Promise<boolean> {
    try {
      // Get the tracking record
      const tracking = await prisma.sequenceCampaignTracking.findUnique({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        }
      });

      if (!tracking) {
        logger.warn('[SEQUENCE_TRACKING] Tracking record not found for link click', {
          campaignId,
          leadId,
          stepId,
          url
        });
        return false;
      }

      // Use the provided step ID or the current step ID
      const actualStepId = stepId || tracking.currentStepId;

      if (!actualStepId) {
        logger.warn('[SEQUENCE_TRACKING] No step ID available for link click', {
          campaignId,
          leadId,
          url
        });
        return false;
      }

      // Check if this is the first click for this step
      const existingClick = await prisma.sequenceStepEvent.findFirst({
        where: {
          sequenceCampaignTrackingId: tracking.id,
          stepId: actualStepId,
          eventType: 'link_clicked'
        }
      });

      const isFirstClick = !existingClick;

      // Create a step event for the link being clicked
      await prisma.sequenceStepEvent.create({
        data: {
          sequenceCampaignTrackingId: tracking.id,
          stepId: actualStepId,
          eventType: 'link_clicked',
          status: 'clicked',
          metadata: {
            url,
            userAgent: metadata.userAgent,
            ip: metadata.ip,
            referrer: metadata.referrer,
            timestamp: metadata.timestamp || new Date().toISOString(),
            isFirstClick
          }
        }
      });

      // Update the tracking record's last interaction time
      await prisma.sequenceCampaignTracking.update({
        where: { id: tracking.id },
        data: {
          lastInteractionAt: new Date()
        }
      });

      // Record the click event for analytics
      await prisma.linkClickEvent.create({
        data: {
          campaignId,
          leadId,
          campaignType: 'sequence',
          url,
          userAgent: metadata.userAgent || '',
          ipAddress: metadata.ip || '',
          metadata: {
            stepId: actualStepId,
            referrer: metadata.referrer || '',
            timestamp: metadata.timestamp || new Date().toISOString(),
            isFirstClick
          }
        }
      });

      // Update campaign metrics only for first clicks
      if (isFirstClick) {
        await prisma.campaign.update({
          where: { id: campaignId },
          data: {
            clickedCount: {
              increment: 1
            }
          }
        });
      }

      logger.info('[SEQUENCE_TRACKING] Link click tracked', {
        campaignId,
        leadId,
        stepId: actualStepId,
        url,
        isFirstClick,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[SEQUENCE_TRACKING] Error tracking link click', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        stepId,
        url
      });
      return false;
    }
  }

  /**
   * Track an unsubscribe event in a sequence campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param metadata Additional metadata about the unsubscribe
   * @returns Success status
   */
  static async trackUnsubscribe(
    campaignId: string,
    leadId: string,
    metadata: {
      userAgent?: string;
      ip?: string;
      timestamp?: string;
      reason?: string;
    }
  ): Promise<boolean> {
    try {
      // Get the tracking record
      const tracking = await prisma.sequenceCampaignTracking.findUnique({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        }
      });

      if (!tracking) {
        logger.warn('[SEQUENCE_TRACKING] Tracking record not found for unsubscribe', {
          campaignId,
          leadId
        });
        return false;
      }

      // Update the tracking record
      await prisma.sequenceCampaignTracking.update({
        where: { id: tracking.id },
        data: {
          status: 'unsubscribed',
          lastInteractionAt: new Date(),
          metadata: {
            ...tracking.metadata as any || {},
            unsubscribeUserAgent: metadata.userAgent,
            unsubscribeIp: metadata.ip,
            unsubscribeTimestamp: metadata.timestamp || new Date().toISOString(),
            unsubscribeReason: metadata.reason
          }
        }
      });

      // Create a step event for the unsubscribe
      if (tracking.currentStepId) {
        await prisma.sequenceStepEvent.create({
          data: {
            sequenceCampaignTrackingId: tracking.id,
            stepId: tracking.currentStepId,
            eventType: 'unsubscribed',
            status: 'unsubscribed',
            metadata: {
              userAgent: metadata.userAgent,
              ip: metadata.ip,
              timestamp: metadata.timestamp || new Date().toISOString(),
              reason: metadata.reason
            }
          }
        });
      }

      // Update the lead status to unsubscribed
      await prisma.lead.update({
        where: { id: leadId },
        data: { status: 'unsubscribed' }
      });

      logger.info('[SEQUENCE_TRACKING] Unsubscribe tracked', {
        campaignId,
        leadId,
        reason: metadata.reason,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[SEQUENCE_TRACKING] Error tracking unsubscribe', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId
      });
      return false;
    }
  }

  /**
   * Track a bounce event in a sequence campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param stepId The step ID (optional, will use current step if not provided)
   * @param bounceType The type of bounce (hard or soft)
   * @param bounceReason The reason for the bounce
   * @returns Success status
   */
  static async trackBounce(
    campaignId: string,
    leadId: string,
    stepId: string | null,
    bounceType: 'hard' | 'soft',
    bounceReason?: string
  ): Promise<boolean> {
    try {
      // Get the tracking record
      const tracking = await prisma.sequenceCampaignTracking.findUnique({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        }
      });

      if (!tracking) {
        logger.warn('[SEQUENCE_TRACKING] Tracking record not found for bounce', {
          campaignId,
          leadId,
          stepId
        });
        return false;
      }

      // Use the provided step ID or the current step ID
      const actualStepId = stepId || tracking.currentStepId;

      if (!actualStepId) {
        logger.warn('[SEQUENCE_TRACKING] No step ID available for bounce', {
          campaignId,
          leadId
        });
        return false;
      }

      // Get the lead email
      const lead = await prisma.lead.findUnique({
        where: { id: leadId },
        select: { email: true }
      });

      if (!lead) {
        logger.warn('[SEQUENCE_TRACKING] Lead not found for bounce tracking', { campaignId, leadId });
        return false;
      }

      // Update the tracking record for hard bounces
      if (bounceType === 'hard') {
        await prisma.sequenceCampaignTracking.update({
          where: { id: tracking.id },
          data: {
            status: 'bounced',
            metadata: {
              ...tracking.metadata as any || {},
              bounceType,
              bounceReason,
              bounceTimestamp: new Date().toISOString()
            }
          }
        });
      }

      // Create a step event for the bounce
      await prisma.sequenceStepEvent.create({
        data: {
          sequenceCampaignTrackingId: tracking.id,
          stepId: actualStepId,
          eventType: 'email_bounced',
          status: 'bounced',
          metadata: {
            bounceType,
            bounceReason,
            timestamp: new Date().toISOString()
          }
        }
      });

      // Update campaign metrics
      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          errorCount: {
            increment: 1
          }
        }
      });

      // Add to suppression list for hard bounces
      if (bounceType === 'hard' && lead.email) {
        await prisma.suppressionList.upsert({
          where: { email: lead.email },
          update: {
            reason: `Bounce: ${bounceReason || bounceType || 'Unknown'}`
          },
          create: {
            email: lead.email,
            reason: `Bounce: ${bounceReason || bounceType || 'Unknown'}`
          }
        });
      }

      logger.info('[SEQUENCE_TRACKING] Bounce tracked', {
        campaignId,
        leadId,
        stepId: actualStepId,
        email: lead.email,
        bounceType,
        bounceReason,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[SEQUENCE_TRACKING] Error tracking bounce', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        stepId,
        bounceType,
        bounceReason
      });
      return false;
    }
  }

  /**
   * Track a reply event in a sequence campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param stepId The step ID (optional, will use current step if not provided)
   * @param messageId The message ID of the reply
   * @param replyContent The content of the reply
   * @returns Success status
   */
  static async trackReply(
    campaignId: string,
    leadId: string,
    stepId: string | null,
    messageId: string,
    replyContent: {
      subject?: string;
      text?: string;
      html?: string;
      from?: string;
      inReplyTo?: string;
    }
  ): Promise<boolean> {
    try {
      // Get the tracking record
      const tracking = await prisma.sequenceCampaignTracking.findUnique({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        }
      });

      if (!tracking) {
        logger.warn('[SEQUENCE_TRACKING] Tracking record not found for reply', {
          campaignId,
          leadId,
          stepId
        });
        return false;
      }

      // Use the provided step ID or the current step ID
      const actualStepId = stepId || tracking.currentStepId;

      if (!actualStepId) {
        logger.warn('[SEQUENCE_TRACKING] No step ID available for reply', {
          campaignId,
          leadId
        });
        return false;
      }

      // Get the lead email
      const lead = await prisma.lead.findUnique({
        where: { id: leadId },
        select: { email: true }
      });

      if (!lead) {
        logger.warn('[SEQUENCE_TRACKING] Lead not found for reply tracking', { campaignId, leadId });
        return false;
      }

      // Check if this is the first reply for this step
      const existingReply = await prisma.sequenceStepEvent.findFirst({
        where: {
          sequenceCampaignTrackingId: tracking.id,
          stepId: actualStepId,
          eventType: 'email_replied'
        }
      });

      const isFirstReply = !existingReply;

      // Create a step event for the reply
      await prisma.sequenceStepEvent.create({
        data: {
          sequenceCampaignTrackingId: tracking.id,
          stepId: actualStepId,
          eventType: 'email_replied',
          status: 'replied',
          metadata: {
            messageId,
            subject: replyContent.subject,
            from: replyContent.from,
            inReplyTo: replyContent.inReplyTo,
            timestamp: new Date().toISOString(),
            isFirstReply
          }
        }
      });

      // Update the tracking record's last interaction time
      await prisma.sequenceCampaignTracking.update({
        where: { id: tracking.id },
        data: {
          lastInteractionAt: new Date()
        }
      });

      // Update campaign metrics only for first replies
      if (isFirstReply) {
        await prisma.campaign.update({
          where: { id: campaignId },
          data: {
            repliedCount: {
              increment: 1
            }
          }
        });
      }

      logger.info('[SEQUENCE_TRACKING] Reply tracked', {
        campaignId,
        leadId,
        stepId: actualStepId,
        email: lead.email,
        messageId,
        subject: replyContent.subject,
        isFirstReply,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[SEQUENCE_TRACKING] Error tracking reply', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        stepId,
        messageId
      });
      return false;
    }
  }

  /**
   * Move a lead to the next step in a sequence campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param nextStepId The next step ID
   * @returns Success status
   */
  static async moveToNextStep(
    campaignId: string,
    leadId: string,
    nextStepId: string
  ): Promise<boolean> {
    try {
      // Get the tracking record
      const tracking = await prisma.sequenceCampaignTracking.findUnique({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        }
      });

      if (!tracking) {
        logger.warn('[SEQUENCE_TRACKING] Tracking record not found for moving to next step', {
          campaignId,
          leadId,
          nextStepId
        });
        return false;
      }

      // Mark the current step as completed if it exists
      if (tracking.currentStepId) {
        // Create a step event for completing the current step
        await prisma.sequenceStepEvent.create({
          data: {
            sequenceCampaignTrackingId: tracking.id,
            stepId: tracking.currentStepId,
            eventType: 'step_completed',
            status: 'completed',
            metadata: {
              timestamp: new Date().toISOString(),
              nextStepId
            }
          }
        });

        // Add the current step to the list of completed steps
        await prisma.sequenceCampaignTracking.update({
          where: { id: tracking.id },
          data: {
            completedStepIds: {
              push: tracking.currentStepId
            }
          }
        });
      }

      // Update the tracking record with the new step
      await prisma.sequenceCampaignTracking.update({
        where: { id: tracking.id },
        data: {
          currentStepId: nextStepId,
          lastInteractionAt: new Date()
        }
      });

      // Create a step event for entering the new step
      await prisma.sequenceStepEvent.create({
        data: {
          sequenceCampaignTrackingId: tracking.id,
          stepId: nextStepId,
          eventType: 'step_entered',
          status: 'active',
          metadata: {
            timestamp: new Date().toISOString(),
            previousStepId: tracking.currentStepId
          }
        }
      });

      logger.info('[SEQUENCE_TRACKING] Lead moved to next step', {
        campaignId,
        leadId,
        previousStepId: tracking.currentStepId,
        nextStepId,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[SEQUENCE_TRACKING] Error moving lead to next step', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        nextStepId
      });
      return false;
    }
  }

  /**
   * Complete a lead's journey in a sequence campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @returns Success status
   */
  static async completeCampaign(
    campaignId: string,
    leadId: string
  ): Promise<boolean> {
    try {
      // Get the tracking record
      const tracking = await prisma.sequenceCampaignTracking.findUnique({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        }
      });

      if (!tracking) {
        logger.warn('[SEQUENCE_TRACKING] Tracking record not found for completing campaign', {
          campaignId,
          leadId
        });
        return false;
      }

      // Mark the current step as completed if it exists
      if (tracking.currentStepId) {
        // Create a step event for completing the current step
        await prisma.sequenceStepEvent.create({
          data: {
            sequenceCampaignTrackingId: tracking.id,
            stepId: tracking.currentStepId,
            eventType: 'step_completed',
            status: 'completed',
            metadata: {
              timestamp: new Date().toISOString(),
              isFinalStep: true
            }
          }
        });

        // Add the current step to the list of completed steps
        await prisma.sequenceCampaignTracking.update({
          where: { id: tracking.id },
          data: {
            completedStepIds: {
              push: tracking.currentStepId
            }
          }
        });
      }

      // Update the tracking record to completed status
      await prisma.sequenceCampaignTracking.update({
        where: { id: tracking.id },
        data: {
          status: 'completed',
          currentStepId: null,
          lastInteractionAt: new Date()
        }
      });

      logger.info('[SEQUENCE_TRACKING] Lead completed campaign', {
        campaignId,
        leadId,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('[SEQUENCE_TRACKING] Error completing campaign for lead', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId
      });
      return false;
    }
  }
}
