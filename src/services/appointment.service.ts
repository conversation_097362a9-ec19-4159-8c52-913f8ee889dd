import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { ReminderService } from './reminder.service';
import { EmailService } from './email.service';

interface CreateAppointmentOptions {
  leadId: string;
  campaignId: string;
  agentId: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  location?: string;
  meetingLink?: string;
  notes?: string;
  metadata?: any;
  reminders?: Array<{
    type: string;
    timeBeforeAppointment: number; // minutes
    content?: string;
  }>;
}

interface UpdateAppointmentOptions {
  id: string;
  title?: string;
  description?: string;
  startTime?: Date;
  endTime?: Date;
  status?: string;
  location?: string;
  meetingLink?: string;
  notes?: string;
  metadata?: any;
}

/**
 * Service for managing appointments created by AI agents
 */
export class AppointmentService {
  /**
   * Create a new appointment
   * @param options Appointment options
   * @returns The created appointment
   */
  static async createAppointment(options: CreateAppointmentOptions) {
    try {
      logger.info('Creating appointment', {
        leadId: options.leadId,
        campaignId: options.campaignId,
        agentId: options.agentId,
        title: options.title,
        startTime: options.startTime,
      });

      // Create the appointment
      const appointment = await prisma.appointment.create({
        data: {
          leadId: options.leadId,
          campaignId: options.campaignId,
          agentId: options.agentId,
          title: options.title,
          description: options.description,
          startTime: options.startTime,
          endTime: options.endTime,
          location: options.location,
          meetingLink: options.meetingLink,
          notes: options.notes,
          metadata: options.metadata,
        },
      });

      // Create reminders if specified
      if (options.reminders && options.reminders.length > 0) {
        await Promise.all(
          options.reminders.map(async (reminder) => {
            return ReminderService.createReminder({
              appointmentId: appointment.id,
              agentId: options.agentId,
              type: reminder.type,
              timeBeforeAppointment: reminder.timeBeforeAppointment,
              content: reminder.content,
            });
          })
        );
      }

      // Get the lead to send confirmation
      const lead = await prisma.lead.findUnique({
        where: { id: options.leadId },
      });

      if (lead && lead.email) {
        // Get the agent's email account
        const agent = await prisma.agent.findUnique({
          where: { id: options.agentId },
          include: {
            campaigns: {
              include: {
                steps: {
                  include: {
                    emailAccount: true,
                  },
                },
              },
            },
          },
        });

        // Find an email account to use
        const emailAccount = agent?.campaigns[0]?.steps[0]?.emailAccount;

        if (emailAccount) {
          // Send appointment confirmation
          await EmailService.sendEmail({
            emailAccountId: emailAccount.id,
            to: lead.email,
            subject: `Appointment Confirmation: ${options.title}`,
            html: `
              <h2>Appointment Confirmation</h2>
              <p>Dear ${lead.firstName || lead.email},</p>
              <p>Your appointment has been scheduled:</p>
              <ul>
                <li><strong>Title:</strong> ${options.title}</li>
                <li><strong>Date:</strong> ${options.startTime.toLocaleDateString()}</li>
                <li><strong>Time:</strong> ${options.startTime.toLocaleTimeString()} - ${options.endTime.toLocaleTimeString()}</li>
                ${options.location ? `<li><strong>Location:</strong> ${options.location}</li>` : ''}
                ${options.meetingLink ? `<li><strong>Meeting Link:</strong> <a href="${options.meetingLink}">${options.meetingLink}</a></li>` : ''}
              </ul>
              ${options.description ? `<p><strong>Description:</strong> ${options.description}</p>` : ''}
              <p>We look forward to meeting with you!</p>
            `,
          });
        }
      }

      return appointment;
    } catch (error) {
      logger.error('Error creating appointment', {
        error: error instanceof Error ? error.message : 'Unknown error',
        leadId: options.leadId,
        campaignId: options.campaignId,
      });
      throw error;
    }
  }

  /**
   * Update an existing appointment
   * @param options Update options
   * @returns The updated appointment
   */
  static async updateAppointment(options: UpdateAppointmentOptions) {
    try {
      logger.info('Updating appointment', {
        id: options.id,
        status: options.status,
      });

      // Update the appointment
      const appointment = await prisma.appointment.update({
        where: { id: options.id },
        data: {
          title: options.title,
          description: options.description,
          startTime: options.startTime,
          endTime: options.endTime,
          status: options.status,
          location: options.location,
          meetingLink: options.meetingLink,
          notes: options.notes,
          metadata: options.metadata,
        },
      });

      // If the appointment time changed, update the reminders
      if (options.startTime) {
        await ReminderService.updateRemindersForAppointment(options.id);
      }

      // If the status changed to cancelled, cancel all pending reminders
      if (options.status === 'cancelled') {
        await ReminderService.cancelRemindersForAppointment(options.id);
      }

      return appointment;
    } catch (error) {
      logger.error('Error updating appointment', {
        error: error instanceof Error ? error.message : 'Unknown error',
        id: options.id,
      });
      throw error;
    }
  }

  /**
   * Get an appointment by ID
   * @param id Appointment ID
   * @returns The appointment
   */
  static async getAppointment(id: string) {
    return prisma.appointment.findUnique({
      where: { id },
      include: {
        lead: true,
        agent: true,
        reminders: true,
      },
    });
  }

  /**
   * Get all appointments for a lead
   * @param leadId Lead ID
   * @returns List of appointments
   */
  static async getAppointmentsForLead(leadId: string) {
    return prisma.appointment.findMany({
      where: { leadId },
      include: {
        agent: true,
        reminders: true,
      },
      orderBy: {
        startTime: 'asc',
      },
    });
  }

  /**
   * Get all appointments for a campaign
   * @param campaignId Campaign ID
   * @returns List of appointments
   */
  static async getAppointmentsForCampaign(campaignId: string) {
    return prisma.appointment.findMany({
      where: { campaignId },
      include: {
        lead: true,
        agent: true,
        reminders: true,
      },
      orderBy: {
        startTime: 'asc',
      },
    });
  }

  /**
   * Get upcoming appointments
   * @param hours Hours in the future to look
   * @returns List of upcoming appointments
   */
  static async getUpcomingAppointments(hours: number = 24) {
    const now = new Date();
    const future = new Date(now.getTime() + hours * 60 * 60 * 1000);

    return prisma.appointment.findMany({
      where: {
        startTime: {
          gte: now,
          lte: future,
        },
        status: 'scheduled',
      },
      include: {
        lead: true,
        agent: true,
        reminders: true,
      },
      orderBy: {
        startTime: 'asc',
      },
    });
  }
}
