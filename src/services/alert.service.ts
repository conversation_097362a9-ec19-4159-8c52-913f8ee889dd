import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export type AlertSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
export type AlertType =
  | 'HIGH_ERROR_RATE'
  | 'HIGH_LATENCY'
  | 'SYSTEM_DEGRADED'
  | 'HIGH_RESOURCE_USAGE'
  | 'SECURITY_ISSUE'
  | 'HIGH_BOUNCE_RATE'
  | 'HIGH_SPAM_COMPLAINTS'
  | 'SENDING_VOLUME_LIMIT';

interface AlertOptions {
  type: AlertType;
  severity: AlertSeverity;
  message: string;
  metadata?: Record<string, any>;
  emailAccountId?: string;
}

export class AlertService {
  static async createAlert(options: AlertOptions) {
    // Create a notification instead of an alert since there's no Alert model
    const notification = await prisma.notification.create({
      data: {
        title: options.message,
        message: options.metadata ? JSON.stringify(options.metadata) : '',
        type: options.severity.toLowerCase(),
        isRead: false,
        organizationId: options.emailAccountId ? await this.getOrganizationIdFromEmailAccount(options.emailAccountId) : '1', // Default org ID
      },
    });

    logger.warn('Alert created as notification:', {
      notificationId: notification.id,
      ...options,
    });

    // Send notifications based on severity
    if (options.severity === 'HIGH' || options.severity === 'CRITICAL') {
      await this.sendNotifications(notification);
    }

    return notification;
  }

  private static async sendNotifications(notification: any) {
    // Implement notification sending (email, Slack, etc.)
    // This is a placeholder for the notification logic
    logger.info('Sending alert notifications', { notificationId: notification.id });
  }

  static async getActiveAlerts() {
    return prisma.notification.findMany({
      where: {
        isRead: false,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  static async resolveAlert(notificationId: string) {
    return prisma.notification.update({
      where: { id: notificationId },
      data: { isRead: true },
    });
  }

  private static async getOrganizationIdFromEmailAccount(emailAccountId: string) {
    const emailAccount = await prisma.emailAccount.findUnique({
      where: { id: emailAccountId },
      select: { userId: true }
    });

    if (!emailAccount) {
      return '1'; // Default organization ID
    }

    // Get the first organization the user is a member of
    const orgMember = await prisma.organizationMember.findFirst({
      where: { userId: emailAccount.userId },
      select: { organizationId: true }
    });

    return orgMember?.organizationId || '1';
  }
}
