import { prisma } from '@/lib/prisma';
import { stripe } from '@/config/stripe';
import { logger } from '@/lib/logger';
import { SubscriptionTier, SUBSCRIPTION_PLANS } from '@/config/subscription-plans';
import { addDays, subDays, subHours } from 'date-fns';

export type UsageMetric = {
  emailsSent: number;
  emailAccounts: number;
  storageUsed: number;
  dailyEmailsSent: number; // Rolling 24-hour window
};

export class EnhancedUsageService {
  /**
   * Track an email being sent
   * Updates both monthly and daily usage
   */
  static async trackEmailSent(organizationId: string) {
    // Update monthly usage
    await this.updateMonthlyUsageMetric(organizationId, 'emailsSent');

    // Update daily usage (rolling 24-hour window)
    await this.updateDailyEmailUsage(organizationId);
  }

  /**
   * Update monthly usage metrics
   */
  private static async updateMonthlyUsageMetric(organizationId: string, metric: keyof Omit<UsageMetric, 'dailyEmailsSent'>) {
    const date = new Date();
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);

    try {
      await prisma.usage.upsert({
        where: {
          organizationId_period: {
            organizationId,
            period: startOfMonth,
          },
        },
        update: {
          [metric]: { increment: 1 },
        },
        create: {
          organizationId,
          period: startOfMonth,
          emailsSent: metric === 'emailsSent' ? 1 : 0,
          emailAccounts: metric === 'emailAccounts' ? 1 : 0,
          storageUsed: 0,
        },
      });
    } catch (error) {
      logger.error('Error updating monthly usage metric:', error);
      // Don't throw the error to prevent campaign processing from failing
    }
  }

  /**
   * Update daily email usage (rolling 24-hour window)
   */
  private static async updateDailyEmailUsage(organizationId: string) {
    const now = new Date();

    try {
      // Create or update today's usage record
      await prisma.dailyUsage.upsert({
        where: {
          id: `${organizationId}_${now.toISOString().split('T')[0]}`,
        },
        update: {
          emailsSent: { increment: 1 },
          updatedAt: now,
        },
        create: {
          id: `${organizationId}_${now.toISOString().split('T')[0]}`,
          organizationId,
          date: now,
          emailsSent: 1,
        },
      });
    } catch (error) {
      logger.error('Error updating daily usage:', error);
      // Don't throw the error to prevent campaign processing from failing
    }
  }

  /**
   * Get current usage metrics including daily usage (rolling 24-hour window)
   */
  static async getCurrentUsage(organizationId: string): Promise<UsageMetric> {
    // Get monthly usage
    const date = new Date();
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);

    const monthlyUsage = await prisma.usage.findUnique({
      where: {
        organizationId_period: {
          organizationId,
          period: startOfMonth,
        },
      },
    });

    // Get daily usage (rolling 24-hour window)
    const twentyFourHoursAgo = subHours(date, 24);
    const dailyUsage = await prisma.dailyUsage.findMany({
      where: {
        organizationId,
        date: {
          gte: twentyFourHoursAgo,
        },
      },
    });

    // Calculate total emails sent in the last 24 hours
    const dailyEmailsSent = dailyUsage.reduce((total, record) => total + record.emailsSent, 0);

    // Report usage to Stripe
    const usage = {
      emailsSent: monthlyUsage?.emailsSent ?? 0,
      emailAccounts: monthlyUsage?.emailAccounts ?? 0,
      storageUsed: monthlyUsage?.storageUsed ?? 0,
      dailyEmailsSent,
    };

    await this.reportUsageToStripe(organizationId, usage);
    return usage;
  }

  /**
   * Check if the organization is within its usage limits
   */
  static async checkUsageLimits(organizationId: string): Promise<{
    withinLimits: boolean;
    limitedBy?: string[];
    currentUsage: UsageMetric;
  }> {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: { subscription: true },
    });

    if (!organization) {
      return {
        withinLimits: false,
        limitedBy: ['organization'],
        currentUsage: { emailsSent: 0, emailAccounts: 0, storageUsed: 0, dailyEmailsSent: 0 }
      };
    }

    // Default to FREE tier if no subscription
    const tier = organization.subscription?.tier || organization.subscriptionTier || SubscriptionTier.FREE;
    const planLimits = SUBSCRIPTION_PLANS[tier].limits;

    const currentUsage = await this.getCurrentUsage(organizationId);
    const limitedBy: string[] = [];

    // Check daily email limit
    if (currentUsage.dailyEmailsSent >= planLimits.dailyEmails) {
      limitedBy.push('dailyEmails');
    }

    // Check monthly email limit
    if (currentUsage.emailsSent >= planLimits.monthlyEmails) {
      limitedBy.push('monthlyEmails');
    }

    // Check email accounts limit
    if (currentUsage.emailAccounts >= planLimits.emailAccounts) {
      limitedBy.push('emailAccounts');
    }

    return {
      withinLimits: limitedBy.length === 0,
      limitedBy: limitedBy.length > 0 ? limitedBy : undefined,
      currentUsage,
    };
  }

  /**
   * Check if a specific AI feature is available for the organization
   */
  static async isAIFeatureAvailable(organizationId: string, feature: import('@/config/subscription-plans').AIFeatureType): Promise<boolean> {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: { subscription: true },
    });

    if (!organization) return false;

    // Default to FREE tier if no subscription
    const tier = organization.subscription?.tier || organization.subscriptionTier || SubscriptionTier.FREE;
    return SUBSCRIPTION_PLANS[tier].aiFeatures[feature];
  }

  /**
   * Report usage metrics to Stripe for billing
   */
  static async reportUsageToStripe(organizationId: string, usage: UsageMetric) {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: { subscription: true },
    });

    if (!organization?.subscription?.stripeSubscriptionId) return;

    // Report each metric to Stripe
    const subscriptionItems = await stripe.subscriptionItems.list({
      subscription: organization.subscription.stripeSubscriptionId,
    });

    for (const item of subscriptionItems.data) {
      // Match the subscription item with the metric
      const metricId = item.price.metadata.usageMetric;
      if (!metricId || !['emailsSent', 'emailAccounts', 'storageUsed'].includes(metricId)) continue;

      const value = usage[metricId as keyof Omit<UsageMetric, 'dailyEmailsSent'>];

      await stripe.subscriptionItems.createUsageRecord(
        item.id,
        {
          quantity: value,
          timestamp: Math.floor(Date.now() / 1000),
          action: 'set', // or 'increment' based on your needs
        }
      );
    }
  }

  /**
   * Create a notification when approaching usage limits
   */
  static async createUsageLimitNotification(organizationId: string, usageType: string, currentValue: number, limit: number) {
    const percentUsed = Math.round((currentValue / limit) * 100);

    // Only create notifications at specific thresholds
    if (percentUsed !== 80 && percentUsed !== 90 && percentUsed !== 95 && percentUsed !== 100) {
      return;
    }

    let type = 'info';
    if (percentUsed >= 95) type = 'error';
    else if (percentUsed >= 90) type = 'warning';
    else if (percentUsed >= 80) type = 'info';

    const message = percentUsed >= 100
      ? `You've reached your ${usageType} limit. Please upgrade your plan to continue.`
      : `You're using ${percentUsed}% of your ${usageType} limit.`;

    await prisma.notification.create({
      data: {
        organizationId,
        title: `Usage Limit Alert: ${usageType}`,
        message,
        type,
      },
    });

    // Also send an email notification if at 90% or higher
    if (percentUsed >= 90) {
      // TODO: Implement email notification
      logger.info(`Should send email notification for ${usageType} at ${percentUsed}%`);
    }
  }
}
