import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { ChromaService } from './chroma.service';
// import * as Tesseract from 'tesseract.js'; // TODO: Fix tesseract.js import
import * as cheerio from 'cheerio';
import * as fs from 'fs';

/**
 * Service for managing knowledge bases for AI agents with vector database support
 */
export class KnowledgeBaseService {

  /**
   * Split text into chunks for vector storage
   */
  private static splitTextIntoChunks(text: string, chunkSize: number = 1000, overlap: number = 200): string[] {
    // Validate input parameters
    if (chunkSize <= 0 || overlap < 0 || overlap >= chunkSize) {
      throw new Error(`Invalid chunk parameters: chunkSize=${chunkSize}, overlap=${overlap}`);
    }

    // Check if text is too large (JavaScript array max length is 2^32 - 1)
    const maxChunks = Math.floor(4294967295 / 2); // Conservative limit
    const estimatedChunks = Math.ceil(text.length / (chunkSize - overlap));

    if (estimatedChunks > maxChunks) {
      throw new Error(`Text too large: would create ${estimatedChunks} chunks, max allowed is ${maxChunks}`);
    }

    const chunks: string[] = [];
    let start = 0;
    let chunkCount = 0;

    while (start < text.length && chunkCount < maxChunks) {
      const end = Math.min(start + chunkSize, text.length);
      const chunk = text.slice(start, end);

      if (chunk.trim().length > 0) { // Only add non-empty chunks
        chunks.push(chunk);
        chunkCount++;
      }

      // Calculate next start position
      const nextStart = end - overlap;

      // If we've reached the end or the next start would be the same or past the current end, break
      if (end >= text.length || nextStart >= end || nextStart <= start) {
        break;
      }

      start = Math.max(nextStart, start + 1); // Ensure we always move forward
    }

    return chunks;
  }

  /**
   * Get or create the organization's knowledge base
   * @param organizationId Organization ID
   * @returns The organization's knowledge base
   */
  static async getOrCreateKnowledgeBase(organizationId: string) {
    try {
      logger.info(`Getting or creating knowledge base for organization: ${organizationId}`);

      // Check if knowledge base already exists
      let knowledgeBase = await prisma.knowledgeBase.findUnique({
        where: { organizationId },
        include: {
          documents: true,
          agents: {
            include: {
              agent: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      if (knowledgeBase) {
        logger.info(`Found existing knowledge base: ${knowledgeBase.id}`);
        return knowledgeBase;
      }

      // Create a unique collection name for Chroma
      // Create collection in Chroma (this will create tenant, database, and collection)
      const collection = await ChromaService.createOrGetCollection(organizationId);

      // Create the knowledge base in the database
      knowledgeBase = await prisma.knowledgeBase.create({
        data: {
          chromaCollectionId: collection.id,
          organizationId,
        },
        include: {
          documents: true,
          agents: {
            include: {
              agent: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      logger.info(`Created new knowledge base: ${knowledgeBase.id}`);
      return knowledgeBase;
    } catch (error) {
      logger.error(`Error getting/creating knowledge base for organization: ${organizationId}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Add a document to the organization's knowledge base
   * @param organizationId Organization ID
   * @param title Document title
   * @param content Document content
   * @param type Document type
   * @param metadata Additional metadata
   * @returns Success status
   */
  static async addDocument(
    organizationId: string,
    title: string,
    content: string,
    type: 'TEXT' | 'PDF' | 'IMAGE' | 'WEB_URL' | 'DOCUMENT',
    metadata?: Record<string, any>
  ) {
    try {
      logger.info(`Adding document to knowledge base for organization: ${organizationId}`);

      // Get or create the knowledge base
      const knowledgeBase = await this.getOrCreateKnowledgeBase(organizationId);

      if (!knowledgeBase.chromaCollectionId) {
        throw new Error(`Knowledge base has no Chroma collection: ${knowledgeBase.id}`);
      }

      // Split content into chunks
      const chunks = this.splitTextIntoChunks(content);

      // Create documents for each chunk
      const documents = chunks.map((chunk, index) => ({
        id: `${knowledgeBase.id}_${Date.now()}_${index}`,
        content: chunk,
        metadata: {
          ...metadata,
          knowledgeBaseId: knowledgeBase.id,
          organizationId,
          type,
          title,
          chunkIndex: index,
          timestamp: new Date().toISOString(),
        },
      }));

      // Add documents to Chroma
      await ChromaService.addDocuments(organizationId, documents);

      // Store document records in database
      const dbDocuments = await Promise.all(
        documents.map(doc =>
          prisma.knowledgeBaseDocument.create({
            data: {
              knowledgeBaseId: knowledgeBase.id,
              title,
              content: doc.content,
              type,
              metadata: doc.metadata,
              vectorId: doc.id,
              chunkIndex: doc.metadata.chunkIndex,
              sourceUrl: metadata?.sourceUrl,
              fileName: metadata?.fileName,
              fileSize: metadata?.fileSize,
              mimeType: metadata?.mimeType,
            },
          })
        )
      );

      // Update vector count
      await prisma.knowledgeBase.update({
        where: { id: knowledgeBase.id },
        data: {
          vectorCount: {
            increment: documents.length,
          },
        },
      });

      logger.info(`Added ${documents.length} chunks to knowledge base: ${knowledgeBase.id}`);
      return {
        success: true,
        documentsAdded: documents.length,
        documents: dbDocuments,
        knowledgeBase
      };
    } catch (error) {
      logger.error(`Error adding document to knowledge base for organization: ${organizationId}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Query organization's knowledge base using vector search
   * @param organizationId Organization ID
   * @param query Query string
   * @param nResults Number of results to return
   * @returns Query results
   */
  static async queryKnowledgeBase(
    organizationId: string,
    query: string,
    nResults: number = 5
  ) {
    try {
      logger.info(`Querying knowledge base for organization ${organizationId} with query: "${query}"`);

      // Get the organization's knowledge base
      const knowledgeBase = await prisma.knowledgeBase.findUnique({
        where: { organizationId },
      });

      if (!knowledgeBase || !knowledgeBase.chromaCollectionId) {
        logger.warn(`No knowledge base found for organization ${organizationId}`);
        return [];
      }

      try {
        // Query the Chroma collection
        const results = await ChromaService.queryCollection(
          organizationId,
          query,
          nResults
        );

        const allResults = [];

        if (results.documents && results.documents[0]) {
          const documents = results.documents[0];
          const metadatas = results.metadatas?.[0] || [];
          const distances = results.distances?.[0] || [];

          for (let i = 0; i < documents.length; i++) {
            allResults.push({
              content: documents[i],
              metadata: metadatas[i] || {},
              distance: distances[i] || 0,
              score: 1 - (distances[i] || 0), // Convert distance to similarity score
              knowledgeBaseId: knowledgeBase.id,
            });
          }
        }

        // Sort by score (highest first)
        allResults.sort((a, b) => b.score - a.score);

        logger.info(`Found ${allResults.length} relevant results for query: "${query}"`);
        return allResults;
      } catch (error) {
        logger.error(`Error querying knowledge base for organization ${organizationId}`, {
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        return [];
      }
    } catch (error) {
      logger.error(`Error querying knowledge base for organization ${organizationId}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        query,
      });
      throw error;
    }
  }

  /**
   * Process PDF file and extract text
   * @param filePath Path to PDF file
   * @returns Extracted text
   */
  static async processPDF(filePath: string): Promise<string> {
    try {
      const pdfParse = require('pdf-parse');
      const dataBuffer = fs.readFileSync(filePath);
      const data = await pdfParse(dataBuffer);
      return data.text;
    } catch (error) {
      logger.error(`Error processing PDF: ${filePath}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Process image file and extract text using OCR
   * @param filePath Path to image file
   * @returns Extracted text
   */
  static async processImage(filePath: string): Promise<string> {
    try {
      // TODO: Fix tesseract.js import and usage
      logger.warn('OCR processing temporarily disabled', { filePath });
      return 'OCR processing temporarily disabled';
    } catch (error) {
      logger.error(`Error processing image: ${filePath}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Scrape content from a web URL
   * @param url URL to scrape
   * @returns Extracted text content
   */
  static async scrapeWebContent(url: string): Promise<string> {
    try {
      const response = await fetch(url);
      const html = await response.text();
      const $ = cheerio.load(html);

      // Remove script and style elements
      $('script, style').remove();

      // Extract text content
      const text = $('body').text().replace(/\s+/g, ' ').trim();
      return text;
    } catch (error) {
      logger.error(`Error scraping web content: ${url}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Link a knowledge base to an agent
   * @param knowledgeBaseId Knowledge base ID
   * @param agentId Agent ID
   * @returns Success status
   */
  static async linkToAgent(knowledgeBaseId: string, agentId: string) {
    try {
      // Check if link already exists
      const existingLink = await prisma.knowledgeBaseAgent.findUnique({
        where: {
          knowledgeBaseId_agentId: {
            knowledgeBaseId,
            agentId,
          },
        },
      });

      if (existingLink) {
        return { success: true, message: 'Knowledge base already linked to agent' };
      }

      // Create the link
      await prisma.knowledgeBaseAgent.create({
        data: {
          knowledgeBaseId,
          agentId,
        },
      });

      logger.info(`Linked knowledge base ${knowledgeBaseId} to agent ${agentId}`);
      return { success: true, message: 'Knowledge base linked to agent successfully' };
    } catch (error) {
      logger.error(`Error linking knowledge base to agent`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        knowledgeBaseId,
        agentId,
      });
      throw error;
    }
  }

  /**
   * Unlink a knowledge base from an agent
   * @param knowledgeBaseId Knowledge base ID
   * @param agentId Agent ID
   * @returns Success status
   */
  static async unlinkFromAgent(knowledgeBaseId: string, agentId: string) {
    try {
      await prisma.knowledgeBaseAgent.deleteMany({
        where: {
          knowledgeBaseId,
          agentId,
        },
      });

      logger.info(`Unlinked knowledge base ${knowledgeBaseId} from agent ${agentId}`);
      return { success: true, message: 'Knowledge base unlinked from agent successfully' };
    } catch (error) {
      logger.error(`Error unlinking knowledge base from agent`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        knowledgeBaseId,
        agentId,
      });
      throw error;
    }
  }

  /**
   * Delete document vectors from the vector store
   * @param organizationId Organization ID
   * @param documentId Document ID
   */
  static async deleteDocumentVectors(organizationId: string, documentId: string): Promise<void> {
    try {
      logger.info('Deleting document vectors', { organizationId, documentId });

      // Get all document chunks for this document
      const documentChunks = await prisma.knowledgeBaseDocument.findMany({
        where: {
          id: documentId,
          knowledgeBase: {
            organizationId,
          },
        },
      });

      if (documentChunks.length === 0) {
        logger.warn('No document chunks found to delete', { organizationId, documentId });
        return;
      }

      // Delete vectors from Chroma
      const vectorIds = documentChunks
        .map(chunk => chunk.vectorId)
        .filter(id => id !== null) as string[];

      if (vectorIds.length > 0) {
        await ChromaService.deleteDocuments(organizationId, vectorIds);
        logger.info('Deleted vectors from Chroma', {
          organizationId,
          documentId,
          vectorCount: vectorIds.length,
        });
      }
    } catch (error) {
      logger.error('Error deleting document vectors', {
        organizationId,
        documentId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Add document to vector store
   * @param organizationId Organization ID
   * @param documentId Document ID
   * @param title Document title
   * @param content Document content
   * @param type Document type
   * @param metadata Document metadata
   */
  static async addDocumentToVectorStore(
    organizationId: string,
    documentId: string,
    title: string,
    content: string,
    type: string,
    metadata: any = {}
  ): Promise<void> {
    try {
      logger.info('Adding document to vector store', { organizationId, documentId, title });

      // Get or create knowledge base
      const knowledgeBase = await this.getOrCreateKnowledgeBase(organizationId);

      // Split content into chunks
      const chunks = this.splitTextIntoChunks(content);

      // Prepare documents for vector storage
      const documents = chunks.map((chunk, index) => ({
        id: `${documentId}_chunk_${index}`,
        content: chunk,
        metadata: {
          documentId,
          title,
          type,
          chunkIndex: index,
          ...metadata,
        },
      }));

      // Add to vector store
      await ChromaService.addDocuments(organizationId, documents);

      // Update vector count
      await prisma.knowledgeBase.update({
        where: { id: knowledgeBase.id },
        data: {
          vectorCount: {
            increment: chunks.length,
          },
        },
      });

      logger.info('Added document to vector store', {
        organizationId,
        documentId,
        chunkCount: chunks.length,
      });
    } catch (error) {
      logger.error('Error adding document to vector store', {
        organizationId,
        documentId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Generate a response using RAG (Retrieval Augmented Generation)
   * @param agentId Agent ID
   * @param query User query
   * @returns Generated response
   */
  static async generateResponse(agentId: string, query: string): Promise<string> {
    try {
      logger.info(`Generating response for agent ${agentId} with query: "${query}"`);

      // Get the agent and its organization
      const agent = await prisma.agent.findUnique({
        where: { id: agentId },
        include: {
          knowledgeBases: {
            include: {
              knowledgeBase: true,
            },
          },
        },
      });

      if (!agent) {
        throw new Error(`Agent not found: ${agentId}`);
      }

      const organizationId = agent.organizationId;

      // Query the knowledge base for relevant content
      const searchResults = await this.queryKnowledgeBase(organizationId, query, 5);

      let knowledgeBaseContent = '';
      if (searchResults.length > 0) {
        knowledgeBaseContent = searchResults
          .map((result: any) => result.content)
          .join('\n\n');
      }

      // Prepare the prompt with knowledge base content
      const systemPrompt = agent.systemPrompt || 'You are a helpful AI assistant.';
      const promptContent = `
${systemPrompt}

${knowledgeBaseContent ? `
Relevant Knowledge Base Content:
${knowledgeBaseContent}

Use the above information to answer the user's question accurately and helpfully.
` : ''}

User Question: ${query}

Please provide a helpful and accurate response based on the available information.`;

      // Generate response using AI provider
      const { AIProviderService } = await import('./ai-provider.service');
      const response = await AIProviderService.getOptimizedResponse(
        promptContent,
        'response',
        60000 // 60 second timeout
      );

      logger.info(`Generated response for agent ${agentId}`, {
        queryLength: query.length,
        responseLength: response.length,
        knowledgeBaseContentLength: knowledgeBaseContent.length,
      });

      return response;
    } catch (error) {
      logger.error(`Error generating response for agent ${agentId}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        query,
      });
      throw error;
    }
  }
}
