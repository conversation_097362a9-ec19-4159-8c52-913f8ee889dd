import { prisma } from '@/lib/prisma';
import redis from '@/lib/redis';
import { logger } from '@/lib/logger';

export interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  checks: {
    database: boolean;
    redis: boolean;
    emailService: boolean;
  };
  metrics: {
    responseTime: number;
    memoryUsage: number;
    cpuLoad: number;
  };
}

export class HealthService {
  static async checkDatabase(): Promise<boolean> {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  static async checkRedis(): Promise<boolean> {
    // Skip Redis health check in development when queue is disabled
    const shouldCheckRedis = process.env.NODE_ENV === 'production' ||
                             process.env.ENABLE_QUEUE_WORKER === 'true' ||
                             process.env.VERCEL_ENV === 'production';

    if (!shouldCheckRedis) {
      logger.info('Redis health check skipped (queue disabled in development)');
      return true; // Return true to avoid marking the service as unhealthy
    }

    try {
      await redis.ping();
      return true;
    } catch (error) {
      logger.error('Redis health check failed:', error);
      return false;
    }
  }

  static async checkEmailService(): Promise<boolean> {
    try {
      // Implement your email service health check here
      return true;
    } catch (error) {
      logger.error('Email service health check failed:', error);
      return false;
    }
  }

  static async getSystemMetrics() {
    return {
      responseTime: process.hrtime()[1] / 1000000, // Nanoseconds to milliseconds
      memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024, // Bytes to MB
      cpuLoad: process.cpuUsage().user / 1000000, // Microseconds to seconds
    };
  }

  static async performHealthCheck(): Promise<HealthCheckResult> {
    const startTime = process.hrtime();

    const [dbHealth, redisHealth, emailHealth] = await Promise.all([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkEmailService(),
    ]);

    const metrics = await this.getSystemMetrics();

    const checks = {
      database: dbHealth,
      redis: redisHealth,
      emailService: emailHealth,
    };

    const status = Object.values(checks).every(Boolean)
      ? 'healthy'
      : Object.values(checks).some(Boolean)
      ? 'degraded'
      : 'unhealthy';

    return { status, checks, metrics };
  }
}