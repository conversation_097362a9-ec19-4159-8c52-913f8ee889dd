import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { startOfDay, endOfDay, subDays, startOfMonth, endOfMonth, subMonths } from 'date-fns';

export interface AnalyticsData {
  revenue: {
    total: number;
    monthly: number;
    growth: number;
  };
  subscriptions: {
    total: number;
    new: number;
    canceled: number;
    upgrades: number;
    downgrades: number;
  };
  usage: {
    totalEmailsSent: number;
    activeUsers: number;
    totalOrganizations: number;
  };
  features: {
    aiAgentsUsage: number;
    knowledgeBaseUsage: number;
    campaignsCreated: number;
  };
  packages: Array<{
    id: string;
    name: string;
    organizationCount: number;
    revenue: number;
  }>;
}

export interface DashboardMetrics {
  totalRevenue: number;
  monthlyRecurringRevenue: number;
  totalUsers: number;
  totalOrganizations: number;
  totalEmailsSent: number;
  activeSubscriptions: number;
  churnRate: number;
  averageRevenuePerUser: number;
}

export class BusinessAnalyticsService {
  /**
   * Get comprehensive analytics data for admin dashboard
   */
  static async getAnalyticsData(days: number = 30): Promise<AnalyticsData> {
    try {
      const endDate = new Date();
      const startDate = subDays(endDate, days);

      // Get revenue data
      const revenue = await this.getRevenueAnalytics(startDate, endDate);
      
      // Get subscription data
      const subscriptions = await this.getSubscriptionAnalytics(startDate, endDate);
      
      // Get usage data
      const usage = await this.getUsageAnalytics(startDate, endDate);
      
      // Get feature usage data
      const features = await this.getFeatureAnalytics(startDate, endDate);
      
      // Get package analytics
      const packages = await this.getPackageAnalytics();

      return {
        revenue,
        subscriptions,
        usage,
        features,
        packages
      };
    } catch (error) {
      logger.error('Error getting analytics data', {
        error: error instanceof Error ? error.message : 'Unknown error',
        days
      });
      throw new Error('Failed to get analytics data');
    }
  }

  /**
   * Get dashboard metrics
   */
  static async getDashboardMetrics(): Promise<DashboardMetrics> {
    try {
      const currentMonth = new Date();
      const lastMonth = subMonths(currentMonth, 1);

      // Total revenue (all time)
      const totalRevenue = await prisma.packageSubscription.aggregate({
        where: {
          isActive: true
        },
        _sum: {
          package: {
            price: true
          }
        }
      });

      // Monthly recurring revenue
      const monthlyRevenue = await this.getMonthlyRecurringRevenue();

      // Total users and organizations
      const [totalUsers, totalOrganizations] = await Promise.all([
        prisma.user.count(),
        prisma.organization.count()
      ]);

      // Total emails sent
      const emailsResult = await prisma.dailyUsage.aggregate({
        _sum: {
          emailsSent: true
        }
      });

      // Active subscriptions
      const activeSubscriptions = await prisma.packageSubscription.count({
        where: {
          isActive: true
        }
      });

      // Churn rate (simplified calculation)
      const churnRate = await this.calculateChurnRate();

      // Average revenue per user
      const averageRevenuePerUser = totalUsers > 0 ? monthlyRevenue / totalUsers : 0;

      return {
        totalRevenue: totalRevenue._sum.package?.price || 0,
        monthlyRecurringRevenue: monthlyRevenue,
        totalUsers,
        totalOrganizations,
        totalEmailsSent: emailsResult._sum.emailsSent || 0,
        activeSubscriptions,
        churnRate,
        averageRevenuePerUser
      };
    } catch (error) {
      logger.error('Error getting dashboard metrics', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to get dashboard metrics');
    }
  }

  /**
   * Get revenue analytics
   */
  private static async getRevenueAnalytics(startDate: Date, endDate: Date) {
    try {
      // Current period revenue
      const currentRevenue = await prisma.packageSubscription.aggregate({
        where: {
          isActive: true,
          startDate: {
            gte: startDate,
            lte: endDate
          }
        },
        _sum: {
          package: {
            price: true
          }
        }
      });

      // Previous period for growth calculation
      const previousStartDate = subDays(startDate, endDate.getTime() - startDate.getTime());
      const previousRevenue = await prisma.packageSubscription.aggregate({
        where: {
          isActive: true,
          startDate: {
            gte: previousStartDate,
            lte: startDate
          }
        },
        _sum: {
          package: {
            price: true
          }
        }
      });

      const current = currentRevenue._sum.package?.price || 0;
      const previous = previousRevenue._sum.package?.price || 0;
      const growth = previous > 0 ? ((current - previous) / previous) * 100 : 0;

      // Monthly revenue
      const monthlyRevenue = await this.getMonthlyRecurringRevenue();

      return {
        total: current,
        monthly: monthlyRevenue,
        growth
      };
    } catch (error) {
      logger.error('Error getting revenue analytics', { error });
      return { total: 0, monthly: 0, growth: 0 };
    }
  }

  /**
   * Get subscription analytics
   */
  private static async getSubscriptionAnalytics(startDate: Date, endDate: Date) {
    try {
      const [total, newSubs, canceled] = await Promise.all([
        prisma.packageSubscription.count({
          where: { isActive: true }
        }),
        prisma.packageSubscription.count({
          where: {
            startDate: {
              gte: startDate,
              lte: endDate
            }
          }
        }),
        prisma.packageSubscription.count({
          where: {
            isActive: false,
            endDate: {
              gte: startDate,
              lte: endDate
            }
          }
        })
      ]);

      return {
        total,
        new: newSubs,
        canceled,
        upgrades: 0, // TODO: Implement upgrade tracking
        downgrades: 0 // TODO: Implement downgrade tracking
      };
    } catch (error) {
      logger.error('Error getting subscription analytics', { error });
      return { total: 0, new: 0, canceled: 0, upgrades: 0, downgrades: 0 };
    }
  }

  /**
   * Get usage analytics
   */
  private static async getUsageAnalytics(startDate: Date, endDate: Date) {
    try {
      const [emailsResult, activeUsers, totalOrganizations] = await Promise.all([
        prisma.dailyUsage.aggregate({
          where: {
            date: {
              gte: startDate,
              lte: endDate
            }
          },
          _sum: {
            emailsSent: true
          }
        }),
        prisma.user.count({
          where: {
            updatedAt: {
              gte: startDate
            }
          }
        }),
        prisma.organization.count()
      ]);

      return {
        totalEmailsSent: emailsResult._sum.emailsSent || 0,
        activeUsers,
        totalOrganizations
      };
    } catch (error) {
      logger.error('Error getting usage analytics', { error });
      return { totalEmailsSent: 0, activeUsers: 0, totalOrganizations: 0 };
    }
  }

  /**
   * Get feature analytics
   */
  private static async getFeatureAnalytics(startDate: Date, endDate: Date) {
    try {
      const [aiAgentsUsage, knowledgeBaseUsage, campaignsCreated] = await Promise.all([
        prisma.agent.count({
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          }
        }),
        prisma.knowledgeBase.count({
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          }
        }),
        prisma.campaign.count({
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          }
        })
      ]);

      return {
        aiAgentsUsage,
        knowledgeBaseUsage,
        campaignsCreated
      };
    } catch (error) {
      logger.error('Error getting feature analytics', { error });
      return { aiAgentsUsage: 0, knowledgeBaseUsage: 0, campaignsCreated: 0 };
    }
  }

  /**
   * Get package analytics
   */
  private static async getPackageAnalytics() {
    try {
      const packages = await prisma.package.findMany({
        where: {
          status: 'ACTIVE'
        },
        include: {
          _count: {
            select: {
              organizations: true
            }
          },
          subscriptions: {
            where: {
              isActive: true
            }
          }
        }
      });

      return packages.map(pkg => ({
        id: pkg.id,
        name: pkg.name,
        organizationCount: pkg._count.organizations,
        revenue: pkg.subscriptions.length * pkg.price
      }));
    } catch (error) {
      logger.error('Error getting package analytics', { error });
      return [];
    }
  }

  /**
   * Calculate monthly recurring revenue
   */
  private static async getMonthlyRecurringRevenue(): Promise<number> {
    try {
      const activeSubscriptions = await prisma.packageSubscription.findMany({
        where: {
          isActive: true
        },
        include: {
          package: true
        }
      });

      let mrr = 0;
      for (const subscription of activeSubscriptions) {
        const { package: pkg } = subscription;
        
        // Convert to monthly revenue based on billing cycle
        switch (pkg.billingCycle) {
          case 'DAILY':
            mrr += pkg.price * 30;
            break;
          case 'WEEKLY':
            mrr += pkg.price * 4.33; // Average weeks per month
            break;
          case 'BIWEEKLY':
            mrr += pkg.price * 2.17; // Average bi-weeks per month
            break;
          case 'MONTHLY':
            mrr += pkg.price;
            break;
          case 'QUARTERLY':
            mrr += pkg.price / 3;
            break;
          case 'YEARLY':
            mrr += pkg.price / 12;
            break;
        }
      }

      return mrr;
    } catch (error) {
      logger.error('Error calculating MRR', { error });
      return 0;
    }
  }

  /**
   * Calculate churn rate
   */
  private static async calculateChurnRate(): Promise<number> {
    try {
      const currentMonth = startOfMonth(new Date());
      const lastMonth = startOfMonth(subMonths(new Date(), 1));

      const [startOfMonthSubs, endOfMonthSubs, canceledSubs] = await Promise.all([
        prisma.packageSubscription.count({
          where: {
            startDate: {
              lt: currentMonth
            },
            isActive: true
          }
        }),
        prisma.packageSubscription.count({
          where: {
            isActive: true
          }
        }),
        prisma.packageSubscription.count({
          where: {
            endDate: {
              gte: currentMonth
            },
            isActive: false
          }
        })
      ]);

      return startOfMonthSubs > 0 ? (canceledSubs / startOfMonthSubs) * 100 : 0;
    } catch (error) {
      logger.error('Error calculating churn rate', { error });
      return 0;
    }
  }

  /**
   * Record daily analytics
   */
  static async recordDailyAnalytics() {
    try {
      const today = startOfDay(new Date());
      
      // Check if analytics for today already exist
      const existingAnalytics = await prisma.businessAnalytics.findFirst({
        where: {
          date: today
        }
      });

      if (existingAnalytics) {
        logger.info('Analytics for today already recorded');
        return;
      }

      const metrics = await this.getDashboardMetrics();
      const analytics = await this.getAnalyticsData(1); // Last 24 hours

      await prisma.businessAnalytics.create({
        data: {
          date: today,
          totalRevenue: metrics.totalRevenue,
          newSubscriptions: analytics.subscriptions.new,
          canceledSubscriptions: analytics.subscriptions.canceled,
          upgrades: analytics.subscriptions.upgrades,
          downgrades: analytics.subscriptions.downgrades,
          totalEmailsSent: analytics.usage.totalEmailsSent,
          totalActiveUsers: analytics.usage.activeUsers,
          totalOrganizations: analytics.usage.totalOrganizations,
          aiAgentsUsage: analytics.features.aiAgentsUsage,
          knowledgeBaseUsage: analytics.features.knowledgeBaseUsage,
          campaignsCreated: analytics.features.campaignsCreated
        }
      });

      logger.info('Daily analytics recorded successfully');
    } catch (error) {
      logger.error('Error recording daily analytics', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}
