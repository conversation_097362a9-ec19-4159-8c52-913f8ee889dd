import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { stripe } from '@/config/stripe';

export interface BusinessMetrics {
  revenue: {
    total: number;
    monthly: number;
    growth: number;
  };
  subscriptions: {
    total: number;
    active: number;
    cancelled: number;
    byPackage: Array<{
      packageName: string;
      count: number;
      revenue: number;
    }>;
  };
  users: {
    total: number;
    active: number;
    newThisMonth: number;
  };
  usage: {
    totalEmailsSent: number;
    emailsSentThisMonth: number;
    averageEmailsPerUser: number;
  };
  packages: {
    total: number;
    active: number;
    mostPopular: string;
  };
}

/**
 * Business Analytics Service - Provides comprehensive business metrics
 * Only accessible by super admins
 */
export class BusinessAnalyticsService {
  /**
   * Get comprehensive business metrics
   */
  static async getBusinessMetrics(): Promise<BusinessMetrics> {
    try {
      const [
        revenueData,
        subscriptionData,
        userData,
        usageData,
        packageData
      ] = await Promise.all([
        this.getRevenueMetrics(),
        this.getSubscriptionMetrics(),
        this.getUserMetrics(),
        this.getUsageMetrics(),
        this.getPackageMetrics()
      ]);

      return {
        revenue: revenueData,
        subscriptions: subscriptionData,
        users: userData,
        usage: usageData,
        packages: packageData
      };
    } catch (error) {
      logger.error('Error getting business metrics', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get revenue metrics
   */
  private static async getRevenueMetrics(): Promise<BusinessMetrics['revenue']> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

      // Get revenue from Stripe (more accurate)
      let stripeRevenue = { total: 0, monthly: 0, lastMonth: 0 };
      try {
        // Get all successful charges
        const charges = await stripe.charges.list({
          limit: 100,
          created: {
            gte: Math.floor(startOfLastMonth.getTime() / 1000)
          }
        });

        const totalRevenue = charges.data
          .filter(charge => charge.status === 'succeeded')
          .reduce((sum, charge) => sum + charge.amount, 0) / 100; // Convert from cents

        const monthlyRevenue = charges.data
          .filter(charge =>
            charge.status === 'succeeded' &&
            charge.created >= Math.floor(startOfMonth.getTime() / 1000)
          )
          .reduce((sum, charge) => sum + charge.amount, 0) / 100;

        const lastMonthRevenue = charges.data
          .filter(charge =>
            charge.status === 'succeeded' &&
            charge.created >= Math.floor(startOfLastMonth.getTime() / 1000) &&
            charge.created < Math.floor(startOfMonth.getTime() / 1000)
          )
          .reduce((sum, charge) => sum + charge.amount, 0) / 100;

        stripeRevenue = {
          total: totalRevenue,
          monthly: monthlyRevenue,
          lastMonth: lastMonthRevenue
        };
      } catch (stripeError) {
        logger.warn('Could not fetch Stripe revenue data', {
          error: stripeError instanceof Error ? stripeError.message : 'Unknown error'
        });
      }

      // Fallback to database calculation
      const organizationsWithPackages = await prisma.organization.findMany({
        where: {
          packageId: { not: null },
          package: {
            price: { gt: 0 }
          }
        },
        include: {
          package: {
            select: {
              price: true
            }
          }
        }
      });

      const dbTotalRevenue = organizationsWithPackages.reduce((sum, org) => {
        return sum + (org.package?.price || 0);
      }, 0);

      // Use Stripe data if available, otherwise use database
      const totalRevenue = stripeRevenue.total > 0 ? stripeRevenue.total : dbTotalRevenue;
      const monthlyRevenue = stripeRevenue.monthly;
      const lastMonthRevenue = stripeRevenue.lastMonth;

      // Calculate growth
      const growth = lastMonthRevenue > 0
        ? ((monthlyRevenue - lastMonthRevenue) / lastMonthRevenue) * 100
        : 0;

      return {
        total: totalRevenue,
        monthly: monthlyRevenue,
        growth: Math.round(growth * 100) / 100
      };
    } catch (error) {
      logger.error('Error getting revenue metrics', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return { total: 0, monthly: 0, growth: 0 };
    }
  }

  /**
   * Get subscription metrics
   */
  private static async getSubscriptionMetrics(): Promise<BusinessMetrics['subscriptions']> {
    try {
      const totalSubscriptions = await prisma.organization.count({
        where: {
          packageId: { not: null }
        }
      });

      const activeSubscriptions = await prisma.organization.count({
        where: {
          packageId: { not: null },
          package: {
            status: 'ACTIVE'
          }
        }
      });

      const cancelledSubscriptions = await prisma.organization.count({
        where: {
          stripeSubscriptionId: null,
          packageId: { not: null }
        }
      });

      // Get subscriptions by package
      const subscriptionsByPackage = await prisma.organization.groupBy({
        by: ['packageId'],
        where: {
          packageId: { not: null }
        },
        _count: {
          id: true
        }
      });

      const packageDetails = await prisma.package.findMany({
        where: {
          id: {
            in: subscriptionsByPackage.map(sub => sub.packageId).filter(Boolean) as string[]
          }
        },
        select: {
          id: true,
          name: true,
          price: true
        }
      });

      const byPackage = subscriptionsByPackage.map(sub => {
        const packageInfo = packageDetails.find(pkg => pkg.id === sub.packageId);
        return {
          packageName: packageInfo?.name || 'Unknown',
          count: sub._count.id,
          revenue: (packageInfo?.price || 0) * sub._count.id
        };
      });

      return {
        total: totalSubscriptions,
        active: activeSubscriptions,
        cancelled: cancelledSubscriptions,
        byPackage
      };
    } catch (error) {
      logger.error('Error getting subscription metrics', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return { total: 0, active: 0, cancelled: 0, byPackage: [] };
    }
  }

  /**
   * Get user metrics (excluding admin users)
   */
  private static async getUserMetrics(): Promise<BusinessMetrics['users']> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Count total users excluding admin users
      const totalUsers = await prisma.user.count({
        where: {
          OR: [
            { isAdmin: { not: true } },
            { isAdmin: null }
          ]
        }
      });

      // Consider users active if they have an organization or have logged in recently (excluding admins)
      const activeUsers = await prisma.user.count({
        where: {
          AND: [
            {
              OR: [
                { isAdmin: { not: true } },
                { isAdmin: null }
              ]
            },
            {
              OR: [
                { ownedOrganization: { isNot: null } },
                { organizations: { some: {} } }
              ]
            }
          ]
        }
      });

      // Count new users this month (excluding admins)
      const newUsersThisMonth = await prisma.user.count({
        where: {
          AND: [
            {
              OR: [
                { isAdmin: { not: true } },
                { isAdmin: null }
              ]
            },
            {
              createdAt: {
                gte: startOfMonth
              }
            }
          ]
        }
      });

      return {
        total: totalUsers,
        active: activeUsers,
        newThisMonth: newUsersThisMonth
      };
    } catch (error) {
      logger.error('Error getting user metrics', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return { total: 0, active: 0, newThisMonth: 0 };
    }
  }

  /**
   * Get usage metrics
   */
  private static async getUsageMetrics(): Promise<BusinessMetrics['usage']> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Get total emails sent from daily usage
      const totalEmailsResult = await prisma.dailyUsage.aggregate({
        _sum: {
          emailsSent: true
        }
      });

      const emailsThisMonthResult = await prisma.dailyUsage.aggregate({
        where: {
          date: {
            gte: startOfMonth
          }
        },
        _sum: {
          emailsSent: true
        }
      });

      const totalEmailsSent = totalEmailsResult._sum.emailsSent || 0;
      const emailsSentThisMonth = emailsThisMonthResult._sum.emailsSent || 0;

      // Calculate average emails per user (excluding admin users)
      const totalUsers = await prisma.user.count({
        where: {
          OR: [
            { isAdmin: { not: true } },
            { isAdmin: null }
          ]
        }
      });
      const averageEmailsPerUser = totalUsers > 0 ? Math.round(totalEmailsSent / totalUsers) : 0;

      return {
        totalEmailsSent,
        emailsSentThisMonth,
        averageEmailsPerUser
      };
    } catch (error) {
      logger.error('Error getting usage metrics', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return { totalEmailsSent: 0, emailsSentThisMonth: 0, averageEmailsPerUser: 0 };
    }
  }

  /**
   * Get package metrics
   */
  private static async getPackageMetrics(): Promise<BusinessMetrics['packages']> {
    try {
      const totalPackages = await prisma.package.count();

      const activePackages = await prisma.package.count({
        where: {
          status: 'ACTIVE'
        }
      });

      // Find most popular package
      const packageUsage = await prisma.organization.groupBy({
        by: ['packageId'],
        where: {
          packageId: { not: null }
        },
        _count: {
          id: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 1
      });

      let mostPopular = 'None';
      if (packageUsage.length > 0 && packageUsage[0].packageId) {
        const popularPackage = await prisma.package.findUnique({
          where: { id: packageUsage[0].packageId },
          select: { name: true }
        });
        mostPopular = popularPackage?.name || 'Unknown';
      }

      return {
        total: totalPackages,
        active: activePackages,
        mostPopular
      };
    } catch (error) {
      logger.error('Error getting package metrics', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return { total: 0, active: 0, mostPopular: 'None' };
    }
  }

  /**
   * Get detailed organization analytics
   */
  static async getOrganizationAnalytics(): Promise<any[]> {
    try {
      const organizations = await prisma.organization.findMany({
        include: {
          owner: {
            select: {
              email: true,
              name: true,
              createdAt: true
            }
          },
          package: {
            select: {
              name: true,
              price: true
            }
          },
          dailyUsage: {
            select: {
              emailsSent: true,
              date: true
            },
            orderBy: {
              date: 'desc'
            },
            take: 30 // Last 30 days
          },
          _count: {
            select: {
              campaigns: true,
              leads: true,
              agents: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return organizations.map(org => ({
        id: org.id,
        name: org.name,
        owner: org.owner,
        package: org.package,
        totalEmailsSent: org.dailyUsage.reduce((sum, usage) => sum + usage.emailsSent, 0),
        emailsSentLast30Days: org.dailyUsage.reduce((sum, usage) => sum + usage.emailsSent, 0),
        campaignCount: org._count.campaigns,
        leadCount: org._count.leads,
        agentCount: org._count.agents,
        createdAt: org.createdAt
      }));
    } catch (error) {
      logger.error('Error getting organization analytics', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}
