import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { EmailService } from './email.service';
import { Queue } from 'bullmq';
import { getRedisConnection } from './queue.service';

interface CreateReminderOptions {
  appointmentId: string;
  agentId: string;
  type: string; // email, notification
  timeBeforeAppointment: number; // minutes
  content?: string;
}

interface ReminderJob {
  reminderId: string;
  appointmentId: string;
  leadId: string;
  agentId: string;
  type: string;
  content?: string;
}

/**
 * Service for managing appointment reminders
 */
export class ReminderService {
  // Create a queue for reminder jobs
  static reminderQueue = new Queue<ReminderJob>('reminder-queue', {
    connection: getRedisConnection(),
  });

  /**
   * Create a new reminder
   * @param options Reminder options
   * @returns The created reminder
   */
  static async createReminder(options: CreateReminderOptions) {
    try {
      logger.info('Creating reminder', {
        appointmentId: options.appointmentId,
        agentId: options.agentId,
        type: options.type,
        timeBeforeAppointment: options.timeBeforeAppointment,
      });

      // Get the appointment to calculate the reminder time
      const appointment = await prisma.appointment.findUnique({
        where: { id: options.appointmentId },
        include: {
          lead: true,
        },
      });

      if (!appointment) {
        throw new Error(`Appointment not found: ${options.appointmentId}`);
      }

      // Create the reminder
      const reminder = await prisma.reminder.create({
        data: {
          appointmentId: options.appointmentId,
          agentId: options.agentId,
          type: options.type,
          timeBeforeAppointment: options.timeBeforeAppointment,
          content: options.content,
        },
      });

      // Schedule the reminder job
      await this.scheduleReminderJob(reminder.id);

      return reminder;
    } catch (error) {
      logger.error('Error creating reminder', {
        error: error instanceof Error ? error.message : 'Unknown error',
        appointmentId: options.appointmentId,
      });
      throw error;
    }
  }

  /**
   * Schedule a reminder job
   * @param reminderId Reminder ID
   */
  static async scheduleReminderJob(reminderId: string) {
    try {
      // Get the reminder with appointment and lead
      const reminder = await prisma.reminder.findUnique({
        where: { id: reminderId },
        include: {
          appointment: {
            include: {
              lead: true,
            },
          },
        },
      });

      if (!reminder || !reminder.appointment) {
        throw new Error(`Reminder or appointment not found: ${reminderId}`);
      }

      // Calculate when to send the reminder
      const reminderTime = new Date(
        reminder.appointment.startTime.getTime() - reminder.timeBeforeAppointment * 60 * 1000
      );

      // If the reminder time is in the past, mark it as failed
      if (reminderTime < new Date()) {
        await prisma.reminder.update({
          where: { id: reminderId },
          data: {
            status: 'failed',
          },
        });
        logger.warn('Reminder time is in the past, marked as failed', {
          reminderId,
          appointmentId: reminder.appointmentId,
          reminderTime,
        });
        return;
      }

      // Calculate delay in milliseconds
      const delay = reminderTime.getTime() - Date.now();

      // Add the job to the queue
      await this.reminderQueue.add(
        'send-reminder',
        {
          reminderId: reminder.id,
          appointmentId: reminder.appointmentId,
          leadId: reminder.appointment.leadId,
          agentId: reminder.agentId,
          type: reminder.type,
          content: reminder.content,
        },
        {
          delay,
          removeOnComplete: true,
          removeOnFail: false,
        }
      );

      logger.info('Scheduled reminder job', {
        reminderId,
        appointmentId: reminder.appointmentId,
        reminderTime,
        delay,
      });
    } catch (error) {
      logger.error('Error scheduling reminder job', {
        error: error instanceof Error ? error.message : 'Unknown error',
        reminderId,
      });
    }
  }

  /**
   * Process a reminder job
   * @param job Reminder job
   */
  static async processReminderJob(job: ReminderJob) {
    try {
      const { reminderId, appointmentId, leadId, agentId, type, content } = job;

      logger.info('Processing reminder job', {
        reminderId,
        appointmentId,
        type,
      });

      // Get the latest appointment data
      const appointment = await prisma.appointment.findUnique({
        where: { id: appointmentId },
        include: {
          lead: true,
        },
      });

      if (!appointment) {
        throw new Error(`Appointment not found: ${appointmentId}`);
      }

      // Skip if appointment is cancelled
      if (appointment.status === 'cancelled') {
        logger.info('Skipping reminder for cancelled appointment', {
          reminderId,
          appointmentId,
        });
        
        await prisma.reminder.update({
          where: { id: reminderId },
          data: {
            status: 'cancelled',
          },
        });
        
        return;
      }

      // Get the agent
      const agent = await prisma.agent.findUnique({
        where: { id: agentId },
        include: {
          campaigns: {
            include: {
              steps: {
                include: {
                  emailAccount: true,
                },
              },
            },
          },
        },
      });

      if (!agent) {
        throw new Error(`Agent not found: ${agentId}`);
      }

      // Find an email account to use
      const emailAccount = agent.campaigns[0]?.steps[0]?.emailAccount;

      if (!emailAccount) {
        throw new Error(`No email account found for agent: ${agentId}`);
      }

      // Send the reminder based on type
      if (type === 'email') {
        await EmailService.sendEmail({
          emailAccountId: emailAccount.id,
          to: appointment.lead.email,
          subject: `Reminder: ${appointment.title}`,
          html: this.generateReminderEmail(appointment, content),
        });
      } else if (type === 'notification') {
        // Create a notification in the database
        await prisma.notification.create({
          data: {
            title: `Appointment Reminder: ${appointment.title}`,
            message: content || `Your appointment is scheduled for ${appointment.startTime.toLocaleString()}`,
            type: 'reminder',
            isRead: false,
            organizationId: agent.organizationId,
          },
        });
      }

      // Update reminder status
      await prisma.reminder.update({
        where: { id: reminderId },
        data: {
          status: 'sent',
          sentAt: new Date(),
        },
      });

      logger.info('Reminder sent successfully', {
        reminderId,
        appointmentId,
        type,
      });
    } catch (error) {
      logger.error('Error processing reminder job', {
        error: error instanceof Error ? error.message : 'Unknown error',
        job,
      });

      // Update reminder status to failed
      await prisma.reminder.update({
        where: { id: job.reminderId },
        data: {
          status: 'failed',
        },
      });
    }
  }

  /**
   * Generate reminder email content
   * @param appointment Appointment data
   * @param customContent Custom content
   * @returns HTML email content
   */
  private static generateReminderEmail(appointment: any, customContent?: string) {
    const timeUntil = this.formatTimeUntil(appointment.startTime);
    
    return `
      <h2>Appointment Reminder</h2>
      <p>Dear ${appointment.lead.firstName || appointment.lead.email},</p>
      <p>This is a reminder about your upcoming appointment:</p>
      <ul>
        <li><strong>Title:</strong> ${appointment.title}</li>
        <li><strong>Date:</strong> ${appointment.startTime.toLocaleDateString()}</li>
        <li><strong>Time:</strong> ${appointment.startTime.toLocaleTimeString()} - ${appointment.endTime.toLocaleTimeString()}</li>
        <li><strong>Time until appointment:</strong> ${timeUntil}</li>
        ${appointment.location ? `<li><strong>Location:</strong> ${appointment.location}</li>` : ''}
        ${appointment.meetingLink ? `<li><strong>Meeting Link:</strong> <a href="${appointment.meetingLink}">${appointment.meetingLink}</a></li>` : ''}
      </ul>
      ${appointment.description ? `<p><strong>Description:</strong> ${appointment.description}</p>` : ''}
      ${customContent ? `<p>${customContent}</p>` : ''}
      <p>We look forward to meeting with you!</p>
    `;
  }

  /**
   * Format time until appointment in a human-readable format
   * @param appointmentTime Appointment time
   * @returns Formatted time string
   */
  private static formatTimeUntil(appointmentTime: Date) {
    const now = new Date();
    const diffMs = appointmentTime.getTime() - now.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);
    
    if (diffMinutes < 60) {
      return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''}`;
    }
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''}`;
    }
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays !== 1 ? 's' : ''}`;
  }

  /**
   * Update reminders for an appointment (when appointment time changes)
   * @param appointmentId Appointment ID
   */
  static async updateRemindersForAppointment(appointmentId: string) {
    try {
      // Get all reminders for the appointment
      const reminders = await prisma.reminder.findMany({
        where: { appointmentId },
      });

      // Reschedule each reminder
      for (const reminder of reminders) {
        if (reminder.status === 'pending') {
          await this.scheduleReminderJob(reminder.id);
        }
      }
    } catch (error) {
      logger.error('Error updating reminders for appointment', {
        error: error instanceof Error ? error.message : 'Unknown error',
        appointmentId,
      });
    }
  }

  /**
   * Cancel all pending reminders for an appointment
   * @param appointmentId Appointment ID
   */
  static async cancelRemindersForAppointment(appointmentId: string) {
    try {
      // Update all pending reminders to cancelled
      await prisma.reminder.updateMany({
        where: {
          appointmentId,
          status: 'pending',
        },
        data: {
          status: 'cancelled',
        },
      });
    } catch (error) {
      logger.error('Error cancelling reminders for appointment', {
        error: error instanceof Error ? error.message : 'Unknown error',
        appointmentId,
      });
    }
  }
}
