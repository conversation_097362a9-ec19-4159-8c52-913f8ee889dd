import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { DnsTrackingService } from './dns-tracking.service';
import { SequenceCampaignService } from './sequence-campaign.service';
import { addTrackingToEmail } from '@/lib/email-tracking';

/**
 * Service for handling all email tracking functionality
 */
export class TrackingService {
  /**
   * Add tracking to an email
   * @param html The HTML content of the email
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param baseUrl The base URL for tracking
   * @param trackingDomains Optional custom tracking domains
   * @returns The HTML with tracking added
   */
  static async addTracking(
    html: string,
    campaignId: string,
    leadId: string,
    baseUrl?: string,
    trackingDomains?: {
      openDomain?: string;
      clickDomain?: string;
      bounceDomain?: string;
      source?: 'custom' | 'sender' | 'default';
    }
  ): Promise<string> {
    try {
      // If no baseUrl is provided, get it from environment
      const trackingBaseUrl = baseUrl || process.env.NEXTAUTH_URL || 'http://localhost:3000';

      // Use the existing addTrackingToEmail function
      return addTrackingToEmail(html, campaignId, leadId, trackingBaseUrl, trackingDomains);
    } catch (error) {
      logger.error('Error adding tracking to email', {
        error: error instanceof Error ? error.message : 'Unknown error',
        campaignId,
        leadId
      });
      return html; // Return original HTML if there's an error
    }
  }

  /**
   * Track an email open event
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param metadata Additional metadata about the open
   * @returns Success status
   */
  static async trackOpen(
    campaignId: string,
    leadId: string,
    metadata: {
      userAgent?: string;
      ip?: string;
      referrer?: string;
      timestamp?: string;
    }
  ): Promise<boolean> {
    try {
      // Find the campaign lead
      const campaignLead = await prisma.campaignLead.findFirst({
        where: {
          id: leadId,
          campaignId,
        },
        include: {
          stepActivities: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 1,
          },
        },
      });

      if (!campaignLead) {
        logger.warn('Campaign lead not found for open tracking', { campaignId, leadId });
        return false;
      }

      // If campaign lead exists but has no activities, we can still track the open
      // This handles cases where the lead exists but hasn't received an email yet
      if (campaignLead.stepActivities.length === 0) {
        logger.info('Campaign lead found but no activities yet', { campaignId, leadId });
        // Continue processing as we can still record the open
      }

      const latestActivity = campaignLead.stepActivities[0];
      const stepId = latestActivity.stepId;

      // Check if we already recorded an open for this lead and step
      const existingOpen = await prisma.stepActivity.findFirst({
        where: {
          campaignLeadId: leadId,
          stepId: stepId,
          type: 'email_opened',
        },
      });

      // Record the email open
      if (!existingOpen) {
        // Create a new open activity if none exists
        await prisma.stepActivity.create({
          data: {
            campaignLeadId: leadId,
            stepId: stepId,
            type: 'email_opened',
            status: 'opened',
            openedAt: new Date(),
            metadata: {
              userAgent: metadata.userAgent || '',
              ip: metadata.ip || '',
              timestamp: metadata.timestamp || new Date().toISOString(),
              referrer: metadata.referrer || '',
              isFirstOpen: true
            },
          },
        });

        // Update campaign metrics only for first opens
        await prisma.campaign.update({
          where: { id: campaignId },
          data: {
            openedCount: {
              increment: 1
            }
          },
        });
      } else {
        // Update the existing open activity with the latest timestamp
        const existingMetadata = existingOpen.metadata as Record<string, any> || {};
        await prisma.stepActivity.update({
          where: { id: existingOpen.id },
          data: {
            openedAt: new Date(),
            metadata: {
              ...existingMetadata,
              latestOpenTimestamp: new Date().toISOString(),
              openCount: ((existingMetadata.openCount || 0) + 1),
              userAgent: metadata.userAgent || existingMetadata.userAgent || '',
              ip: metadata.ip || existingMetadata.ip || '',
            }
          }
        });
      }

      // Update the campaign lead status
      await prisma.campaignLead.update({
        where: { id: leadId },
        data: {
          status: 'active',
          lastInteractionAt: new Date()
        },
      });

      // Get the campaign type to handle sequence campaigns properly
      const campaign = await prisma.campaign.findUnique({
        where: { id: campaignId },
        select: { type: true }
      });

      // Process sequence campaign if applicable
      if (campaign?.type === 'sequence') {
        try {
          // Process the sequence campaign
          await SequenceCampaignService.processLeadAfterInteraction(campaignId, leadId, 'open');
        } catch (sequenceError) {
          logger.error('Error processing sequence campaign after open', {
            error: sequenceError instanceof Error ? sequenceError.message : 'Unknown error',
            campaignId,
            leadId
          });
        }
      }

      return true;
    } catch (error) {
      logger.error('Error tracking email open', {
        error: error instanceof Error ? error.message : 'Unknown error',
        campaignId,
        leadId,
      });
      return false;
    }
  }

  /**
   * Track a link click event
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param url The URL that was clicked
   * @param metadata Additional metadata about the click
   * @returns Success status
   */
  static async trackClick(
    campaignId: string,
    leadId: string,
    url: string,
    metadata: {
      userAgent?: string;
      ip?: string;
      referrer?: string;
      timestamp?: string;
    }
  ): Promise<boolean> {
    try {
      logger.info(`[TRACKING] Processing link click`, {
        campaignId,
        leadId,
        url,
        metadata
      });

      // Find the campaign lead
      let campaignLead;
      try {
        campaignLead = await prisma.campaignLead.findFirst({
          where: {
            id: leadId,
            campaignId,
          },
          include: {
            stepActivities: {
              orderBy: {
                createdAt: 'desc',
              },
              take: 1,
            },
          },
        });
      } catch (dbError) {
        logger.error('[TRACKING] Database error finding campaign lead', {
          error: dbError instanceof Error ? dbError.message : 'Unknown error',
          stack: dbError instanceof Error ? dbError.stack : 'No stack trace',
          campaignId,
          leadId,
          url
        });

        // Create a minimal tracking record even if we can't find the campaign lead
        try {
          // Find the first step of the campaign to use as a reference
          const firstStep = await prisma.campaignStep.findFirst({
            where: { campaignId },
            orderBy: { position: 'asc' }
          });

          if (firstStep) {
            await prisma.stepActivity.create({
              data: {
                campaignLeadId: leadId,
                stepId: firstStep.id,
                type: 'link_clicked',
                status: 'clicked',
                clickedAt: new Date(),
                metadata: {
                  url,
                  userAgent: metadata.userAgent || '',
                  ip: metadata.ip || '',
                  timestamp: metadata.timestamp || new Date().toISOString(),
                  error: 'Database error finding campaign lead'
                }
              }
            });
          } else {
            logger.warn('[TRACKING] Could not create tracking record - no campaign steps found', {
              campaignId,
              leadId,
              url
            });
          }

          logger.info('[TRACKING] Created minimal tracking record despite database error', {
            campaignId,
            leadId,
            url
          });
        } catch (trackingError) {
          logger.error('[TRACKING] Failed to create minimal tracking record', {
            error: trackingError instanceof Error ? trackingError.message : 'Unknown error',
            campaignId,
            leadId,
            url
          });
        }

        return true; // Return true so the user is still redirected
      }

      if (!campaignLead) {
        logger.warn('[TRACKING] Campaign lead not found for click tracking', {
          campaignId,
          leadId,
          url,
          metadata
        });

        // Create a minimal tracking record even if we can't find the campaign lead
        try {
          // Find the first step of the campaign to use as a reference
          const firstStep = await prisma.campaignStep.findFirst({
            where: { campaignId },
            orderBy: { position: 'asc' }
          });

          if (firstStep) {
            await prisma.stepActivity.create({
              data: {
                campaignLeadId: leadId,
                stepId: firstStep.id,
                type: 'link_clicked',
                status: 'clicked',
                clickedAt: new Date(),
                metadata: {
                  url,
                  userAgent: metadata.userAgent || '',
                  ip: metadata.ip || '',
                  timestamp: metadata.timestamp || new Date().toISOString(),
                  error: 'Campaign lead not found'
                }
              }
            });
          } else {
            logger.warn('[TRACKING] Could not create tracking record - no campaign steps found', {
              campaignId,
              leadId,
              url
            });
          }

          logger.info('[TRACKING] Created minimal tracking record for missing campaign lead', {
            campaignId,
            leadId,
            url
          });
        } catch (trackingError) {
          logger.error('[TRACKING] Failed to create minimal tracking record', {
            error: trackingError instanceof Error ? trackingError.message : 'Unknown error',
            campaignId,
            leadId,
            url
          });
        }

        return true; // Return true so the user is still redirected
      }

      // If campaign lead exists but has no activities, we can still track the click
      // This handles cases where the lead exists but hasn't received an email yet
      if (campaignLead.stepActivities.length === 0) {
        logger.info('Campaign lead found but no activities yet for click tracking', {
          campaignId,
          leadId,
          url
        });
        // We need a step ID to record the activity, so try to find the first step of the campaign
        const campaignFirstStep = await prisma.campaignStep.findFirst({
          where: { campaignId },
          orderBy: { position: 'asc' }
        });

        if (!campaignFirstStep) {
          logger.warn('No steps found for campaign', { campaignId });
          return false;
        }

        // Create a temporary activity object to use for the rest of the function
        const tempActivity = { stepId: campaignFirstStep.id };
        campaignLead.stepActivities = [tempActivity as any];
      }

      const latestActivity = campaignLead.stepActivities[0];

      // Record the link click
      await prisma.stepActivity.create({
        data: {
          campaignLeadId: leadId,
          stepId: latestActivity.stepId,
          type: 'link_clicked',
          status: 'clicked',
          clickedAt: new Date(),
          metadata: {
            url,
            userAgent: metadata.userAgent || '',
            ip: metadata.ip || '',
            timestamp: metadata.timestamp || new Date().toISOString(),
            referrer: metadata.referrer || '',
          },
        },
      });

      // Update the campaign lead status
      await prisma.campaignLead.update({
        where: { id: leadId },
        data: {
          status: 'active',
          lastInteractionAt: new Date()
        },
      });

      // Update campaign metrics
      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          clickedCount: {
            increment: 1
          }
        },
      });

      // Get the campaign type to handle sequence campaigns properly
      const campaign = await prisma.campaign.findUnique({
        where: { id: campaignId },
        select: { type: true }
      });

      // Process sequence campaign if applicable
      if (campaign?.type === 'sequence') {
        try {
          logger.info(`[TRACKING] Processing sequence campaign after click`, {
            campaignId,
            leadId,
            url
          });

          // Process the sequence campaign
          await SequenceCampaignService.processLeadAfterInteraction(campaignId, leadId, 'click');

          logger.info(`[TRACKING] Successfully processed sequence campaign after click`, {
            campaignId,
            leadId,
            url
          });
        } catch (sequenceError) {
          logger.error('[TRACKING] Error processing sequence campaign after click', {
            error: sequenceError instanceof Error ? sequenceError.message : 'Unknown error',
            stack: sequenceError instanceof Error ? sequenceError.stack : 'No stack trace',
            campaignId,
            leadId,
            url
          });
        }
      }

      logger.info('[TRACKING] Successfully tracked link click', {
        campaignId,
        leadId,
        url
      });
      return true;
    } catch (error) {
      logger.error('[TRACKING] Error tracking link click', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        url,
        metadata
      });
      return false;
    }
  }
  /**
   * Track an unsubscribe event
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param metadata Additional metadata about the unsubscribe
   * @returns Success status
   */
  static async trackUnsubscribe(
    campaignId: string,
    leadId: string,
    metadata: {
      userAgent?: string;
      ip?: string;
      timestamp?: string;
      reason?: string;
    }
  ): Promise<boolean> {
    try {
      // Find the campaign lead
      const campaignLead = await prisma.campaignLead.findFirst({
        where: {
          id: leadId,
          campaignId,
        },
        include: {
          lead: true,
          stepActivities: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 1,
          },
        },
      });

      if (!campaignLead) {
        logger.warn('Campaign lead not found for unsubscribe', { campaignId, leadId });
        return false;
      }

      // Update the campaign lead status to unsubscribed
      await prisma.campaignLead.update({
        where: {
          id: leadId,
        },
        data: {
          status: 'unsubscribed',
          unsubscribedAt: new Date(),
          lastInteractionAt: new Date(),
        },
      });

      // Mark the lead as opt-out to prevent future emails
      await prisma.lead.update({
        where: {
          id: campaignLead.leadId,
        },
        data: {
          status: 'unsubscribed',
        },
      });

      // Record the unsubscribe activity
      if (campaignLead.stepActivities.length > 0) {
        const latestActivity = campaignLead.stepActivities[0];

        await prisma.stepActivity.create({
          data: {
            campaignLeadId: leadId,
            stepId: latestActivity.stepId,
            type: 'unsubscribed',
            status: 'unsubscribed',
            metadata: {
              userAgent: metadata.userAgent || '',
              ip: metadata.ip || '',
              timestamp: metadata.timestamp || new Date().toISOString(),
              reason: metadata.reason || 'User unsubscribed',
            },
          },
        });
      }

      logger.info('Lead unsubscribed', {
        campaignId,
        leadId,
        email: campaignLead.lead.email
      });

      return true;
    } catch (error) {
      logger.error('Error tracking unsubscribe', {
        error: error instanceof Error ? error.message : 'Unknown error',
        campaignId,
        leadId,
      });
      return false;
    }
  }

  /**
   * Track a bounce event
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param bounceType The type of bounce (hard or soft)
   * @param bounceReason The reason for the bounce
   * @returns Success status
   */
  static async trackBounce(
    campaignId: string,
    leadId: string,
    bounceType: 'hard' | 'soft',
    bounceReason?: string
  ): Promise<boolean> {
    try {
      // Find the campaign lead
      const campaignLead = await prisma.campaignLead.findFirst({
        where: {
          id: leadId,
          campaignId,
        },
        include: {
          lead: true,
          stepActivities: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 1,
          },
        },
      });

      if (!campaignLead || campaignLead.stepActivities.length === 0) {
        logger.warn('Campaign lead not found for bounce tracking', { campaignId, leadId });
        return false;
      }

      const latestActivity = campaignLead.stepActivities[0];

      // Update the campaign lead status to bounced
      await prisma.campaignLead.update({
        where: {
          id: leadId,
        },
        data: {
          status: 'bounced',
        },
      });

      // Record the bounce activity
      await prisma.stepActivity.create({
        data: {
          campaignLeadId: leadId,
          stepId: latestActivity.stepId,
          type: 'email_bounced',
          status: 'bounced',
          bouncedAt: new Date(),
          metadata: {
            bounceType,
            bounceReason,
            timestamp: new Date().toISOString(),
          },
        },
      });

      // Update campaign metrics
      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          // Increment error count for bounces
          errorCount: {
            increment: 1
          }
        },
      });

      // Add to suppression list for hard bounces
      if (bounceType === 'hard') {
        await prisma.suppressionList.upsert({
          where: { email: campaignLead.lead.email },
          update: {
            reason: `Bounce: ${bounceReason || bounceType || 'Unknown'}`
          },
          create: {
            email: campaignLead.lead.email,
            reason: `Bounce: ${bounceReason || bounceType || 'Unknown'}`
          }
        });
      }

      logger.info('Email bounce tracked', {
        campaignId,
        leadId,
        email: campaignLead.lead.email,
        bounceType,
        bounceReason
      });

      return true;
    } catch (error) {
      logger.error('Error tracking email bounce', {
        error: error instanceof Error ? error.message : 'Unknown error',
        campaignId,
        leadId,
      });
      return false;
    }
  }

  /**
   * Track a reply event
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param messageId The message ID of the reply
   * @param replyContent The content of the reply
   * @returns Success status
   */
  static async trackReply(
    campaignId: string,
    leadId: string,
    messageId: string,
    replyContent: {
      subject?: string;
      text?: string;
      html?: string;
      from?: string;
      inReplyTo?: string;
    }
  ): Promise<boolean> {
    try {
      // Find the campaign lead
      const campaignLead = await prisma.campaignLead.findFirst({
        where: {
          id: leadId,
          campaignId,
        },
        include: {
          lead: true,
          stepActivities: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 1,
          },
        },
      });

      if (!campaignLead || campaignLead.stepActivities.length === 0) {
        logger.warn('Campaign lead not found for reply tracking', { campaignId, leadId });
        return false;
      }

      const latestActivity = campaignLead.stepActivities[0];

      // Record the reply activity
      await prisma.stepActivity.create({
        data: {
          campaignLeadId: leadId,
          stepId: latestActivity.stepId,
          type: 'email_replied',
          status: 'replied',
          repliedAt: new Date(),
          metadata: {
            messageId,
            subject: replyContent.subject,
            from: replyContent.from,
            inReplyTo: replyContent.inReplyTo,
            timestamp: new Date().toISOString(),
          },
        },
      });

      // Update the campaign lead status
      await prisma.campaignLead.update({
        where: { id: leadId },
        data: {
          status: 'active',
          lastInteractionAt: new Date()
        },
      });

      // Update campaign metrics
      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          repliedCount: {
            increment: 1
          }
        },
      });

      // Get the campaign type to handle sequence campaigns properly
      const campaign = await prisma.campaign.findUnique({
        where: { id: campaignId },
        select: { type: true }
      });

      // Process sequence campaign if applicable
      if (campaign?.type === 'sequence') {
        try {
          // Process the sequence campaign
          await SequenceCampaignService.processLeadAfterInteraction(campaignId, leadId, 'reply');
        } catch (sequenceError) {
          logger.error('Error processing sequence campaign after reply', {
            error: sequenceError instanceof Error ? sequenceError.message : 'Unknown error',
            campaignId,
            leadId
          });
        }
      }

      logger.info('Email reply tracked', {
        campaignId,
        leadId,
        email: campaignLead.lead.email,
        messageId
      });

      return true;
    } catch (error) {
      logger.error('Error tracking email reply', {
        error: error instanceof Error ? error.message : 'Unknown error',
        campaignId,
        leadId,
      });
      return false;
    }
  }

  /**
   * Generate a unique reply-to address for tracking replies
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param baseEmail The base email address (e.g., <EMAIL>)
   * @returns The unique reply-to address
   */
  static generateReplyTrackingAddress(
    campaignId: string,
    leadId: string,
    baseEmail: string
  ): string {
    // Extract the parts of the email address
    const [username, domain] = baseEmail.split('@');

    // Generate a unique identifier
    const uniqueId = `${campaignId.substring(0, 8)}-${leadId.substring(0, 8)}`;

    // Create the new email address
    return `${username}+${uniqueId}@${domain}`;
  }

  /**
   * Parse a tracking reply-to address to extract campaign and lead IDs
   * @param replyToEmail The reply-to email address
   * @returns The extracted campaign and lead IDs
   */
  static parseReplyTrackingAddress(replyToEmail: string): { campaignId?: string; leadId?: string } {
    try {
      if (!replyToEmail) {
        logger.warn('Empty reply-to email address provided');
        return {};
      }

      // Handle different formats of email addresses
      // Some email clients might format the address as "Name <<EMAIL>>"
      let cleanEmail = replyToEmail;
      const angleMatch = replyToEmail.match(/<([^>]+)>/);
      if (angleMatch && angleMatch[1]) {
        cleanEmail = angleMatch[1];
      }

      // Extract the username part of the email
      const parts = cleanEmail.split('@');
      if (parts.length !== 2) {
        logger.warn('Invalid email format', { replyToEmail });
        return {};
      }

      const username = parts[0];

      // Check if it contains a plus sign
      if (!username.includes('+')) {
        return {};
      }

      // Extract the unique identifier
      const plusParts = username.split('+');
      if (plusParts.length < 2) {
        logger.warn('Invalid tracking format in reply-to address', { replyToEmail });
        return {};
      }

      const uniqueId = plusParts[1];

      // Parse the campaign and lead IDs
      const idParts = uniqueId.split('-');
      if (idParts.length !== 2) {
        logger.warn('Invalid ID format in reply-to address', { replyToEmail, uniqueId });
        return {};
      }

      const [campaignIdPart, leadIdPart] = idParts;

      // Validate the extracted IDs
      if (!campaignIdPart || !leadIdPart) {
        logger.warn('Missing campaign or lead ID in reply-to address', {
          replyToEmail,
          campaignIdPart,
          leadIdPart
        });
        return {};
      }

      // Return the extracted IDs
      return {
        campaignId: campaignIdPart,
        leadId: leadIdPart
      };
    } catch (error) {
      logger.error('Error parsing reply tracking address', {
        error: error instanceof Error ? error.message : 'Unknown error',
        replyToEmail,
        stack: error instanceof Error ? error.stack : undefined
      });
      return {};
    }
  }
}