import { prisma } from '@/lib/prisma';
import { SpamAssassinClient } from '@/lib/spam-assassin';

interface SpamCheckResult {
  score: number;
  details: Array<{
    rule: string;
    description: string;
    score: number;
  }>;
}

export class EmailQualityService {
  private static spamClient = new SpamAssassinClient();

  static async checkSpamScore(html: string, subject: string): Promise<SpamCheckResult> {
    const result = await this.spamClient.check({
      html,
      subject,
    });

    return {
      score: result.score,
      details: result.details.map(rule => ({
        rule: rule.rule,
        description: rule.description,
        score: rule.score,
      })),
    };
  }

  static async previewEmail(templateId: string, testData: Record<string, any>) {
    const template = await prisma.emailTemplate.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      throw new Error('Template not found');
    }

    // Replace variables in template with test data
    let previewHtml = template.content;
    Object.entries(testData).forEach(([key, value]) => {
      previewHtml = previewHtml.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });

    return {
      subject: template.subject,
      html: previewHtml,
    };
  }
}