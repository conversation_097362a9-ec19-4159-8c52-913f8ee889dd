import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { BillingCycle, PackageStatus } from '@prisma/client';

export interface CreatePackageData {
  name: string;
  description?: string;
  price: number;
  billingCycle: BillingCycle;
  
  // Email limits
  dailyEmailLimit?: number;
  monthlyEmailLimit?: number;
  
  // Feature flags
  aiAgentsEnabled: boolean;
  knowledgeBaseEnabled: boolean;
  emailTrackingEnabled: boolean;
  customDomainsEnabled: boolean;
  apiAccessEnabled: boolean;
  prioritySupportEnabled: boolean;
  
  // AI specific limits
  maxAiAgents?: number;
  maxKnowledgeBases?: number;
  
  // Other limits
  maxEmailAccounts?: number;
  maxLeads?: number;
  maxCampaigns?: number;
  storageLimit?: number;
  
  // Metadata
  isDefault?: boolean;
  sortOrder?: number;
}

export interface UpdatePackageData extends Partial<CreatePackageData> {
  status?: PackageStatus;
}

export class AdminPackageService {
  /**
   * Create a new package
   */
  static async createPackage(data: CreatePackageData, createdBy?: string) {
    try {
      // If this is set as default, unset other defaults
      if (data.isDefault) {
        await prisma.package.updateMany({
          where: { isDefault: true },
          data: { isDefault: false }
        });
      }

      const package_ = await prisma.package.create({
        data: {
          ...data,
          createdBy
        }
      });

      logger.info('Package created', {
        packageId: package_.id,
        name: package_.name,
        createdBy
      });

      return package_;
    } catch (error) {
      logger.error('Error creating package', {
        error: error instanceof Error ? error.message : 'Unknown error',
        data
      });
      throw new Error('Failed to create package');
    }
  }

  /**
   * Update an existing package
   */
  static async updatePackage(packageId: string, data: UpdatePackageData) {
    try {
      // If this is set as default, unset other defaults
      if (data.isDefault) {
        await prisma.package.updateMany({
          where: { 
            isDefault: true,
            id: { not: packageId }
          },
          data: { isDefault: false }
        });
      }

      const package_ = await prisma.package.update({
        where: { id: packageId },
        data
      });

      logger.info('Package updated', {
        packageId: package_.id,
        name: package_.name
      });

      return package_;
    } catch (error) {
      logger.error('Error updating package', {
        error: error instanceof Error ? error.message : 'Unknown error',
        packageId,
        data
      });
      throw new Error('Failed to update package');
    }
  }

  /**
   * Delete a package (soft delete by setting status to ARCHIVED)
   */
  static async deletePackage(packageId: string) {
    try {
      // Check if any organizations are using this package
      const organizationsCount = await prisma.organization.count({
        where: { packageId }
      });

      if (organizationsCount > 0) {
        throw new Error(`Cannot delete package: ${organizationsCount} organizations are currently using it`);
      }

      const package_ = await prisma.package.update({
        where: { id: packageId },
        data: { status: PackageStatus.ARCHIVED }
      });

      logger.info('Package archived', {
        packageId: package_.id,
        name: package_.name
      });

      return package_;
    } catch (error) {
      logger.error('Error deleting package', {
        error: error instanceof Error ? error.message : 'Unknown error',
        packageId
      });
      throw error;
    }
  }

  /**
   * Get all packages
   */
  static async getAllPackages(includeArchived = false) {
    try {
      const packages = await prisma.package.findMany({
        where: includeArchived ? {} : {
          status: { not: PackageStatus.ARCHIVED }
        },
        include: {
          _count: {
            select: {
              organizations: true,
              subscriptions: true
            }
          }
        },
        orderBy: [
          { isDefault: 'desc' },
          { sortOrder: 'asc' },
          { createdAt: 'asc' }
        ]
      });

      return packages;
    } catch (error) {
      logger.error('Error fetching packages', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to fetch packages');
    }
  }

  /**
   * Get a single package by ID
   */
  static async getPackageById(packageId: string) {
    try {
      const package_ = await prisma.package.findUnique({
        where: { id: packageId },
        include: {
          organizations: {
            select: {
              id: true,
              name: true,
              createdAt: true
            }
          },
          subscriptions: {
            include: {
              package: {
                select: {
                  name: true
                }
              }
            }
          },
          _count: {
            select: {
              organizations: true,
              subscriptions: true
            }
          }
        }
      });

      if (!package_) {
        throw new Error('Package not found');
      }

      return package_;
    } catch (error) {
      logger.error('Error fetching package', {
        error: error instanceof Error ? error.message : 'Unknown error',
        packageId
      });
      throw error;
    }
  }

  /**
   * Get the default package
   */
  static async getDefaultPackage() {
    try {
      const defaultPackage = await prisma.package.findFirst({
        where: { 
          isDefault: true,
          status: PackageStatus.ACTIVE
        }
      });

      if (!defaultPackage) {
        // Create a default free package if none exists
        return await this.createDefaultFreePackage();
      }

      return defaultPackage;
    } catch (error) {
      logger.error('Error fetching default package', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to fetch default package');
    }
  }

  /**
   * Create the default free package
   */
  static async createDefaultFreePackage() {
    try {
      const freePackage = await prisma.package.create({
        data: {
          name: 'Free',
          description: 'Free package with basic features',
          price: 0,
          billingCycle: BillingCycle.MONTHLY,
          dailyEmailLimit: 100,
          monthlyEmailLimit: 3000,
          aiAgentsEnabled: false,
          knowledgeBaseEnabled: false,
          emailTrackingEnabled: true,
          customDomainsEnabled: false,
          apiAccessEnabled: false,
          prioritySupportEnabled: false,
          maxEmailAccounts: 1,
          maxLeads: 1000,
          maxCampaigns: 10,
          storageLimit: 1024 * 1024 * 1024, // 1GB
          isDefault: true,
          sortOrder: 0
        }
      });

      logger.info('Default free package created', {
        packageId: freePackage.id
      });

      return freePackage;
    } catch (error) {
      logger.error('Error creating default free package', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to create default free package');
    }
  }

  /**
   * Assign a package to an organization
   */
  static async assignPackageToOrganization(organizationId: string, packageId: string) {
    try {
      // Verify package exists and is active
      const package_ = await prisma.package.findUnique({
        where: { id: packageId }
      });

      if (!package_ || package_.status !== PackageStatus.ACTIVE) {
        throw new Error('Package not found or inactive');
      }

      // Update organization
      const organization = await prisma.organization.update({
        where: { id: organizationId },
        data: { packageId }
      });

      // Create subscription record
      await prisma.packageSubscription.create({
        data: {
          organizationId,
          packageId,
          startDate: new Date(),
          isActive: true,
          autoRenew: true
        }
      });

      logger.info('Package assigned to organization', {
        organizationId,
        packageId,
        packageName: package_.name
      });

      return organization;
    } catch (error) {
      logger.error('Error assigning package to organization', {
        error: error instanceof Error ? error.message : 'Unknown error',
        organizationId,
        packageId
      });
      throw error;
    }
  }
}
