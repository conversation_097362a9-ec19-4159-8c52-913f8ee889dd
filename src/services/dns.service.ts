import dns from 'dns';
import { promisify } from 'util';

const resolveTxt = promisify(dns.resolveTxt);

export class DNSService {
  static async verifySpfRecord(domain: string): Promise<boolean> {
    try {
      const records = await resolveTxt(domain);
      const spfRecord = records.flat().find(record => 
        record.toString().startsWith('v=spf1')
      );
      return !!spfRecord;
    } catch (error) {
      return false;
    }
  }

  static async verifyDkimRecord(selector: string, domain: string): Promise<boolean> {
    try {
      const dkimDomain = `${selector}._domainkey.${domain}`;
      const records = await resolveTxt(dkimDomain);
      return records.length > 0;
    } catch (error) {
      return false;
    }
  }

  static async verifyDmarcRecord(domain: string): Promise<boolean> {
    try {
      const dmarcDomain = `_dmarc.${domain}`;
      const records = await resolveTxt(dmarcDomain);
      const dmarcRecord = records.flat().find(record =>
        record.toString().startsWith('v=DMARC1')
      );
      return !!dmarcRecord;
    } catch (error) {
      return false;
    }
  }

  static generateDnsRecords(domain: string, selector: string) {
    return {
      spf: `v=spf1 include:_spf.${domain} ~all`,
      dkim: {
        selector,
        record: 'v=DKIM1; k=rsa; p=YOUR_PUBLIC_KEY'
      },
      dmarc: `v=DMARC1; p=quarantine; rua=mailto:dmarc@${domain}`
    };
  }
}