import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { stripe } from '@/config/stripe';

export interface PackageData {
  name: string;
  description: string;
  price: number;
  billingCycle: 'WEEKLY' | 'MONTHLY' | 'YEARLY';
  dailyEmailLimit: number;
  monthlyEmailLimit?: number;
  emailAccountLimit: number;
  aiFeatures: string[];
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
}

/**
 * Package Management Service - Handles subscription packages
 * Only accessible by super admins
 */
export class PackageManagementService {
  /**
   * Create a new package
   */
  static async createPackage(packageData: PackageData, createdByAdminId: string): Promise<any> {
    try {
      // Create package in database
      const newPackage = await prisma.package.create({
        data: {
          ...packageData,
          isDefault: false
        }
      });

      // Create Stripe product and price if not free
      if (packageData.price > 0) {
        try {
          const stripeProduct = await stripe.products.create({
            name: packageData.name,
            description: packageData.description,
            metadata: {
              packageId: newPackage.id,
              dailyEmailLimit: packageData.dailyEmailLimit.toString(),
              emailAccountLimit: packageData.emailAccountLimit.toString(),
              aiFeatures: JSON.stringify(packageData.aiFeatures)
            }
          });

          const stripePrice = await stripe.prices.create({
            product: stripeProduct.id,
            unit_amount: Math.round(packageData.price * 100), // Convert to cents
            currency: 'usd',
            recurring: {
              interval: packageData.billingCycle.toLowerCase() as 'week' | 'month' | 'year'
            },
            metadata: {
              packageId: newPackage.id
            }
          });

          // Update package with Stripe IDs
          await prisma.package.update({
            where: { id: newPackage.id },
            data: {
              stripeProductId: stripeProduct.id,
              stripePriceId: stripePrice.id
            }
          });

          logger.info('Package created with Stripe integration', {
            packageId: newPackage.id,
            stripeProductId: stripeProduct.id,
            stripePriceId: stripePrice.id,
            createdBy: createdByAdminId
          });
        } catch (stripeError) {
          logger.error('Error creating Stripe product for package', {
            error: stripeError instanceof Error ? stripeError.message : 'Unknown error',
            packageId: newPackage.id
          });
          // Continue without Stripe integration for now
        }
      }

      logger.info('Package created successfully', {
        packageId: newPackage.id,
        name: packageData.name,
        price: packageData.price,
        createdBy: createdByAdminId
      });

      return newPackage;
    } catch (error) {
      logger.error('Error creating package', {
        error: error instanceof Error ? error.message : 'Unknown error',
        packageData,
        createdByAdminId
      });
      throw error;
    }
  }

  /**
   * Update an existing package
   */
  static async updatePackage(packageId: string, packageData: Partial<PackageData>, updatedByAdminId: string): Promise<any> {
    try {
      const existingPackage = await prisma.package.findUnique({
        where: { id: packageId }
      });

      if (!existingPackage) {
        throw new Error('Package not found');
      }

      // Update package in database
      const updatedPackage = await prisma.package.update({
        where: { id: packageId },
        data: packageData
      });

      // Update Stripe product if it exists and price changed
      if (existingPackage.stripeProductId && packageData.price !== undefined) {
        try {
          // Update product metadata
          await stripe.products.update(existingPackage.stripeProductId, {
            name: packageData.name || existingPackage.name,
            description: packageData.description || existingPackage.description,
            metadata: {
              packageId: packageId,
              dailyEmailLimit: (packageData.dailyEmailLimit || existingPackage.dailyEmailLimit).toString(),
              emailAccountLimit: (packageData.emailAccountLimit || existingPackage.emailAccountLimit).toString(),
              aiFeatures: JSON.stringify(packageData.aiFeatures || existingPackage.aiFeatures)
            }
          });

          // If price changed, create new price and archive old one
          if (packageData.price !== existingPackage.price && existingPackage.stripePriceId) {
            // Archive old price
            await stripe.prices.update(existingPackage.stripePriceId, {
              active: false
            });

            // Create new price
            const newPrice = await stripe.prices.create({
              product: existingPackage.stripeProductId,
              unit_amount: Math.round((packageData.price || 0) * 100),
              currency: 'usd',
              recurring: {
                interval: (packageData.billingCycle || existingPackage.billingCycle).toLowerCase() as 'week' | 'month' | 'year'
              },
              metadata: {
                packageId: packageId
              }
            });

            // Update package with new price ID
            await prisma.package.update({
              where: { id: packageId },
              data: {
                stripePriceId: newPrice.id
              }
            });
          }
        } catch (stripeError) {
          logger.error('Error updating Stripe product for package', {
            error: stripeError instanceof Error ? stripeError.message : 'Unknown error',
            packageId
          });
        }
      }

      logger.info('Package updated successfully', {
        packageId,
        updatedBy: updatedByAdminId
      });

      return updatedPackage;
    } catch (error) {
      logger.error('Error updating package', {
        error: error instanceof Error ? error.message : 'Unknown error',
        packageId,
        updatedByAdminId
      });
      throw error;
    }
  }

  /**
   * Delete a package (archive it)
   */
  static async deletePackage(packageId: string, deletedByAdminId: string): Promise<boolean> {
    try {
      const existingPackage = await prisma.package.findUnique({
        where: { id: packageId },
        include: {
          organizations: true
        }
      });

      if (!existingPackage) {
        throw new Error('Package not found');
      }

      // Check if package is in use
      if (existingPackage.organizations.length > 0) {
        throw new Error('Cannot delete package that is currently in use by organizations');
      }

      // Archive the package instead of deleting
      await prisma.package.update({
        where: { id: packageId },
        data: {
          status: 'ARCHIVED'
        }
      });

      // Archive Stripe product if it exists
      if (existingPackage.stripeProductId) {
        try {
          await stripe.products.update(existingPackage.stripeProductId, {
            active: false
          });
        } catch (stripeError) {
          logger.error('Error archiving Stripe product', {
            error: stripeError instanceof Error ? stripeError.message : 'Unknown error',
            packageId,
            stripeProductId: existingPackage.stripeProductId
          });
        }
      }

      logger.info('Package archived successfully', {
        packageId,
        deletedBy: deletedByAdminId
      });

      return true;
    } catch (error) {
      logger.error('Error deleting package', {
        error: error instanceof Error ? error.message : 'Unknown error',
        packageId,
        deletedByAdminId
      });
      throw error;
    }
  }

  /**
   * Get all packages
   */
  static async getAllPackages(): Promise<any[]> {
    try {
      const packages = await prisma.package.findMany({
        include: {
          _count: {
            select: {
              organizations: true
            }
          }
        },
        orderBy: [
          { isDefault: 'desc' },
          { price: 'asc' }
        ]
      });

      return packages;
    } catch (error) {
      logger.error('Error getting all packages', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get package by ID
   */
  static async getPackageById(packageId: string): Promise<any> {
    try {
      const packageData = await prisma.package.findUnique({
        where: { id: packageId },
        include: {
          organizations: {
            select: {
              id: true,
              name: true,
              owner: {
                select: {
                  email: true,
                  name: true
                }
              }
            }
          }
        }
      });

      return packageData;
    } catch (error) {
      logger.error('Error getting package by ID', {
        error: error instanceof Error ? error.message : 'Unknown error',
        packageId
      });
      throw error;
    }
  }

  /**
   * Create default free package
   */
  static async createDefaultFreePackage(): Promise<any> {
    try {
      // Check if free package already exists
      const existingFreePackage = await prisma.package.findFirst({
        where: {
          name: 'Free',
          status: 'ACTIVE'
        }
      });

      if (existingFreePackage) {
        return existingFreePackage;
      }

      // Create default free package
      const freePackage = await prisma.package.create({
        data: {
          name: 'Free',
          description: 'Default free package with basic email sending. AI features and knowledge base are disabled.',
          price: 0,
          billingCycle: 'MONTHLY',
          dailyEmailLimit: 100,
          monthlyEmailLimit: 3000,
          emailAccountLimit: 1,
          aiFeatures: [], // No AI features for free package
          status: 'ACTIVE',
          isDefault: true
        }
      });

      logger.info('Default free package created', {
        packageId: freePackage.id
      });

      return freePackage;
    } catch (error) {
      logger.error('Error creating default free package', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Assign free package to all organizations without a package
   */
  static async assignFreePackageToOrganizations(): Promise<{ assignedCount: number }> {
    try {
      // Get or create free package
      const freePackage = await this.createDefaultFreePackage();

      // Find organizations without a package
      const organizationsWithoutPackage = await prisma.organization.findMany({
        where: { packageId: null }
      });

      if (organizationsWithoutPackage.length > 0) {
        // Assign free package to all organizations without a package
        await prisma.organization.updateMany({
          where: { packageId: null },
          data: { packageId: freePackage.id }
        });

        logger.info('Assigned free package to organizations', {
          count: organizationsWithoutPackage.length,
          packageId: freePackage.id
        });
      }

      return { assignedCount: organizationsWithoutPackage.length };
    } catch (error) {
      logger.error('Error assigning free package to organizations', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}
