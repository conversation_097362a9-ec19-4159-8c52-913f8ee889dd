import { prisma } from '@/lib/prisma';
import { PLANS } from '@/config/stripe';
import { stripe } from '@/config/stripe';
import { BaseUsageService } from './base-usage.service';

export type UsageMetric = {
  emailsSent: number;
  emailAccounts: number;
  storageUsed: number;
};

export class UsageService extends BaseUsageService {
  static async trackEmailSent(organizationId: string) {
    await this.updateUsageMetric(organizationId, 'emailsSent');
  }

  // API calls tracking removed as it's not in the schema

  static async trackStorageUsed(organizationId: string, bytes: number) {
    const date = new Date();
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);

    await prisma.usage.upsert({
      where: {
        organizationId_period: {
          organizationId,
          period: startOfMonth,
        },
      },
      update: {
        storageUsed: { increment: bytes },
      },
      create: {
        organizationId,
        period: startOfMonth,
        storageUsed: bytes,
        emailsSent: 0,
        emailAccounts: 0,
      },
    });
  }

  private static async updateUsageMetric(organizationId: string, metric: keyof UsageMetric) {
    const date = new Date();
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);

    try {
      await prisma.usage.upsert({
        where: {
          organizationId_period: {
            organizationId,
            period: startOfMonth,
          },
        },
        update: {
          [metric]: { increment: 1 },
        },
        create: {
          organizationId,
          period: startOfMonth,
          emailsSent: metric === 'emailsSent' ? 1 : 0,
          emailAccounts: metric === 'emailAccounts' ? 1 : 0,
          storageUsed: 0,
        },
      });
    } catch (error) {
      console.error('Error updating usage metric:', error);
      // Don't throw the error to prevent campaign processing from failing
    }
  }

  static async getCurrentUsage(organizationId: string): Promise<UsageMetric> {
    const usage = await super.getCurrentUsage(organizationId);
    // Report usage to Stripe after fetching
    await this.reportUsageToStripe(organizationId, usage);
    return usage;
  }

  static async checkUsageLimits(organizationId: string): Promise<{
    withinLimits: boolean;
    limitedBy?: string[];
  }> {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: { subscription: true },
    });

    if (!organization?.subscription) {
      return { withinLimits: false, limitedBy: ['subscription'] };
    }

    const currentUsage = await this.getCurrentUsage(organizationId);
    const plan = Object.values(PLANS).find(
      (p) => p.id === organization.subscription.stripePriceId
    );

    if (!plan) {
      return { withinLimits: false, limitedBy: ['plan'] };
    }

    const limitedBy: string[] = [];

    // Check various limits based on plan
    if (plan.name === 'Basic') {
      if (currentUsage.emailsSent >= 1000) limitedBy.push('emailsSent');
      if (currentUsage.emailAccounts >= 1) limitedBy.push('emailAccounts');
      if (currentUsage.storageUsed >= 1_000_000_000) limitedBy.push('storage'); // 1GB
    } else if (plan.name === 'Pro') {
      if (currentUsage.emailsSent >= 10000) limitedBy.push('emailsSent');
      if (currentUsage.emailAccounts >= 5) limitedBy.push('emailAccounts');
      if (currentUsage.storageUsed >= 5_000_000_000) limitedBy.push('storage'); // 5GB
    }

    return {
      withinLimits: limitedBy.length === 0,
      limitedBy: limitedBy.length > 0 ? limitedBy : undefined,
    };
  }

  static async reportUsageToStripe(organizationId: string, usage: UsageMetric) {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: { subscription: true },
    });

    if (!organization?.subscription?.stripeSubscriptionId) return;

    // Report each metric to Stripe
    const subscriptionItems = await stripe.subscriptionItems.list({
      subscription: organization.subscription.stripeSubscriptionId,
    });

    for (const item of subscriptionItems.data) {
      // Match the subscription item with the metric
      const metricId = item.price.metadata.usageMetric;
      if (!metricId || !['emailsSent', 'emailAccounts', 'storageUsed'].includes(metricId)) continue;

      const value = usage[metricId as keyof UsageMetric];

      await stripe.subscriptionItems.createUsageRecord(
        item.id,
        {
          quantity: value,
          timestamp: Math.floor(Date.now() / 1000),
          action: 'set', // or 'increment' based on your needs
        }
      );
    }
  }
}
