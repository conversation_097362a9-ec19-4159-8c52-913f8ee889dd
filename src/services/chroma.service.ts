import { Embeddings } from '@langchain/core/embeddings';
import { logger } from '@/lib/logger';
import { AIProviderService } from './ai-provider.service';

/**
 * Service for interacting with Chroma vector database using REST API
 * Follows the hierarchy: Tenant -> Database -> Collection
 */
export class ChromaService {
  private static embeddings: Embeddings;
  private static baseUrl = process.env.CHROMA_API_URL || 'https://chroma.wattlesol.com/api/v2';

  // Default database name for all organizations
  private static readonly DEFAULT_DATABASE = 'knowledge_base';

  /**
   * Make HTTP request to Chroma API
   */
  private static async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseUrl}${endpoint}`;

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Chroma API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * Initialize the embeddings model using the AI provider service
   */
  private static getEmbeddings() {
    if (!this.embeddings) {
      const providerInfo = AIProviderService.getProviderInfo();

      logger.info('Initializing embeddings for Chroma', {
        provider: providerInfo.provider,
        embeddingModel: providerInfo.embeddingModel,
        baseUrl: providerInfo.baseUrl,
      });

      this.embeddings = AIProviderService.getEmbeddings();
    }
    return this.embeddings;
  }

  /**
   * Get tenant name for an organization
   */
  private static getTenantName(organizationId: string): string {
    return `org_${organizationId}`;
  }

  /**
   * Get collection name for an organization
   */
  private static getCollectionName(organizationId: string): string {
    return `kb_${organizationId}`;
  }

  /**
   * Create or get a tenant for an organization
   */
  static async createOrGetTenant(organizationId: string) {
    const tenantName = this.getTenantName(organizationId);

    try {
      // Try to get existing tenant
      const tenant = await this.makeRequest(`/tenants/${tenantName}`);
      logger.info(`Found existing tenant: ${tenantName}`);
      return tenant;
    } catch (error) {
      // If tenant doesn't exist, create it
      try {
        const tenant = await this.makeRequest('/tenants', {
          method: 'POST',
          body: JSON.stringify({
            name: tenantName,
          }),
        });
        logger.info(`Created new tenant: ${tenantName}`);
        return tenant;
      } catch (createError) {
        logger.error(`Error creating tenant: ${tenantName}`, {
          error: createError instanceof Error ? createError.message : 'Unknown error',
        });
        throw createError;
      }
    }
  }

  /**
   * Create or get a database for a tenant
   */
  static async createOrGetDatabase(organizationId: string) {
    const tenantName = this.getTenantName(organizationId);
    const databaseName = this.DEFAULT_DATABASE;

    try {
      // Try to get existing database
      const database = await this.makeRequest(`/tenants/${tenantName}/databases/${databaseName}`);
      logger.info(`Found existing database: ${databaseName} for tenant: ${tenantName}`);
      return database;
    } catch (error) {
      // If database doesn't exist, create it
      try {
        const database = await this.makeRequest(`/tenants/${tenantName}/databases`, {
          method: 'POST',
          body: JSON.stringify({
            name: databaseName,
          }),
        });
        logger.info(`Created new database: ${databaseName} for tenant: ${tenantName}`);
        return database;
      } catch (createError) {
        logger.error(`Error creating database: ${databaseName} for tenant: ${tenantName}`, {
          error: createError instanceof Error ? createError.message : 'Unknown error',
        });
        throw createError;
      }
    }
  }

  /**
   * Create or get a collection for an organization
   */
  static async createOrGetCollection(organizationId: string) {
    const tenantName = this.getTenantName(organizationId);
    const databaseName = this.DEFAULT_DATABASE;
    const collectionName = this.getCollectionName(organizationId);

    try {
      // Ensure tenant exists
      await this.createOrGetTenant(organizationId);

      // Ensure database exists
      await this.createOrGetDatabase(organizationId);

      // Try to get existing collection
      try {
        const collections = await this.makeRequest(`/tenants/${tenantName}/databases/${databaseName}/collections`);

        // Find collection by name
        const existingCollection = collections.find((col: any) => col.name === collectionName);
        if (existingCollection) {
          logger.info(`Found existing collection: ${collectionName}`);
          return existingCollection;
        }
      } catch (error) {
        // Collections endpoint might return 404 if no collections exist, which is fine
        logger.info(`No existing collections found, will create new one`);
      }

      // Create new collection
      const collection = await this.makeRequest(`/tenants/${tenantName}/databases/${databaseName}/collections`, {
        method: 'POST',
        body: JSON.stringify({
          name: collectionName,
          metadata: {
            organization_id: organizationId,
            created_at: new Date().toISOString(),
          },
        }),
      });

      logger.info(`Created new collection: ${collectionName} for organization: ${organizationId}`);
      return collection;
    } catch (error) {
      logger.error(`Error creating/getting collection for organization: ${organizationId}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }



  /**
   * Add documents to a collection
   */
  static async addDocuments(
    organizationId: string,
    documents: Array<{
      id: string;
      content: string;
      metadata?: Record<string, any>;
    }>
  ) {
    try {
      const tenantName = this.getTenantName(organizationId);
      const databaseName = this.DEFAULT_DATABASE;

      // Ensure collection exists
      const collection = await this.createOrGetCollection(organizationId);
      const collectionId = collection.id;

      const embeddings = this.getEmbeddings();

      // Generate embeddings for all documents
      const texts = documents.map(doc => doc.content);

      logger.info(`Generating embeddings for ${texts.length} documents`, {
        textLengths: texts.map(t => t.length),
        organizationId,
      });

      const embeddingVectors = await embeddings.embedDocuments(texts);

      // Prepare data for Chroma
      const ids = documents.map(doc => doc.id);
      const metadatas = documents.map(doc => doc.metadata || {});

      logger.info(`Prepared data for Chroma`, {
        idsCount: ids.length,
        embeddingsCount: embeddingVectors.length,
        embeddingDimensions: embeddingVectors[0]?.length,
        documentsCount: texts.length,
        metadatasCount: metadatas.length,
      });

      await this.makeRequest(`/tenants/${tenantName}/databases/${databaseName}/collections/${collectionId}/add`, {
        method: 'POST',
        body: JSON.stringify({
          ids,
          embeddings: embeddingVectors,
          documents: texts,
          metadatas,
        }),
      });

      logger.info(`Added ${documents.length} documents to collection for organization: ${organizationId}`);
      return { success: true, count: documents.length };
    } catch (error) {
      logger.error(`Error adding documents to collection for organization: ${organizationId}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        documentCount: documents.length,
      });
      throw error;
    }
  }

  /**
   * Query a collection for similar documents
   */
  static async queryCollection(
    organizationId: string,
    query: string,
    nResults: number = 5,
    where?: Record<string, any>
  ) {
    try {
      const tenantName = this.getTenantName(organizationId);
      const databaseName = this.DEFAULT_DATABASE;

      // Ensure collection exists
      const collection = await this.createOrGetCollection(organizationId);
      const collectionId = collection.id;

      const embeddings = this.getEmbeddings();

      // Generate embedding for the query
      const queryEmbedding = await embeddings.embedQuery(query);

      const results = await this.makeRequest(`/tenants/${tenantName}/databases/${databaseName}/collections/${collectionId}/query`, {
        method: 'POST',
        body: JSON.stringify({
          query_embeddings: [queryEmbedding],
          n_results: nResults,
          where,
          include: ['documents', 'metadatas', 'distances'],
        }),
      });

      logger.info(`Queried collection for organization: ${organizationId}`, {
        query,
        resultsCount: results.documents?.[0]?.length || 0,
      });

      return results;
    } catch (error) {
      logger.error(`Error querying collection for organization: ${organizationId}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        query,
      });
      throw error;
    }
  }



  /**
   * Delete documents from a collection
   */
  static async deleteDocuments(organizationId: string, ids: string[]) {
    try {
      const tenantName = this.getTenantName(organizationId);
      const databaseName = this.DEFAULT_DATABASE;

      // Ensure collection exists
      const collection = await this.createOrGetCollection(organizationId);
      const collectionId = collection.id;

      await this.makeRequest(`/tenants/${tenantName}/databases/${databaseName}/collections/${collectionId}/delete`, {
        method: 'POST',
        body: JSON.stringify({
          ids,
        }),
      });

      logger.info(`Deleted ${ids.length} documents from collection for organization: ${organizationId}`);
      return { success: true, count: ids.length };
    } catch (error) {
      logger.error(`Error deleting documents from collection for organization: ${organizationId}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        documentCount: ids.length,
      });
      throw error;
    }
  }

  /**
   * Delete a collection for an organization
   */
  static async deleteCollection(organizationId: string) {
    try {
      const tenantName = this.getTenantName(organizationId);
      const databaseName = this.DEFAULT_DATABASE;
      const collectionName = this.getCollectionName(organizationId);

      // Try to get the collection first to get its ID
      try {
        const collections = await this.makeRequest(`/tenants/${tenantName}/databases/${databaseName}/collections`);
        const collection = collections.find((col: any) => col.name === collectionName);

        if (collection) {
          await this.makeRequest(`/tenants/${tenantName}/databases/${databaseName}/collections/${collection.id}`, {
            method: 'DELETE',
          });
          logger.info(`Deleted collection: ${collectionName} for organization: ${organizationId}`);
          return { success: true };
        } else {
          logger.info(`Collection ${collectionName} not found for organization: ${organizationId}`);
          return { success: true, message: 'Collection not found' };
        }
      } catch (error) {
        logger.warn(`Error finding collection to delete for organization: ${organizationId}`, {
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        return { success: true, message: 'Collection not found or already deleted' };
      }
    } catch (error) {
      logger.error(`Error deleting collection for organization: ${organizationId}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }


}
