import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import axios from 'axios';
import dns from 'dns';
import { promisify } from 'util';

// Promisify DNS functions
const resolveTxt = promisify(dns.resolveTxt);
const resolve4 = promisify(dns.resolve4);

// Reputation thresholds
const REPUTATION_THRESHOLDS = {
  CRITICAL: 40,  // Below this score, account is suspended
  WARNING: 60,   // Below this score, warnings are issued
  GOOD: 80       // Above this score, account is considered healthy
};

// Bounce rate thresholds (percentage)
const BOUNCE_RATE_THRESHOLDS = {
  CRITICAL: 5,   // Above 5% is critical
  WARNING: 2     // Above 2% is concerning
};

// Open rate thresholds (percentage)
const OPEN_RATE_THRESHOLDS = {
  CRITICAL: 5,   // Below 5% is critical
  WARNING: 10    // Below 10% is concerning
};

interface ReputationCheckResult {
  score: number;
  provider: string;
  details: {
    spamScore?: number;
    blacklists?: string[];
    spfValid?: boolean;
    dkimValid?: boolean;
    dmarcValid?: boolean;
    bounceRate?: number;
    openRate?: number;
    deliverability?: number;
  };
}

export class ReputationMonitoringService {
  /**
   * Check the reputation of an email account
   */
  static async checkReputation(emailAccountId: string): Promise<ReputationCheckResult> {
    try {
      const emailAccount = await prisma.emailAccount.findUnique({
        where: { id: emailAccountId }
      });

      if (!emailAccount) {
        throw new Error('Email account not found');
      }

      // Get domain from email
      const domain = emailAccount.email.split('@')[1];
      
      // Perform checks in parallel
      const [
        blacklistResult,
        authenticationResult,
        bounceRateResult,
        openRateResult
      ] = await Promise.all([
        this.checkBlacklists(domain),
        this.checkAuthentication(domain),
        this.calculateBounceRate(emailAccountId),
        this.calculateOpenRate(emailAccountId)
      ]);

      // Calculate overall reputation score (0-100)
      const score = this.calculateReputationScore({
        blacklists: blacklistResult.blacklists,
        spfValid: authenticationResult.spfValid,
        dkimValid: authenticationResult.dkimValid,
        dmarcValid: authenticationResult.dmarcValid,
        bounceRate: bounceRateResult.bounceRate,
        openRate: openRateResult.openRate
      });

      // Compile results
      const result: ReputationCheckResult = {
        score,
        provider: 'Composite',
        details: {
          blacklists: blacklistResult.blacklists,
          spfValid: authenticationResult.spfValid,
          dkimValid: authenticationResult.dkimValid,
          dmarcValid: authenticationResult.dmarcValid,
          bounceRate: bounceRateResult.bounceRate,
          openRate: openRateResult.openRate,
          deliverability: score // Use the overall score as deliverability estimate
        }
      };

      // Save the reputation check result
      await prisma.reputationCheck.create({
        data: {
          emailAccountId,
          score: result.score,
          provider: result.provider,
          details: result.details,
        },
      });

      // Update the email account with the latest score
      await prisma.emailAccount.update({
        where: { id: emailAccountId },
        data: {
          reputationScore: result.score,
          lastChecked: new Date(),
        },
      });

      // Take action based on the reputation score
      await this.handleReputationScore(emailAccountId, result);

      return result;
    } catch (error) {
      logger.error('Error checking email reputation', {
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Check if a domain is on any blacklists using MX Toolbox API
   */
  private static async checkBlacklists(domain: string): Promise<{ blacklists: string[] }> {
    try {
      // Get the IP address for the domain
      const ips = await resolve4(domain);
      const ip = ips[0]; // Use the first IP

      // Call MX Toolbox API
      const response = await axios.get('https://api.mxtoolbox.com/api/v1/Lookup', {
        params: {
          command: 'blacklist',
          argument: ip
        },
        headers: {
          'Authorization': `Basic ${process.env.MXTOOLBOX_API_KEY}`
        }
      });

      // Extract blacklists from response
      const blacklists = response.data.BlacklistsListed || [];
      return { blacklists };
    } catch (error) {
      logger.error('Error checking blacklists', {
        domain,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      // Return empty result on error
      return { blacklists: [] };
    }
  }

  /**
   * Check SPF, DKIM, and DMARC records for a domain
   */
  private static async checkAuthentication(domain: string): Promise<{ 
    spfValid: boolean; 
    dkimValid: boolean; 
    dmarcValid: boolean; 
  }> {
    try {
      // Check SPF record
      const spfValid = await this.checkSpfRecord(domain);
      
      // Check DKIM record (using default selector 'default')
      const dkimValid = await this.checkDkimRecord('default', domain);
      
      // Check DMARC record
      const dmarcValid = await this.checkDmarcRecord(domain);

      return { spfValid, dkimValid, dmarcValid };
    } catch (error) {
      logger.error('Error checking authentication records', {
        domain,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      // Return all false on error
      return { spfValid: false, dkimValid: false, dmarcValid: false };
    }
  }

  /**
   * Check if a domain has a valid SPF record
   */
  private static async checkSpfRecord(domain: string): Promise<boolean> {
    try {
      const records = await resolveTxt(domain);
      return records.some(record => 
        record.some(txt => txt.toLowerCase().includes('v=spf1'))
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if a domain has a valid DKIM record
   */
  private static async checkDkimRecord(selector: string, domain: string): Promise<boolean> {
    try {
      const records = await resolveTxt(`${selector}._domainkey.${domain}`);
      return records.length > 0;
    } catch (error) {
      // Try with alternative selectors if default fails
      try {
        const records = await resolveTxt(`mail._domainkey.${domain}`);
        return records.length > 0;
      } catch {
        return false;
      }
    }
  }

  /**
   * Check if a domain has a valid DMARC record
   */
  private static async checkDmarcRecord(domain: string): Promise<boolean> {
    try {
      const records = await resolveTxt(`_dmarc.${domain}`);
      return records.some(record => 
        record.some(txt => txt.toLowerCase().includes('v=dmarc1'))
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Calculate bounce rate for an email account
   */
  private static async calculateBounceRate(emailAccountId: string): Promise<{ bounceRate: number }> {
    try {
      // Get total emails sent in the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const totalSent = await prisma.stepActivity.count({
        where: {
          step: {
            emailAccountId
          },
          sentAt: {
            gte: thirtyDaysAgo
          }
        }
      });

      // Get bounced emails in the last 30 days
      const totalBounced = await prisma.stepActivity.count({
        where: {
          step: {
            emailAccountId
          },
          sentAt: {
            gte: thirtyDaysAgo
          },
          status: 'bounced'
        }
      });

      // Calculate bounce rate (handle division by zero)
      const bounceRate = totalSent > 0 ? (totalBounced / totalSent) : 0;

      return { bounceRate };
    } catch (error) {
      logger.error('Error calculating bounce rate', {
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      // Return zero on error
      return { bounceRate: 0 };
    }
  }

  /**
   * Calculate open rate for an email account
   */
  private static async calculateOpenRate(emailAccountId: string): Promise<{ openRate: number }> {
    try {
      // Get total emails sent in the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const totalSent = await prisma.stepActivity.count({
        where: {
          step: {
            emailAccountId
          },
          sentAt: {
            gte: thirtyDaysAgo
          }
        }
      });

      // Get opened emails in the last 30 days
      const totalOpened = await prisma.stepActivity.count({
        where: {
          step: {
            emailAccountId
          },
          sentAt: {
            gte: thirtyDaysAgo
          },
          openedAt: {
            not: null
          }
        }
      });

      // Calculate open rate (handle division by zero)
      const openRate = totalSent > 0 ? (totalOpened / totalSent) : 0;

      return { openRate };
    } catch (error) {
      logger.error('Error calculating open rate', {
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      // Return zero on error
      return { openRate: 0 };
    }
  }

  /**
   * Calculate overall reputation score based on various factors
   */
  private static calculateReputationScore(factors: {
    blacklists: string[];
    spfValid: boolean;
    dkimValid: boolean;
    dmarcValid: boolean;
    bounceRate: number;
    openRate: number;
  }): number {
    // Start with a perfect score
    let score = 100;

    // Deduct for each blacklist (up to 50 points)
    score -= Math.min(factors.blacklists.length * 10, 50);

    // Deduct for missing authentication (up to 30 points)
    if (!factors.spfValid) score -= 10;
    if (!factors.dkimValid) score -= 10;
    if (!factors.dmarcValid) score -= 10;

    // Deduct for high bounce rate (up to 50 points)
    if (factors.bounceRate > BOUNCE_RATE_THRESHOLDS.CRITICAL / 100) {
      score -= 50;
    } else if (factors.bounceRate > BOUNCE_RATE_THRESHOLDS.WARNING / 100) {
      score -= 25;
    }

    // Deduct for low open rate (up to 20 points)
    if (factors.openRate < OPEN_RATE_THRESHOLDS.CRITICAL / 100) {
      score -= 20;
    } else if (factors.openRate < OPEN_RATE_THRESHOLDS.WARNING / 100) {
      score -= 10;
    }

    // Ensure score is between 0 and 100
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Handle reputation score and take appropriate actions
   */
  private static async handleReputationScore(emailAccountId: string, result: ReputationCheckResult) {
    const emailAccount = await prisma.emailAccount.findUnique({
      where: { id: emailAccountId },
      include: {
        user: true,
      }
    });

    if (!emailAccount) {
      throw new Error('Email account not found');
    }

    // Critical reputation - suspend the account
    if (result.score < REPUTATION_THRESHOLDS.CRITICAL) {
      await this.suspendEmailAccount(emailAccountId, result);
    }
    // Warning reputation - notify but don't suspend
    else if (result.score < REPUTATION_THRESHOLDS.WARNING) {
      await this.warnAboutReputation(emailAccountId, result);
    }
    // Good reputation - if account was suspended, reactivate it
    else if (result.score >= REPUTATION_THRESHOLDS.GOOD && emailAccount.status === 'suspended') {
      await this.reactivateEmailAccount(emailAccountId, result);
    }
  }

  /**
   * Suspend an email account due to poor reputation
   */
  private static async suspendEmailAccount(emailAccountId: string, result: ReputationCheckResult) {
    const emailAccount = await prisma.emailAccount.findUnique({
      where: { id: emailAccountId },
      include: {
        user: true,
      }
    });

    if (!emailAccount) {
      throw new Error('Email account not found');
    }

    // Only suspend if not already suspended
    if (emailAccount.status !== 'suspended') {
      // Update account status
      await prisma.emailAccount.update({
        where: { id: emailAccountId },
        data: {
          status: 'suspended',
        },
      });

      // Create a notification
      await prisma.notification.create({
        data: {
          userId: emailAccount.userId,
          organizationId: emailAccount.userId, // Assuming organization ID is the same as user ID
          title: 'Email Account Suspended',
          message: `Your email account ${emailAccount.email} has been suspended due to poor reputation (score: ${result.score.toFixed(1)})`,
          type: 'warning',
          isRead: false,
        },
      });

      logger.info(`Email account ${emailAccountId} suspended due to poor reputation`, {
        emailAccountId,
        score: result.score,
      });
    }
  }

  /**
   * Warn about poor reputation but don't suspend
   */
  private static async warnAboutReputation(emailAccountId: string, result: ReputationCheckResult) {
    const emailAccount = await prisma.emailAccount.findUnique({
      where: { id: emailAccountId },
      include: {
        user: true,
      }
    });

    if (!emailAccount) {
      throw new Error('Email account not found');
    }

    // Create a notification
    await prisma.notification.create({
      data: {
        userId: emailAccount.userId,
        organizationId: emailAccount.userId, // Assuming organization ID is the same as user ID
        title: 'Email Reputation Warning',
        message: `Your email account ${emailAccount.email} has a concerning reputation score of ${result.score.toFixed(1)}`,
        type: 'info',
        isRead: false,
      },
    });

    logger.info(`Email account ${emailAccountId} reputation warning issued`, {
      emailAccountId,
      score: result.score,
    });
  }

  /**
   * Reactivate a previously suspended email account
   */
  private static async reactivateEmailAccount(emailAccountId: string, result: ReputationCheckResult) {
    const emailAccount = await prisma.emailAccount.findUnique({
      where: { id: emailAccountId },
      include: {
        user: true,
      }
    });

    if (!emailAccount) {
      throw new Error('Email account not found');
    }

    // Update account status
    await prisma.emailAccount.update({
      where: { id: emailAccountId },
      data: {
        status: 'active',
      },
    });

    // Create a notification
    await prisma.notification.create({
      data: {
        userId: emailAccount.userId,
        organizationId: emailAccount.userId, // Assuming organization ID is the same as user ID
        title: 'Email Account Reactivated',
        message: `Your email account ${emailAccount.email} has been reactivated (score: ${result.score.toFixed(1)})`,
        type: 'success',
        isRead: false,
      },
    });

    logger.info(`Email account ${emailAccountId} reactivated due to improved reputation`, {
      emailAccountId,
      score: result.score,
    });
  }

  /**
   * Get detailed recommendations for improving email reputation
   */
  static async getReputationRecommendations(emailAccountId: string) {
    const emailAccount = await prisma.emailAccount.findUnique({
      where: { id: emailAccountId },
      include: {
        reputationChecks: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
      },
    });

    if (!emailAccount || emailAccount.reputationChecks.length === 0) {
      return {
        score: emailAccount?.reputationScore || 0,
        recommendations: [
          {
            type: 'GENERAL',
            priority: 'MEDIUM',
            message: 'Run a reputation check to get personalized recommendations',
            action: 'RUN_CHECK',
          },
        ],
      };
    }

    const latestCheck = emailAccount.reputationChecks[0];
    const recommendations = [];

    // Extract details from the latest check
    const details = latestCheck.details as any;

    // Check for blacklists
    if (details.blacklists && details.blacklists.length > 0) {
      recommendations.push({
        type: 'BLACKLIST',
        priority: 'HIGH',
        message: `Your email is listed on ${details.blacklists.length} blacklist(s): ${details.blacklists.join(', ')}`,
        action: 'DELIST',
        actionUrl: `https://mxtoolbox.com/blacklists.aspx?domain=${emailAccount.email.split('@')[1]}`,
      });
    }

    // Check for SPF
    if (details.spfValid === false) {
      recommendations.push({
        type: 'SPF',
        priority: 'HIGH',
        message: 'Your domain is missing a valid SPF record',
        action: 'CONFIGURE_SPF',
        actionUrl: 'https://www.spf-record.com/',
      });
    }

    // Check for DKIM
    if (details.dkimValid === false) {
      recommendations.push({
        type: 'DKIM',
        priority: 'HIGH',
        message: 'Your domain is missing DKIM signing',
        action: 'CONFIGURE_DKIM',
        actionUrl: 'https://www.dmarcanalyzer.com/dkim/',
      });
    }

    // Check for DMARC
    if (details.dmarcValid === false) {
      recommendations.push({
        type: 'DMARC',
        priority: 'HIGH',
        message: 'Your domain is missing a DMARC policy',
        action: 'CONFIGURE_DMARC',
        actionUrl: 'https://www.dmarcanalyzer.com/dmarc/',
      });
    }

    // Check bounce rate
    if (details.bounceRate && details.bounceRate > BOUNCE_RATE_THRESHOLDS.WARNING / 100) {
      recommendations.push({
        type: 'BOUNCE_RATE',
        priority: 'HIGH',
        message: `Your bounce rate (${(details.bounceRate * 100).toFixed(1)}%) is too high`,
        action: 'CLEAN_LIST',
        actionUrl: `/leads`,
      });
    }

    // Check open rate
    if (details.openRate && details.openRate < OPEN_RATE_THRESHOLDS.WARNING / 100) {
      recommendations.push({
        type: 'OPEN_RATE',
        priority: 'MEDIUM',
        message: `Your open rate (${(details.openRate * 100).toFixed(1)}%) is too low`,
        action: 'IMPROVE_CONTENT',
        actionUrl: `/templates`,
      });
    }

    // If reputation is low, suggest warmup
    if (latestCheck.score < REPUTATION_THRESHOLDS.WARNING) {
      recommendations.push({
        type: 'WARMUP',
        priority: 'HIGH',
        message: 'Your email reputation is low. Consider using the email warmup feature.',
        action: 'START_WARMUP',
        actionUrl: `/email-accounts/${emailAccountId}?tab=reputation`,
      });
    }

    return {
      score: latestCheck.score,
      recommendations,
    };
  }
}
