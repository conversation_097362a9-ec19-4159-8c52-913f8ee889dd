import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { CampaignService } from './campaign.service';

export class SequenceCampaignService {
  /**
   * Process all active sequence campaigns
   * This checks conditions and moves leads to the next step when conditions are met
   */
  static async processActiveSequenceCampaigns() {
    try {
      logger.info('[SEQUENCE_DEBUG] Starting to process active sequence campaigns');

      // Find all active sequence campaigns
      const activeSequenceCampaigns = await prisma.campaign.findMany({
        where: {
          status: 'active',
          type: 'sequence',
        },
        include: {
          steps: {
            orderBy: {
              position: 'asc',
            },
            include: {
              emailAccount: true,
              emailAccounts: {
                include: {
                  emailAccount: true
                }
              },
              conditionStep: true,
              conditionedSteps: true,
            },
          },
          leads: {
            include: {
              lead: true,
              currentStep: true,
              stepActivities: {
                orderBy: {
                  createdAt: 'desc',
                },
                include: {
                  step: true,
                }
              },
            },
          },
        },
      });

      logger.info(`[SEQUENCE_DEBUG] Found ${activeSequenceCampaigns.length} active sequence campaigns`);

      // Process each campaign
      for (const campaign of activeSequenceCampaigns) {
        await this.processSequenceCampaign(campaign);
      }

      logger.info('[SEQUENCE_DEBUG] Completed processing active sequence campaigns');
    } catch (error) {
      logger.error('[SEQUENCE_DEBUG] Error processing active sequence campaigns', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
      });
    }
  }

  /**
   * Process a single sequence campaign
   * This checks conditions for each lead and moves them to the next step when conditions are met
   */
  static async processSequenceCampaign(campaign: any) {
    try {
      logger.info(`[SEQUENCE_DEBUG] Processing sequence campaign: ${campaign.id} - ${campaign.name}`);

      // Process each lead in the campaign
      for (const campaignLead of campaign.leads) {
        // Skip leads that have completed the campaign or are unsubscribed/bounced
        if (campaignLead.status !== 'active') {
          logger.info(`[SEQUENCE_DEBUG] Skipping lead ${campaignLead.lead.email} - status: ${campaignLead.status}`);
          continue;
        }

        // Skip leads that don't have a current step
        if (!campaignLead.currentStepId) {
          logger.info(`[SEQUENCE_DEBUG] Skipping lead ${campaignLead.lead.email} - no current step`);
          continue;
        }

        await this.processLeadInSequence(campaign, campaignLead);
      }

      // After processing all leads, check if the campaign should be marked as completed
      await this.updateSequenceCampaignStatus(campaign);

      logger.info(`[SEQUENCE_DEBUG] Completed processing sequence campaign: ${campaign.id}`);
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error processing sequence campaign ${campaign.id}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId: campaign.id,
      });
    }
  }

  /**
   * Process a single lead in a sequence campaign
   * This checks the current step's conditions and moves the lead to the next step if conditions are met
   */
  static async processLeadInSequence(campaign: any, campaignLead: any) {
    try {
      const currentStep = campaignLead.currentStep;

      // If current step is not found, log error and return
      if (!currentStep) {
        logger.error(`[SEQUENCE_DEBUG] Current step not found for lead ${campaignLead.lead.email} in campaign ${campaign.id}`);
        return;
      }

      logger.info(`[SEQUENCE_DEBUG] Processing lead ${campaignLead.lead.email} at step ${currentStep.position} (${currentStep.type})`);

      // Handle different step types
      switch (currentStep.type) {
        case 'email':
          await this.processEmailStep(campaign, campaignLead, currentStep);
          break;
        case 'wait':
          await this.processWaitStep(campaign, campaignLead, currentStep);
          break;
        case 'condition':
          await this.processConditionStep(campaign, campaignLead, currentStep);
          break;
        case 'action':
          await this.processActionStep(campaign, campaignLead, currentStep);
          break;
        default:
          logger.warn(`[SEQUENCE_DEBUG] Unknown step type: ${currentStep.type}`);
      }
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error processing lead ${campaignLead.lead.email} in sequence`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId: campaign.id,
        leadId: campaignLead.id,
      });
    }
  }

  /**
   * Process an email step for a lead
   * If the email hasn't been sent yet, send it
   * If the email has been sent, check if there are any next steps to move to
   */
  static async processEmailStep(campaign: any, campaignLead: any, currentStep: any) {
    try {
      // Check if the email has already been sent
      const emailSent = campaignLead.stepActivities.some(
        (activity: any) =>
          activity.stepId === currentStep.id &&
          ['sent', 'opened', 'clicked', 'replied'].includes(activity.status)
      );

      // If email hasn't been sent yet, send it
      if (!emailSent) {
        logger.info(`[SEQUENCE_DEBUG] Email not yet sent for lead ${campaignLead.lead.email} at step ${currentStep.position}`);

        // Find a valid email account for sending
        const emailAccountId = await CampaignService.selectEmailAccount(currentStep.id);

        if (!emailAccountId) {
          logger.error(`[SEQUENCE_DEBUG] No valid email account found for step ${currentStep.id}`);
          return;
        }

        // Queue the email for sending
        await CampaignService.queueCampaignEmail(
          campaign.id,
          campaignLead.id,
          campaignLead.lead.email,
          currentStep.subject || '',
          currentStep.content || '',
          emailAccountId,
          Math.floor(Math.random() * 15) + 5 // 5-20 seconds delay
        );

        logger.info(`[SEQUENCE_DEBUG] Queued email for lead ${campaignLead.lead.email} at step ${currentStep.position}`);
        return;
      }

      // If email has been sent, check if there are any next steps
      logger.info(`[SEQUENCE_DEBUG] Email already sent for lead ${campaignLead.lead.email} at step ${currentStep.position}`);

      // Check if we need to wait for conditions before moving to the next step
      const nextStep = this.findNextStep(campaign, currentStep);

      if (nextStep) {
        // If the next step is a condition step, we don't automatically move to it
        // The condition checking process will handle this when the condition is evaluated
        if (nextStep.type === 'condition') {
          logger.info(`[SEQUENCE_DEBUG] Next step is a condition step. Waiting for condition evaluation for lead ${campaignLead.lead.email}`);
          return;
        }

        // If the next step is a wait step, we don't automatically move to it either
        // We'll let the sequence processor handle it in the next run
        if (nextStep.type === 'wait') {
          logger.info(`[SEQUENCE_DEBUG] Next step is a wait step. Moving lead ${campaignLead.lead.email} to wait step`);
          await this.moveLeadToNextStep(campaignLead, nextStep);
          return;
        }

        // For other step types (email, action), move to the next step
        logger.info(`[SEQUENCE_DEBUG] Moving lead ${campaignLead.lead.email} to next step (${nextStep.type})`);
        await this.moveLeadToNextStep(campaignLead, nextStep);
      } else {
        // If there are no more steps, mark the lead as completed
        await this.completeLeadInCampaign(campaignLead);
      }
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error processing email step for lead ${campaignLead.lead.email}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId: campaign.id,
        leadId: campaignLead.id,
        stepId: currentStep.id,
      });
    }
  }

  /**
   * Process a wait step for a lead
   * Check if the wait duration has passed, and if so, move to the next step
   */
  static async processWaitStep(campaign: any, campaignLead: any, currentStep: any) {
    try {
      // Check if we've recorded that we're waiting
      const waitActivity = campaignLead.stepActivities.find(
        (activity: any) => activity.stepId === currentStep.id && activity.type === 'wait_started'
      );

      // If we haven't recorded that we're waiting, create a wait activity
      if (!waitActivity) {
        logger.info(`[SEQUENCE_DEBUG] Starting wait period for lead ${campaignLead.lead.email} at step ${currentStep.position}`);

        // Create a wait activity to mark the start of the wait period
        await prisma.stepActivity.create({
          data: {
            campaignLeadId: campaignLead.id,
            stepId: currentStep.id,
            type: 'wait_started',
            status: 'waiting',
            metadata: {
              waitDuration: currentStep.waitDuration,
              waitUntil: currentStep.waitUntil
                ? new Date(currentStep.waitUntil).toISOString()
                : new Date(Date.now() + (currentStep.waitDuration || 24) * 60 * 60 * 1000).toISOString(),
            },
          },
        });

        return;
      }

      // Check if the wait period has ended
      const now = new Date();
      let waitUntil: Date;

      if (currentStep.waitUntil) {
        // If there's a specific date to wait until
        waitUntil = new Date(currentStep.waitUntil);
      } else if (waitActivity.metadata?.waitUntil) {
        // If the wait activity has a waitUntil timestamp
        waitUntil = new Date(waitActivity.metadata.waitUntil);
      } else if (currentStep.waitDuration) {
        // Calculate based on wait duration and when the wait started
        const waitStarted = new Date(waitActivity.createdAt);
        waitUntil = new Date(waitStarted.getTime() + currentStep.waitDuration * 60 * 60 * 1000);
      } else {
        // Default to 24 hours from when the wait started
        const waitStarted = new Date(waitActivity.createdAt);
        waitUntil = new Date(waitStarted.getTime() + 24 * 60 * 60 * 1000);
      }

      // If the wait period has ended, move to the next step
      if (now >= waitUntil) {
        logger.info(`[SEQUENCE_DEBUG] Wait period ended for lead ${campaignLead.lead.email} at step ${currentStep.position}`);

        // Mark the wait as completed
        await prisma.stepActivity.create({
          data: {
            campaignLeadId: campaignLead.id,
            stepId: currentStep.id,
            type: 'wait_completed',
            status: 'completed',
          },
        });

        // Find the next step in the sequence
        const nextStep = this.findNextStep(campaign, currentStep);

        if (nextStep) {
          await this.moveLeadToNextStep(campaignLead, nextStep);
        } else {
          // If there are no more steps, mark the lead as completed
          await this.completeLeadInCampaign(campaignLead);
        }
      } else {
        logger.info(`[SEQUENCE_DEBUG] Still waiting for lead ${campaignLead.lead.email} at step ${currentStep.position} - wait until ${waitUntil.toISOString()}`);
      }
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error processing wait step for lead ${campaignLead.lead.email}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId: campaign.id,
        leadId: campaignLead.id,
        stepId: currentStep.id,
      });
    }
  }

  /**
   * Process a condition step for a lead
   * Check if the condition is met, and if so, move to the appropriate next step
   */
  static async processConditionStep(campaign: any, campaignLead: any, currentStep: any) {
    try {
      logger.info(`[SEQUENCE_DEBUG] Processing condition step for lead ${campaignLead.lead.email} at step ${currentStep.position}`);

      // Log detailed information about the condition step
      logger.info(`[SEQUENCE_DEBUG] Condition step details:`, {
        stepId: currentStep.id,
        conditionType: currentStep.conditionType,
        conditionStepId: currentStep.conditionStepId,
        conditionTimeframe: currentStep.conditionTimeframe,
        campaignId: campaign.id,
        leadId: campaignLead.id,
        leadEmail: campaignLead.lead.email
      });

      // Skip if no condition step is specified
      if (!currentStep.conditionStepId) {
        logger.warn(`[SEQUENCE_DEBUG] Condition step ${currentStep.id} has no conditionStepId`);

        // Move to the next step in the sequence
        const nextStep = this.findNextStep(campaign, currentStep);

        if (nextStep) {
          await this.moveLeadToNextStep(campaignLead, nextStep);
        } else {
          await this.completeLeadInCampaign(campaignLead);
        }

        return;
      }

      // We'll always re-evaluate conditions to ensure they're properly processed
      // This is more aggressive than the previous approach which skipped recent evaluations
      logger.info(`[SEQUENCE_DEBUG] Always evaluating condition for lead ${campaignLead.lead.email} at step ${currentStep.position}`);

      // Get the most recent evaluation for logging purposes
      const recentEvaluations = campaignLead.stepActivities.filter(
        (activity: any) =>
          activity.stepId === currentStep.id &&
          activity.type === 'condition_evaluated'
      );

      if (recentEvaluations.length > 0) {
        const mostRecent = recentEvaluations[0];
        logger.info(`[SEQUENCE_DEBUG] Previous condition evaluation found:`, {
          result: mostRecent.status === 'condition_met' ? 'met' : 'not met',
          timestamp: new Date(mostRecent.createdAt).toISOString(),
          timeSince: `${Math.round((Date.now() - new Date(mostRecent.createdAt).getTime()) / 1000 / 60)} minutes ago`
        });
      }

      // Get the condition step (the step we're checking conditions for)
      const conditionStep = campaign.steps.find((step: any) => step.id === currentStep.conditionStepId);

      if (!conditionStep) {
        logger.error(`[SEQUENCE_DEBUG] Condition step reference not found: ${currentStep.conditionStepId}`);
        return;
      }

      logger.info(`[SEQUENCE_DEBUG] Checking condition for lead ${campaignLead.lead.email}`, {
        conditionType: currentStep.conditionType,
        conditionStepId: currentStep.conditionStepId,
        conditionStepPosition: conditionStep.position,
        timeframe: currentStep.conditionTimeframe || 48
      });

      // Check if the condition is met
      const conditionMet = await this.checkCondition(campaignLead, currentStep, conditionStep);

      // Record the condition evaluation
      await prisma.stepActivity.create({
        data: {
          campaignLeadId: campaignLead.id,
          stepId: currentStep.id,
          type: 'condition_evaluated',
          status: conditionMet ? 'condition_met' : 'condition_not_met',
          metadata: {
            conditionType: currentStep.conditionType,
            conditionStepId: currentStep.conditionStepId,
            conditionTimeframe: currentStep.conditionTimeframe,
            result: conditionMet,
            timestamp: new Date().toISOString()
          },
        },
      });

      // If the condition is not met, we'll check if we should wait longer or move on
      if (!conditionMet) {
        // For positive conditions (opened, clicked, replied), we'll check if we're still within the timeframe
        if (['opened', 'clicked', 'replied'].includes(currentStep.conditionType)) {
          // Get the sent activity for the condition step
          const sentActivity = campaignLead.stepActivities.find(
            (activity: any) =>
              activity.stepId === conditionStep.id &&
              (activity.status === 'sent' || activity.type === 'email_sent')
          );

          if (sentActivity) {
            const sentTime = new Date(sentActivity.sentAt || sentActivity.createdAt);
            const timeframeHours = currentStep.conditionTimeframe || 48;
            const timeframeEnd = new Date(sentTime.getTime() + timeframeHours * 60 * 60 * 1000);
            const now = new Date();

            // Calculate how much time is left in the timeframe
            const hoursLeft = Math.round((timeframeEnd.getTime() - now.getTime()) / (60 * 60 * 1000));

            // If we're still within the timeframe, don't move yet
            if (now <= timeframeEnd) {
              logger.info(`[SEQUENCE_DEBUG] Condition not met but still within timeframe for lead ${campaignLead.lead.email}. ${hoursLeft} hours left in timeframe.`);

              // Create a record that we checked but are still waiting
              await prisma.stepActivity.create({
                data: {
                  campaignLeadId: campaignLead.id,
                  stepId: currentStep.id,
                  type: 'condition_waiting',
                  status: 'waiting',
                  metadata: {
                    conditionType: currentStep.conditionType,
                    timeframeEnd: timeframeEnd.toISOString(),
                    hoursLeft,
                    timestamp: new Date().toISOString()
                  },
                },
              });

              return;
            } else {
              logger.info(`[SEQUENCE_DEBUG] Timeframe expired for condition ${currentStep.conditionType}. Moving to next step.`);
            }
          } else {
            logger.warn(`[SEQUENCE_DEBUG] No sent activity found for condition step. This is unusual.`);
          }
        }
      }

      // Find the next step based on the condition result
      let nextStep;

      if (conditionMet) {
        logger.info(`[SEQUENCE_DEBUG] Condition MET for lead ${campaignLead.lead.email}. Taking YES path.`);
        // If condition is met, find the first conditioned step (if any)
        nextStep = currentStep.conditionedSteps && currentStep.conditionedSteps.length > 0
          ? currentStep.conditionedSteps.sort((a: any, b: any) => a.position - b.position)[0]
          : this.findNextStep(campaign, currentStep);
      } else {
        logger.info(`[SEQUENCE_DEBUG] Condition NOT MET for lead ${campaignLead.lead.email}. Taking NO path.`);
        // If condition is not met, move to the next step in the sequence
        nextStep = this.findNextStep(campaign, currentStep);
      }

      if (nextStep) {
        logger.info(`[SEQUENCE_DEBUG] Moving lead ${campaignLead.lead.email} to next step ${nextStep.position} (${nextStep.type})`);
        await this.moveLeadToNextStep(campaignLead, nextStep);
      } else {
        logger.info(`[SEQUENCE_DEBUG] No next step found for lead ${campaignLead.lead.email}. Completing campaign.`);
        await this.completeLeadInCampaign(campaignLead);
      }
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error processing condition step for lead ${campaignLead.lead.email}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId: campaign.id,
        leadId: campaignLead.id,
        stepId: currentStep.id,
      });
    }
  }

  /**
   * Process an action step for a lead
   * Perform the action and move to the next step
   */
  static async processActionStep(campaign: any, campaignLead: any, currentStep: any) {
    try {
      // Check if the action has already been performed
      const actionPerformed = campaignLead.stepActivities.some(
        (activity: any) => activity.stepId === currentStep.id && activity.type === 'action_performed'
      );

      if (actionPerformed) {
        logger.info(`[SEQUENCE_DEBUG] Action already performed for lead ${campaignLead.lead.email} at step ${currentStep.position}`);
        return;
      }

      // Perform the action based on the action type
      switch (currentStep.actionType) {
        case 'update_lead':
          await this.performUpdateLeadAction(campaignLead, currentStep);
          break;
        // Add more action types as needed
        default:
          logger.warn(`[SEQUENCE_DEBUG] Unknown action type: ${currentStep.actionType}`);
      }

      // Record that the action was performed
      await prisma.stepActivity.create({
        data: {
          campaignLeadId: campaignLead.id,
          stepId: currentStep.id,
          type: 'action_performed',
          status: 'completed',
          metadata: {
            actionType: currentStep.actionType,
            actionValue: currentStep.actionValue,
          },
        },
      });

      // Find the next step in the sequence
      const nextStep = this.findNextStep(campaign, currentStep);

      if (nextStep) {
        await this.moveLeadToNextStep(campaignLead, nextStep);
      } else {
        await this.completeLeadInCampaign(campaignLead);
      }
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error processing action step for lead ${campaignLead.lead.email}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId: campaign.id,
        leadId: campaignLead.id,
        stepId: currentStep.id,
      });
    }
  }

  /**
   * Check if a condition is met for a lead
   */
  static async checkCondition(campaignLead: any, conditionStep: any, targetStep: any) {
    try {
      // Get the condition type and timeframe
      const conditionType = conditionStep.conditionType;
      const timeframeHours = conditionStep.conditionTimeframe || 48; // Default to 48 hours

      logger.info(`[SEQUENCE_DEBUG] Checking condition ${conditionType} for lead ${campaignLead.lead.email}`, {
        conditionType,
        timeframeHours,
        targetStepId: targetStep.id,
        targetStepPosition: targetStep.position,
        targetStepType: targetStep.type
      });

      // Get the latest activities for the target step from the database to ensure we have the most up-to-date data
      const latestActivities = await prisma.stepActivity.findMany({
        where: {
          campaignLeadId: campaignLead.id,
          stepId: targetStep.id
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Combine with the activities we already have in memory
      const existingActivities = campaignLead.stepActivities.filter(
        (activity: any) => activity.stepId === targetStep.id
      );

      // Use a Set to deduplicate by ID
      const activityIds = new Set();
      const targetStepActivities = [];

      // Add latest activities from database first
      for (const activity of latestActivities) {
        if (!activityIds.has(activity.id)) {
          activityIds.add(activity.id);
          targetStepActivities.push(activity);
        }
      }

      // Then add any activities from memory that aren't already included
      for (const activity of existingActivities) {
        if (!activityIds.has(activity.id)) {
          activityIds.add(activity.id);
          targetStepActivities.push(activity);
        }
      }

      logger.info(`[SEQUENCE_DEBUG] Found ${targetStepActivities.length} activities for target step`, {
        leadEmail: campaignLead.lead.email,
        stepId: targetStep.id,
        activityCount: targetStepActivities.length
      });

      logger.info(`[SEQUENCE_DEBUG] Found ${targetStepActivities.length} activities for target step ${targetStep.id}`);

      // If there are no activities for the target step, the condition can't be met
      if (targetStepActivities.length === 0) {
        logger.info(`[SEQUENCE_DEBUG] No activities found for target step ${targetStep.id}`);
        return false;
      }

      // Get the most recent email sent activity
      const sentActivity = targetStepActivities.find(
        (activity: any) => activity.status === 'sent' || activity.type === 'email_sent'
      );

      // If the email hasn't been sent, the condition can't be met
      if (!sentActivity) {
        logger.info(`[SEQUENCE_DEBUG] Email not sent for target step ${targetStep.id}`);
        return false;
      }

      // Calculate the timeframe end time
      const sentTime = new Date(sentActivity.sentAt || sentActivity.createdAt);
      const timeframeEnd = new Date(sentTime.getTime() + timeframeHours * 60 * 60 * 1000);
      const now = new Date();

      // Check if we're still within the timeframe
      const timeframeActive = now <= timeframeEnd;

      logger.info(`[SEQUENCE_DEBUG] Timeframe check for condition ${conditionType}`, {
        sentTime: sentTime.toISOString(),
        timeframeEnd: timeframeEnd.toISOString(),
        now: now.toISOString(),
        timeframeActive,
        timeframeHours
      });

      // If the timeframe has ended and we're checking for an action that should have happened,
      // then the condition is not met
      if (!timeframeActive && ['opened', 'clicked', 'replied'].includes(conditionType)) {
        logger.info(`[SEQUENCE_DEBUG] Timeframe ended for condition ${conditionType}`);
        return false;
      }

      // If the timeframe has ended and we're checking for an action that should NOT have happened,
      // then the condition is met
      if (!timeframeActive && ['not_opened', 'not_clicked', 'not_replied'].includes(conditionType)) {
        logger.info(`[SEQUENCE_DEBUG] Timeframe ended for negative condition ${conditionType}`);
        return true;
      }

      // Check for specific activities based on the condition type
      let conditionMet = false;
      let activityFound = false;

      // Log all activities for debugging
      targetStepActivities.forEach((activity: any) => {
        logger.info(`[SEQUENCE_DEBUG] Activity for step ${targetStep.id}:`, {
          type: activity.type,
          status: activity.status,
          createdAt: new Date(activity.createdAt).toISOString()
        });
      });

      // Check the condition based on the condition type
      switch (conditionType) {
        case 'opened':
          // First check in the activities we already have
          activityFound = targetStepActivities.some(
            (activity: any) => activity.status === 'opened' || activity.type === 'email_opened'
          );

          // If not found, do a direct database check as a fallback
          if (!activityFound) {
            logger.info(`[SEQUENCE_DEBUG] No open activity found in memory, checking database directly`);

            // Check directly in the database for any open activities
            const openActivity = await prisma.stepActivity.findFirst({
              where: {
                campaignLeadId: campaignLead.id,
                stepId: targetStep.id,
                type: 'email_opened'
              }
            });

            activityFound = !!openActivity;

            if (activityFound) {
              logger.info(`[SEQUENCE_DEBUG] Found open activity in database that wasn't in memory!`, {
                activityId: openActivity.id,
                createdAt: openActivity.createdAt
              });
            }
          }

          conditionMet = activityFound;

          logger.info(`[SEQUENCE_DEBUG] Checked 'opened' condition: ${conditionMet}`, {
            leadEmail: campaignLead.lead.email,
            activityFound,
            activityCount: targetStepActivities.length,
            activities: targetStepActivities.map((a: any) => ({
              id: a.id,
              type: a.type,
              status: a.status,
              createdAt: a.createdAt
            }))
          });
          break;

        case 'not_opened':
          activityFound = targetStepActivities.some(
            (activity: any) => activity.status === 'opened' || activity.type === 'email_opened'
          );
          conditionMet = !activityFound;
          logger.info(`[SEQUENCE_DEBUG] Checked 'not_opened' condition: ${conditionMet}`);
          break;

        case 'clicked':
          activityFound = targetStepActivities.some(
            (activity: any) => activity.status === 'clicked' || activity.type === 'link_clicked'
          );
          conditionMet = activityFound;
          logger.info(`[SEQUENCE_DEBUG] Checked 'clicked' condition: ${conditionMet}`);
          break;

        case 'not_clicked':
          activityFound = targetStepActivities.some(
            (activity: any) => activity.status === 'clicked' || activity.type === 'link_clicked'
          );
          conditionMet = !activityFound;
          logger.info(`[SEQUENCE_DEBUG] Checked 'not_clicked' condition: ${conditionMet}`);
          break;

        case 'replied':
          activityFound = targetStepActivities.some(
            (activity: any) => activity.status === 'replied' || activity.type === 'email_replied'
          );
          conditionMet = activityFound;
          logger.info(`[SEQUENCE_DEBUG] Checked 'replied' condition: ${conditionMet}`);
          break;

        case 'not_replied':
          activityFound = targetStepActivities.some(
            (activity: any) => activity.status === 'replied' || activity.type === 'email_replied'
          );
          conditionMet = !activityFound;
          logger.info(`[SEQUENCE_DEBUG] Checked 'not_replied' condition: ${conditionMet}`);
          break;

        default:
          logger.warn(`[SEQUENCE_DEBUG] Unknown condition type: ${conditionType}`);
          conditionMet = false;
      }

      // Log the final result
      logger.info(`[SEQUENCE_DEBUG] Condition check result for ${conditionType}: ${conditionMet}`, {
        leadEmail: campaignLead.lead.email,
        conditionType,
        conditionMet,
        timeframeActive
      });

      return conditionMet;
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error checking condition`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignLeadId: campaignLead.id,
        conditionStepId: conditionStep.id,
        targetStepId: targetStep.id,
      });
      return false;
    }
  }

  /**
   * Perform an update lead action
   */
  static async performUpdateLeadAction(campaignLead: any, actionStep: any) {
    try {
      // Parse the action value (expected format: "field:value")
      const actionValue = actionStep.actionValue || '';
      const [field, value] = actionValue.split(':').map(s => s.trim());

      if (!field || value === undefined) {
        logger.warn(`[SEQUENCE_DEBUG] Invalid action value format: ${actionValue}`);
        return;
      }

      // Update the lead based on the field
      switch (field.toLowerCase()) {
        case 'tag':
          // Add a tag to the lead's tags array
          await prisma.lead.update({
            where: { id: campaignLead.leadId },
            data: {
              tags: {
                push: value
              }
            },
          });
          logger.info(`[SEQUENCE_DEBUG] Added tag "${value}" to lead ${campaignLead.lead.email}`);
          break;

        case 'status':
          // Update the lead status
          await prisma.lead.update({
            where: { id: campaignLead.leadId },
            data: { status: value },
          });
          logger.info(`[SEQUENCE_DEBUG] Updated status to "${value}" for lead ${campaignLead.lead.email}`);
          break;

        // Add more fields as needed

        default:
          // For custom fields, update the lead's customFields
          await prisma.lead.update({
            where: { id: campaignLead.leadId },
            data: {
              customFields: {
                ...(campaignLead.lead.customFields || {}),
                [field]: value,
              },
            },
          });
          logger.info(`[SEQUENCE_DEBUG] Updated custom field "${field}" to "${value}" for lead ${campaignLead.lead.email}`);
      }
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error performing update lead action`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignLeadId: campaignLead.id,
        actionStepId: actionStep.id,
        actionValue: actionStep.actionValue,
      });
    }
  }

  /**
   * Find the next step in the sequence
   */
  static findNextStep(campaign: any, currentStep: any) {
    // Get all steps sorted by position
    const sortedSteps = [...campaign.steps].sort((a, b) => a.position - b.position);

    // Find the index of the current step
    const currentIndex = sortedSteps.findIndex(step => step.id === currentStep.id);

    // If the current step is not found or is the last step, return null
    if (currentIndex === -1 || currentIndex === sortedSteps.length - 1) {
      return null;
    }

    // Return the next step
    return sortedSteps[currentIndex + 1];
  }

  /**
   * Move a lead to the next step in the sequence
   */
  static async moveLeadToNextStep(campaignLead: any, nextStep: any) {
    try {
      logger.info(`[SEQUENCE_DEBUG] Moving lead ${campaignLead.lead.email} to step ${nextStep.position} (${nextStep.type})`);

      // Update the campaign lead's current step
      await prisma.campaignLead.update({
        where: { id: campaignLead.id },
        data: { currentStepId: nextStep.id },
      });

      // Create a step transition activity
      await prisma.stepActivity.create({
        data: {
          campaignLeadId: campaignLead.id,
          stepId: nextStep.id,
          type: 'step_entered',
          status: 'active',
          metadata: {
            previousStepId: campaignLead.currentStepId,
            timestamp: new Date().toISOString(),
          },
        },
      });
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error moving lead to next step`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignLeadId: campaignLead.id,
        nextStepId: nextStep.id,
      });
    }
  }

  /**
   * Mark a lead as completed in the campaign
   */
  static async completeLeadInCampaign(campaignLead: any) {
    try {
      logger.info(`[SEQUENCE_DEBUG] Completing lead ${campaignLead.lead.email} in campaign`);

      // Update the campaign lead status to completed
      await prisma.campaignLead.update({
        where: { id: campaignLead.id },
        data: {
          status: 'completed',
          completedAt: new Date(),
        },
      });

      // Create a completion activity
      await prisma.stepActivity.create({
        data: {
          campaignLeadId: campaignLead.id,
          stepId: campaignLead.currentStepId,
          type: 'campaign_completed',
          status: 'completed',
          metadata: {
            timestamp: new Date().toISOString(),
          },
        },
      });

      // Get the campaign to check if all leads are completed
      const campaign = await prisma.campaign.findUnique({
        where: { id: campaignLead.campaignId },
        include: {
          leads: true,
        },
      });

      if (campaign) {
        // Check if we should update the campaign status
        await this.updateSequenceCampaignStatus(campaign);
      }
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error completing lead in campaign`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignLeadId: campaignLead.id,
      });
    }
  }
  /**
   * Check if a sequence campaign should be marked as completed
   * A sequence campaign is only completed when all leads have either:
   * 1. Completed the entire sequence (reached the end)
   * 2. Been marked as unsubscribed, bounced, or otherwise inactive
   */
  /**
   * Check if a sequence campaign should be marked as completed
   * A sequence campaign is only completed when all leads have either:
   * 1. Completed the entire sequence (reached the end)
   * 2. Been marked as unsubscribed, bounced, or otherwise inactive
   *
   * This method is optimized for production use with proper error handling and logging
   */
  static async updateSequenceCampaignStatus(campaign: any) {
    const startTime = Date.now();
    const campaignId = campaign?.id || 'unknown';

    try {
      logger.info(`[SEQUENCE_DEBUG] Checking if sequence campaign ${campaignId} should be marked as completed`);

      // Validate campaign object
      if (!campaign || !campaignId || campaignId === 'unknown') {
        logger.error(`[SEQUENCE_DEBUG] Invalid campaign object provided to updateSequenceCampaignStatus`);
        return;
      }

      // If the campaign is already completed, don't do anything
      if (campaign.status === 'completed') {
        logger.info(`[SEQUENCE_DEBUG] Campaign ${campaignId} is already completed`);
        return;
      }

      // If the campaign is not active or in_progress, don't do anything
      if (campaign.status !== 'active' && campaign.status !== 'in_progress') {
        logger.info(`[SEQUENCE_DEBUG] Campaign ${campaignId} is not active or in_progress (status: ${campaign.status})`);
        return;
      }

      // Get the latest campaign data with all leads - use a transaction for consistency
      const updatedCampaign = await prisma.$transaction(async (tx) => {
        const campaign = await tx.campaign.findUnique({
          where: { id: campaignId },
          include: {
            leads: {
              select: {
                id: true,
                status: true,
                leadId: true,
                campaignId: true
              }
            },
            steps: {
              orderBy: {
                position: 'asc',
              },
              select: {
                id: true,
                position: true,
                type: true
              }
            },
          },
        });

        return campaign;
      }, {
        maxWait: 5000, // 5 seconds max wait time
        timeout: 10000 // 10 seconds transaction timeout
      });

      if (!updatedCampaign) {
        logger.error(`[SEQUENCE_DEBUG] Campaign ${campaignId} not found when checking completion status`);
        return;
      }

      // Check if there are any active leads still in the campaign
      const activeLeads = updatedCampaign.leads.filter(lead => lead.status === 'active');
      const completedLeads = updatedCampaign.leads.filter(lead => lead.status === 'completed');
      const totalLeads = updatedCampaign.leads.length;

      logger.info(`[SEQUENCE_DEBUG] Campaign ${campaignId} status check:`, {
        activeLeads: activeLeads.length,
        completedLeads: completedLeads.length,
        totalLeads,
        currentStatus: updatedCampaign.status
      });

      // Check for error leads
      const errorLeads = updatedCampaign.leads.filter(lead => lead.status === 'error');
      const unsubscribedLeads = updatedCampaign.leads.filter(lead => lead.status === 'unsubscribed');
      const bouncedLeads = updatedCampaign.leads.filter(lead => lead.status === 'bounced');

      // IMPORTANT: For sequence campaigns, we should NEVER automatically mark as completed
      // unless all leads have explicitly completed the entire sequence
      // Just because there are no active leads doesn't mean the sequence is done
      // Some leads might be waiting for conditions to be met

      // Check if any leads are in a condition step
      const leadsInConditionStep = await prisma.campaignLead.count({
        where: {
          campaignId: campaignId,
          currentStep: {
            type: 'condition'
          }
        }
      });

      logger.info(`[SEQUENCE_DEBUG] Campaign ${campaignId} has ${leadsInConditionStep} leads in condition steps`);

      // Only mark as completed if:
      // 1. There are no active leads
      // 2. There are no leads in condition steps (waiting for conditions)
      // 3. At least one lead has completed the sequence successfully
      if (activeLeads.length === 0 && leadsInConditionStep === 0 && totalLeads > 0 && completedLeads.length > 0) {
        logger.info(`[SEQUENCE_DEBUG] All leads in campaign ${campaignId} have completed or are inactive. Marking campaign as completed.`, {
          completedLeads: completedLeads.length,
          errorLeads: errorLeads.length,
          unsubscribedLeads: unsubscribedLeads.length,
          bouncedLeads: bouncedLeads.length
        });

        try {
          // Mark the campaign as completed using a transaction
          await prisma.$transaction(async (tx) => {
            // Update the campaign status
            await tx.campaign.update({
              where: { id: campaignId },
              data: {
                status: 'completed',
                endDate: new Date(),
              },
            });

            // Create a notification about campaign completion
            await tx.notification.create({
              data: {
                userId: updatedCampaign.userId,
                organizationId: updatedCampaign.organizationId,
                title: 'Sequence Campaign Completed',
                message: `Your sequence campaign "${updatedCampaign.name}" has completed.`,
                type: 'success',
                isRead: false,
              },
            }).catch(notifError => {
              // Log but don't fail the transaction if notification creation fails
              logger.warn(`[SEQUENCE_DEBUG] Failed to create notification for campaign completion`, {
                error: notifError instanceof Error ? notifError.message : 'Unknown error',
                campaignId
              });
            });
          }, {
            maxWait: 5000, // 5 seconds max wait time
            timeout: 10000 // 10 seconds transaction timeout
          });

          logger.info(`[SEQUENCE_DEBUG] Campaign ${campaignId} marked as completed successfully`);
        } catch (updateError) {
          logger.error(`[SEQUENCE_DEBUG] Failed to update campaign status to completed`, {
            error: updateError instanceof Error ? updateError.message : 'Unknown error',
            stack: updateError instanceof Error ? updateError.stack : 'No stack trace',
            campaignId
          });
          // Don't rethrow - we want to continue execution
        }
      } else if (updatedCampaign.status === 'active' && completedLeads.length > 0) {
        // If some leads have completed but others are still active, mark as in_progress
        logger.info(`[SEQUENCE_DEBUG] Some leads in campaign ${campaignId} have completed, but others are still active. Marking as in_progress.`);

        try {
          await prisma.campaign.update({
            where: { id: campaignId },
            data: {
              status: 'in_progress',
            },
          });

          logger.info(`[SEQUENCE_DEBUG] Campaign ${campaignId} marked as in_progress successfully`);
        } catch (updateError) {
          logger.error(`[SEQUENCE_DEBUG] Failed to update campaign status to in_progress`, {
            error: updateError instanceof Error ? updateError.message : 'Unknown error',
            stack: updateError instanceof Error ? updateError.stack : 'No stack trace',
            campaignId
          });
          // Don't rethrow - we want to continue execution
        }
      }

      // Log performance metrics
      const duration = Date.now() - startTime;
      logger.info(`[SEQUENCE_DEBUG] Campaign status check completed in ${duration}ms`, {
        campaignId,
        duration,
        activeLeads: activeLeads.length,
        totalLeads
      });
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error checking sequence campaign completion status`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        duration: Date.now() - startTime
      });

      // Report the error to monitoring systems if available
      if (process.env.NODE_ENV === 'production') {
        try {
          // This could be integrated with error reporting services like Sentry
          console.error(`PRODUCTION ERROR: Sequence campaign status check failed for ${campaignId}`);
        } catch (reportError) {
          // Just log and continue if error reporting fails
          logger.error(`Failed to report error to monitoring system`, {
            originalError: error instanceof Error ? error.message : 'Unknown error',
            reportError: reportError instanceof Error ? reportError.message : 'Unknown error'
          });
        }
      }
    }
  }
  /**
   * Process a lead after an interaction (open, click, reply)
   * This method is called from tracking endpoints when a lead interacts with an email
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param interactionType The type of interaction (open, click, reply)
   */
  static async processLeadAfterInteraction(
    campaignId: string,
    leadId: string,
    interactionType: 'open' | 'click' | 'reply'
  ): Promise<boolean> {
    try {
      logger.info(`[SEQUENCE_DEBUG] Processing lead after ${interactionType} interaction`, {
        campaignId,
        leadId,
        interactionType
      });

      // Get the campaign with all necessary data
      const campaign = await prisma.campaign.findUnique({
        where: { id: campaignId },
        include: {
          steps: {
            orderBy: {
              position: 'asc',
            },
            include: {
              emailAccount: true,
              emailAccounts: {
                include: {
                  emailAccount: true
                }
              },
              conditionStep: true,
              conditionedSteps: true,
            },
          },
          leads: {
            where: { id: leadId }, // Only get the specific lead we need
            include: {
              lead: true,
              currentStep: true,
              stepActivities: {
                orderBy: {
                  createdAt: 'desc',
                },
                include: {
                  step: true,
                }
              },
            },
          },
        },
      });

      if (!campaign) {
        logger.warn(`[SEQUENCE_DEBUG] Campaign not found for interaction processing`, {
          campaignId,
          leadId,
          interactionType
        });
        return false;
      }

      if (campaign.leads.length === 0) {
        logger.warn(`[SEQUENCE_DEBUG] Lead not found in campaign for interaction processing`, {
          campaignId,
          leadId,
          interactionType
        });
        return false;
      }

      const campaignLead = campaign.leads[0]; // We only queried for one lead

      // If the lead is not in an active state, don't process
      if (campaignLead.status !== 'active') {
        logger.info(`[SEQUENCE_DEBUG] Lead is not active (status: ${campaignLead.status}), skipping interaction processing`, {
          campaignId,
          leadId,
          interactionType
        });
        return false;
      }

      // If the lead doesn't have a current step, don't process
      if (!campaignLead.currentStepId) {
        logger.warn(`[SEQUENCE_DEBUG] Lead has no current step, skipping interaction processing`, {
          campaignId,
          leadId,
          interactionType
        });
        return false;
      }

      // Process the lead in the sequence
      await this.processLeadInSequence(campaign, campaignLead);

      logger.info(`[SEQUENCE_DEBUG] Successfully processed lead after ${interactionType} interaction`, {
        campaignId,
        leadId,
        interactionType
      });

      return true;
    } catch (error) {
      logger.error(`[SEQUENCE_DEBUG] Error processing lead after interaction`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        interactionType
      });
      return false;
    }
  }
}