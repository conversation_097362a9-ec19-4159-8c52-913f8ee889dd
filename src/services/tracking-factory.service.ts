import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { StandardCampaignTrackingService } from './standard-campaign-tracking.service';
import { SequenceCampaignTrackingService } from './sequence-campaign-tracking.service';

/**
 * Factory service for routing tracking requests to the appropriate service based on campaign type
 */
export class TrackingFactoryService {
  /**
   * Get the campaign type for a given campaign ID
   * @param campaignId The campaign ID
   * @returns The campaign type (standard or sequence)
   */
  static async getCampaignType(campaignId: string): Promise<'standard' | 'sequence' | null> {
    try {
      const campaign = await prisma.campaign.findUnique({
        where: { id: campaignId },
        select: { type: true }
      });

      if (!campaign) {
        logger.warn('[TRACKING_FACTORY] Campaign not found', { campaignId });
        return null;
      }

      return campaign.type as 'standard' | 'sequence';
    } catch (error) {
      logger.error('[TRACKING_FACTORY] Error getting campaign type', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId
      });
      return null;
    }
  }

  /**
   * Get the step ID for a sequence campaign lead
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @returns The step ID or null if not found
   */
  static async getSequenceStepId(campaignId: string, leadId: string): Promise<string | null> {
    try {
      const tracking = await prisma.sequenceCampaignTracking.findUnique({
        where: {
          campaignId_leadId: {
            campaignId,
            leadId
          }
        },
        select: { currentStepId: true }
      });

      return tracking?.currentStepId || null;
    } catch (error) {
      logger.error('[TRACKING_FACTORY] Error getting sequence step ID', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId
      });
      return null;
    }
  }

  /**
   * Add tracking to an email
   * @param html The HTML content of the email
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param stepId The step ID (for sequence campaigns)
   * @param baseUrl The base URL for tracking
   * @param trackingDomains Optional custom tracking domains
   * @returns The HTML with tracking added
   */
  static async addTracking(
    html: string,
    campaignId: string,
    leadId: string,
    stepId?: string,
    baseUrl?: string,
    trackingDomains?: {
      openDomain?: string;
      clickDomain?: string;
      bounceDomain?: string;
      source?: 'custom' | 'sender' | 'default';
    }
  ): Promise<string> {
    try {
      const campaignType = await this.getCampaignType(campaignId);

      if (!campaignType) {
        logger.warn('[TRACKING_FACTORY] Campaign type not found, using standard tracking', { campaignId });
        return StandardCampaignTrackingService.addTracking(html, campaignId, leadId, baseUrl, trackingDomains);
      }

      if (campaignType === 'standard') {
        return StandardCampaignTrackingService.addTracking(html, campaignId, leadId, baseUrl, trackingDomains);
      } else if (campaignType === 'sequence') {
        if (!stepId) {
          logger.warn('[TRACKING_FACTORY] Step ID not provided for sequence campaign, trying to find current step', { campaignId, leadId });
          stepId = await this.getSequenceStepId(campaignId, leadId);

          if (!stepId) {
            logger.error('[TRACKING_FACTORY] Step ID not found for sequence campaign', { campaignId, leadId });
            return html; // Return original HTML if step ID not found
          }
        }

        return SequenceCampaignTrackingService.addTracking(html, campaignId, leadId, stepId, baseUrl, trackingDomains);
      }

      logger.warn('[TRACKING_FACTORY] Unknown campaign type, using standard tracking', { campaignId, campaignType });
      return StandardCampaignTrackingService.addTracking(html, campaignId, leadId, baseUrl, trackingDomains);
    } catch (error) {
      logger.error('[TRACKING_FACTORY] Error adding tracking to email', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        stepId
      });
      return html; // Return original HTML if there's an error
    }
  }

  /**
   * Track an email open
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param metadata Additional metadata about the open
   * @returns Success status
   */
  static async trackOpen(
    campaignId: string,
    leadId: string,
    metadata: {
      userAgent?: string;
      ip?: string;
      referrer?: string;
      timestamp?: string;
      stepId?: string; // Optional step ID for sequence campaigns
    }
  ): Promise<boolean> {
    try {
      const campaignType = await this.getCampaignType(campaignId);

      if (!campaignType) {
        logger.warn('[TRACKING_FACTORY] Campaign type not found for open tracking', { campaignId, leadId });
        return false;
      }

      if (campaignType === 'standard') {
        return StandardCampaignTrackingService.trackOpen(campaignId, leadId, metadata);
      } else if (campaignType === 'sequence') {
        const stepId = metadata.stepId || await this.getSequenceStepId(campaignId, leadId);
        return SequenceCampaignTrackingService.trackOpen(campaignId, leadId, stepId, metadata);
      }

      logger.warn('[TRACKING_FACTORY] Unknown campaign type for open tracking', { campaignId, campaignType });
      return false;
    } catch (error) {
      logger.error('[TRACKING_FACTORY] Error tracking email open', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId
      });
      return false;
    }
  }

  /**
   * Track a link click
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param url The URL that was clicked
   * @param metadata Additional metadata about the click
   * @returns Success status
   */
  static async trackClick(
    campaignId: string,
    leadId: string,
    url: string,
    metadata: {
      userAgent?: string;
      ip?: string;
      referrer?: string;
      timestamp?: string;
      stepId?: string; // Optional step ID for sequence campaigns
    }
  ): Promise<boolean> {
    try {
      const campaignType = await this.getCampaignType(campaignId);

      if (!campaignType) {
        logger.warn('[TRACKING_FACTORY] Campaign type not found for click tracking', { campaignId, leadId, url });
        return false;
      }

      if (campaignType === 'standard') {
        return StandardCampaignTrackingService.trackClick(campaignId, leadId, url, metadata);
      } else if (campaignType === 'sequence') {
        const stepId = metadata.stepId || await this.getSequenceStepId(campaignId, leadId);
        return SequenceCampaignTrackingService.trackClick(campaignId, leadId, stepId, url, metadata);
      }

      logger.warn('[TRACKING_FACTORY] Unknown campaign type for click tracking', { campaignId, campaignType, url });
      return false;
    } catch (error) {
      logger.error('[TRACKING_FACTORY] Error tracking link click', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        url
      });
      return false;
    }
  }

  /**
   * Track an unsubscribe event
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param metadata Additional metadata about the unsubscribe
   * @returns Success status
   */
  static async trackUnsubscribe(
    campaignId: string,
    leadId: string,
    metadata: {
      userAgent?: string;
      ip?: string;
      timestamp?: string;
      reason?: string;
    }
  ): Promise<boolean> {
    try {
      const campaignType = await this.getCampaignType(campaignId);

      if (!campaignType) {
        logger.warn('[TRACKING_FACTORY] Campaign type not found for unsubscribe tracking', { campaignId, leadId });
        return false;
      }

      if (campaignType === 'standard') {
        return StandardCampaignTrackingService.trackUnsubscribe(campaignId, leadId, metadata);
      } else if (campaignType === 'sequence') {
        return SequenceCampaignTrackingService.trackUnsubscribe(campaignId, leadId, metadata);
      }

      logger.warn('[TRACKING_FACTORY] Unknown campaign type for unsubscribe tracking', { campaignId, campaignType });
      return false;
    } catch (error) {
      logger.error('[TRACKING_FACTORY] Error tracking unsubscribe', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId
      });
      return false;
    }
  }

  /**
   * Track a bounce event
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param bounceType The type of bounce (hard or soft)
   * @param bounceReason The reason for the bounce
   * @param metadata Additional metadata about the bounce
   * @returns Success status
   */
  static async trackBounce(
    campaignId: string,
    leadId: string,
    bounceType: 'hard' | 'soft',
    bounceReason?: string,
    metadata?: {
      stepId?: string; // Optional step ID for sequence campaigns
    }
  ): Promise<boolean> {
    try {
      const campaignType = await this.getCampaignType(campaignId);

      if (!campaignType) {
        logger.warn('[TRACKING_FACTORY] Campaign type not found for bounce tracking', { campaignId, leadId });
        return false;
      }

      if (campaignType === 'standard') {
        return StandardCampaignTrackingService.trackBounce(campaignId, leadId, bounceType, bounceReason);
      } else if (campaignType === 'sequence') {
        const stepId = metadata?.stepId || await this.getSequenceStepId(campaignId, leadId);
        return SequenceCampaignTrackingService.trackBounce(campaignId, leadId, stepId, bounceType, bounceReason);
      }

      logger.warn('[TRACKING_FACTORY] Unknown campaign type for bounce tracking', { campaignId, campaignType });
      return false;
    } catch (error) {
      logger.error('[TRACKING_FACTORY] Error tracking bounce', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        bounceType,
        bounceReason
      });
      return false;
    }
  }

  /**
   * Track a reply event
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param messageId The message ID of the reply
   * @param replyContent The content of the reply
   * @param metadata Additional metadata about the reply
   * @returns Success status
   */
  static async trackReply(
    campaignId: string,
    leadId: string,
    messageId: string,
    replyContent: {
      subject?: string;
      text?: string;
      html?: string;
      from?: string;
      inReplyTo?: string;
    },
    metadata?: {
      stepId?: string; // Optional step ID for sequence campaigns
    }
  ): Promise<boolean> {
    try {
      const campaignType = await this.getCampaignType(campaignId);

      if (!campaignType) {
        logger.warn('[TRACKING_FACTORY] Campaign type not found for reply tracking', { campaignId, leadId });
        return false;
      }

      if (campaignType === 'standard') {
        return StandardCampaignTrackingService.trackReply(campaignId, leadId, messageId, replyContent);
      } else if (campaignType === 'sequence') {
        const stepId = metadata?.stepId || await this.getSequenceStepId(campaignId, leadId);
        return SequenceCampaignTrackingService.trackReply(campaignId, leadId, stepId, messageId, replyContent);
      }

      logger.warn('[TRACKING_FACTORY] Unknown campaign type for reply tracking', { campaignId, campaignType });
      return false;
    } catch (error) {
      logger.error('[TRACKING_FACTORY] Error tracking reply', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        messageId
      });
      return false;
    }
  }

  /**
   * Track an email being sent
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param stepId The step ID (for sequence campaigns)
   * @returns Success status
   */
  static async trackEmailSent(
    campaignId: string,
    leadId: string,
    stepId?: string
  ): Promise<boolean> {
    try {
      const campaignType = await this.getCampaignType(campaignId);

      if (!campaignType) {
        logger.warn('[TRACKING_FACTORY] Campaign type not found for email sent tracking', { campaignId, leadId });
        return false;
      }

      if (campaignType === 'standard') {
        return StandardCampaignTrackingService.trackEmailSent(campaignId, leadId);
      } else if (campaignType === 'sequence') {
        if (!stepId) {
          logger.warn('[TRACKING_FACTORY] Step ID not provided for sequence campaign, trying to find current step', { campaignId, leadId });
          stepId = await this.getSequenceStepId(campaignId, leadId);

          if (!stepId) {
            logger.error('[TRACKING_FACTORY] Step ID not found for sequence campaign', { campaignId, leadId });
            return false;
          }
        }

        return SequenceCampaignTrackingService.trackEmailSent(campaignId, leadId, stepId);
      }

      logger.warn('[TRACKING_FACTORY] Unknown campaign type for email sent tracking', { campaignId, campaignType });
      return false;
    } catch (error) {
      logger.error('[TRACKING_FACTORY] Error tracking email sent', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        stepId
      });
      return false;
    }
  }

  /**
   * Initialize tracking for a lead in a campaign
   * @param campaignId The campaign ID
   * @param leadId The lead ID
   * @param initialStepId The initial step ID (for sequence campaigns)
   * @returns Success status
   */
  static async initializeTracking(
    campaignId: string,
    leadId: string,
    initialStepId?: string
  ): Promise<boolean> {
    try {
      const campaignType = await this.getCampaignType(campaignId);

      if (!campaignType) {
        logger.warn('[TRACKING_FACTORY] Campaign type not found for initializing tracking', { campaignId, leadId });
        return false;
      }

      if (campaignType === 'standard') {
        return StandardCampaignTrackingService.initializeTracking(campaignId, leadId);
      } else if (campaignType === 'sequence') {
        if (!initialStepId) {
          logger.error('[TRACKING_FACTORY] Initial step ID not provided for sequence campaign', { campaignId, leadId });
          return false;
        }

        return SequenceCampaignTrackingService.initializeTracking(campaignId, leadId, initialStepId);
      }

      logger.warn('[TRACKING_FACTORY] Unknown campaign type for initializing tracking', { campaignId, campaignType });
      return false;
    } catch (error) {
      logger.error('[TRACKING_FACTORY] Error initializing tracking', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        campaignId,
        leadId,
        initialStepId
      });
      return false;
    }
  }
}
