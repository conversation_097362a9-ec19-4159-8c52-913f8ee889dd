import { prisma } from '@/lib/prisma';
import { Notification } from '@prisma/client';

interface CreateNotificationData {
  title: string;
  message: string;
  type?: string;
  organizationId: string;
  userId?: string;
}

export class NotificationService {
  static async listNotifications(organizationId: string) {
    return prisma.notification.findMany({
      where: { organizationId },
      orderBy: { createdAt: 'desc' },
    });
  }

  static async getNotification(id: string) {
    return prisma.notification.findUnique({
      where: { id },
    });
  }

  static async createNotification(data: CreateNotificationData) {
    return prisma.notification.create({
      data,
    });
  }

  static async markAsRead(id: string) {
    return prisma.notification.update({
      where: { id },
      data: { isRead: true },
    });
  }

  static async deleteNotification(id: string) {
    return prisma.notification.delete({
      where: { id },
    });
  }
}
