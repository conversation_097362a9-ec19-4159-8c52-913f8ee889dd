import { stripe } from '@/config/stripe';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { BillingCycle } from '@prisma/client';
import Stripe from 'stripe';

export interface StripePackageData {
  packageId: string;
  organizationId: string;
  customerId?: string;
  paymentMethodId?: string;
  trial?: boolean;
  trialDays?: number;
}

export class StripePackageService {
  /**
   * Create or get Stripe customer for organization
   */
  static async createOrGetCustomer(organizationId: string, userEmail: string, userName?: string): Promise<string> {
    try {
      // Check if organization already has a customer ID
      const organization = await prisma.organization.findUnique({
        where: { id: organizationId },
        select: { customerId: true, name: true }
      });

      if (organization?.customerId) {
        // Verify customer exists in Stripe
        try {
          await stripe.customers.retrieve(organization.customerId);
          return organization.customerId;
        } catch (error) {
          logger.warn('Stripe customer not found, creating new one', {
            organizationId,
            customerId: organization.customerId
          });
        }
      }

      // Create new Stripe customer
      const customer = await stripe.customers.create({
        email: userEmail,
        name: userName || organization?.name || 'Unknown',
        metadata: {
          organizationId,
          source: 'avian-email-platform'
        }
      });

      // Update organization with customer ID
      await prisma.organization.update({
        where: { id: organizationId },
        data: { customerId: customer.id }
      });

      logger.info('Created Stripe customer', {
        organizationId,
        customerId: customer.id,
        email: userEmail
      });

      return customer.id;
    } catch (error) {
      logger.error('Error creating/getting Stripe customer', {
        error: error instanceof Error ? error.message : 'Unknown error',
        organizationId,
        userEmail
      });
      throw new Error('Failed to create Stripe customer');
    }
  }

  /**
   * Create Stripe product and price for a package
   */
  static async createStripeProductForPackage(packageId: string): Promise<{ productId: string; priceId: string }> {
    try {
      const package_ = await prisma.package.findUnique({
        where: { id: packageId }
      });

      if (!package_) {
        throw new Error('Package not found');
      }

      // Check if Stripe product and price already exist
      if (package_.stripeProductId && package_.stripePriceId) {
        try {
          // Verify they exist in Stripe
          await stripe.products.retrieve(package_.stripeProductId);
          await stripe.prices.retrieve(package_.stripePriceId);

          return {
            productId: package_.stripeProductId,
            priceId: package_.stripePriceId
          };
        } catch (error) {
          logger.warn('Stripe product/price not found, creating new ones', {
            packageId,
            stripeProductId: package_.stripeProductId,
            stripePriceId: package_.stripePriceId
          });
        }
      }

      // Create Stripe product
      const product = await stripe.products.create({
        name: package_.name,
        description: package_.description || `${package_.name} subscription package`,
        metadata: {
          packageId: package_.id,
          source: 'avian-email-platform'
        }
      });

      // Convert billing cycle to Stripe interval
      const intervalMapping: Record<BillingCycle, { interval: Stripe.PriceCreateParams.Recurring.Interval; interval_count?: number }> = {
        DAILY: { interval: 'day' },
        WEEKLY: { interval: 'week' },
        BIWEEKLY: { interval: 'week', interval_count: 2 },
        MONTHLY: { interval: 'month' },
        QUARTERLY: { interval: 'month', interval_count: 3 },
        YEARLY: { interval: 'year' }
      };

      const recurringConfig = intervalMapping[package_.billingCycle];

      // Create Stripe price
      const price = await stripe.prices.create({
        product: product.id,
        unit_amount: Math.round(package_.price * 100), // Convert to cents
        currency: 'usd',
        recurring: recurringConfig,
        metadata: {
          packageId: package_.id,
          source: 'avian-email-platform'
        }
      });

      // Update package with Stripe IDs
      await prisma.package.update({
        where: { id: packageId },
        data: {
          stripeProductId: product.id,
          stripePriceId: price.id
        }
      });

      logger.info('Created Stripe product and price for package', {
        packageId,
        productId: product.id,
        priceId: price.id,
        price: package_.price,
        billingCycle: package_.billingCycle
      });

      return {
        productId: product.id,
        priceId: price.id
      };
    } catch (error) {
      logger.error('Error creating Stripe product for package', {
        error: error instanceof Error ? error.message : 'Unknown error',
        packageId
      });
      throw new Error('Failed to create Stripe product');
    }
  }

  /**
   * Create subscription for a package
   */
  static async createSubscription(data: StripePackageData): Promise<Stripe.Subscription> {
    try {
      const { packageId, organizationId, customerId, paymentMethodId, trial = false, trialDays = 14 } = data;

      // Get package details
      const package_ = await prisma.package.findUnique({
        where: { id: packageId }
      });

      if (!package_) {
        throw new Error('Package not found');
      }

      if (package_.price === 0) {
        // Handle free package - no Stripe subscription needed
        return await this.handleFreePackageSubscription(packageId, organizationId);
      }

      // Ensure we have a customer ID
      const finalCustomerId = customerId || await this.createOrGetCustomer(organizationId, '', '');

      // Create or get Stripe product and price
      const { priceId } = await this.createStripeProductForPackage(packageId);

      // Prepare subscription data
      const subscriptionData: Stripe.SubscriptionCreateParams = {
        customer: finalCustomerId,
        items: [{ price: priceId }],
        metadata: {
          organizationId,
          packageId,
          source: 'avian-email-platform'
        },
        expand: ['latest_invoice.payment_intent']
      };

      // Add payment method if provided
      if (paymentMethodId) {
        subscriptionData.default_payment_method = paymentMethodId;
      }

      // Add trial if requested
      if (trial && trialDays > 0) {
        subscriptionData.trial_period_days = trialDays;
      }

      // Create Stripe subscription
      const subscription = await stripe.subscriptions.create(subscriptionData);

      // Create local subscription record
      await prisma.packageSubscription.create({
        data: {
          organizationId,
          packageId,
          stripeSubscriptionId: subscription.id,
          startDate: new Date(),
          isActive: subscription.status === 'active' || subscription.status === 'trialing',
          autoRenew: true,
          lastPaymentDate: subscription.status === 'active' ? new Date() : null,
          nextPaymentDate: new Date(subscription.current_period_end * 1000)
        }
      });

      // Update organization package
      await prisma.organization.update({
        where: { id: organizationId },
        data: { packageId }
      });

      logger.info('Created package subscription', {
        organizationId,
        packageId,
        subscriptionId: subscription.id,
        status: subscription.status,
        trial
      });

      return subscription;
    } catch (error) {
      logger.error('Error creating package subscription', {
        error: error instanceof Error ? error.message : 'Unknown error',
        organizationId: data.organizationId,
        packageId: data.packageId
      });
      throw new Error('Failed to create subscription');
    }
  }

  /**
   * Handle free package subscription (no Stripe involved)
   */
  private static async handleFreePackageSubscription(packageId: string, organizationId: string): Promise<any> {
    // Create local subscription record for free package
    await prisma.packageSubscription.create({
      data: {
        organizationId,
        packageId,
        startDate: new Date(),
        isActive: true,
        autoRenew: true
      }
    });

    // Update organization package
    await prisma.organization.update({
      where: { id: organizationId },
      data: { packageId }
    });

    logger.info('Created free package subscription', {
      organizationId,
      packageId
    });

    // Return a mock subscription object for consistency
    return {
      id: `free_${organizationId}_${packageId}`,
      status: 'active',
      current_period_end: Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60), // 1 year from now
      metadata: {
        organizationId,
        packageId,
        type: 'free'
      }
    };
  }

  /**
   * Cancel subscription
   */
  static async cancelSubscription(organizationId: string, immediate = false): Promise<void> {
    try {
      const subscription = await prisma.packageSubscription.findFirst({
        where: {
          organizationId,
          isActive: true
        }
      });

      if (!subscription) {
        throw new Error('No active subscription found');
      }

      if (subscription.stripeSubscriptionId) {
        // Cancel Stripe subscription
        await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
          cancel_at_period_end: !immediate
        });

        if (immediate) {
          await stripe.subscriptions.cancel(subscription.stripeSubscriptionId);
        }
      }

      // Update local subscription
      await prisma.packageSubscription.update({
        where: { id: subscription.id },
        data: {
          isActive: immediate ? false : true,
          autoRenew: false,
          endDate: immediate ? new Date() : undefined
        }
      });

      // If immediate cancellation, assign free package
      if (immediate) {
        const freePackage = await prisma.package.findFirst({
          where: { isDefault: true }
        });

        if (freePackage) {
          await prisma.organization.update({
            where: { id: organizationId },
            data: { packageId: freePackage.id }
          });
        }
      }

      logger.info('Cancelled subscription', {
        organizationId,
        subscriptionId: subscription.id,
        immediate
      });
    } catch (error) {
      logger.error('Error cancelling subscription', {
        error: error instanceof Error ? error.message : 'Unknown error',
        organizationId
      });
      throw new Error('Failed to cancel subscription');
    }
  }

  /**
   * Update subscription to different package
   */
  static async updateSubscription(organizationId: string, newPackageId: string): Promise<Stripe.Subscription | null> {
    try {
      const currentSubscription = await prisma.packageSubscription.findFirst({
        where: {
          organizationId,
          isActive: true
        },
        include: {
          package: true
        }
      });

      if (!currentSubscription) {
        throw new Error('No active subscription found');
      }

      const newPackage = await prisma.package.findUnique({
        where: { id: newPackageId }
      });

      if (!newPackage) {
        throw new Error('New package not found');
      }

      // If switching to free package
      if (newPackage.price === 0) {
        await this.cancelSubscription(organizationId, true);
        return null;
      }

      // If switching from free package
      if (!currentSubscription.stripeSubscriptionId) {
        return await this.createSubscription({
          packageId: newPackageId,
          organizationId
        });
      }

      // Update existing Stripe subscription
      const { priceId } = await this.createStripeProductForPackage(newPackageId);

      const updatedSubscription = await stripe.subscriptions.update(
        currentSubscription.stripeSubscriptionId,
        {
          items: [{
            id: (await stripe.subscriptions.retrieve(currentSubscription.stripeSubscriptionId)).items.data[0].id,
            price: priceId
          }],
          metadata: {
            organizationId,
            packageId: newPackageId,
            source: 'avian-email-platform'
          }
        }
      );

      // Update local records
      await prisma.packageSubscription.update({
        where: { id: currentSubscription.id },
        data: { packageId: newPackageId }
      });

      await prisma.organization.update({
        where: { id: organizationId },
        data: { packageId: newPackageId }
      });

      logger.info('Updated subscription package', {
        organizationId,
        oldPackageId: currentSubscription.packageId,
        newPackageId,
        subscriptionId: updatedSubscription.id
      });

      return updatedSubscription;
    } catch (error) {
      logger.error('Error updating subscription', {
        error: error instanceof Error ? error.message : 'Unknown error',
        organizationId,
        newPackageId
      });
      throw new Error('Failed to update subscription');
    }
  }

  /**
   * Get subscription status for organization
   */
  static async getSubscriptionStatus(organizationId: string) {
    try {
      const subscription = await prisma.packageSubscription.findFirst({
        where: {
          organizationId,
          isActive: true
        },
        include: {
          package: true
        }
      });

      if (!subscription) {
        return {
          hasSubscription: false,
          package: null,
          status: 'none'
        };
      }

      let stripeStatus = null;
      if (subscription.stripeSubscriptionId) {
        try {
          const stripeSubscription = await stripe.subscriptions.retrieve(subscription.stripeSubscriptionId);
          stripeStatus = stripeSubscription.status;
        } catch (error) {
          logger.warn('Could not retrieve Stripe subscription', {
            subscriptionId: subscription.stripeSubscriptionId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return {
        hasSubscription: true,
        package: subscription.package,
        status: stripeStatus || 'active',
        subscription: subscription,
        nextPaymentDate: subscription.nextPaymentDate,
        autoRenew: subscription.autoRenew
      };
    } catch (error) {
      logger.error('Error getting subscription status', {
        error: error instanceof Error ? error.message : 'Unknown error',
        organizationId
      });
      throw new Error('Failed to get subscription status');
    }
  }
}
