import { stripe } from '@/config/stripe';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

const PLANS = {
  plan1: {
    id: 'price_123456789',
    usagePrices: ['price_987654321'],
  },
  plan2: {
    id: 'price_987654321',
    usagePrices: ['price_123456789'],
  },
};

export class StripeService {
  static async createCustomer(organizationId: string, email: string, name: string) {
    try {
      const customer = await stripe.customers.create({
        email,
        name,
        metadata: {
          organizationId,
        },
      });
      
      return customer;
    } catch (error) {
      logger.error('Error creating Stripe customer:', error);
      throw new Error('Failed to create Stripe customer');
    }
  }

  static async updateCustomerMetadata(customerId: string, metadata: Record<string, string>) {
    try {
      return await stripe.customers.update(customerId, { metadata });
    } catch (error) {
      logger.error('Error updating Stripe customer:', error);
      throw new Error('Failed to update Stripe customer');
    }
  }

  static async createSubscription(
    organizationId: string,
    priceId: string,
    customerId: string
  ) {
    const plan = Object.values(PLANS).find(p => p.id === priceId);
    if (!plan) throw new Error('Invalid plan');

    // Create subscription with both base price and usage prices
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [
        { price: priceId }, // Base plan price
        ...plan.usagePrices.map(price => ({ price })), // Usage-based prices
      ],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
      metadata: {
        organizationId,
      },
    });

    await prisma.subscription.create({
      data: {
        id: subscription.id,
        organization: { connect: { id: organizationId } },
        stripeSubscriptionId: subscription.id,
        stripePriceId: priceId,
        stripeCustomerId: customerId,
        status: subscription.status.toUpperCase() as any,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      },
    });

    return subscription;
  }

  static async cancelSubscription(subscriptionId: string) {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });

    await prisma.subscription.update({
      where: { stripeSubscriptionId: subscriptionId },
      data: { cancelAtPeriodEnd: true },
    });

    return subscription;
  }

  static async getUpcomingInvoice(subscriptionId: string, customerId: string) {
    return stripe.invoices.retrieveUpcoming({
      customer: customerId,
      subscription: subscriptionId,
    });
  }
}
