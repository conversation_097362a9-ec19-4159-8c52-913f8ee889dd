import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import crypto from 'crypto';
import nodemailer from 'nodemailer';

export class EmailVerificationService {
  /**
   * Send a verification email for an email account
   */
  static async sendVerificationEmail(emailAccountId: string): Promise<void> {
    try {
      // Get the email account
      const emailAccount = await prisma.emailAccount.findUnique({
        where: { id: emailAccountId },
        include: {
          user: true,
        },
      });

      if (!emailAccount) {
        throw new Error(`Email account not found: ${emailAccountId}`);
      }

      // Generate a verification token
      const token = crypto.randomBytes(32).toString('hex');
      const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Store the token
      await prisma.verificationToken.create({
        data: {
          identifier: `email-account:${emailAccountId}`,
          token,
          expires,
        },
      });

      // Create the verification URL
      const verificationUrl = `${process.env.NEXTAUTH_URL}/email-accounts/verify?token=${token}`;

      logger.info('Generated verification URL', {
        url: verificationUrl.replace(token, token.substring(0, 10) + '...'),
        tokenLength: token.length
      });

      // Send the verification email
      // Log SMTP settings for debugging (without showing the full password)
      const smtpPassword = process.env.SMTP_PASSWORD || '';
      logger.info('SMTP Configuration', {
        host: process.env.SMTP_HOST,
        port: process.env.SMTP_PORT,
        secure: true,
        user: process.env.SMTP_USER,
        passwordLength: smtpPassword.length,
        passwordFirstChar: smtpPassword.charAt(0),
        passwordLastChar: smtpPassword.charAt(smtpPassword.length - 1),
      });

      const transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '465'),
        secure: true, // Use SSL/TLS
        auth: {
          user: process.env.SMTP_USER,
          // Make sure to trim any quotes that might have been added in the .env file
          pass: smtpPassword.replace(/^["'](.+)["']$/, '$1'),
        },
        tls: {
          // Do not fail on invalid certs
          rejectUnauthorized: false
        },
        debug: true, // Enable debug output
      });

      const text = `
Hello ${emailAccount.user.name || 'there'},

Please verify your email account by clicking the link below:
${verificationUrl}

This link will expire in 24 hours.

If you did not request this verification, please ignore this email.

Thanks,
The Avian Email Team
      `;

      const html = `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .button { display: inline-block; padding: 10px 20px; background-color: #4F46E5; color: white; text-decoration: none; border-radius: 4px; }
    .footer { margin-top: 30px; font-size: 12px; color: #666; }
  </style>
</head>
<body>
  <div class="container">
    <h2>Verify Your Email Account</h2>
    <p>Hello ${emailAccount.user.name || 'there'},</p>
    <p>Please verify your email account by clicking the button below:</p>
    <p><a href="${verificationUrl}" class="button">Verify Email Account</a></p>
    <p>Or copy and paste this link into your browser:</p>
    <p>${verificationUrl}</p>
    <p>This link will expire in 24 hours.</p>
    <p>If you did not request this verification, please ignore this email.</p>
    <div class="footer">
      <p>Thanks,<br>The Avian Email Team</p>
    </div>
  </div>
</body>
</html>
      `;

      try {
        // Verify SMTP connection before sending
        logger.info('Verifying SMTP connection...');
        await new Promise((resolve, reject) => {
          transporter.verify(function (error, success) {
            if (error) {
              logger.error('SMTP connection verification failed', { error: error.message });
              reject(error);
            } else {
              logger.info('SMTP connection verified successfully', { success });
              resolve(success);
            }
          });
        });

        logger.info('Attempting to send verification email', {
          to: emailAccount.email,
          from: process.env.SMTP_FROM || '"Avian Email" <<EMAIL>>',
          subject: 'Verify your email account'
        });

        const info = await transporter.sendMail({
          from: process.env.SMTP_FROM || '"Avian Email" <<EMAIL>>',
          to: emailAccount.email,
          subject: 'Verify your email account',
          text,
          html,
        });

        logger.info('Verification email sent successfully', {
          messageId: info.messageId,
          response: info.response,
          emailAccountId
        });
      } catch (emailError) {
        logger.error('Failed to send verification email via SMTP', {
          error: emailError instanceof Error ? emailError.message : 'Unknown error',
          stack: emailError instanceof Error ? emailError.stack : undefined,
          emailAccountId
        });

        // Throw the error to be caught by the outer try/catch
        throw emailError;
      }

      logger.info('Verification email sent', { emailAccountId });
    } catch (error) {
      logger.error('Error sending verification email', {
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Verify an email account using a token
   */
  static async verifyEmailAccount(token: string): Promise<string> {
    try {
      // Find the verification token
      logger.info('Looking up verification token', { tokenPrefix: token.substring(0, 10) + '...' });

      const verificationToken = await prisma.verificationToken.findUnique({
        where: { token },
      });

      if (!verificationToken) {
        logger.error('Verification token not found in database', { tokenPrefix: token.substring(0, 10) + '...' });
        throw new Error('Invalid verification token');
      }

      logger.info('Verification token found', {
        identifier: verificationToken.identifier,
        expires: verificationToken.expires
      });

      // Check if the token is for an email account
      if (!verificationToken.identifier.startsWith('email-account:')) {
        throw new Error('Invalid token type');
      }

      // Check if the token has expired
      if (verificationToken.expires < new Date()) {
        await prisma.verificationToken.delete({ where: { token } });
        throw new Error('Verification token has expired');
      }

      // Extract the email account ID
      const emailAccountId = verificationToken.identifier.replace('email-account:', '');

      // Update the email account status
      await prisma.emailAccount.update({
        where: { id: emailAccountId },
        data: { status: 'verified' },
      });

      // Delete the token
      await prisma.verificationToken.delete({ where: { token } });

      logger.info('Email account verified', { emailAccountId });
      return emailAccountId;
    } catch (error) {
      logger.error('Error verifying email account', {
        token,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }
}
