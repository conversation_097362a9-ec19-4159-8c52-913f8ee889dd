import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export class AdminAccessService {
  /**
   * Check if a user has admin access
   * Admin access is granted if:
   * 1. User has isAdmin flag set to true (global admin)
   * 2. User owns an organization (organization admin)
   * 3. User has ADMIN role in any organization
   */
  static async isAdmin(userEmail: string): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { email: userEmail },
        include: {
          ownedOrganization: true,
          organizations: {
            where: {
              role: 'ADMIN'
            }
          }
        }
      });

      if (!user) {
        return false;
      }

      // Check global admin flag
      if (user.isAdmin) {
        return true;
      }

      // Check if user owns an organization
      if (user.ownedOrganization) {
        return true;
      }

      // Check if user has admin role in any organization
      if (user.organizations.length > 0) {
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Error checking admin access', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userEmail
      });
      return false;
    }
  }

  /**
   * Get admin level for a user
   * Returns: 'global' | 'organization' | 'none'
   */
  static async getAdminLevel(userEmail: string): Promise<'global' | 'organization' | 'none'> {
    try {
      const user = await prisma.user.findUnique({
        where: { email: userEmail },
        include: {
          ownedOrganization: true,
          organizations: {
            where: {
              role: 'ADMIN'
            }
          }
        }
      });

      if (!user) {
        return 'none';
      }

      // Global admin has highest level
      if (user.isAdmin) {
        return 'global';
      }

      // Organization owner or admin
      if (user.ownedOrganization || user.organizations.length > 0) {
        return 'organization';
      }

      return 'none';
    } catch (error) {
      logger.error('Error getting admin level', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userEmail
      });
      return 'none';
    }
  }

  /**
   * Make a user a global admin
   */
  static async makeGlobalAdmin(userEmail: string, adminEmail: string): Promise<void> {
    try {
      // Check if the requesting user is a global admin
      const requestingUser = await prisma.user.findUnique({
        where: { email: adminEmail }
      });

      if (!requestingUser?.isAdmin) {
        throw new Error('Only global admins can create other global admins');
      }

      // Update the target user
      await prisma.user.update({
        where: { email: userEmail },
        data: { isAdmin: true }
      });

      logger.info('User made global admin', {
        userEmail,
        adminEmail
      });
    } catch (error) {
      logger.error('Error making user global admin', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userEmail,
        adminEmail
      });
      throw error;
    }
  }

  /**
   * Remove global admin access from a user
   */
  static async removeGlobalAdmin(userEmail: string, adminEmail: string): Promise<void> {
    try {
      // Check if the requesting user is a global admin
      const requestingUser = await prisma.user.findUnique({
        where: { email: adminEmail }
      });

      if (!requestingUser?.isAdmin) {
        throw new Error('Only global admins can remove global admin access');
      }

      // Don't allow removing admin access from yourself
      if (userEmail === adminEmail) {
        throw new Error('Cannot remove admin access from yourself');
      }

      // Update the target user
      await prisma.user.update({
        where: { email: userEmail },
        data: { isAdmin: false }
      });

      logger.info('Global admin access removed', {
        userEmail,
        adminEmail
      });
    } catch (error) {
      logger.error('Error removing global admin access', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userEmail,
        adminEmail
      });
      throw error;
    }
  }

  /**
   * Get all global admins
   */
  static async getGlobalAdmins(): Promise<Array<{ id: string; name: string | null; email: string; createdAt: Date }>> {
    try {
      const admins = await prisma.user.findMany({
        where: { isAdmin: true },
        select: {
          id: true,
          name: true,
          email: true,
          createdAt: true
        }
      });

      return admins;
    } catch (error) {
      logger.error('Error getting global admins', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to get global admins');
    }
  }

  /**
   * Check if user can access admin features for a specific organization
   */
  static async canAccessOrganizationAdmin(userEmail: string, organizationId: string): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { email: userEmail },
        include: {
          ownedOrganization: true,
          organizations: {
            where: {
              organizationId,
              role: { in: ['ADMIN', 'OWNER'] }
            }
          }
        }
      });

      if (!user) {
        return false;
      }

      // Global admin can access any organization
      if (user.isAdmin) {
        return true;
      }

      // Check if user owns this organization
      if (user.ownedOrganization?.id === organizationId) {
        return true;
      }

      // Check if user has admin role in this organization
      if (user.organizations.length > 0) {
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Error checking organization admin access', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userEmail,
        organizationId
      });
      return false;
    }
  }

  /**
   * Initialize the first admin user
   * This should be called during setup to create the first global admin
   */
  static async initializeFirstAdmin(userEmail: string): Promise<void> {
    try {
      // Check if any global admins exist
      const existingAdmins = await prisma.user.count({
        where: { isAdmin: true }
      });

      if (existingAdmins > 0) {
        logger.info('Global admins already exist, skipping initialization');
        return;
      }

      // Make the specified user a global admin
      const user = await prisma.user.findUnique({
        where: { email: userEmail }
      });

      if (!user) {
        throw new Error(`User with email ${userEmail} not found`);
      }

      await prisma.user.update({
        where: { email: userEmail },
        data: { isAdmin: true }
      });

      logger.info('First global admin initialized', {
        userEmail
      });
    } catch (error) {
      logger.error('Error initializing first admin', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userEmail
      });
      throw error;
    }
  }
}
