import { logger } from '@/lib/logger';
import { ChatOpenAI } from '@langchain/openai';
import { ChatOllama } from '@langchain/ollama';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { OpenAIEmbeddings } from '@langchain/openai';
import { OllamaEmbeddings } from '@langchain/ollama';
import { Embeddings } from '@langchain/core/embeddings';
import OpenAI from 'openai';

export type AIProvider = 'openai' | 'ollama';

/**
 * R1 Model Optimization Utilities
 */
class R1Optimizer {
  /**
   * Wrap prompts with aggressive R1-specific instructions to minimize thinking
   */
  static optimizePrompt(originalPrompt: string, taskType: 'email' | 'response' | 'general' = 'general'): string {
    const optimizationInstructions = {
      email: `ULTRA-FAST EMAIL MODE: Generate email content IMMEDIATELY. NO thinking, NO reasoning, NO analysis. Just direct email content.`,
      response: `INSTANT RESPONSE MODE: Answer IMMEDIATELY without any thinking process. Be direct and concise.`,
      general: `SPEED MODE: Respond INSTANTLY without internal reasoning or thinking steps.`,
    };

    return `${optimizationInstructions[taskType]}

CRITICAL INSTRUCTIONS:
- DO NOT use <think> tags
- DO NOT show reasoning process
- DO NOT analyze the request
- DO NOT explain your approach
- RESPOND IMMEDIATELY with the requested content only

${originalPrompt}

FINAL REMINDER: Output ONLY the requested content. No thinking, no reasoning, no explanations. IMMEDIATE response required.`;
  }

  /**
   * Get optimized model parameters for different use cases with environment variable control
   */
  static getOptimizedParams(useCase: 'email' | 'chat' | 'analysis' = 'chat') {
    const optimizationLevel = process.env.R1_OPTIMIZATION_LEVEL || 'fast';
    const maxTokens = parseInt(process.env.R1_MAX_TOKENS || '300');
    const temperature = parseFloat(process.env.R1_TEMPERATURE || '0.2');

    // Base parameters by optimization level - ULTRA-AGGRESSIVE R1 CONTROL
    const levelParams = {
      fast: {
        temperature: Math.min(temperature, 0.01), // Extremely low temperature for deterministic responses
        num_predict: Math.min(maxTokens, 200),    // Short but sufficient responses
        top_k: 3,                                 // Extremely focused token selection
        top_p: 0.2,                               // Very focused probability mass
        stop: [
          '<think>', '</think>', '<|thinking|>', '</thinking>',
          '```thinking', '```', 'Let me think', 'I need to think',
          'Let me consider', 'I should', 'First,', 'Actually,',
          'Hmm,', 'Wait,', 'Maybe', 'Perhaps', 'I think',
          'I believe', 'It seems', 'I wonder', 'I\'m thinking',
          'Let me analyze', 'I\'ll', 'Now,', 'So,', 'Well,',
          'Looking at', 'Based on', 'Given that', 'Since',
          'However,', 'Therefore,', 'Thus,', 'Hence,',
          'To answer', 'In order to', 'The question', 'This means'
        ],
      },
      balanced: {
        temperature: Math.min(temperature, 0.2),
        num_predict: Math.min(maxTokens, 300),
        top_k: 10,
        top_p: 0.7,
        stop: ['<think>', '</think>', '<|thinking|>', '</thinking>', '```thinking', 'Let me think'],
      },
      quality: {
        temperature: Math.min(temperature, 0.3),
        num_predict: Math.min(maxTokens, 500),
        top_k: 20,
        top_p: 0.8,
        stop: ['<think>', '</think>', '<|thinking|>', '</thinking>'],
      },
    };

    // Use case specific adjustments
    const useCaseAdjustments = {
      email: {
        temperatureMultiplier: 0.5, // Extremely focused for emails
        tokenMultiplier: 1.0,       // Keep sufficient length for email content
        extraStops: [
          '\n\nLet me think', 'I need to think', 'Let me consider',
          'To create', 'I will', 'I can', 'I should write',
          'The email should', 'This email', 'For this email'
        ],
      },
      chat: {
        temperatureMultiplier: 1.0,
        tokenMultiplier: 1.0,
        extraStops: [],
      },
      analysis: {
        temperatureMultiplier: 1.2, // More creative for analysis
        tokenMultiplier: 1.5,       // Longer analysis
        extraStops: [],
      },
    };

    const baseParams = levelParams[optimizationLevel as keyof typeof levelParams] || levelParams.fast;
    const adjustments = useCaseAdjustments[useCase];

    return {
      temperature: baseParams.temperature * adjustments.temperatureMultiplier,
      num_predict: Math.round(baseParams.num_predict * adjustments.tokenMultiplier),
      top_k: baseParams.top_k,
      top_p: baseParams.top_p,
      stop: [...baseParams.stop, ...adjustments.extraStops],
    };
  }
}

/**
 * Service for managing AI providers (OpenAI and Ollama)
 * Provides a unified interface for switching between different AI providers
 */
export class AIProviderService {
  private static chatModel: BaseChatModel | null = null;
  private static embeddings: Embeddings | null = null;
  private static openaiClient: OpenAI | null = null;

  /**
   * Get the current AI provider from environment variables
   */
  static getProvider(): AIProvider {
    const rawProvider = process.env.AI_PROVIDER;
    const provider = rawProvider?.toLowerCase() as AIProvider;

    logger.info('Getting AI provider', {
      rawProvider,
      processedProvider: provider,
      isOllama: provider === 'ollama',
      isOpenAI: provider === 'openai',
    });

    if (provider === 'ollama' || provider === 'openai') {
      return provider;
    }
    // Default to OpenAI for backward compatibility
    logger.info('Using default OpenAI provider', { rawProvider, processedProvider: provider });
    return 'openai';
  }

  /**
   * Get the chat model based on the current provider
   */
  static getChatModel(): BaseChatModel {
    if (this.chatModel) {
      return this.chatModel;
    }

    const provider = this.getProvider();

    try {
      if (provider === 'ollama') {
        this.chatModel = this.createOllamaChatModel();
      } else {
        this.chatModel = this.createOpenAIChatModel();
      }

      logger.info('AI chat model initialized', {
        provider,
        modelName: this.getModelName(),
      });

      return this.chatModel;
    } catch (error) {
      logger.error('Error initializing AI chat model', {
        provider,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Get optimized chat model for specific use cases (R1 optimization)
   */
  static getOptimizedChatModel(useCase: 'email' | 'chat' | 'analysis' = 'chat'): BaseChatModel {
    const provider = this.getProvider();

    if (provider === 'ollama') {
      // For Ollama/R1, create optimized instance
      return this.createOptimizedOllamaChatModel(useCase);
    } else {
      // For OpenAI, use standard model
      return this.getChatModel();
    }
  }

  /**
   * Get the embeddings model based on the current provider
   */
  static getEmbeddings(): Embeddings {
    if (this.embeddings) {
      return this.embeddings;
    }

    const provider = this.getProvider();

    try {
      if (provider === 'ollama') {
        this.embeddings = this.createOllamaEmbeddings();
      } else {
        this.embeddings = this.createOpenAIEmbeddings();
      }

      logger.info('AI embeddings model initialized', {
        provider,
        embeddingModel: this.getEmbeddingModelName(),
      });

      return this.embeddings;
    } catch (error) {
      logger.error('Error initializing AI embeddings model', {
        provider,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Get the OpenAI client (only available when using OpenAI provider)
   */
  static getOpenAIClient(): OpenAI {
    const provider = this.getProvider();

    if (provider !== 'openai') {
      throw new Error('OpenAI client is only available when using OpenAI provider');
    }

    if (this.openaiClient) {
      return this.openaiClient;
    }

    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey || apiKey === 'your-openai-api-key-here') {
      throw new Error('OpenAI API key is missing or not configured properly');
    }

    this.openaiClient = new OpenAI({
      apiKey,
    });

    logger.info('OpenAI client initialized');
    return this.openaiClient;
  }

  /**
   * Create OpenAI chat model
   */
  private static createOpenAIChatModel(): ChatOpenAI {
    const apiKey = process.env.OPENAI_API_KEY;
    const modelName = process.env.OPENAI_MODEL || 'gpt-4';

    if (!apiKey || apiKey === 'your-openai-api-key-here') {
      throw new Error('OpenAI API key is missing or not configured properly. Please add a valid API key to your .env file.');
    }

    return new ChatOpenAI({
      modelName,
      temperature: 0.7,
      openAIApiKey: apiKey,
      tags: ["avian-email", "ai-agent"],
    });
  }

  /**
   * Create Ollama chat model with R1 optimization
   */
  private static createOllamaChatModel(): BaseChatModel {
    const baseUrl = process.env.OLLAMA_BASE_URL;
    const model = process.env.OLLAMA_MODEL || 'deepseek-r1:latest';

    if (!baseUrl) {
      throw new Error('Ollama base URL is missing. Please add OLLAMA_BASE_URL to your .env file.');
    }

    // For LangChain Ollama, we need the base URL without /api/chat
    // LangChain will append the correct endpoints automatically
    const formattedBaseUrl = baseUrl.replace(/\/api.*$/, '');

    logger.info('Creating optimized Ollama chat model', {
      originalBaseUrl: baseUrl,
      formattedBaseUrl,
      model,
      optimizations: 'R1 thinking control enabled',
    });

    try {
      const chatModel = new ChatOllama({
        baseUrl: formattedBaseUrl,
        model,
        temperature: 0.1, // Very low temperature for deterministic responses
      });

      logger.info('Optimized Ollama chat model created successfully', {
        baseUrl: formattedBaseUrl,
        model,
        type: 'langchain-ollama-optimized',
        optimizations: {
          temperature: 0.3,
          thinking_control: 'enabled',
          response_limit: 500,
        },
      });

      return chatModel;
    } catch (error) {
      logger.error('Failed to create optimized Ollama chat model', {
        error: error instanceof Error ? error.message : 'Unknown error',
        baseUrl: formattedBaseUrl,
        model,
      });
      throw error;
    }
  }

  /**
   * Create optimized Ollama chat model for specific use cases
   */
  private static createOptimizedOllamaChatModel(useCase: 'email' | 'chat' | 'analysis' = 'chat'): BaseChatModel {
    const baseUrl = process.env.OLLAMA_BASE_URL;
    const model = process.env.OLLAMA_MODEL || 'deepseek-r1:latest';

    if (!baseUrl) {
      throw new Error('Ollama base URL is missing. Please add OLLAMA_BASE_URL to your .env file.');
    }

    const formattedBaseUrl = baseUrl.replace(/\/api.*$/, '');
    const optimizedParams = R1Optimizer.getOptimizedParams(useCase);

    logger.info('Creating use-case optimized Ollama chat model', {
      baseUrl: formattedBaseUrl,
      model,
      useCase,
      optimizations: optimizedParams,
    });

    try {
      const chatModel = new ChatOllama({
        baseUrl: formattedBaseUrl,
        model,
        temperature: optimizedParams.temperature,
      });

      logger.info('Use-case optimized Ollama chat model created', {
        baseUrl: formattedBaseUrl,
        model,
        useCase,
        type: 'langchain-ollama-optimized',
      });

      return chatModel;
    } catch (error) {
      logger.error('Failed to create optimized Ollama chat model', {
        error: error instanceof Error ? error.message : 'Unknown error',
        baseUrl: formattedBaseUrl,
        model,
        useCase,
      });
      throw error;
    }
  }

  /**
   * Create OpenAI embeddings model
   */
  private static createOpenAIEmbeddings(): OpenAIEmbeddings {
    const apiKey = process.env.OPENAI_API_KEY;

    logger.info('Creating OpenAI embeddings model', {
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey?.length || 0,
      apiKeyPrefix: apiKey?.substring(0, 10) || 'none',
      isDefaultKey: apiKey === 'your-openai-api-key-here',
    });

    if (!apiKey || apiKey === 'your-openai-api-key-here') {
      logger.error('OpenAI API key validation failed', {
        hasApiKey: !!apiKey,
        isDefaultKey: apiKey === 'your-openai-api-key-here',
        keyLength: apiKey?.length || 0,
      });
      throw new Error('OpenAI API key is missing or not configured properly');
    }

    try {
      const embeddings = new OpenAIEmbeddings({
        openAIApiKey: apiKey,
        modelName: 'text-embedding-ada-002',
      });

      logger.info('OpenAI embeddings model created successfully');
      return embeddings;
    } catch (error) {
      logger.error('Failed to create OpenAI embeddings model', {
        error: error instanceof Error ? error.message : 'Unknown error',
        apiKeyLength: apiKey?.length || 0,
      });
      throw error;
    }
  }

  /**
   * Create Ollama embeddings model
   */
  private static createOllamaEmbeddings(): OllamaEmbeddings {
    const baseUrl = process.env.OLLAMA_BASE_URL;
    const model = process.env.OLLAMA_EMBEDDING_MODEL || 'nomic-embed-text:latest';

    if (!baseUrl) {
      throw new Error('Ollama base URL is missing. Please add OLLAMA_BASE_URL to your .env file.');
    }

    // For LangChain Ollama, we need the base URL without /api/chat
    // LangChain will append the correct endpoints automatically
    const formattedBaseUrl = baseUrl.replace(/\/api.*$/, '');

    logger.info('Creating Ollama embeddings model', {
      originalBaseUrl: baseUrl,
      formattedBaseUrl,
      model,
    });

    try {
      return new OllamaEmbeddings({
        baseUrl: formattedBaseUrl,
        model,
      });
    } catch (error) {
      logger.error('Failed to create Ollama embeddings model', {
        error: error instanceof Error ? error.message : 'Unknown error',
        baseUrl: formattedBaseUrl,
        model,
      });
      throw error;
    }
  }

  /**
   * Get the current model name
   */
  static getModelName(): string {
    const provider = this.getProvider();
    if (provider === 'ollama') {
      return process.env.OLLAMA_MODEL || 'deepseek-r1';
    } else {
      return process.env.OPENAI_MODEL || 'gpt-4';
    }
  }

  /**
   * Get the current embedding model name
   */
  static getEmbeddingModelName(): string {
    const provider = this.getProvider();
    if (provider === 'ollama') {
      return process.env.OLLAMA_EMBEDDING_MODEL || 'nomic-embed-text';
    } else {
      return 'text-embedding-ada-002';
    }
  }

  /**
   * Reset cached models (useful for testing or configuration changes)
   */
  static reset(): void {
    this.chatModel = null;
    this.embeddings = null;
    this.openaiClient = null;
    logger.info('AI provider service reset');
  }

  /**
   * Get provider configuration info
   */
  static getProviderInfo(): {
    provider: AIProvider;
    chatModel: string;
    embeddingModel: string;
    baseUrl?: string;
  } {
    const provider = this.getProvider();
    const info = {
      provider,
      chatModel: this.getModelName(),
      embeddingModel: this.getEmbeddingModelName(),
    };

    if (provider === 'ollama') {
      return {
        ...info,
        baseUrl: process.env.OLLAMA_BASE_URL,
      };
    }

    return info;
  }

  /**
   * Optimize prompt for R1 model to reduce thinking time
   */
  static optimizePromptForR1(prompt: string, taskType: 'email' | 'response' | 'general' = 'general'): string {
    const provider = this.getProvider();

    if (provider === 'ollama' && this.getModelName().includes('r1')) {
      return R1Optimizer.optimizePrompt(prompt, taskType);
    }

    // For non-R1 models, return original prompt
    return prompt;
  }

  /**
   * Get optimized chat response with R1 optimization and timeout handling
   */
  static async getOptimizedResponse(
    prompt: string,
    taskType: 'email' | 'response' | 'general' = 'general',
    timeoutMs: number = 30000 // 30 second default timeout
  ): Promise<string> {
    const optimizedPrompt = this.optimizePromptForR1(prompt, taskType);
    const optimizedModel = this.getOptimizedChatModel(taskType === 'email' ? 'email' : 'chat');

    // Create a timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`AI response timeout after ${timeoutMs}ms`));
      }, timeoutMs);
    });

    try {
      // Race between the AI response and timeout
      const response = await Promise.race([
        optimizedModel.invoke([
          {
            role: 'user',
            content: optimizedPrompt,
          },
        ]),
        timeoutPromise
      ]);

      return response.content.toString();
    } catch (error) {
      logger.error('Error in optimized AI response', {
        error: error instanceof Error ? error.message : 'Unknown error',
        taskType,
        timeout: timeoutMs,
        promptLength: prompt.length,
      });
      throw error;
    }
  }

  /**
   * Test the connection to the AI provider
   */
  static async testConnection(): Promise<boolean> {
    const provider = this.getProvider();

    logger.info('Testing AI provider connection', {
      provider,
      timestamp: new Date().toISOString(),
    });

    try {
      // Reset cache to pick up new configuration
      this.reset();

      const chatModel = this.getChatModel();

      logger.info('AI chat model initialized', {
        modelName: this.getModelName(),
        provider,
        timestamp: new Date().toISOString(),
      });

      const testMessage = 'Hello, please respond with just "OK" to test the connection.';

      logger.info('Sending test message to AI model', {
        message: testMessage,
        provider,
        timestamp: new Date().toISOString(),
      });

      // Test with a simple prompt
      const response = await chatModel.invoke([
        {
          role: 'user',
          content: testMessage,
        },
      ]);

      const responseText = response.content.toString().toLowerCase();
      const isWorking = responseText.includes('ok') || responseText.includes('hello');

      logger.info('AI provider connection test completed', {
        provider,
        success: isWorking,
        response: response.content.toString().substring(0, 100),
        timestamp: new Date().toISOString(),
      });

      return isWorking;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      logger.error('AI provider connection test failed', {
        provider,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      });

      return false;
    }
  }
}
