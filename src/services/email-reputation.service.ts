import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import axios from 'axios';

// API keys from environment variables
const GOOGLE_POSTMASTER_API_KEY = process.env.GOOGLE_POSTMASTER_API_KEY;
const CLOUDFLARE_API_KEY = process.env.CLOUD_FLARE_API_KEY;
const MXTOOLBOX_API_KEY = process.env.MXTOOLBOX_API_KEY;

export class EmailReputationService {
  /**
   * Check the reputation of an email domain using multiple services
   */
  static async checkDomainReputation(domain: string): Promise<{
    score: number;
    details: any;
    provider: string;
  }> {
    try {
      // Try Google Postmaster API first
      const googleResult = await this.checkWithGooglePostmaster(domain);
      if (googleResult) {
        return {
          score: googleResult.score,
          details: googleResult.details,
          provider: 'Google Postmaster'
        };
      }

      // Fall back to Cloudflare
      const cloudflareResult = await this.checkWithCloudflare(domain);
      if (cloudflareResult) {
        return {
          score: cloudflareResult.score,
          details: cloudflareResult.details,
          provider: 'Cloudflare'
        };
      }

      // Fall back to MXToolbox
      const mxToolboxResult = await this.checkWithMxToolbox(domain);
      if (mxToolboxResult) {
        return {
          score: mxToolboxResult.score,
          details: mxToolboxResult.details,
          provider: 'MXToolbox'
        };
      }

      // If all checks fail, return a neutral score
      logger.warn('All reputation checks failed, returning neutral score', { domain });
      return {
        score: 50, // Neutral score
        details: { message: 'No reputation data available' },
        provider: 'Default'
      };
    } catch (error) {
      logger.error('Error checking domain reputation', {
        domain,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      // Return a neutral score in case of error
      return {
        score: 50, // Neutral score
        details: { error: 'Error checking reputation' },
        provider: 'Error'
      };
    }
  }

  /**
   * Check email domain reputation using Google Postmaster API
   */
  private static async checkWithGooglePostmaster(domain: string): Promise<{
    score: number;
    details: any;
  } | null> {
    if (!GOOGLE_POSTMASTER_API_KEY) {
      logger.warn('Google Postmaster API key not configured, skipping check');
      return null;
    }

    try {
      // Google Postmaster API endpoint
      const endpoint = `https://gmail.googleapis.com/gmail/v1/users/me/settings/sendAs/${domain}/reputation`;
      
      const response = await axios.get(endpoint, {
        headers: {
          'Authorization': `Bearer ${GOOGLE_POSTMASTER_API_KEY}`
        }
      });

      if (response.data && response.data.reputationScore) {
        // Convert Google's reputation categories to a numeric score
        const reputationMap: { [key: string]: number } = {
          'HIGH': 90,
          'MEDIUM': 70,
          'LOW': 40,
          'BAD': 10
        };

        const score = reputationMap[response.data.reputationScore] || 50;
        
        return {
          score,
          details: response.data
        };
      }

      logger.warn('No reputation data from Google Postmaster', { domain });
      return null;
    } catch (error) {
      logger.error('Error checking with Google Postmaster', {
        domain,
        error: error.response?.data || (error instanceof Error ? error.message : 'Unknown error')
      });
      return null;
    }
  }

  /**
   * Check email domain reputation using Cloudflare API
   */
  private static async checkWithCloudflare(domain: string): Promise<{
    score: number;
    details: any;
  } | null> {
    if (!CLOUDFLARE_API_KEY) {
      logger.warn('Cloudflare API key not configured, skipping check');
      return null;
    }

    try {
      // Cloudflare Email Security API endpoint
      const endpoint = `https://api.cloudflare.com/client/v4/accounts/email/routing/domains/${domain}/reputation`;
      
      const response = await axios.get(endpoint, {
        headers: {
          'Authorization': `Bearer ${CLOUDFLARE_API_KEY}`
        }
      });

      if (response.data && response.data.success && response.data.result) {
        // Convert Cloudflare's data to a score
        const result = response.data.result;
        let score = 50; // Default neutral score
        
        // Calculate score based on Cloudflare's data
        if (result.spf && result.dkim && result.dmarc) {
          score += 30; // Good configuration
        } else if (result.spf && result.dkim) {
          score += 20; // Decent configuration
        } else if (result.spf || result.dkim) {
          score += 10; // Basic configuration
        }
        
        // Adjust for blacklists
        if (result.blacklists && result.blacklists.length > 0) {
          score -= 10 * result.blacklists.length;
        }
        
        // Ensure score is within 0-100 range
        score = Math.max(0, Math.min(100, score));
        
        return {
          score,
          details: response.data.result
        };
      }

      logger.warn('No reputation data from Cloudflare', { domain });
      return null;
    } catch (error) {
      logger.error('Error checking with Cloudflare', {
        domain,
        error: error.response?.data || (error instanceof Error ? error.message : 'Unknown error')
      });
      return null;
    }
  }

  /**
   * Check email domain reputation using MXToolbox API
   */
  private static async checkWithMxToolbox(domain: string): Promise<{
    score: number;
    details: any;
  } | null> {
    if (!MXTOOLBOX_API_KEY) {
      logger.warn('MXToolbox API key not configured, skipping check');
      return null;
    }

    try {
      // Check blacklist status
      const blacklistResponse = await axios.get(`https://api.mxtoolbox.com/api/v1/Lookup?command=blacklist&argument=${domain}`, {
        headers: {
          'Authorization': `${MXTOOLBOX_API_KEY}`
        }
      });

      // Check DMARC configuration
      const dmarcResponse = await axios.get(`https://api.mxtoolbox.com/api/v1/Lookup?command=dmarc&argument=${domain}`, {
        headers: {
          'Authorization': `${MXTOOLBOX_API_KEY}`
        }
      });

      // Check SPF configuration
      const spfResponse = await axios.get(`https://api.mxtoolbox.com/api/v1/Lookup?command=spf&argument=${domain}`, {
        headers: {
          'Authorization': `${MXTOOLBOX_API_KEY}`
        }
      });

      // Calculate score based on results
      let score = 50; // Start with neutral score
      const details: any = {
        blacklist: blacklistResponse.data,
        dmarc: dmarcResponse.data,
        spf: spfResponse.data
      };

      // Check blacklist status
      if (blacklistResponse.data && blacklistResponse.data.Failed && blacklistResponse.data.Failed > 0) {
        // Domain is on blacklists
        score -= blacklistResponse.data.Failed * 5;
      } else {
        // Domain is not on blacklists
        score += 20;
      }

      // Check DMARC
      if (dmarcResponse.data && dmarcResponse.data.Success) {
        score += 15;
      }

      // Check SPF
      if (spfResponse.data && spfResponse.data.Success) {
        score += 15;
      }

      // Ensure score is within 0-100 range
      score = Math.max(0, Math.min(100, score));

      return {
        score,
        details
      };
    } catch (error) {
      logger.error('Error checking with MXToolbox', {
        domain,
        error: error.response?.data || (error instanceof Error ? error.message : 'Unknown error')
      });
      return null;
    }
  }

  /**
   * Save reputation check results to the database
   */
  static async saveReputationCheck(emailAccountId: string, score: number, provider: string, details: any): Promise<void> {
    try {
      await prisma.reputationCheck.create({
        data: {
          emailAccountId,
          score,
          provider,
          details
        }
      });

      // Update the email account with the latest score
      await prisma.emailAccount.update({
        where: { id: emailAccountId },
        data: {
          reputationScore: score,
          lastChecked: new Date()
        }
      });

      logger.info('Saved reputation check', { emailAccountId, score, provider });
    } catch (error) {
      logger.error('Error saving reputation check', {
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Check reputation for all email accounts in an organization
   */
  static async checkAllAccountsReputation(organizationId: string): Promise<void> {
    try {
      // Get all email accounts for the organization
      const emailAccounts = await prisma.emailAccount.findMany({
        where: {
          user: {
            organizations: {
              some: {
                organizationId
              }
            }
          }
        }
      });

      logger.info(`Checking reputation for ${emailAccounts.length} email accounts`, { organizationId });

      // Check reputation for each account
      for (const account of emailAccounts) {
        try {
          // Extract domain from email
          const domain = account.email.split('@')[1];
          
          // Check domain reputation
          const reputationResult = await this.checkDomainReputation(domain);
          
          // Save the results
          await this.saveReputationCheck(
            account.id,
            reputationResult.score,
            reputationResult.provider,
            reputationResult.details
          );
        } catch (accountError) {
          logger.error('Error checking account reputation', {
            accountId: account.id,
            email: account.email,
            error: accountError instanceof Error ? accountError.message : 'Unknown error'
          });
        }
      }

      logger.info('Completed reputation checks for all accounts', { organizationId });
    } catch (error) {
      logger.error('Error checking all accounts reputation', {
        organizationId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}
