import { prisma } from '@/lib/prisma';
import { UsageMetric } from './usage.service';

export class BaseUsageService {
  protected static async getCurrentUsage(organizationId: string): Promise<UsageMetric> {
    const date = new Date();
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);

    const usage = await prisma.usage.findUnique({
      where: {
        organizationId_period: {
          organizationId,
          period: startOfMonth,
        },
      },
    });

    return {
      emailsSent: usage?.emailsSent ?? 0,
      emailAccounts: usage?.emailAccounts ?? 0,
      storageUsed: usage?.storageUsed ?? 0,
    };
  }
}