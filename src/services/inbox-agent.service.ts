import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { AgentService } from './agent.service';
import { KnowledgeBaseService } from './knowledge-base.service';
import { EmailService } from './email.service';
import { AgentToolsService } from './agent-tools.service';
import { CalendarService } from './calendar.service';
import { AIProviderService } from './ai-provider.service';

/**
 * Service for managing AI agent integration with email inboxes
 */
export class InboxAgentService {
  /**
   * Enable AI agent for an email account
   */
  static async enableAgentForInbox(
    emailAccountId: string,
    agentId: string,
    options: {
      aiReplyEnabled?: boolean;
      aiReplyDelay?: number;
      aiReplyPrompt?: string;
    } = {}
  ) {
    try {
      // Verify the agent exists and belongs to the same organization
      const agent = await prisma.agent.findUnique({
        where: { id: agentId },
        include: { organization: true },
      });

      if (!agent) {
        throw new Error(`Agent not found: ${agentId}`);
      }

      // Verify the email account exists
      const emailAccount = await prisma.emailAccount.findUnique({
        where: { id: emailAccountId },
        include: { user: { include: { ownedOrganization: true } } },
      });

      if (!emailAccount) {
        throw new Error(`Email account not found: ${emailAccountId}`);
      }

      // Check if agent and email account belong to the same organization
      const userOrgId = emailAccount.user.ownedOrganization?.id;
      if (agent.organizationId !== userOrgId) {
        throw new Error('Agent and email account must belong to the same organization');
      }

      // Update the email account with AI agent settings
      const updatedAccount = await prisma.emailAccount.update({
        where: { id: emailAccountId },
        data: {
          aiAgentEnabled: true,
          aiAgentId: agentId,
          aiReplyEnabled: options.aiReplyEnabled ?? true,
          aiReplyDelay: options.aiReplyDelay ?? 0,
          aiReplyPrompt: options.aiReplyPrompt,
        },
        include: {
          agent: true,
        },
      });

      logger.info('AI agent enabled for inbox', {
        emailAccountId,
        agentId,
        agentName: agent.name,
        aiReplyEnabled: options.aiReplyEnabled ?? true,
        aiReplyDelay: options.aiReplyDelay ?? 0,
      });

      return updatedAccount;
    } catch (error) {
      logger.error('Error enabling AI agent for inbox', {
        emailAccountId,
        agentId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Disable AI agent for an email account
   */
  static async disableAgentForInbox(emailAccountId: string) {
    try {
      const updatedAccount = await prisma.emailAccount.update({
        where: { id: emailAccountId },
        data: {
          aiAgentEnabled: false,
          aiAgentId: null,
          aiReplyEnabled: false,
          aiReplyDelay: null,
          aiReplyPrompt: null,
        },
      });

      logger.info('AI agent disabled for inbox', { emailAccountId });
      return updatedAccount;
    } catch (error) {
      logger.error('Error disabling AI agent for inbox', {
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Process incoming email with AI agent
   */
  static async processIncomingEmailWithAgent(
    receivedEmailId: string,
    emailAccountId: string
  ): Promise<boolean> {
    try {
      // Get the email account with agent configuration
      const emailAccount = await prisma.emailAccount.findUnique({
        where: { id: emailAccountId },
        include: {
          agent: {
            include: {
              knowledgeBases: {
                include: {
                  knowledgeBase: true,
                },
              },
            },
          },
        },
      });

      if (!emailAccount?.aiAgentEnabled || !emailAccount.agent) {
        logger.info('AI agent not enabled for this inbox', { emailAccountId });
        return false;
      }

      // Get the received email
      const receivedEmail = await prisma.receivedEmail.findUnique({
        where: { id: receivedEmailId },
        include: {
          lead: true,
        },
      });

      if (!receivedEmail) {
        throw new Error(`Received email not found: ${receivedEmailId}`);
      }

      // Check if this email should be processed (not already replied to)
      if (receivedEmail.isRepliedTo) {
        logger.info('Email already replied to', { receivedEmailId });
        return false;
      }

      // Apply delay if configured
      if (emailAccount.aiReplyDelay && emailAccount.aiReplyDelay > 0) {
        const delayMs = emailAccount.aiReplyDelay * 60 * 1000; // Convert minutes to milliseconds
        logger.info('Delaying AI reply', {
          receivedEmailId,
          delayMinutes: emailAccount.aiReplyDelay,
        });

        // Schedule the reply for later (you might want to use a queue system for this)
        setTimeout(async () => {
          await this.generateAndSendReply(receivedEmail, emailAccount);
        }, delayMs);

        return true;
      }

      // Generate and send reply immediately
      await this.generateAndSendReply(receivedEmail, emailAccount);
      return true;
    } catch (error) {
      logger.error('Error processing incoming email with agent', {
        receivedEmailId,
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Generate and send AI reply
   */
  private static async generateAndSendReply(
    receivedEmail: any,
    emailAccount: any
  ) {
    try {
      // Prepare context for AI agent
      let knowledgeBaseContent = '';

      // Get knowledge base content if agent has access
      if (emailAccount.agent.knowledgeBases.length > 0) {
        const query = `${receivedEmail.subject} ${receivedEmail.textBody}`;
        const kbResults = await KnowledgeBaseService.queryKnowledgeBase(
          emailAccount.agent.knowledgeBases[0].knowledgeBase.organizationId,
          query,
          5 // Get top 5 relevant documents
        );

        // Convert results to string
        if (kbResults && kbResults.length > 0) {
          knowledgeBaseContent = kbResults
            .map((result: any) => result.content)
            .join('\n\n');
        }
      }

      // Use custom prompt if configured, otherwise use agent's system prompt
      const customPrompt = emailAccount.aiReplyPrompt || emailAccount.agent.systemPrompt;

      // Generate reply using the agent
      const replyContent = await this.generateContextualReply(
        emailAccount.agent.id,
        receivedEmail,
        knowledgeBaseContent,
        customPrompt
      );

      // Send the reply
      await EmailService.sendEmail({
        emailAccountId: emailAccount.id,
        to: receivedEmail.from,
        subject: `Re: ${receivedEmail.subject}`,
        html: replyContent.html,
        headers: {
          'In-Reply-To': receivedEmail.messageId,
          'References': receivedEmail.references || receivedEmail.messageId,
        },
      });

      // Mark the original email as replied to
      await prisma.receivedEmail.update({
        where: { id: receivedEmail.id },
        data: { isRepliedTo: true },
      });

      // Store the agent reply
      await prisma.agentReply.create({
        data: {
          agentId: emailAccount.agent.id,
          receivedEmailId: receivedEmail.id,
          leadId: receivedEmail.leadId,
          subject: `Re: ${receivedEmail.subject}`,
          textContent: replyContent.text,
          htmlContent: replyContent.html,
          status: 'SENT',
          sentAt: new Date(),
        },
      });

      logger.info('AI reply sent successfully', {
        receivedEmailId: receivedEmail.id,
        agentId: emailAccount.agent.id,
        to: receivedEmail.from,
      });
    } catch (error) {
      logger.error('Error generating and sending AI reply', {
        receivedEmailId: receivedEmail.id,
        agentId: emailAccount.agent?.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Generate contextual reply using AI agent with knowledge base and tools
   */
  private static async generateContextualReply(
    agentId: string,
    receivedEmail: any,
    knowledgeBaseContent: string,
    customPrompt?: string
  ): Promise<{ text: string; html: string }> {
    try {
      // Analyze email intent to determine which tools to use
      const emailContent = receivedEmail.textBody || receivedEmail.htmlBody || '';
      const intent = AgentToolsService.analyzeEmailIntent(emailContent);

      logger.info('Email intent analysis', {
        receivedEmailId: receivedEmail.id,
        intent,
        agentId,
      });

      // Check if this is an appointment request
      const appointmentRequest = CalendarService.parseAppointmentRequest(emailContent);

      // If it's an appointment request and agent has calendar capabilities, handle it
      if (appointmentRequest.isAppointmentRequest && receivedEmail.leadId) {
        const agent = await prisma.agent.findUnique({
          where: { id: agentId },
        });

        if (agent?.calendarEnabled && agent.capabilities.includes('appointment_scheduling')) {
          try {
            // Extract attendee info from email
            const fromEmail = this.extractEmailAddress(receivedEmail.from);
            const fromName = this.extractNameFromEmail(receivedEmail.from);

            // Schedule appointment
            const appointmentResult = await CalendarService.scheduleAppointment(
              agentId,
              receivedEmail.leadId,
              receivedEmail.campaignId || '',
              {
                title: appointmentRequest.purpose || 'Meeting Request',
                description: `Scheduled via email: ${receivedEmail.subject}`,
                preferredDate: appointmentRequest.preferredDate,
                duration: appointmentRequest.duration,
                attendeeEmail: fromEmail || receivedEmail.from,
                attendeeName: fromName,
              }
            );

            if (appointmentResult.success) {
              // Generate confirmation email
              const confirmationText = `Thank you for your interest in scheduling a meeting!

I've successfully scheduled your appointment:
- Date & Time: ${appointmentResult.appointment.startTime.toLocaleString()}
- Duration: ${appointmentRequest.duration || 30} minutes
- Meeting Link: ${appointmentResult.appointment.meetingLink || 'Will be provided separately'}

Looking forward to speaking with you!`;

              return {
                text: confirmationText,
                html: confirmationText.replace(/\n/g, '<br>'),
              };
            }
          } catch (appointmentError) {
            logger.error('Error scheduling appointment from email', {
              error: appointmentError instanceof Error ? appointmentError.message : 'Unknown error',
              receivedEmailId: receivedEmail.id,
            });
          }
        }
      }

      // Prepare enhanced context with knowledge base
      const emailContext = `
Original Email:
From: ${receivedEmail.from}
Subject: ${receivedEmail.subject}
Content: ${emailContent}
`;

      // Get comprehensive knowledge base content if needed
      let enhancedKnowledgeContent = knowledgeBaseContent;
      if (intent.needsKnowledgeBase && receivedEmail.leadId) {
        try {
          // Get more targeted knowledge base content
          const agent = await prisma.agent.findUnique({
            where: { id: agentId },
            include: {
              knowledgeBases: {
                include: {
                  knowledgeBase: true,
                },
              },
            },
          });

          if (agent?.knowledgeBases.length) {
            const query = `${receivedEmail.subject} ${emailContent}`;
            const kbResults = await KnowledgeBaseService.queryKnowledgeBase(
              agent.organizationId,
              query,
              10 // Get more results for comprehensive response
            );

            // Convert results to string
            if (kbResults && kbResults.length > 0) {
              enhancedKnowledgeContent = kbResults
                .map((result: any) => result.content)
                .join('\n\n');
            }
          }
        } catch (kbError) {
          logger.error('Error querying knowledge base', {
            error: kbError instanceof Error ? kbError.message : 'Unknown error',
            agentId,
          });
        }
      }

      const knowledgeContext = enhancedKnowledgeContent
        ? `\n\nRelevant Knowledge Base Information:\n${enhancedKnowledgeContent}`
        : '';

      // Add intent-based instructions
      const intentInstructions = this.getIntentInstructions(intent);

      const fullContext = `${emailContext}${knowledgeContext}

${intentInstructions}`;

      // Get thread context for better replies
      let threadContext = '';
      try {
        // Get previous emails in the thread
        const threadEmails = await prisma.receivedEmail.findMany({
          where: {
            OR: [
              { inReplyTo: receivedEmail.messageId },
              { references: { contains: receivedEmail.messageId } },
              { messageId: receivedEmail.inReplyTo || '' },
            ],
          },
          orderBy: { receivedAt: 'asc' },
          take: 5, // Limit to last 5 emails in thread
        });

        if (threadEmails.length > 0) {
          threadContext = threadEmails
            .map(email => `From: ${email.from}\nSubject: ${email.subject}\nContent: ${email.textBody || email.htmlBody}`)
            .join('\n\n---\n\n');
        }
      } catch (threadError) {
        logger.error('Error getting thread context', {
          error: threadError instanceof Error ? threadError.message : 'Unknown error',
          receivedEmailId: receivedEmail.id,
        });
      }

      // Use the enhanced agent service to generate reply
      const reply = await AgentService.generateEmailReply(
        agentId,
        emailContent,
        receivedEmail.subject,
        threadContext,
        enhancedKnowledgeContent,
        receivedEmail.emailAccountId
      );

      // Update lead status if needed
      if (intent.needsLeadUpdate && receivedEmail.leadId) {
        try {
          await CalendarService.updateLead(receivedEmail.leadId, {
            status: this.inferLeadStatus(emailContent),
            customFields: {
              lastEmailIntent: intent.intent,
              lastEmailDate: new Date(),
            },
          });
        } catch (leadUpdateError) {
          logger.error('Error updating lead status', {
            error: leadUpdateError instanceof Error ? leadUpdateError.message : 'Unknown error',
            leadId: receivedEmail.leadId,
          });
        }
      }

      return reply;
    } catch (error) {
      logger.error('Error generating contextual reply', {
        agentId,
        receivedEmailId: receivedEmail.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Get AI agent configuration for an email account
   */
  static async getAgentConfiguration(emailAccountId: string) {
    try {
      const emailAccount = await prisma.emailAccount.findUnique({
        where: { id: emailAccountId },
        include: {
          agent: {
            include: {
              knowledgeBases: {
                include: {
                  knowledgeBase: true,
                },
              },
            },
          },
        },
      });

      if (!emailAccount) {
        throw new Error(`Email account not found: ${emailAccountId}`);
      }

      return {
        aiAgentEnabled: emailAccount.aiAgentEnabled,
        aiReplyEnabled: emailAccount.aiReplyEnabled,
        aiReplyDelay: emailAccount.aiReplyDelay,
        aiReplyPrompt: emailAccount.aiReplyPrompt,
        agent: emailAccount.agent,
      };
    } catch (error) {
      logger.error('Error getting agent configuration', {
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Extract email address from email string
   */
  private static extractEmailAddress(emailString: string): string | null {
    const emailRegex = /<([^>]+)>|([^\s<>]+@[^\s<>]+)/;
    const match = emailString.match(emailRegex);
    return match ? (match[1] || match[2]) : null;
  }

  /**
   * Extract name from email string
   */
  private static extractNameFromEmail(emailString: string): string | null {
    const nameMatch = emailString.match(/^([^<]+)</);
    return nameMatch ? nameMatch[1].trim().replace(/"/g, '') : null;
  }

  /**
   * Get intent-based instructions for AI response
   */
  private static getIntentInstructions(intent: any): string {
    switch (intent.intent) {
      case 'appointment':
        return `
IMPORTANT: This email is requesting an appointment or meeting.
- If calendar integration is available, offer specific time slots
- Be helpful and accommodating with scheduling
- Provide clear next steps for booking`;

      case 'information':
        return `
IMPORTANT: This email is requesting information.
- Use the knowledge base information provided above to give accurate answers
- Be comprehensive but concise
- If you don't have specific information, offer to connect them with a specialist`;

      case 'status_update':
        return `
IMPORTANT: This email indicates a change in lead status or interest level.
- Acknowledge their current situation
- Provide appropriate next steps based on their interest level
- Be supportive and helpful`;

      default:
        return `
IMPORTANT: Provide a helpful, professional response.
- Use any relevant knowledge base information
- Be friendly and engaging
- Offer appropriate next steps or assistance`;
    }
  }

  /**
   * Infer lead status from email content
   */
  private static inferLeadStatus(emailContent: string): string {
    const content = emailContent.toLowerCase();

    if (content.includes('interested') || content.includes('yes') || content.includes('sounds good')) {
      return 'interested';
    }

    if (content.includes('not interested') || content.includes('no thanks') || content.includes('not right now')) {
      return 'not_interested';
    }

    if (content.includes('thinking') || content.includes('considering') || content.includes('maybe')) {
      return 'considering';
    }

    if (content.includes('budget') || content.includes('price') || content.includes('cost')) {
      return 'qualified';
    }

    return 'active'; // Default status
  }
}
