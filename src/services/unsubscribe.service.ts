import { prisma } from '@/lib/prisma';
import { createHash } from 'crypto';

export class UnsubscribeService {
  static generateUnsubscribeToken(email: string, campaignId: string) {
    return createHash('sha256')
      .update(`${email}:${campaignId}:${process.env.ENCRYPTION_KEY}`)
      .digest('hex');
  }

  static async unsubscribe(token: string, email: string) {
    // Since Unsubscribe model doesn't exist, we'll just log the unsubscribe action
    console.log(`Unsubscribe request for ${email} with token ${token}`);

    // Update all leads with this email to mark them as unsubscribed
    await prisma.lead.updateMany({
      where: {
        email,
      },
      data: {
        status: 'unsubscribed',
        updatedAt: new Date(),
      },
    });

    return { success: true };
  }

  static async isUnsubscribed(email: string) {
    // Check if the lead is marked as unsubscribed
    const lead = await prisma.lead.findFirst({
      where: {
        email,
        status: 'unsubscribed'
      },
    });
    return !!lead;
  }
}