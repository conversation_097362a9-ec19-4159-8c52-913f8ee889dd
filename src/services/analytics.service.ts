import { PrismaClient } from '@prisma/client';
import redis from '@/lib/redis';

const prisma = new PrismaClient();

export class AnalyticsService {
  static async trackOpen(campaignId: string, recipientId: string) {
    await Promise.all([
      prisma.recipient.update({
        where: { id: recipientId },
        data: {
          openedAt: new Date(),
          // No opens field in the Recipient model
        },
      }),
      redis.hincrby(`campaign:${campaignId}:metrics`, 'opens', 1),
    ]);
  }

  static async trackClick(campaignId: string, recipientId: string, linkUrl: string) {
    await Promise.all([
      // Update recipient with clickedAt timestamp
      prisma.recipient.update({
        where: { id: recipientId },
        data: {
          clickedAt: new Date(),
        },
      }),
      redis.hincrby(`campaign:${campaignId}:metrics`, 'clicks', 1),
      redis.hincrby(`campaign:${campaignId}:links`, linkUrl, 1),
    ]);
  }

  static async getCampaignMetrics(campaignId: string) {
    const [campaign, metricsData, linkData] = await Promise.all([
      prisma.campaign.findUnique({
        where: { id: campaignId },
        include: {
          leads: true,
          _count: {
            select: {
              leads: true,
            },
          },
        },
      }),
      redis.hgetall(`campaign:${campaignId}:metrics`),
      redis.hgetall(`campaign:${campaignId}:links`),
    ]);

    if (!campaign) {
      throw new Error('Campaign not found');
    }

    const delivered = campaign.leads.filter(r => r.status === 'completed').length;
    const bounced = campaign.leads.filter(r => r.status === 'bounced').length;
    const opened = parseInt(metricsData.opens || '0');

    return {
      totalRecipients: campaign._count.leads,
      delivered,
      bounced,
      opened,
      openRate: delivered > 0 ? (opened / delivered) * 100 : 0,
      bounceRate: campaign._count.leads > 0 ? (bounced / campaign._count.leads) * 100 : 0,
      clicks: parseInt(metricsData.clicks || '0'),
      clickRate: delivered > 0 ? (parseInt(metricsData.clicks || '0') / delivered) * 100 : 0,
      linkClickMap: linkData,
    };
  }

  static async generateReport(campaignId: string) {
    const metrics = await this.getCampaignMetrics(campaignId);
    const campaign = await prisma.campaign.findUnique({
      where: { id: campaignId },
      include: {
        leads: true,
      },
    });

    if (!campaign) {
      throw new Error('Campaign not found');
    }

    const recipientActivity = campaign.leads.map(lead => ({
      id: lead.id,
      status: lead.status,
      joinedAt: lead.joinedAt,
      completedAt: lead.completedAt,
      unsubscribedAt: lead.unsubscribedAt,
      lastInteractionAt: lead.lastInteractionAt,
    }));

    return {
      metrics,
      recipientActivity,
      generatedAt: new Date(),
    };
  }
}