import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import Imap from 'imap';
import { simpleParser } from 'mailparser';
import { EmailService } from './email.service';
import { AgentService } from './agent.service';
import { InboxAgentService } from './inbox-agent.service';
import { decrypt } from '@/lib/crypto';
import { processIncomingEmailForReplyTracking } from '@/lib/reply-tracking';

interface ImapConfig {
  user: string;
  password: string;
  host: string;
  port: number;
  tls: boolean;
}

export class ImapService {
  /**
   * Fetch emails for a specific email account
   */
  static async fetchEmails(emailAccountId: string): Promise<void> {
    try {
      // Get the email account
      const emailAccount = await prisma.emailAccount.findUnique({
        where: { id: emailAccountId },
      });

      if (!emailAccount) {
        throw new Error(`Email account not found: ${emailAccountId}`);
      }

      if (!emailAccount.imapEnabled || !emailAccount.imapHost || !emailAccount.imapPort) {
        throw new Error(`IMAP not configured for email account: ${emailAccountId}`);
      }

      // Decrypt the IMAP password
      let password = '';
      try {
        if (emailAccount.imapPassword) {
          password = await decrypt(emailAccount.imapPassword);
          logger.info('Successfully decrypted IMAP password', { emailAccountId });
        } else {
          logger.warn('No IMAP password found for account', { emailAccountId });
        }
      } catch (decryptError) {
        logger.error('Failed to decrypt IMAP password', {
          emailAccountId,
          error: decryptError instanceof Error ? decryptError.message : 'Unknown error',
        });
        throw new Error('Failed to decrypt IMAP password');
      }

      // Configure IMAP client
      const imapConfig: ImapConfig = {
        user: emailAccount.imapUsername || emailAccount.email,
        password: password,
        host: emailAccount.imapHost,
        port: emailAccount.imapPort,
        tls: true,
      };

      // Connect to IMAP server and fetch emails
      await this.connectAndFetchEmails(emailAccountId, imapConfig);

      // Update last sync time
      await prisma.emailAccount.update({
        where: { id: emailAccountId },
        data: { lastImapSync: new Date() },
      });

      logger.info('IMAP sync completed', { emailAccountId });
    } catch (error) {
      logger.error('Error fetching emails via IMAP', {
        emailAccountId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Connect to IMAP server and fetch emails
   */
  private static async connectAndFetchEmails(emailAccountId: string, config: ImapConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      const imap = new Imap({
        user: config.user,
        password: config.password,
        host: config.host,
        port: config.port,
        tls: config.tls,
        tlsOptions: { rejectUnauthorized: false }, // For development, consider removing in production
      });

      imap.once('ready', () => {
        imap.openBox('INBOX', false, (err, box) => {
          if (err) {
            imap.end();
            return reject(err);
          }

          // Get emails from the last 7 days
          const lastWeek = new Date();
          lastWeek.setDate(lastWeek.getDate() - 7);

          // Search for all emails from the last 30 days
          const lastMonth = new Date();
          lastMonth.setDate(lastMonth.getDate() - 30);

          // Search for ALL emails, not just unread ones
          logger.info('Searching for emails', {
            emailAccountId,
            searchCriteria: `ALL emails since ${lastMonth.toISOString()}`
          });

          imap.search(['ALL', ['SINCE', lastMonth]], (err, results) => {
            if (err) {
              logger.error('Error searching for emails', {
                emailAccountId,
                error: err.message,
                stack: err.stack
              });
              imap.end();
              return reject(err);
            }

            logger.info('Search results', {
              emailAccountId,
              foundEmails: results.length
            });

            if (results.length === 0) {
              logger.info('No emails found', { emailAccountId });
              imap.end();
              return resolve();
            }

            const fetch = imap.fetch(results, { bodies: '', markSeen: false });
            const emails: any[] = [];

            fetch.on('message', (msg) => {
              msg.on('body', (stream) => {
                simpleParser(stream as any, async (err, parsed) => {
                  if (err) {
                    logger.error('Error parsing email', { error: err.message });
                    return;
                  }

                  try {
                    // Validate parsed email data
                    if (!parsed) {
                      logger.error('Parsed email is null or undefined');
                      return;
                    }

                    // Log the structure of the parsed email for debugging
                    logger.info('Parsed email structure', {
                      hasMessageId: !!parsed.messageId,
                      hasInReplyTo: !!parsed.inReplyTo,
                      referencesType: parsed.references ? typeof parsed.references : 'undefined',
                      isReferencesArray: parsed.references ? Array.isArray(parsed.references) : false,
                    });

                    // Log the email details
                    logger.info('Processing email', {
                      emailAccountId,
                      messageId: parsed.messageId,
                      subject: parsed.subject,
                      from: parsed.from?.text,
                      date: parsed.date
                    });

                    // Process the email
                    await this.processEmail(emailAccountId, parsed);
                    emails.push(parsed);
                  } catch (error) {
                    logger.error('Error processing email', {
                      error: error instanceof Error ? error.message : 'Unknown error',
                      stack: error instanceof Error ? error.stack : undefined,
                    });
                  }
                });
              });
            });

            fetch.once('error', (err) => {
              imap.end();
              reject(err);
            });

            fetch.once('end', () => {
              imap.end();
              resolve();
            });
          });
        });
      });

      imap.once('error', (err) => {
        reject(err);
      });

      imap.once('end', () => {
        logger.info('IMAP connection ended', { emailAccountId });
      });

      imap.connect();
    });
  }

  /**
   * Process a received email
   */
  private static async processEmail(emailAccountId: string, parsedEmail: any): Promise<void> {
    try {
      // Get the email account to check if this is a sent email
      const emailAccount = await prisma.emailAccount.findUnique({
        where: { id: emailAccountId },
      });

      if (!emailAccount) {
        throw new Error(`Email account not found: ${emailAccountId}`);
      }

      // Extract email data
      const messageId = parsedEmail.messageId || `no-id-${Date.now()}`;
      const inReplyTo = parsedEmail.inReplyTo || null;

      // Handle references which could be an array, string, or undefined
      let references = null;
      if (parsedEmail.references) {
        if (Array.isArray(parsedEmail.references)) {
          references = parsedEmail.references.join(' ');
        } else if (typeof parsedEmail.references === 'string') {
          references = parsedEmail.references;
        }
      }
      const from = parsedEmail.from?.text || '';
      const to = parsedEmail.to?.text || '';
      const cc = parsedEmail.cc?.text || null;
      const subject = parsedEmail.subject || '(No Subject)';
      const textBody = parsedEmail.text || null;
      const htmlBody = parsedEmail.html || null;
      const receivedAt = parsedEmail.date || new Date();
      const headers = parsedEmail.headers || {};

      // Extract the sender's email address
      const fromEmail = this.extractEmailAddress(from);

      // Skip emails sent from this email account (campaign emails, etc.)
      // Only process incoming emails and replies to our campaigns
      // Check if the sender email matches the account email (more reliable than headers)
      if (fromEmail && fromEmail.toLowerCase() === emailAccount.email.toLowerCase()) {
        logger.info('Skipping email sent from this account', {
          messageId,
          emailAccountId,
          subject,
          from: fromEmail,
          accountEmail: emailAccount.email,
          reason: 'From address matches account email'
        });
        return;
      }

      // Check if this email already exists by messageId
      const existingEmail = await prisma.receivedEmail.findFirst({
        where: { messageId },
      });

      if (existingEmail) {
        logger.info('Email already exists, skipping', {
          messageId,
          emailAccountId,
          subject,
          from,
          receivedAt: receivedAt.toISOString()
        });
        return;
      }

      // Also check by subject, from, and approximate date to avoid duplicates
      // This helps with emails that might have different message IDs but are essentially the same
      const receivedAtDate = new Date(receivedAt);
      const startDate = new Date(receivedAtDate);
      startDate.setMinutes(startDate.getMinutes() - 5);
      const endDate = new Date(receivedAtDate);
      endDate.setMinutes(endDate.getMinutes() + 5);

      const similarEmail = await prisma.receivedEmail.findFirst({
        where: {
          subject,
          from,
          receivedAt: {
            gte: startDate,
            lte: endDate
          },
          emailAccountId
        }
      });

      if (similarEmail) {
        logger.info('Similar email already exists, skipping', {
          messageId,
          existingMessageId: similarEmail.messageId,
          emailAccountId,
          subject,
          from,
          receivedAt: receivedAt.toISOString()
        });
        return;
      }

      logger.info('Storing new email in database', {
        messageId,
        emailAccountId,
        subject,
        from,
        receivedAt: receivedAt.toISOString()
      });

      // Create the received email record
      const receivedEmail = await prisma.receivedEmail.create({
        data: {
          emailAccountId,
          messageId,
          inReplyTo,
          references,
          from,
          to,
          cc,
          subject,
          textBody,
          htmlBody,
          receivedAt,
          headers: headers as any,
        },
      });

      // If this is a reply to a campaign email, process it
      if (inReplyTo) {
        await this.processReply(receivedEmail.id, inReplyTo);
      }

      // Check if this is a reply to a tracked email (using the reply-to address)
      const isTrackedReply = await processIncomingEmailForReplyTracking({
        to: to,
        from: from,
        subject: subject,
        messageId: messageId,
        inReplyTo: inReplyTo,
        text: textBody,
        html: htmlBody,
        headers: headers
      });

      if (isTrackedReply) {
        logger.info('Processed as a tracked reply', { messageId, emailAccountId });
      }

      // Process with inbox AI agent if enabled
      try {
        const agentProcessed = await InboxAgentService.processIncomingEmailWithAgent(
          receivedEmail.id,
          emailAccountId
        );

        if (agentProcessed) {
          logger.info('Email processed by inbox AI agent', {
            messageId,
            emailAccountId,
            receivedEmailId: receivedEmail.id
          });
        }
      } catch (agentError) {
        logger.error('Error processing email with inbox AI agent', {
          messageId,
          emailAccountId,
          receivedEmailId: receivedEmail.id,
          error: agentError instanceof Error ? agentError.message : 'Unknown error',
        });
        // Don't throw here - we want to continue processing even if agent fails
      }

      logger.info('Email processed successfully', { messageId });
    } catch (error) {
      logger.error('Error processing email', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Process a reply to a campaign email
   */
  private static async processReply(receivedEmailId: string, inReplyTo: string): Promise<void> {
    try {
      // Find the original campaign email by message ID
      // This would require storing message IDs when sending campaign emails
      // For now, we'll use a simplified approach

      // Get the received email
      const receivedEmail = await prisma.receivedEmail.findUnique({
        where: { id: receivedEmailId },
        include: {
          emailAccount: true,
        },
      });

      if (!receivedEmail) {
        throw new Error(`Received email not found: ${receivedEmailId}`);
      }

      // Extract the email address from the From field
      const fromEmail = this.extractEmailAddress(receivedEmail.from);
      if (!fromEmail) {
        throw new Error(`Could not extract email address from: ${receivedEmail.from}`);
      }

      // Find the lead by email
      const lead = await prisma.lead.findFirst({
        where: { email: fromEmail },
      });

      if (lead) {
        // Update the received email with the lead ID
        await prisma.receivedEmail.update({
          where: { id: receivedEmailId },
          data: { leadId: lead.id },
        });

        // Find campaigns that this lead is part of
        const campaignLeads = await prisma.campaignLead.findMany({
          where: { leadId: lead.id },
          include: {
            campaign: {
              include: {
                agent: true,
              },
            },
          },
        });

        // If there's an active campaign with an AI agent, process the reply
        for (const campaignLead of campaignLeads) {
          if (
            campaignLead.campaign.status === 'active' &&
            campaignLead.campaign.agentId
          ) {
            // Update the received email with campaign info
            await prisma.receivedEmail.update({
              where: { id: receivedEmailId },
              data: {
                campaignId: campaignLead.campaignId,
                isProcessed: true,
              },
            });

            // If there's an AI agent, generate a reply
            if (campaignLead.campaign.agent) {
              await this.generateAgentReply(
                receivedEmailId,
                campaignLead.campaign.agentId,
                campaignLead.campaignId,
                lead.id
              );
            }

            break; // Process only the first matching campaign
          }
        }
      }
    } catch (error) {
      logger.error('Error processing reply', {
        receivedEmailId,
        inReplyTo,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Generate an AI agent reply to an email
   */
  private static async generateAgentReply(
    receivedEmailId: string,
    agentId: string,
    campaignId: string,
    leadId: string
  ): Promise<void> {
    try {
      // Get the received email
      const receivedEmail = await prisma.receivedEmail.findUnique({
        where: { id: receivedEmailId },
      });

      if (!receivedEmail) {
        throw new Error(`Received email not found: ${receivedEmailId}`);
      }

      // Generate a reply using the AI agent
      const replyContent = await AgentService.generateEmailReply(
        agentId,
        receivedEmail.textBody || '',
        receivedEmail.subject
      );

      // Create the agent reply
      const agentReply = await prisma.agentReply.create({
        data: {
          agentId,
          receivedEmailId,
          campaignId,
          leadId,
          subject: `Re: ${receivedEmail.subject}`,
          textContent: replyContent.text,
          htmlContent: replyContent.html,
        },
      });

      // Update the received email to mark it as replied to
      await prisma.receivedEmail.update({
        where: { id: receivedEmailId },
        data: {
          isRepliedTo: true,
          agentReplyId: agentReply.id,
        },
      });

      // Send the reply email
      await this.sendAgentReply(agentReply.id);

      logger.info('Agent reply generated and sent', { agentReplyId: agentReply.id });
    } catch (error) {
      logger.error('Error generating agent reply', {
        receivedEmailId,
        agentId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Send an agent reply email
   */
  private static async sendAgentReply(agentReplyId: string): Promise<void> {
    try {
      // Get the agent reply with related data
      const agentReply = await prisma.agentReply.findUnique({
        where: { id: agentReplyId },
        include: {
          receivedEmail: {
            include: {
              emailAccount: true,
            },
          },
          lead: true,
        },
      });

      if (!agentReply || !agentReply.receivedEmail || !agentReply.lead) {
        throw new Error(`Agent reply or related data not found: ${agentReplyId}`);
      }

      // Send the email
      await EmailService.sendEmail({
        emailAccountId: agentReply.receivedEmail.emailAccountId,
        from: agentReply.receivedEmail.emailAccount.email,
        to: agentReply.lead.email,
        subject: agentReply.subject,
        html: agentReply.htmlContent || agentReply.textContent,
      });

      // Update the agent reply status
      await prisma.agentReply.update({
        where: { id: agentReplyId },
        data: {
          status: 'SENT',
          sentAt: new Date(),
        },
      });

      logger.info('Agent reply email sent', { agentReplyId });
    } catch (error) {
      // Update the agent reply with the error
      await prisma.agentReply.update({
        where: { id: agentReplyId },
        data: {
          status: 'FAILED',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });

      logger.error('Error sending agent reply email', {
        agentReplyId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Extract email address from a string
   */
  private static extractEmailAddress(str: string): string | null {
    const matches = str.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
    return matches ? matches[0] : null;
  }

  /**
   * Clean up sent emails that were incorrectly stored as received emails
   */
  static async cleanupSentEmails(): Promise<void> {
    try {
      logger.info('Starting cleanup of sent emails from received emails table');

      // Get all email accounts
      const emailAccounts = await prisma.emailAccount.findMany({
        select: { id: true, email: true },
      });

      let totalCleaned = 0;

      for (const account of emailAccounts) {
        // Find received emails that are actually sent emails
        // Only delete emails where the sender email matches the account email
        const sentEmails = await prisma.receivedEmail.findMany({
          where: {
            emailAccountId: account.id,
            from: {
              contains: account.email,
              mode: 'insensitive'
            }
          }
        });

        if (sentEmails.length > 0) {
          logger.info(`Found ${sentEmails.length} sent emails to clean up for account ${account.email}`);

          // Delete these emails
          await prisma.receivedEmail.deleteMany({
            where: {
              id: {
                in: sentEmails.map(email => email.id)
              }
            }
          });

          totalCleaned += sentEmails.length;
        }
      }

      logger.info(`Cleanup completed. Removed ${totalCleaned} sent emails from received emails table`);
    } catch (error) {
      logger.error('Error cleaning up sent emails', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Schedule IMAP sync for all enabled accounts
   */
  static async scheduleImapSync(): Promise<void> {
    try {
      // Note: We no longer automatically clean up emails during sync
      // as this was too aggressive and was removing legitimate received emails

      // Get all email accounts with IMAP enabled
      const emailAccounts = await prisma.emailAccount.findMany({
        where: { imapEnabled: true },
      });

      logger.info(`Scheduling IMAP sync for ${emailAccounts.length} accounts`);

      // Process each account
      for (const account of emailAccounts) {
        try {
          await this.fetchEmails(account.id);
        } catch (error) {
          logger.error('Error syncing account', {
            emailAccountId: account.id,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    } catch (error) {
      logger.error('Error scheduling IMAP sync', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}
