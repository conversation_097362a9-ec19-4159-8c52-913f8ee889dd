import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';
import { CalendarService } from './calendar.service';
import { KnowledgeBaseService } from './knowledge-base.service';
import { AIProviderService } from './ai-provider.service';
import { DynamicTool } from '@langchain/core/tools';

/**
 * AI Agent Tools Service - Provides agentic capabilities for AI agents
 * Integrates with LangChain tools for appointment scheduling, lead updates, and knowledge base queries
 */
export class AgentToolsService {
  /**
   * Create appointment scheduling tool
   */
  static createAppointmentTool(agentId: string, leadId: string, campaignId: string) {
    return new DynamicTool({
      name: "schedule_appointment",
      description: `Schedule an appointment with a lead. Use this when the customer requests a meeting, demo, or consultation.
      Input should be a JSON string with: title (required), description (optional), preferredDate (optional ISO format), duration (optional minutes), attendeeEmail (required), attendeeName (optional)`,
      func: async (input) => {
        try {
          const params = typeof input === 'string' ? JSON.parse(input) : input;
          const { title, description, preferredDate, duration, attendeeEmail, attendeeName } = params;

          const result = await CalendarService.scheduleAppointment(
            agentId,
            leadId,
            campaignId,
            {
              title,
              description,
              preferredDate: preferredDate ? new Date(preferredDate) : undefined,
              duration,
              attendeeEmail,
              attendeeName,
            }
          );

          if (result.success) {
            return `Appointment scheduled successfully! Meeting details:
- Title: ${title}
- Date: ${result.appointment.startTime}
- Duration: ${duration || 30} minutes
- Meeting Link: ${result.appointment.meetingLink || 'Will be provided separately'}

The appointment has been added to your calendar and a confirmation email will be sent.`;
          } else {
            return `I apologize, but I couldn't schedule the appointment: ${result.error}. Please let me know your preferred time and I'll try to find an alternative slot.`;
          }
        } catch (error) {
          logger.error('Error in appointment scheduling tool', {
            error: error instanceof Error ? error.message : 'Unknown error',
            agentId,
            leadId,
          });
          return "I encountered an error while trying to schedule the appointment. Please contact us directly to book your meeting.";
        }
      },
    });
  }

  /**
   * Create lead update tool
   */
  static createLeadUpdateTool(leadId: string) {
    return new DynamicTool({
      name: "update_lead",
      description: `Update lead information based on the conversation. Use this to track lead status, add notes, or update custom fields.
      Input should be a JSON string with: status (optional), notes (optional), tags (optional array), customFields (optional object)`,
      func: async (input) => {
        try {
          const params = typeof input === 'string' ? JSON.parse(input) : input;
          const { status, notes, tags, customFields } = params;

          const result = await CalendarService.updateLead(leadId, {
            status,
            notes,
            tags,
            customFields,
          });

          if (result.success) {
            return `Lead information updated successfully.`;
          } else {
            return `Failed to update lead information: ${result.error}`;
          }
        } catch (error) {
          logger.error('Error in lead update tool', {
            error: error instanceof Error ? error.message : 'Unknown error',
            leadId,
          });
          return "Error updating lead information.";
        }
      },
    });
  }

  /**
   * Create knowledge base query tool
   */
  static createKnowledgeBaseTool(agentId: string) {
    return new DynamicTool({
      name: "query_knowledge_base",
      description: `Search the knowledge base for relevant information to answer customer questions. Use this when you need specific information about products, services, or policies.
      Input should be a JSON string with: query (required), maxResults (optional, default 5)`,
      func: async (input) => {
        try {
          const params = typeof input === 'string' ? JSON.parse(input) : input;
          const { query, maxResults = 5 } = params;

          // Get agent's knowledge bases
          const agent = await prisma.agent.findUnique({
            where: { id: agentId },
            include: {
              knowledgeBases: {
                include: {
                  knowledgeBase: true,
                },
              },
            },
          });

          if (!agent?.knowledgeBases.length) {
            return "No knowledge base is connected to this agent.";
          }

          // Query the first knowledge base (can be enhanced to query multiple)
          const results = await KnowledgeBaseService.queryKnowledgeBase(
            agent.organizationId,
            query,
            maxResults
          );

          if (results && results.length > 0) {
            const formattedResults = results
              .map((result: any) => result.content)
              .join('\n\n');

            return `Here's what I found in our knowledge base:

${formattedResults}

This information should help answer your question. Is there anything specific you'd like me to clarify?`;
          } else {
            return "I couldn't find specific information about that in our knowledge base. Let me connect you with a team member who can provide more detailed assistance.";
          }
        } catch (error) {
          logger.error('Error in knowledge base query tool', {
            error: error instanceof Error ? error.message : 'Unknown error',
            agentId,
          });
          return "I encountered an error while searching our knowledge base. Please let me know how else I can help you.";
        }
      },
    });
  }

  /**
   * Create available slots tool
   */
  static createAvailableSlotseTool(agentId: string) {
    return new DynamicTool({
      name: "get_available_slots",
      description: `Get available time slots for scheduling appointments. Use this when a customer asks about availability.
      Input should be a JSON string with: date (optional ISO format, defaults to tomorrow), duration (optional minutes, default 30)`,
      func: async (input) => {
        try {
          const params = typeof input === 'string' ? JSON.parse(input) : input;
          const { date, duration = 30 } = params;

          const targetDate = date ? new Date(date) : new Date(Date.now() + 24 * 60 * 60 * 1000); // Tomorrow
          const slots = await CalendarService.getAvailableSlots(agentId, targetDate, duration);

          if (slots.length === 0) {
            return `I don't have any available slots for ${targetDate.toDateString()}. Would you like me to check a different date?`;
          }

          const slotList = slots.slice(0, 5).map((slot, index) =>
            `${index + 1}. ${slot.startTime.toLocaleTimeString()} - ${slot.endTime.toLocaleTimeString()}`
          ).join('\n');

          return `Here are the available time slots for ${targetDate.toDateString()}:

${slotList}

Would you like me to schedule one of these times for you?`;
        } catch (error) {
          logger.error('Error in available slots tool', {
            error: error instanceof Error ? error.message : 'Unknown error',
            agentId,
          });
          return "I encountered an error while checking availability. Please let me know your preferred time and I'll do my best to accommodate.";
        }
      },
    });
  }

  /**
   * Get all tools for an agent
   */
  static getAgentTools(agentId: string, leadId?: string, campaignId?: string) {
    const tools = [];

    // Always include knowledge base tool
    tools.push(this.createKnowledgeBaseTool(agentId));

    // Include appointment and lead tools if we have lead context
    if (leadId && campaignId) {
      tools.push(this.createAppointmentTool(agentId, leadId, campaignId));
      tools.push(this.createLeadUpdateTool(leadId));
      tools.push(this.createAvailableSlotseTool(agentId));
    }

    return tools;
  }

  /**
   * Analyze email content and determine which tools to use
   */
  static analyzeEmailIntent(emailContent: string): {
    needsAppointment: boolean;
    needsKnowledgeBase: boolean;
    needsLeadUpdate: boolean;
    intent: string;
  } {
    const content = emailContent.toLowerCase();

    // Check for appointment-related keywords
    const appointmentKeywords = [
      'schedule', 'appointment', 'meeting', 'call', 'demo', 'consultation',
      'book', 'available', 'time', 'calendar', 'discuss', 'talk', 'when can we'
    ];

    const needsAppointment = appointmentKeywords.some(keyword =>
      content.includes(keyword)
    );

    // Check for information request keywords
    const infoKeywords = [
      'how', 'what', 'why', 'when', 'where', 'tell me', 'explain', 'information',
      'details', 'price', 'cost', 'feature', 'service', 'product', 'help'
    ];

    const needsKnowledgeBase = infoKeywords.some(keyword =>
      content.includes(keyword)
    );

    // Check for lead status update keywords
    const statusKeywords = [
      'interested', 'not interested', 'maybe', 'thinking', 'considering',
      'budget', 'timeline', 'decision', 'team', 'manager'
    ];

    const needsLeadUpdate = statusKeywords.some(keyword =>
      content.includes(keyword)
    );

    // Determine primary intent
    let intent = 'general';
    if (needsAppointment) intent = 'appointment';
    else if (needsKnowledgeBase) intent = 'information';
    else if (needsLeadUpdate) intent = 'status_update';

    return {
      needsAppointment,
      needsKnowledgeBase,
      needsLeadUpdate,
      intent,
    };
  }
}
