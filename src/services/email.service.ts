import nodemailer from 'nodemailer';
import { PrismaClient } from '@prisma/client';
import CryptoJS from 'crypto-js';
import { logger } from '@/lib/logger';
import { DnsTrackingService } from './dns-tracking.service';
import { TrackingService } from './tracking.service';
import { generateReplyTrackingAddress } from '@/lib/reply-tracking';

const prisma = new PrismaClient();

export class EmailService {
  private static async decryptPassword(encrypted: string, overridePassword?: string): Promise<string> {
    // If an override password is provided, use it instead of decrypting
    if (overridePassword) {
      logger.info('Using override password for SMTP authentication');
      return overridePassword;
    }

    try {
      // Log the encryption key (without revealing it fully)
      const keyLength = process.env.ENCRYPTION_KEY?.length || 0;
      logger.info(`Using encryption key with length: ${keyLength}`);
      logger.info(`First 3 chars of key: ${process.env.ENCRYPTION_KEY?.substring(0, 3) || ''}...`);
      logger.info(`Last 3 chars of key: ...${process.env.ENCRYPTION_KEY?.substring(keyLength - 3) || ''}`);

      if (!process.env.ENCRYPTION_KEY) {
        logger.error('ENCRYPTION_KEY is not set in environment variables');
        throw new Error('ENCRYPTION_KEY is not set');
      }

      // Try to decrypt
      const bytes = CryptoJS.AES.decrypt(encrypted, process.env.ENCRYPTION_KEY);
      const decrypted = bytes.toString(CryptoJS.enc.Utf8);

      // If decryption failed, the result will be an empty string
      if (!decrypted) {
        logger.error('Decryption resulted in empty string', {
          encryptedLength: encrypted.length,
          keyLength
        });
        throw new Error('Failed to decrypt password - empty result');
      }

      logger.info(`Successfully decrypted password (length: ${decrypted.length})`);
      return decrypted;
    } catch (error) {
      logger.error('Failed to decrypt password', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        encryptedLength: encrypted.length
      });
      throw new Error(`Failed to decrypt password: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static async createTransporter(emailAccountId: string, overridePassword?: string) {
    try {
      logger.info(`Creating transporter for email account: ${emailAccountId}`);

      const emailAccount = await prisma.emailAccount.findUnique({
        where: { id: emailAccountId }
      });

      if (!emailAccount) {
        logger.error(`Email account not found: ${emailAccountId}`);
        throw new Error('Email account not found');
      }

      logger.info(`Found email account: ${emailAccount.email} (provider: ${emailAccount.provider})`);
      logger.info(`SMTP settings: ${emailAccount.smtpHost}:${emailAccount.smtpPort}`);

      if (emailAccount.provider === 'gmail') {
        logger.info('Creating Gmail OAuth2 transporter');

        if (!process.env.GMAIL_CLIENT_ID || !process.env.GMAIL_CLIENT_SECRET) {
          logger.error('Gmail OAuth2 credentials are missing');
          throw new Error('Gmail OAuth2 credentials are missing');
        }

        if (!emailAccount.refreshToken || !emailAccount.accessToken) {
          logger.error('Gmail OAuth2 tokens are missing');
          throw new Error('Gmail OAuth2 tokens are missing');
        }

        return nodemailer.createTransport({
          service: 'gmail',
          auth: {
            type: 'OAuth2',
            user: emailAccount.email,
            clientId: process.env.GMAIL_CLIENT_ID,
            clientSecret: process.env.GMAIL_CLIENT_SECRET,
            refreshToken: emailAccount.refreshToken,
            accessToken: emailAccount.accessToken,
          }
        });
      }

      // Custom SMTP
      if (!emailAccount.smtpHost || !emailAccount.smtpPort || !emailAccount.smtpUsername ||
          (!emailAccount.smtpPassword && !overridePassword)) {
        logger.error('SMTP credentials are missing or incomplete', {
          hasHost: !!emailAccount.smtpHost,
          hasPort: !!emailAccount.smtpPort,
          hasUsername: !!emailAccount.smtpUsername,
          hasPassword: !!emailAccount.smtpPassword,
          hasOverridePassword: !!overridePassword
        });
        throw new Error('SMTP credentials are missing or incomplete');
      }

      // Get decrypted password
      let password;
      try {
        password = await this.decryptPassword(
          emailAccount.smtpPassword || '', // Pass empty string if null
          overridePassword
        );
      } catch (decryptError) {
        logger.error('Failed to decrypt password for transporter', {
          error: decryptError instanceof Error ? decryptError.message : 'Unknown error',
          emailAccountId
        });
        throw decryptError;
      }

      logger.info('Creating SMTP transporter');
      const transporter = nodemailer.createTransport({
        host: emailAccount.smtpHost,
        port: emailAccount.smtpPort,
        secure: emailAccount.smtpPort === 465,
        auth: {
          user: emailAccount.smtpUsername,
          pass: password
        },
        // Add debug options for troubleshooting in non-production environments
        debug: process.env.NODE_ENV !== 'production',
        logger: process.env.NODE_ENV !== 'production'
      });

      // Verify the connection
      try {
        logger.info('Verifying SMTP connection...');
        await transporter.verify();
        logger.info('SMTP connection verified successfully');
      } catch (verifyError) {
        logger.error('Failed to verify SMTP connection', {
          error: verifyError instanceof Error ? verifyError.message : 'Unknown error',
          host: emailAccount.smtpHost,
          port: emailAccount.smtpPort
        });
        // Don't throw here, just log the error and continue
      }

      return transporter;
    } catch (error) {
      logger.error('Failed to create transporter', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        emailAccountId
      });
      throw error;
    }
  }

  static async sendEmail({
    emailAccountId,
    to,
    subject,
    html,
    from,
    overridePassword,
    skipReputationCheck = false,
    maxRetries = 3,
    campaignId,
    headers = {}
  }: {
    emailAccountId: string,
    to: string,
    subject: string,
    html: string,
    from?: string,
    overridePassword?: string,
    skipReputationCheck?: boolean,
    maxRetries?: number,
    campaignId?: string,
    headers?: Record<string, any>
  }) {
    logger.info(`[CAMPAIGN_DEBUG] Sending email to ${to} using account ${emailAccountId}`, {
      subject,
      emailAccountId,
      to,
      campaignId,
      environment: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
      timestamp: new Date().toISOString()
    });

    // Check email account status first
    logger.info(`[CAMPAIGN_DEBUG] Finding email account: ${emailAccountId}`);
    const emailAccount = await prisma.emailAccount.findUnique({
      where: { id: emailAccountId }
    });

    if (!emailAccount) {
      logger.error(`[CAMPAIGN_DEBUG] Email account not found: ${emailAccountId}`, {
        emailAccountId,
        timestamp: new Date().toISOString()
      });
      throw new Error('Email account not found');
    }

    logger.info(`[CAMPAIGN_DEBUG] Found email account: ${emailAccount.email}`, {
      emailAccountId: emailAccount.id,
      email: emailAccount.email,
      status: emailAccount.status,
      provider: emailAccount.provider,
      smtpHost: emailAccount.smtpHost,
      smtpPort: emailAccount.smtpPort,
      hasSmtpUsername: !!emailAccount.smtpUsername,
      hasSmtpPassword: !!emailAccount.smtpPassword,
      reputationScore: emailAccount.reputationScore
    });

    logger.info(`Using email account: ${emailAccount.email} (status: ${emailAccount.status})`);

    // Check if account is suspended
    if (emailAccount.status === 'suspended' && !skipReputationCheck) {
      logger.warn(`Attempted to send email from suspended account ${emailAccount.email}`, {
        emailAccountId,
        to,
        subject
      });
      throw new Error('Email account is suspended due to poor reputation');
    }

    // Check reputation score if available
    if (emailAccount.reputationScore !== null && emailAccount.reputationScore < 40 && !skipReputationCheck) {
      logger.warn(`Attempted to send email from account with poor reputation score ${emailAccount.reputationScore}`, {
        emailAccountId,
        to,
        subject
      });
      throw new Error(`Email account has poor reputation score (${emailAccount.reputationScore})`);
    }

    // Implement retry logic
    let lastError: Error | null = null;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          logger.info(`[CAMPAIGN_DEBUG] Retry attempt ${attempt}/${maxRetries} for sending email to ${to}`, {
            attempt,
            maxRetries,
            to,
            emailAccountId,
            campaignId,
            timestamp: new Date().toISOString()
          });
          // Add a small delay between retries (exponential backoff)
          const delayMs = 1000 * Math.pow(2, attempt - 1);
          logger.info(`[CAMPAIGN_DEBUG] Waiting ${delayMs}ms before retry`);
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }

        // Create transporter
        const transporter = await this.createTransporter(emailAccountId, overridePassword);

        // Get the organization ID for this email account
        const user = await prisma.user.findUnique({
          where: { id: emailAccount.userId },
          include: { organizations: { include: { organization: true } } }
        });

        const organizationId = user?.organizations[0]?.organizationId;
        logger.info(`Found organization ID: ${organizationId || 'none'}`);

        // Get custom bounce domain if available
        let defaultHeaders = {};
        let returnPath = '';
        // Define trackingDomains at a higher scope so it's available for tracking
        let trackingDomains = {
          openDomain: undefined,
          clickDomain: undefined,
          bounceDomain: undefined,
          source: 'default' as 'custom' | 'sender' | 'default'
        };

        if (organizationId) {
          // Use the sender's email for better domain alignment
          const senderEmail = from || emailAccount.email;
          trackingDomains = await DnsTrackingService.getTrackingDomains(organizationId, senderEmail);

          if (trackingDomains.bounceDomain) {
            // Create a unique bounce address for this email
            const bounceAddress = `bounce+${emailAccountId.substring(0, 8)}@${trackingDomains.bounceDomain}`;
            defaultHeaders = {
              'Return-Path': bounceAddress,
              // Add DKIM-friendly headers
              'X-Entity-Ref-ID': `${campaignId || 'system'}-${Date.now()}`,
              'List-Unsubscribe': `<https://${trackingDomains.clickDomain}/unsubscribe?id=${emailAccountId.substring(0, 8)}>`
            };
            returnPath = bounceAddress;
            logger.info(`Using bounce address: ${bounceAddress}`);
          } else {
            logger.info('No bounce domain configured, using default headers');
          }
        }

        // Merge custom headers with default headers
        const mergedHeaders = { ...defaultHeaders, ...headers };
        logger.info(`Using email headers:`, { headers: Object.keys(mergedHeaders) });

        // Add tracking to the email if campaignId is provided
        let processedHtml = html;
        let replyTo = from || emailAccount.email;

        if (campaignId) {
          try {
            // Find the campaign lead
            const campaignLead = await prisma.campaignLead.findFirst({
              where: {
                campaignId,
                lead: {
                  email: to
                }
              }
            });

            if (campaignLead) {
              // Add tracking to the email
              processedHtml = await TrackingService.addTracking(
                html,
                campaignId,
                campaignLead.id,
                undefined, // Use default base URL
                {
                  openDomain: trackingDomains?.openDomain,
                  clickDomain: trackingDomains?.clickDomain,
                  bounceDomain: trackingDomains?.bounceDomain,
                  source: trackingDomains?.source
                }
              );

              // Use the same email address for reply-to to ensure replies land in the correct inbox
              // This ensures that when leads reply, their emails go to the same address that sent the campaign
              replyTo = from || emailAccount.email;

              // Add the reply-to to the headers
              mergedHeaders['Reply-To'] = replyTo;

              logger.info(`Using sender email as reply-to address: ${replyTo}`);
            } else {
              logger.warn(`Could not find campaign lead for ${to} in campaign ${campaignId}`);
            }
          } catch (trackingError) {
            logger.error('Error adding tracking to email', {
              error: trackingError instanceof Error ? trackingError.message : 'Unknown error',
              campaignId,
              to
            });
            // Continue with the original HTML if tracking fails
          }
        }

        // Send the email
        logger.info(`Sending email to ${to} with subject "${subject}"`);
        const result = await transporter.sendMail({
          from: from || emailAccount.email,
          to,
          subject,
          html: processedHtml,
          replyTo: replyTo,
          headers: mergedHeaders,
          ...(returnPath ? { returnPath } : {})
        });

        logger.info(`Email sent successfully to ${to}`, {
          messageId: result.messageId,
          response: result.response
        });

        // Note: We don't store sent campaign emails in the receivedEmail table
        // as they should not appear in the inbox. Only incoming emails and replies
        // should be stored there. Campaign emails are tracked separately through
        // the campaign system and step activities.
        logger.info(`Campaign email sent successfully with messageId: ${result.messageId}`);

        // Update last used timestamp
        await prisma.emailAccount.update({
          where: { id: emailAccountId },
          data: { lastUsed: new Date() }
        });

        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        logger.error(`[CAMPAIGN_DEBUG] Failed to send email (attempt ${attempt}/${maxRetries}):`, {
          emailAccountId,
          to,
          subject,
          campaignId,
          attempt,
          maxRetries,
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : 'No stack trace',
          environment: process.env.NODE_ENV,
          vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
          timestamp: new Date().toISOString()
        });

        // If this is the last attempt, we'll throw the error after the loop
        if (attempt === maxRetries) {
          logger.error(`[CAMPAIGN_DEBUG] All retry attempts exhausted for sending email to ${to}`);
          break;
        }

        // Check if we should retry based on the error
        const errorMessage = error instanceof Error ? error.message : String(error);
        const shouldRetry = (
          errorMessage.includes('ECONNREFUSED') ||
          errorMessage.includes('ETIMEDOUT') ||
          errorMessage.includes('ENOTFOUND') ||
          errorMessage.includes('connection closed') ||
          errorMessage.includes('421') || // SMTP temporary error
          errorMessage.includes('timeout') ||
          errorMessage.includes('network') ||
          errorMessage.includes('temporarily') ||
          errorMessage.includes('try again')
        );

        if (!shouldRetry) {
          logger.info(`[CAMPAIGN_DEBUG] Not retrying due to non-retryable error: ${errorMessage}`, {
            errorMessage,
            emailAccountId,
            to,
            campaignId
          });
          break;
        } else {
          logger.info(`[CAMPAIGN_DEBUG] Will retry due to retryable error: ${errorMessage}`, {
            errorMessage,
            emailAccountId,
            to,
            campaignId,
            nextAttempt: attempt + 1,
            maxRetries
          });
        }
      }
    }

    // If we got here, all retries failed
    logger.error(`[CAMPAIGN_DEBUG] All ${maxRetries} attempts to send email failed`, {
      emailAccountId,
      to,
      subject,
      campaignId,
      maxRetries,
      error: lastError?.message || 'Unknown error',
      stack: lastError?.stack || 'No stack trace',
      environment: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
      timestamp: new Date().toISOString(),
      smtpHost: emailAccount.smtpHost,
      smtpPort: emailAccount.smtpPort,
      provider: emailAccount.provider
    });

    // Log Redis connection info if available
    if (process.env.REDIS_URL || process.env.REDIS_HOST) {
      logger.info(`[CAMPAIGN_DEBUG] Redis connection info:`, {
        redisUrl: process.env.REDIS_URL ? 'configured' : 'not-configured',
        redisHost: process.env.REDIS_HOST ? 'configured' : 'not-configured',
        redisPort: process.env.REDIS_PORT || 'default',
        redisPassword: process.env.REDIS_PASSWORD ? 'configured' : 'not-configured'
      });
    }

    throw lastError || new Error('Failed to send email after multiple attempts');
  }

  /**
   * Send a system email using the system SMTP settings
   * This is used for sending notifications, not for campaigns
   */
  static async sendSystemEmail({
    to,
    subject,
    html,
    maxRetries = 3
  }: {
    to: string,
    subject: string,
    html: string,
    maxRetries?: number
  }) {
    logger.info(`Sending system email to ${to}`, { subject });

    // Check if system SMTP settings are configured
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
      logger.error('System SMTP settings are not configured');
      throw new Error('System SMTP settings are not configured');
    }

    // Implement retry logic
    let lastError: Error | null = null;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          logger.info(`Retry attempt ${attempt}/${maxRetries} for sending system email to ${to}`);
          // Add a small delay between retries (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt - 1)));
        }

        // Create a transporter using system SMTP settings
        logger.info('Creating system SMTP transporter');
        const transporter = nodemailer.createTransport({
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT || '587'),
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASSWORD,
          },
          // Add debug options for troubleshooting in non-production environments
          debug: process.env.NODE_ENV !== 'production',
          logger: process.env.NODE_ENV !== 'production'
        });

        // Verify the connection
        try {
          logger.info('Verifying system SMTP connection...');
          await transporter.verify();
          logger.info('System SMTP connection verified successfully');
        } catch (verifyError) {
          logger.error('Failed to verify system SMTP connection', {
            error: verifyError instanceof Error ? verifyError.message : 'Unknown error',
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT
          });
          // Don't throw here, just log the error and continue
        }

        // Send the email
        logger.info(`Sending system email to ${to} with subject "${subject}"`);
        const result = await transporter.sendMail({
          from: process.env.SMTP_FROM || '"Avian Email" <<EMAIL>>',
          to,
          subject,
          html,
        });

        logger.info(`System email sent successfully to ${to}`, {
          messageId: result.messageId,
          response: result.response
        });

        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        logger.error(`Failed to send system email (attempt ${attempt}/${maxRetries}):`, {
          to,
          subject,
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        });

        // If this is the last attempt, we'll throw the error after the loop
        if (attempt === maxRetries) {
          break;
        }

        // Check if we should retry based on the error
        const errorMessage = error instanceof Error ? error.message : String(error);
        const shouldRetry = (
          errorMessage.includes('ECONNREFUSED') ||
          errorMessage.includes('ETIMEDOUT') ||
          errorMessage.includes('ENOTFOUND') ||
          errorMessage.includes('connection closed') ||
          errorMessage.includes('421') // SMTP temporary error
        );

        if (!shouldRetry) {
          logger.info(`Not retrying due to non-retryable error: ${errorMessage}`);
          break;
        }
      }
    }

    // If we got here, all retries failed
    logger.error(`All ${maxRetries} attempts to send system email failed`, {
      to,
      subject
    });
    throw lastError || new Error('Failed to send system email after multiple attempts');
  }
}