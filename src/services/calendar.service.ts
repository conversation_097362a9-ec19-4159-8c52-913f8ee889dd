import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

export interface CalendarEvent {
  id?: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  attendeeEmail: string;
  attendeeName?: string;
  location?: string;
  meetingLink?: string;
}

export interface AvailableSlot {
  startTime: Date;
  endTime: Date;
  duration: number; // in minutes
}

export interface CalendarProvider {
  createEvent(event: CalendarEvent): Promise<{ id: string; meetingLink?: string }>;
  getAvailableSlots(date: Date, duration: number): Promise<AvailableSlot[]>;
  updateEvent(eventId: string, updates: Partial<CalendarEvent>): Promise<void>;
  cancelEvent(eventId: string): Promise<void>;
}

/**
 * Google Calendar Integration
 */
class GoogleCalendarProvider implements CalendarProvider {
  private accessToken: string;
  private calendarId: string;

  constructor(config: any) {
    this.accessToken = config.accessToken;
    this.calendarId = config.calendarId || 'primary';
  }

  async createEvent(event: CalendarEvent): Promise<{ id: string; meetingLink?: string }> {
    try {
      const response = await fetch(
        `https://www.googleapis.com/calendar/v3/calendars/${this.calendarId}/events`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            summary: event.title,
            description: event.description,
            start: {
              dateTime: event.startTime.toISOString(),
              timeZone: 'UTC',
            },
            end: {
              dateTime: event.endTime.toISOString(),
              timeZone: 'UTC',
            },
            attendees: [
              {
                email: event.attendeeEmail,
                displayName: event.attendeeName,
              },
            ],
            location: event.location,
            conferenceData: {
              createRequest: {
                requestId: `meet-${Date.now()}`,
                conferenceSolutionKey: {
                  type: 'hangoutsMeet',
                },
              },
            },
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Google Calendar API error: ${response.statusText}`);
      }

      const result = await response.json();
      return {
        id: result.id,
        meetingLink: result.conferenceData?.entryPoints?.[0]?.uri,
      };
    } catch (error) {
      logger.error('Error creating Google Calendar event', {
        error: error instanceof Error ? error.message : 'Unknown error',
        event,
      });
      throw error;
    }
  }

  async getAvailableSlots(date: Date, duration: number): Promise<AvailableSlot[]> {
    // Implementation for getting available slots from Google Calendar
    // This would involve checking busy times and returning free slots
    const slots: AvailableSlot[] = [];

    // For now, return some default business hours slots
    const businessStart = 9; // 9 AM
    const businessEnd = 17; // 5 PM

    for (let hour = businessStart; hour < businessEnd; hour++) {
      const startTime = new Date(date);
      startTime.setHours(hour, 0, 0, 0);

      const endTime = new Date(startTime);
      endTime.setMinutes(endTime.getMinutes() + duration);

      if (endTime.getHours() <= businessEnd) {
        slots.push({
          startTime,
          endTime,
          duration,
        });
      }
    }

    return slots;
  }

  async updateEvent(eventId: string, updates: Partial<CalendarEvent>): Promise<void> {
    // Implementation for updating Google Calendar event
    logger.info('Updating Google Calendar event', { eventId, updates });
  }

  async cancelEvent(eventId: string): Promise<void> {
    // Implementation for canceling Google Calendar event
    logger.info('Canceling Google Calendar event', { eventId });
  }
}

/**
 * Cal.com Integration
 */
class CalComProvider implements CalendarProvider {
  private apiKey: string;
  private baseUrl: string;

  constructor(config: any) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || 'https://api.cal.com/v1';
  }

  async createEvent(event: CalendarEvent): Promise<{ id: string; meetingLink?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/bookings`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: event.title,
          description: event.description,
          startTime: event.startTime.toISOString(),
          endTime: event.endTime.toISOString(),
          attendees: [
            {
              email: event.attendeeEmail,
              name: event.attendeeName,
            },
          ],
          location: event.location,
        }),
      });

      if (!response.ok) {
        throw new Error(`Cal.com API error: ${response.statusText}`);
      }

      const result = await response.json();
      return {
        id: result.id,
        meetingLink: result.meetingUrl,
      };
    } catch (error) {
      logger.error('Error creating Cal.com event', {
        error: error instanceof Error ? error.message : 'Unknown error',
        event,
      });
      throw error;
    }
  }

  async getAvailableSlots(date: Date, duration: number): Promise<AvailableSlot[]> {
    // Implementation for getting available slots from Cal.com
    const slots: AvailableSlot[] = [];

    // Default business hours implementation
    const businessStart = 9;
    const businessEnd = 17;

    for (let hour = businessStart; hour < businessEnd; hour++) {
      const startTime = new Date(date);
      startTime.setHours(hour, 0, 0, 0);

      const endTime = new Date(startTime);
      endTime.setMinutes(endTime.getMinutes() + duration);

      if (endTime.getHours() <= businessEnd) {
        slots.push({
          startTime,
          endTime,
          duration,
        });
      }
    }

    return slots;
  }

  async updateEvent(eventId: string, updates: Partial<CalendarEvent>): Promise<void> {
    logger.info('Updating Cal.com event', { eventId, updates });
  }

  async cancelEvent(eventId: string): Promise<void> {
    logger.info('Canceling Cal.com event', { eventId });
  }
}

/**
 * Main Calendar Service
 */
export class CalendarService {
  /**
   * Get calendar provider for an agent
   */
  static async getProvider(agentId: string): Promise<CalendarProvider | null> {
    try {
      const agent = await prisma.agent.findUnique({
        where: { id: agentId },
      });

      if (!agent?.calendarEnabled || !agent.calendarProvider || !agent.calendarConfig) {
        return null;
      }

      switch (agent.calendarProvider) {
        case 'google':
          return new GoogleCalendarProvider(agent.calendarConfig);
        case 'cal_com':
          return new CalComProvider(agent.calendarConfig);
        default:
          logger.warn('Unknown calendar provider', { provider: agent.calendarProvider });
          return null;
      }
    } catch (error) {
      logger.error('Error getting calendar provider', {
        agentId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }

  /**
   * Schedule an appointment
   */
  static async scheduleAppointment(
    agentId: string,
    leadId: string,
    campaignId: string,
    appointmentDetails: {
      title: string;
      description?: string;
      preferredDate?: Date;
      duration?: number;
      attendeeEmail: string;
      attendeeName?: string;
    }
  ): Promise<{ success: boolean; appointment?: any; error?: string }> {
    try {
      const provider = await this.getProvider(agentId);
      if (!provider) {
        return { success: false, error: 'Calendar provider not configured' };
      }

      const agent = await prisma.agent.findUnique({
        where: { id: agentId },
      });

      const duration = appointmentDetails.duration || agent?.defaultMeetingDuration || 30;

      // Find available slot
      const preferredDate = appointmentDetails.preferredDate || new Date();
      const availableSlots = await provider.getAvailableSlots(preferredDate, duration);

      if (availableSlots.length === 0) {
        return { success: false, error: 'No available time slots found' };
      }

      // Use the first available slot
      const slot = availableSlots[0];

      // Create calendar event
      const calendarEvent = await provider.createEvent({
        title: appointmentDetails.title,
        description: appointmentDetails.description,
        startTime: slot.startTime,
        endTime: slot.endTime,
        attendeeEmail: appointmentDetails.attendeeEmail,
        attendeeName: appointmentDetails.attendeeName,
      });

      // Store appointment in database
      const appointment = await prisma.appointment.create({
        data: {
          leadId,
          campaignId,
          agentId,
          title: appointmentDetails.title,
          description: appointmentDetails.description,
          startTime: slot.startTime,
          endTime: slot.endTime,
          status: 'scheduled',
          meetingLink: calendarEvent.meetingLink,
          metadata: {
            calendarEventId: calendarEvent.id,
            calendarProvider: agent?.calendarProvider,
          },
        },
      });

      logger.info('Appointment scheduled successfully', {
        appointmentId: appointment.id,
        agentId,
        leadId,
        startTime: slot.startTime,
      });

      return { success: true, appointment };
    } catch (error) {
      logger.error('Error scheduling appointment', {
        agentId,
        leadId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get available time slots for an agent
   */
  static async getAvailableSlots(
    agentId: string,
    date: Date,
    duration: number = 30
  ): Promise<AvailableSlot[]> {
    try {
      const provider = await this.getProvider(agentId);
      if (!provider) {
        return [];
      }

      return await provider.getAvailableSlots(date, duration);
    } catch (error) {
      logger.error('Error getting available slots', {
        agentId,
        date,
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return [];
    }
  }

  /**
   * Update lead information
   */
  static async updateLead(
    leadId: string,
    updates: {
      status?: string;
      notes?: string;
      customFields?: any;
      tags?: string[];
    }
  ): Promise<{ success: boolean; lead?: any; error?: string }> {
    try {
      const lead = await prisma.lead.update({
        where: { id: leadId },
        data: {
          status: updates.status,
          customFields: updates.customFields ? {
            ...(updates.customFields as any),
          } : undefined,
          tags: updates.tags,
        },
      });

      logger.info('Lead updated successfully', {
        leadId,
        updates,
      });

      return { success: true, lead };
    } catch (error) {
      logger.error('Error updating lead', {
        leadId,
        updates,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Parse appointment request from email content
   */
  static parseAppointmentRequest(emailContent: string): {
    isAppointmentRequest: boolean;
    preferredDate?: Date;
    duration?: number;
    purpose?: string;
  } {
    const content = emailContent.toLowerCase();

    // Keywords that indicate appointment request
    const appointmentKeywords = [
      'schedule', 'appointment', 'meeting', 'call', 'demo', 'consultation',
      'book', 'available', 'time', 'calendar', 'discuss', 'talk'
    ];

    const isAppointmentRequest = appointmentKeywords.some(keyword =>
      content.includes(keyword)
    );

    if (!isAppointmentRequest) {
      return { isAppointmentRequest: false };
    }

    // Try to extract date/time information
    let preferredDate: Date | undefined;
    let duration: number | undefined;
    let purpose: string | undefined;

    // Simple date parsing (can be enhanced with more sophisticated NLP)
    const datePatterns = [
      /tomorrow/i,
      /next week/i,
      /monday|tuesday|wednesday|thursday|friday|saturday|sunday/i,
      /\d{1,2}\/\d{1,2}\/\d{4}/,
      /\d{1,2}-\d{1,2}-\d{4}/,
    ];

    for (const pattern of datePatterns) {
      if (pattern.test(content)) {
        // For now, default to next business day
        preferredDate = new Date();
        preferredDate.setDate(preferredDate.getDate() + 1);
        break;
      }
    }

    // Extract duration
    const durationMatch = content.match(/(\d+)\s*(minute|hour)/i);
    if (durationMatch) {
      const value = parseInt(durationMatch[1]);
      duration = durationMatch[2].toLowerCase().includes('hour') ? value * 60 : value;
    }

    // Extract purpose
    const purposePatterns = [
      /demo/i,
      /consultation/i,
      /discussion/i,
      /presentation/i,
      /review/i,
    ];

    for (const pattern of purposePatterns) {
      if (pattern.test(content)) {
        purpose = content.match(pattern)?.[0];
        break;
      }
    }

    return {
      isAppointmentRequest: true,
      preferredDate,
      duration,
      purpose,
    };
  }
}
