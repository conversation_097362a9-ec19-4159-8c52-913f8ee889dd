import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { initLangChain } from '@/lib/langchain';
import * as cheerio from 'cheerio';
import * as puppeteer from 'puppeteer';
import { PromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { RunnableSequence } from '@langchain/core/runnables';
import { KnowledgeBaseService } from './knowledge-base.service';
import { AIProviderService } from './ai-provider.service';

// Initialize LangChain tracing
initLangChain();

/**
 * Service for enhancing AI agents with web scraping, data processing, and personalized email generation
 */
export class AgentEnhancementService {
  /**
   * Initialize the LLM model using the AI provider service with R1 optimization
   */
  private static getLLM(useCase: 'email' | 'chat' | 'analysis' = 'email') {
    try {
      const providerInfo = AIProviderService.getProviderInfo();

      logger.info('Initializing optimized LLM for agent enhancement', {
        provider: providerInfo.provider,
        chatModel: providerInfo.chatModel,
        baseUrl: providerInfo.baseUrl,
        useCase,
        optimization: 'R1 thinking control enabled',
      });

      return AIProviderService.getOptimizedChatModel(useCase);
    } catch (error) {
      logger.error('Error initializing optimized LLM for agent enhancement', {
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: AIProviderService.getProvider(),
        useCase,
      });
      throw error;
    }
  }
  /**
   * Scrape data from a website URL
   * @param url The URL to scrape
   * @returns The scraped content
   */
  static async scrapeWebsite(url: string): Promise<string> {
    try {
      logger.info(`Scraping website: ${url}`);

      // Use Puppeteer for scraping
      return this.scrapeWithPuppeteer(url);
    } catch (error) {
      logger.error(`Error scraping website ${url}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return '';
    }
  }

  /**
   * Scrape a website using Puppeteer (for JavaScript-heavy sites)
   * @param url The URL to scrape
   * @returns The scraped content
   */
  private static async scrapeWithPuppeteer(url: string): Promise<string> {
    let browser: puppeteer.Browser | undefined;
    try {
      logger.info(`Scraping website with Puppeteer: ${url}`);

      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

      // Wait for content to load
      await page.waitForSelector('body', { timeout: 5000 });

      const content = await page.content();
      const $ = cheerio.load(content);

      // Remove scripts, styles, and other non-content elements
      $('script, style, meta, link, noscript').remove();

      return $('body').text();
    } catch (error) {
      logger.error(`Error scraping website with Puppeteer ${url}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return '';
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Scrape a LinkedIn profile
   * @param url LinkedIn profile URL
   * @returns Structured profile data
   */
  static async scrapeLinkedInProfile(url: string): Promise<any> {
    try {
      logger.info(`Scraping LinkedIn profile: ${url}`);

      const content = await this.scrapeWithPuppeteer(url);

      // Use LLM to extract structured information from the LinkedIn profile
      const llm = this.getLLM();

      // Create a prompt content using array join to avoid template string issues
      const promptContent = [
        "Extract structured information from the following LinkedIn profile content.",
        "IMPORTANT: Return ONLY a valid JSON object (no markdown formatting, no code blocks, no explanations).",
        "The JSON object must have exactly these fields:",
        "- name: The person's full name",
        "- title: Their current job title",
        "- company: Their current company",
        "- location: Their location",
        "- about: A summary of their about section",
        "- experience: An array of their work experiences (company, title, duration)",
        "- education: An array of their education entries",
        "- skills: An array of their listed skills",
        "",
        "LinkedIn content:",
        content,
        "",
        "Return only the JSON object:"
      ].join("\n");

      // Create a simple prompt template
      const prompt = PromptTemplate.fromTemplate("{content}");

      const chain = RunnableSequence.from([
        prompt,
        llm,
        new StringOutputParser(),
      ]);

      const result = await chain.invoke(
        { content: promptContent },
        { runName: "linkedin-profile-extraction" }
      );

      try {
        // Clean the result to remove markdown formatting
        const cleanedResult = this.cleanMarkdownJson(result);
        return JSON.parse(cleanedResult);
      } catch (parseError) {
        logger.error('Error parsing LinkedIn profile data', {
          error: parseError instanceof Error ? parseError.message : 'Unknown error',
          result: typeof result === 'string' ? result.substring(0, 200) + '...' : 'Not a string',
        });
        return {
          raw: content,
          name: "Extracted from content",
          title: "Extracted from content",
          company: "Extracted from content",
          location: "Extracted from content",
          about: "Extracted from content",
          experience: [],
          education: [],
          skills: []
        };
      }
    } catch (error) {
      logger.error(`Error scraping LinkedIn profile ${url}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }

  /**
   * Generate a personalized email based on lead data, scraped information, and knowledge base
   * @param leadId The lead ID
   * @param agentId The agent ID
   * @param scrapedData Data scraped from websites/LinkedIn
   * @returns Generated email content
   */
  static async generatePersonalizedEmail(
    leadId: string,
    agentId: string,
    scrapedData: any
  ): Promise<{ subject: string; text: string; html: string }> {
    try {
      logger.info(`Generating personalized email for lead ${leadId} with agent ${agentId}`);

      // Get the lead data
      const lead = await prisma.lead.findUnique({
        where: { id: leadId },
        include: {
          campaigns: true,
        },
      });

      if (!lead) {
        throw new Error(`Lead not found: ${leadId}`);
      }

      // Get the agent with knowledge bases
      const agent = await prisma.agent.findUnique({
        where: { id: agentId },
        include: {
          knowledgeBases: {
            include: {
              knowledgeBase: true,
            },
          },
        },
      });

      if (!agent) {
        throw new Error(`Agent not found: ${agentId}`);
      }

      // Get agent configuration
      const config = agent.config as any || {};
      const emailTemplate = config.emailTemplate || '';
      const tone = config.tone || 'professional';

      // Extract knowledge base content using vector search
      let knowledgeBaseContent = '';

      // Get the organization ID from the agent
      const organizationId = agent.organizationId;

      try {
        // Use vector search to find relevant content from the organization's knowledge base
        const searchQuery = `${lead.company || ''} ${lead.firstName || ''} ${lead.lastName || ''} business services IT software development`.trim();

        logger.info(`Searching knowledge base for organization ${organizationId}`, {
          agentId,
          searchQuery,
          leadId,
        });

        const searchResults = await KnowledgeBaseService.queryKnowledgeBase(
          organizationId,
          searchQuery,
          5 // Get top 5 relevant chunks
        );

        if (searchResults.length > 0) {
          knowledgeBaseContent = searchResults
            .map((result: any) => result.content)
            .join('\n\n');
          logger.info(`Found ${searchResults.length} relevant knowledge base chunks for agent ${agentId}`, {
            contentLength: knowledgeBaseContent.length,
            firstChunkPreview: searchResults[0]?.content?.substring(0, 100) + '...',
            searchQuery,
            organizationId,
          });
        } else {
          logger.warn(`No relevant content found in knowledge base for agent ${agentId}`, {
            organizationId,
            searchQuery,
            agentId,
          });

          // Add a default knowledge base content if none found
          knowledgeBaseContent = `We are a professional service provider focused on delivering high-quality solutions to help businesses grow and succeed. Our team specializes in innovative approaches to meet our clients' unique needs.`;

          logger.info(`Using default knowledge base content for agent ${agentId}`);
        }
      } catch (vectorError) {
        logger.error(`Vector search failed for agent ${agentId}`, {
          error: vectorError instanceof Error ? vectorError.message : 'Unknown error',
          organizationId,
          agentId,
          leadId,
        });

        // Add a default knowledge base content on error
        knowledgeBaseContent = `We are a professional service provider focused on delivering high-quality solutions to help businesses grow and succeed. Our team specializes in innovative approaches to meet our clients' unique needs.`;

        logger.info(`Using default knowledge base content due to error for agent ${agentId}`);
      }

      // Use optimized LLM for email generation (R1 optimization)
      const llm = this.getLLM('email');

      // Create a safe template string with proper escaping
      const leadName = lead.firstName ? `${lead.firstName} ${lead.lastName || ''}` : '';
      const leadEmail = lead.email || '';
      const leadCompany = lead.company || 'Unknown';

      try {
        // Get the system prompt or use a default one
        const systemPrompt = agent.systemPrompt || `You are an AI email assistant that writes personalized, engaging emails for our company.
Your task is to create emails that are:
1. Highly personalized based on the recipient's information
2. Engaging and likely to get a response
3. Professional but conversational in tone
4. Concise (3-5 short paragraphs)
5. Focused on providing value to the recipient by explaining OUR services
6. Ending with a clear call to action

IMPORTANT RULES:
- DO NOT make assumptions about the recipient's industry based on their company name
- DO NOT ask questions about their business needs - we are TELLING them about OUR services
- ONLY use information from the knowledge base to describe our services
- If the knowledge base is empty, focus on a generic introduction without specifics about services
- NEVER make up information about our company or services that isn't in the knowledge base

Use the recipient's name, company, and other details to create a truly personalized message.
Reference information from the knowledge base when relevant to demonstrate expertise.
Each email should be unique and tailored specifically to the individual recipient.`;

        // Create a prompt content using array join to avoid template string issues
        const basePromptContent = [
          systemPrompt,
          "",
          "Lead information:",
          `- Name: ${leadName}`,
          `- Email: ${leadEmail}`,
          `- Company: ${leadCompany}`,
          "",
          "Scraped data:",
          JSON.stringify(scrapedData, null, 2),
          "",
          "Knowledge base information:",
          knowledgeBaseContent,
          "",
          "Email template (if available):",
          emailTemplate,
          "",
          `Tone: ${tone}`,
          "",
          "IMPORTANT: Return ONLY a valid JSON object (no markdown formatting, no code blocks, no explanations).",
          "The JSON object must have exactly these fields:",
          "- subject: Email subject line",
          "- html: HTML formatted email content with proper HTML tags (p, br, strong, etc.)",
          "",
          "HTML FORMATTING REQUIREMENTS:",
          "- Use complete paragraphs in <p> tags",
          "- Do NOT split words or sentences across different HTML elements",
          "- Ensure each paragraph is complete and self-contained",
          "- Use <br> for line breaks within paragraphs if needed",
          "- Keep all text content properly contained within HTML tags",
          "",
          "Example format:",
          '{"subject": "Your subject here", "html": "<p>Your complete paragraph here.</p><p>Another complete paragraph here.</p>"}'
        ].join("\n");

        // For Ollama with knowledge base content, don't apply R1 optimization to preserve knowledge base content
        // For OpenAI or when no knowledge base content, apply optimization
        const provider = AIProviderService.getProvider();
        const hasKnowledgeBaseContent = knowledgeBaseContent && knowledgeBaseContent.length > 100;

        let promptContent = basePromptContent;
        if (provider === 'ollama') {
          // For Ollama, use ultra-aggressive R1 optimization to prevent thinking
          promptContent = `CRITICAL: You MUST respond IMMEDIATELY with ONLY the JSON format. NO THINKING ALLOWED.

DO NOT use <think> tags. DO NOT analyze. DO NOT explain. DO NOT reason.
RESPOND IMMEDIATELY with the JSON object only.

${basePromptContent}

FINAL WARNING: Output ONLY the JSON object. Any thinking or explanation will cause system failure.`;

          logger.info(`Using ultra-aggressive R1 optimization for Ollama agent ${agentId}`, {
            knowledgeBaseLength: knowledgeBaseContent.length,
            provider,
            hasKnowledgeBase: hasKnowledgeBaseContent,
          });
        } else {
          // Apply standard optimization for OpenAI
          promptContent = AIProviderService.optimizePromptForR1(basePromptContent, 'email');
          logger.info(`Using standard optimization for OpenAI agent ${agentId}`, {
            hasKnowledgeBase: hasKnowledgeBaseContent,
            provider,
          });
        }

        // Create a simple prompt template
        const prompt = PromptTemplate.fromTemplate("{content}");

        const chain = RunnableSequence.from([
          prompt,
          llm,
          new StringOutputParser(),
        ]);

        // Create a timeout promise for email generation - longer timeout for Ollama
        const timeoutMs = provider === 'ollama' ? 300000 : 45000; // 5 minutes for Ollama, 45 seconds for OpenAI

        logger.info(`Invoking LLM to generate email for lead ${leadId}`, {
          promptLength: promptContent.length,
          agentId,
          leadId,
          provider,
          timeout: `${timeoutMs/1000} seconds`,
          optimizationType: provider === 'ollama' ? 'ultra-aggressive-r1' : 'standard',
        });

        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`Email generation timeout after ${timeoutMs/1000} seconds for lead ${leadId}`));
          }, timeoutMs);
        });

        // Race between the LLM response and timeout
        const result = await Promise.race([
          chain.invoke(
            { content: promptContent },
            {
              runName: "personalized-email-generation",
              metadata: {
                leadId,
                agentId,
                leadName,
                leadCompany
              }
            }
          ),
          timeoutPromise
        ]);

        logger.info(`LLM response received for lead ${leadId}`, {
          responseLength: typeof result === 'string' ? result.length : 'Not a string',
          agentId,
        });

        try {
          // Clean the result to remove markdown formatting
          const cleanedResult = this.cleanMarkdownJson(result);
          logger.info('Cleaned AI response', {
            originalLength: result.length,
            cleanedLength: cleanedResult.length,
            originalStart: result.substring(0, 50),
            cleanedStart: cleanedResult.substring(0, 50)
          });
          const parsedResult = JSON.parse(cleanedResult);

          // Ensure we have the required fields
          if (!parsedResult.subject || !parsedResult.html) {
            throw new Error('AI response missing required fields (subject or html)');
          }

          // Generate text version from HTML if not provided (for backward compatibility)
          if (!parsedResult.text) {
            parsedResult.text = this.htmlToText(parsedResult.html);
          }

          logger.info(`Successfully generated email for lead ${leadId}`, {
            hasSubject: !!parsedResult.subject,
            hasHtml: !!parsedResult.html,
            hasText: !!parsedResult.text,
            htmlLength: parsedResult.html?.length || 0,
            textLength: parsedResult.text?.length || 0
          });
          return parsedResult;
        } catch (parseError) {
          logger.error('Error parsing generated email', {
            error: parseError instanceof Error ? parseError.message : 'Unknown error',
            result: typeof result === 'string' ? result.substring(0, 200) + '...' : 'Not a string',
            cleanedResult: typeof result === 'string' ? this.cleanMarkdownJson(result).substring(0, 200) + '...' : 'Not a string',
            timestamp: new Date().toISOString()
          });

          // Return a fallback email if parsing fails
          return this.generateFallbackEmail(lead, scrapedData);
        }
      } catch (llmError) {
        logger.error(`Error invoking LLM for lead ${leadId}`, {
          error: llmError instanceof Error ? llmError.message : 'Unknown error',
          leadId,
          agentId
        });

        // Return a fallback email if LLM fails
        return this.generateFallbackEmail(lead, scrapedData);
      }
    } catch (error) {
      logger.error(`Error generating personalized email`, {
        leadId,
        agentId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      // Create a very basic fallback email for any other errors
      const fallbackHtml = `<p>Hello,</p><p>I wanted to reach out and connect with you.</p><p>Best regards,<br>AI Agent</p>`;
      return {
        subject: `Following up`,
        text: this.htmlToText(fallbackHtml),
        html: fallbackHtml
      };
    }
  }

  /**
   * Convert HTML to plain text by removing HTML tags
   */
  private static htmlToText(html: string): string {
    if (!html || typeof html !== 'string') {
      return '';
    }

    return html
      // Replace <br> and <br/> with newlines
      .replace(/<br\s*\/?>/gi, '\n')
      // Replace </p> with double newlines for paragraph breaks
      .replace(/<\/p>/gi, '\n\n')
      // Replace <p> with nothing (paragraph start)
      .replace(/<p[^>]*>/gi, '')
      // Remove all other HTML tags
      .replace(/<[^>]*>/g, '')
      // Decode HTML entities
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      // Clean up extra whitespace and newlines
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Replace multiple newlines with double newlines
      .replace(/^\s+|\s+$/g, '') // Trim start and end
      .trim();
  }

  /**
   * Clean markdown-wrapped JSON responses from AI models
   */
  private static cleanMarkdownJson(text: string): string {
    if (typeof text !== 'string') {
      return text;
    }

    // Remove markdown code block formatting
    let cleaned = text.trim();

    // Remove ```json at the beginning
    cleaned = cleaned.replace(/^```json\s*/i, '');

    // Remove ``` at the end
    cleaned = cleaned.replace(/\s*```\s*$/, '');

    // Remove any remaining ``` markers
    cleaned = cleaned.replace(/```/g, '');

    // Trim whitespace
    cleaned = cleaned.trim();

    return cleaned;
  }

  /**
   * Generate a fallback email when AI generation fails
   * @param lead The lead data
   * @param scrapedData Data scraped from websites/LinkedIn
   * @returns Fallback email content
   */
  private static generateFallbackEmail(
    lead: any,
    scrapedData: any
  ): { subject: string; text: string; html: string } {
    const subject = `Following up with ${lead.firstName || lead.email}`;

    // Use scraped data if available
    let companyInfo = lead.company || 'your company';
    if (scrapedData && scrapedData.linkedInProfile && scrapedData.linkedInProfile.company) {
      companyInfo = scrapedData.linkedInProfile.company;
    }

    // Generate HTML first (primary format)
    const html = `<p>Hello ${lead.firstName || ''},</p><p>I noticed you work at ${companyInfo} and wanted to connect.</p><p>Best regards,<br>AI Agent</p>`;

    // Generate text from HTML
    const text = this.htmlToText(html);

    return { subject, text, html };
  }
}
