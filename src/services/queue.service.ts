import { Queue, Worker } from 'bullmq';
import { prisma } from '@/lib/prisma';
import { EmailService } from './email.service';
import { UsageService } from './usage.service';
import { DnsTrackingService } from './dns-tracking.service';
import { CampaignService } from './campaign.service';
import { logger } from '@/lib/logger';
import { addTrackingToEmail } from '@/lib/email-tracking';
import { addTrackingToEmail as addSimplifiedTracking } from '@/lib/simplified-tracking';
import { addTrackingToEmail as addOptimizedTracking } from '@/lib/optimized-tracking';
import { runWorkerThread } from '@/lib/worker-thread';
import redis from '@/lib/redis';

interface EmailJob {
  campaignId: string;
  recipientId: string;
  to: string;
  subject: string;
  html: string;
  emailAccountId: string;
}

// Parse Redis URL to get connection options
const redisUrl = process.env.REDIS_URL ? new URL(process.env.REDIS_URL) : null;

// Create connection options for BullMQ with optimized pooling
export const getRedisConnection = () => {
  if (redisUrl) {
    // If using a URL, check if it is an upstash/cloud URL and set TLS accordingly
    const isCloud = redisUrl.hostname.includes('upstash') || redisUrl.protocol === 'rediss:';
    return {
      host: redisUrl.hostname,
      port: parseInt(redisUrl.port),
      username: redisUrl.username || undefined,
      password: redisUrl.password || undefined,
      tls: isCloud ? {} : undefined,
      maxRetriesPerRequest: null,
      enableReadyCheck: false,
      enableOfflineQueue: true,
      connectTimeout: 10000,
      disconnectTimeout: 2000,
      keepAlive: 30000,
      retryStrategy: (times: number) => Math.min(Math.exp(times), 10000)
    };
  } else {
    // Fallback to the new Redis server (no TLS for local/portainer Redis)
    return {
      host: '*************',
      port: 32769,
      maxRetriesPerRequest: null,
      enableReadyCheck: false,
      enableOfflineQueue: true,
      connectTimeout: 5000,
      keepAlive: 30000
    };
  }
};

// Only create queue in production or when explicitly enabled
const shouldCreateQueue = process.env.NODE_ENV === 'production' ||
                         process.env.ENABLE_QUEUE_WORKER === 'true' ||
                         process.env.VERCEL_ENV === 'production';

export const emailQueue = shouldCreateQueue ? new Queue<EmailJob>('email-queue', {
  connection: getRedisConnection()
}) : null;

// Only set up Redis event handlers if queue is enabled
if (shouldCreateQueue) {
  // Log Redis connection status with detailed information
  redis.on('error', (error) => {
    logger.error('Redis connection error in queue service:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      redisUrl: process.env.REDIS_URL ? 'configured' : 'not-configured',
      redisHost: process.env.REDIS_HOST || 'not-configured',
      redisPort: process.env.REDIS_PORT || 'not-configured',
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    });
  });

  redis.on('connect', () => {
    logger.info('Redis connected successfully in queue service', {
      redisUrl: process.env.REDIS_URL ? 'configured' : 'not-configured',
      redisHost: process.env.REDIS_HOST || 'not-configured',
      redisPort: process.env.REDIS_PORT || 'not-configured',
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    });
  });

  // Add additional connection event handlers
  redis.on('reconnecting', (timeToReconnect) => {
    logger.warn('Redis reconnecting in queue service', {
      timeToReconnect,
      timestamp: new Date().toISOString()
    });
  });

  redis.on('close', () => {
    logger.warn('Redis connection closed in queue service', {
      timestamp: new Date().toISOString()
    });
  });

  redis.on('end', () => {
    logger.warn('Redis connection ended in queue service', {
      timestamp: new Date().toISOString()
    });
  });
} else {
  logger.info('[CAMPAIGN_DEBUG] Queue worker disabled in development mode', {
    environment: process.env.NODE_ENV,
    enableQueueWorker: process.env.ENABLE_QUEUE_WORKER,
    vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
    timestamp: new Date().toISOString()
  });
}

/**
 * Update the campaign status based on the progress of email sending
 * This function is called after each email is sent or fails
 */
async function updateCampaignStatus(campaignId: string) {
  try {
    // Get the campaign with all leads and activities
    const campaign = await prisma.campaign.findUnique({
      where: { id: campaignId },
      include: {
        leads: {
          include: {
            stepActivities: true,
          },
        },
        steps: {
          orderBy: {
            position: 'asc',
          },
        },
      },
    });

    if (!campaign) {
      logger.warn(`Campaign ${campaignId} not found when updating status`);
      return;
    }

    // If the campaign is already completed, don't update
    if (campaign.status === 'completed') {
      return;
    }

    // Check if all leads have been processed for the initial step
    const initialStep = campaign.steps[0];
    if (!initialStep) {
      logger.warn(`Campaign ${campaignId} has no steps`);
      return;
    }

    // Count leads with different statuses
    let totalLeads = campaign.leads.length;
    let sentCount = 0;
    let openedCount = 0;
    let clickedCount = 0;
    let repliedCount = 0;
    let pendingCount = 0;
    let errorCount = 0;

    for (const lead of campaign.leads) {
      // Find the latest activity for this lead
      const activities = lead.stepActivities.filter(a => a.stepId === initialStep.id);
      if (activities.length === 0) {
        continue;
      }

      // Sort by createdAt descending to get the latest status
      activities.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      const latestActivity = activities[0];

      // Check if there's any sent activity for this lead
      const hasSentActivity = activities.some(a => a.type === 'email_sent' || a.status === 'sent');
      if (hasSentActivity) {
        sentCount++;
      }

      // Now check the latest status
      switch (latestActivity.status) {
        case 'opened':
          openedCount++;
          break;
        case 'clicked':
          clickedCount++;
          break;
        case 'replied':
          repliedCount++;
          break;
        case 'pending':
          pendingCount++;
          break;
        case 'failed':
        case 'error':
          errorCount++;
          break;
      }
    }

    // Update campaign metrics
    await prisma.campaign.update({
      where: { id: campaignId },
      data: {
        sentCount,
        openedCount,
        clickedCount,
        repliedCount,
        pendingCount,
        errorCount,
      },
    });

    // Determine the new status based on the current state
    let newStatus = campaign.status;

    // If all leads have been processed (no pending), determine final status
    if (pendingCount === 0 && totalLeads > 0) {
      // If there were only errors and no successful sends, mark as failed
      if (errorCount > 0 && sentCount === 0) {
        newStatus = 'failed';

        await prisma.campaign.update({
          where: { id: campaignId },
          data: {
            status: newStatus,
            endDate: new Date(),
            errorMessage: 'Campaign failed: All email sends resulted in errors',
          },
        });

        // Create a notification about the failed campaign
        await prisma.notification.create({
          data: {
            userId: campaign.userId,
            organizationId: campaign.organizationId,
            title: 'Campaign Failed',
            message: `Campaign "${campaign.name}" has failed because all email sends resulted in errors. Check the campaign details for more information.`,
            type: 'error',
            isRead: false,
          },
        });

        logger.error(`Campaign ${campaignId} failed - all sends resulted in errors`);
      }
      // If there were some successful sends, mark as completed with warning
      else if (errorCount > 0 && sentCount > 0) {
        newStatus = 'completed_with_errors';

        await prisma.campaign.update({
          where: { id: campaignId },
          data: {
            status: newStatus,
            endDate: new Date(),
            errorMessage: `Campaign completed with ${errorCount} errors out of ${totalLeads} total emails`,
          },
        });

        // Create a notification about the partial success
        await prisma.notification.create({
          data: {
            userId: campaign.userId,
            organizationId: campaign.organizationId,
            title: 'Campaign Completed with Errors',
            message: `Campaign "${campaign.name}" completed but ${errorCount} out of ${totalLeads} emails failed to send. Check the campaign details for more information.`,
            type: 'warning',
            isRead: false,
          },
        });

        logger.warn(`Campaign ${campaignId} completed with ${errorCount} errors out of ${totalLeads} total emails`);
      }
      // If all sends were successful, mark as completed
      else if (sentCount > 0 && errorCount === 0) {
        newStatus = 'completed';

        await prisma.campaign.update({
          where: { id: campaignId },
          data: {
            status: newStatus,
            endDate: new Date(),
            errorMessage: null, // Clear any previous error messages
          },
        });

        logger.info(`Campaign ${campaignId} completed successfully`);
      }
    }
    // If some emails have been sent but not all, and the campaign is active, mark as in_progress
    else if (sentCount > 0 && pendingCount > 0 && campaign.status === 'active') {
      newStatus = 'in_progress';

      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          status: newStatus,
        },
      });

      logger.info(`Campaign ${campaignId} in progress`);
    }

    // Return the updated status for real-time updates
    return {
      campaignId,
      status: newStatus,
      metrics: {
        totalLeads,
        sentCount,
        openedCount,
        clickedCount,
        repliedCount,
        pendingCount,
        errorCount,
        progress: totalLeads > 0 ? Math.round((sentCount / totalLeads) * 100) : 0
      }
    };
  } catch (error) {
    logger.error(`Error updating campaign status for ${campaignId}`, {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return null;
  }
}

// Only create worker in production or when explicitly enabled
const shouldCreateWorker = process.env.NODE_ENV === 'production' ||
                          process.env.ENABLE_QUEUE_WORKER === 'true' ||
                          process.env.VERCEL_ENV === 'production';

let worker: Worker<EmailJob> | null = null;

if (shouldCreateWorker) {
  worker = new Worker<EmailJob>(
    'email-queue',
    async (job) => {
    const { campaignId, recipientId, to, subject, html, emailAccountId } = job.data;

    logger.info(`[CAMPAIGN_DEBUG] Processing email job ${job.id} for campaign ${campaignId}`, {
      jobId: job.id,
      campaignId,
      recipientId,
      to,
      subject: subject.substring(0, 30) + (subject.length > 30 ? '...' : ''),
      emailAccountId,
      environment: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
      timestamp: new Date().toISOString()
    });

    try {
      // Check usage limits before sending
      logger.info(`[CAMPAIGN_DEBUG] Finding email account: ${emailAccountId}`);
      const emailAccount = await prisma.emailAccount.findUnique({
        where: { id: emailAccountId },
        include: { user: true },
      });

      if (!emailAccount) {
        logger.error(`[CAMPAIGN_DEBUG] Email account not found: ${emailAccountId}`);
        throw new Error('Email account not found');
      }

      logger.info(`[CAMPAIGN_DEBUG] Using email account: ${emailAccount.email}`, {
        emailAccountId: emailAccount.id,
        email: emailAccount.email,
        status: emailAccount.status,
        provider: emailAccount.provider,
        smtpHost: emailAccount.smtpHost,
        smtpPort: emailAccount.smtpPort,
        hasSmtpUsername: !!emailAccount.smtpUsername,
        hasSmtpPassword: !!emailAccount.smtpPassword,
        reputationScore: emailAccount.reputationScore
      });

      // Check if account is verified and active
      if (emailAccount.status !== 'active' && emailAccount.status !== 'verified') {
        logger.error(`[CAMPAIGN_DEBUG] Email account is not active or verified: ${emailAccount.email} (status: ${emailAccount.status})`);
        throw new Error(`Email account cannot be used for sending: ${emailAccount.status}`);
      }

      // Get the user's organization
      const organizationMember = await prisma.organizationMember.findFirst({
        where: { userId: emailAccount.userId },
      });

      if (!organizationMember) {
        logger.error(`User has no organization: ${emailAccount.userId}`);
        throw new Error('User has no organization');
      }

      // Store the organizationId for later use
      const organizationId = organizationMember.organizationId;
      logger.info(`Found organization ID: ${organizationId}`);

      // Check usage limits
      const withinLimits = await UsageService.checkUsageLimits(organizationId);
      if (!withinLimits) {
        logger.error(`Usage limits exceeded for organization: ${organizationId}`);
        throw new Error('Usage limits exceeded');
      }

      // Get custom tracking domains if available
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      logger.info(`Using base URL for tracking: ${baseUrl}`);

      // Get tracking domains using the sender's email domain as a fallback
      const trackingDomains = await DnsTrackingService.getTrackingDomains(organizationId, emailAccount.email);
      logger.info('Tracking domains for email sending:', {
        openDomain: trackingDomains.openDomain,
        clickDomain: trackingDomains.clickDomain,
        bounceDomain: trackingDomains.bounceDomain,
        senderEmail: emailAccount.email
      });

      // Add tracking to the email using a worker thread for better performance
      logger.info('[CAMPAIGN_DEBUG] Adding tracking to email using worker thread...');

      // Use a worker thread for CPU-intensive HTML processing
      let trackedHtml;
      try {
        const workerResult = await runWorkerThread<any, {html?: string, error?: string, stack?: string}>('src/workers/email-tracking.worker.js', {
          html, // Use the original HTML as input
          campaignId,
          leadId: recipientId,
          baseUrl,
          trackingDomains: {
            openDomain: trackingDomains.openDomain,
            clickDomain: trackingDomains.clickDomain,
            source: trackingDomains.source
          }
        });

        if (workerResult.error) {
          logger.error('[CAMPAIGN_DEBUG] Worker thread error processing email', {
            error: workerResult.error,
            stack: workerResult.stack,
            campaignId,
            recipientId
          });
          // Fall back to optimized tracking if worker fails
          trackedHtml = addOptimizedTracking(
            html,
            campaignId,
            recipientId,
            baseUrl,
            {
              openDomain: trackingDomains.openDomain,
              clickDomain: trackingDomains.clickDomain
            }
          );
        } else {
          trackedHtml = workerResult.html;
        }
      } catch (workerError) {
        logger.error('[CAMPAIGN_DEBUG] Error running worker thread', {
          error: workerError instanceof Error ? workerError.message : 'Unknown error',
          stack: workerError instanceof Error ? workerError.stack : 'No stack trace',
          campaignId,
          recipientId
        });

        // Fall back to optimized tracking if worker fails
        trackedHtml = addOptimizedTracking(
          html,
          campaignId,
          recipientId,
          baseUrl,
          {
            openDomain: trackingDomains.openDomain,
            clickDomain: trackingDomains.clickDomain
          }
        );
      }

      logger.info('[CAMPAIGN_DEBUG] Tracking added successfully');

      // Send email with retry logic built into the EmailService
      logger.info(`[CAMPAIGN_DEBUG] Sending email to ${to}...`, {
        to,
        from: emailAccount.email,
        subject: subject.substring(0, 30) + (subject.length > 30 ? '...' : ''),
        campaignId,
        recipientId,
        emailAccountId,
        timestamp: new Date().toISOString()
      });

      // Declare emailResult at the function scope level so it's accessible throughout
      let emailResult;

      try {
        emailResult = await EmailService.sendEmail({
          emailAccountId,
          to,
          subject,
          html: trackedHtml,
          from: emailAccount.email,
          maxRetries: 3, // Use retry logic in the EmailService
          campaignId // Pass the campaign ID for tracking and headers
        });

        logger.info(`[CAMPAIGN_DEBUG] Email sent successfully to ${to}`, {
          messageId: emailResult?.messageId || 'unknown',
          response: emailResult?.response || 'unknown',
          campaignId,
          recipientId,
          to,
          timestamp: new Date().toISOString()
        });
      } catch (sendError) {
        logger.error(`[CAMPAIGN_DEBUG] Error in EmailService.sendEmail`, {
          error: sendError instanceof Error ? sendError.message : 'Unknown error',
          stack: sendError instanceof Error ? sendError.stack : 'No stack trace',
          to,
          from: emailAccount.email,
          campaignId,
          recipientId,
          timestamp: new Date().toISOString()
        });
        throw sendError; // Re-throw to be caught by the outer try/catch
      }

      // Track usage
      await UsageService.trackEmailSent(organizationId);
      logger.info(`Usage tracked for organization: ${organizationId}`);

      // Update step activity status to sent
      await prisma.stepActivity.updateMany({
        where: {
          campaignLeadId: recipientId,
          status: 'pending',
        },
        data: {
          status: 'sent',
          sentAt: new Date(),
        },
      });
      logger.info(`Step activity updated for recipient: ${recipientId}`);

      // Update campaign lead status to completed
      await prisma.campaignLead.update({
        where: {
          id: recipientId,
        },
        data: {
          status: 'completed',
          completedAt: new Date(),
        },
      });
      logger.info(`Campaign lead status updated to completed for recipient: ${recipientId}`);

      // Update campaign status and get the latest metrics
      const updatedStatus = await updateCampaignStatus(campaignId);

      // Log the updated status
      if (updatedStatus) {
        logger.info(`Campaign ${campaignId} status updated:`, {
          status: updatedStatus.status,
          progress: updatedStatus.metrics.progress,
          sentCount: updatedStatus.metrics.sentCount,
          totalLeads: updatedStatus.metrics.totalLeads
        });
      }

      // Get the campaign lead to get the step ID
      const campaignLead = await prisma.campaignLead.findUnique({
        where: { id: recipientId },
        include: { currentStep: true },
      });

      if (!campaignLead || !campaignLead.currentStepId) {
        logger.error(`Campaign lead or current step not found: ${recipientId}`);
        throw new Error('Campaign lead or current step not found');
      }

      // Update campaign lead status and create step activity
      await prisma.stepActivity.create({
        data: {
          campaignLeadId: recipientId,
          stepId: campaignLead.currentStepId,
          status: 'sent',
          type: 'email_sent',
          sentAt: new Date(),
          metadata: {
            senderEmail: emailAccount.email,
            senderName: emailAccount.name || '',
            subject: subject,
            messageId: emailResult?.messageId || 'unknown'
          },
        },
      });

      // Log the email sent
      logger.info(`Email job ${job.id} completed successfully for campaign ${campaignId}, recipient ${recipientId}`, {
        to,
        subject: subject.substring(0, 30) + (subject.length > 30 ? '...' : ''),
        messageId: emailResult?.messageId || 'unknown'
      });

    } catch (error) {
      try {
        logger.error(`[CAMPAIGN_DEBUG] Email job ${job.id} failed for campaign ${campaignId}, recipient ${recipientId}`, {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : 'No stack trace',
          to,
          subject: subject.substring(0, 30) + (subject.length > 30 ? '...' : ''),
          jobId: job.id,
          campaignId,
          recipientId,
          emailAccountId,
          environment: process.env.NODE_ENV,
          vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
          timestamp: new Date().toISOString()
        });

        // Get the campaign lead to get the step ID
        logger.info(`[CAMPAIGN_DEBUG] Finding campaign lead for recipient ${recipientId}`);
        const campaignLead = await prisma.campaignLead.findUnique({
          where: { id: recipientId },
          include: { currentStep: true, campaign: true },
        });

        if (campaignLead && campaignLead.currentStepId) {
          logger.info(`[CAMPAIGN_DEBUG] Found campaign lead and step for recipient ${recipientId}`, {
            campaignLeadId: campaignLead.id,
            stepId: campaignLead.currentStepId,
            campaignId: campaignLead.campaign?.id
          });

          // Create a step activity for the failure
          logger.info(`[CAMPAIGN_DEBUG] Creating failure activity for campaign ${campaignId}, recipient ${recipientId}`);
          await prisma.stepActivity.create({
            data: {
              campaignLeadId: recipientId,
              stepId: campaignLead.currentStepId,
              status: 'failed',
              type: 'email_error',
              error: error instanceof Error ? error.message : 'Unknown error',
              metadata: {
                errorDetails: error instanceof Error ? error.stack : 'No stack trace',
                attemptedAt: new Date().toISOString()
              }
            },
          });
          logger.info(`[CAMPAIGN_DEBUG] Created failure activity for campaign ${campaignId}, recipient ${recipientId}`);

          // Update campaign with error message
          if (campaignLead.campaign) {
            logger.info(`[CAMPAIGN_DEBUG] Updating campaign ${campaignId} with error message`);
            await prisma.campaign.update({
              where: { id: campaignLead.campaign.id },
              data: {
                errorMessage: `Failed to send email to ${to}: ${error instanceof Error ? error.message : 'Unknown error'}`
              }
            });
          }

          // Update campaign status to reflect the error
          logger.info(`[CAMPAIGN_DEBUG] Updating campaign status for ${campaignId}`);
          await updateCampaignStatus(campaignId);
        } else {
          logger.error(`[CAMPAIGN_DEBUG] Could not create failure activity - campaign lead or step not found: ${recipientId}`, {
            campaignId,
            recipientId,
            error: 'Campaign lead or step not found'
          });
        }
      } catch (activityError) {
        logger.error(`[CAMPAIGN_DEBUG] Failed to create step activity for failed email:`, {
          error: activityError instanceof Error ? activityError.message : 'Unknown error',
          stack: activityError instanceof Error ? activityError.stack : 'No stack trace',
          campaignId,
          recipientId,
          timestamp: new Date().toISOString()
        });
      }

      // Determine if this error should cause the job to be retried
      const errorMessage = error instanceof Error ? error.message : String(error);
      const isRetryableError = (
        errorMessage.includes('ECONNREFUSED') ||
        errorMessage.includes('ETIMEDOUT') ||
        errorMessage.includes('ENOTFOUND') ||
        errorMessage.includes('connection closed') ||
        errorMessage.includes('421') // SMTP temporary error
      );

      if (isRetryableError) {
        logger.info(`Error is retryable, BullMQ will retry the job: ${errorMessage}`);
      } else {
        logger.info(`Error is not retryable, job will fail permanently: ${errorMessage}`);
      }

      // Rethrow the error to let BullMQ handle retries based on its configuration
      throw error;
    }
  },
  {
    connection: getRedisConnection(),
    concurrency: 20 // Increased from 5 to 20 for faster processing
  }
  );

  worker.on('completed', async (job) => {
  logger.info(`[CAMPAIGN_DEBUG] Email job ${job.id} completed successfully`, {
    jobId: job.id,
    campaignId: job.data.campaignId,
    recipientId: job.data.recipientId,
    to: job.data.to,
    processingTime: job.processedOn ? Date.now() - job.processedOn : 'unknown',
    environment: process.env.NODE_ENV,
    vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
    timestamp: new Date().toISOString()
  });
});

worker.on('failed', async (job, error) => {
  logger.error(`[CAMPAIGN_DEBUG] Email job ${job?.id} failed permanently`, {
    jobId: job?.id,
    campaignId: job?.data?.campaignId,
    recipientId: job?.data?.recipientId,
    to: job?.data?.to,
    error: error instanceof Error ? error.message : 'Unknown error',
    stack: error instanceof Error ? error.stack : 'No stack trace',
    attempts: job?.attemptsMade || 0,
    processingTime: job?.processedOn ? Date.now() - job.processedOn : 'unknown',
    environment: process.env.NODE_ENV,
    vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
    timestamp: new Date().toISOString()
  });

  // If the job has campaign data, update the campaign status to reflect the failure
  if (job?.data?.campaignId) {
    try {
      logger.info(`[CAMPAIGN_DEBUG] Updating campaign status after job failure for campaign ${job.data.campaignId}`);

      // Update campaign with error message
      if (job?.data?.to) {
        try {
          await prisma.campaign.update({
            where: { id: job.data.campaignId },
            data: {
              errorMessage: `Failed to send email to ${job.data.to}: ${error instanceof Error ? error.message : 'Unknown error'}`
            }
          });
          logger.info(`[CAMPAIGN_DEBUG] Updated campaign ${job.data.campaignId} with error message`);
        } catch (updateError) {
          logger.error(`[CAMPAIGN_DEBUG] Failed to update campaign with error message`, {
            campaignId: job.data.campaignId,
            error: updateError instanceof Error ? updateError.message : 'Unknown error'
          });
        }
      }

      await updateCampaignStatus(job.data.campaignId);
    } catch (statusError) {
      logger.error(`[CAMPAIGN_DEBUG] Failed to update campaign status after job failure`, {
        campaignId: job.data.campaignId,
        error: statusError instanceof Error ? statusError.message : 'Unknown error',
        stack: statusError instanceof Error ? statusError.stack : 'No stack trace'
      });
    }
  }
});

worker.on('stalled', (jobId) => {
  logger.warn(`[CAMPAIGN_DEBUG] Email job ${jobId} stalled`, {
    jobId,
    environment: process.env.NODE_ENV,
    vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
    timestamp: new Date().toISOString()
  });
});

worker.on('error', (error) => {
  logger.error('[CAMPAIGN_DEBUG] Queue worker error:', {
    error: error instanceof Error ? error.message : 'Unknown error',
    stack: error instanceof Error ? error.stack : 'No stack trace',
    environment: process.env.NODE_ENV,
    vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
    timestamp: new Date().toISOString()
  });
});

  // Log when the worker is ready
  worker.on('ready', () => {
    logger.info('[CAMPAIGN_DEBUG] Email queue worker is ready', {
      environment: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
      timestamp: new Date().toISOString(),
      redisUrl: process.env.REDIS_URL ? 'configured' : 'not-configured',
      redisHost: process.env.REDIS_HOST ? 'configured' : 'not-configured'
    });
  });

  // Log when the worker is closed
  worker.on('closed', () => {
    logger.info('[CAMPAIGN_DEBUG] Email queue worker is closed', {
      environment: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
      timestamp: new Date().toISOString()
    });
  });
} else {
  logger.info('[CAMPAIGN_DEBUG] Queue worker disabled in development mode', {
    environment: process.env.NODE_ENV,
    enableQueueWorker: process.env.ENABLE_QUEUE_WORKER,
    vercelEnv: process.env.VERCEL_ENV || 'not-vercel',
    timestamp: new Date().toISOString()
  });
}

/**
 * Check if the queue is healthy and connected
 * This can be used to verify that the Redis connection and queue are working properly
 */
export async function checkQueueHealth(): Promise<{
  healthy: boolean;
  redisConnected: boolean;
  workerActive: boolean;
  queueExists: boolean;
  details?: string;
  error?: string;
}> {
  try {
    // Check Redis connection
    const redisConnected = redis.status === 'ready';

    // Check if worker is active
    const workerActive = worker ? worker.isRunning() : false;

    // Check if queue exists
    let queueExists = false;
    if (emailQueue) {
      try {
        const queueInfo = await emailQueue.getJobCounts();
        queueExists = true;

        logger.info('[CAMPAIGN_DEBUG] Queue health check', {
          redisConnected,
          workerActive,
          queueExists,
          queueInfo,
          timestamp: new Date().toISOString()
        });
      } catch (queueError) {
        logger.error('[CAMPAIGN_DEBUG] Error checking queue existence', {
          error: queueError instanceof Error ? queueError.message : 'Unknown error',
          timestamp: new Date().toISOString()
        });
      }
    } else {
      logger.info('[CAMPAIGN_DEBUG] Queue not created (disabled in development)', {
        environment: process.env.NODE_ENV,
        enableQueueWorker: process.env.ENABLE_QUEUE_WORKER,
        timestamp: new Date().toISOString()
      });
    }

    // Overall health status
    const healthy = redisConnected && workerActive && queueExists;

    return {
      healthy,
      redisConnected,
      workerActive,
      queueExists,
      details: healthy ? 'Queue system is healthy' : 'Queue system has issues',
    };
  } catch (error) {
    logger.error('[CAMPAIGN_DEBUG] Error checking queue health', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      timestamp: new Date().toISOString()
    });

    return {
      healthy: false,
      redisConnected: false,
      workerActive: false,
      queueExists: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
