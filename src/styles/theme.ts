import { theme } from 'antd';
import type { ThemeConfig } from 'antd';

// Banner blue color: bg-blue-600 (Tail<PERSON>) = #2563eb
const BANNER_BLUE = '#2563eb';

// Light theme configuration
export const lightTheme: ThemeConfig = {
  token: {
    colorPrimary: BANNER_BLUE,
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#f5222d',
    colorInfo: BANNER_BLUE,
    colorTextBase: '#000000',
    colorBgBase: '#ffffff',
    borderRadius: 6,
  },
  components: {
    Layout: {
      bodyBg: '#f0f2f5',
      headerBg: '#ffffff',
      siderBg: '#ffffff',
    },
    Menu: {
      itemBg: 'transparent',
      itemSelectedBg: '#e6f7ff',
      itemHoverBg: '#f5f5f5',
    },
    Card: {
      colorBgContainer: '#ffffff',
    },
    Table: {
      colorBgContainer: '#ffffff',
      headerBg: '#fafafa',
    },
  },
};

// Dark theme configuration
export const darkTheme: ThemeConfig = {
  token: {
    colorPrimary: BANNER_BLUE,
    colorSuccess: '#49aa19',
    colorWarning: '#d89614',
    colorError: '#d32029',
    colorInfo: BANNER_BLUE,
    colorTextBase: '#ffffff',
    colorBgBase: '#141414',
    borderRadius: 6,
  },
  components: {
    Layout: {
      bodyBg: '#000000',
      headerBg: '#141414',
      siderBg: '#141414',
    },
    Menu: {
      itemBg: 'transparent',
      itemSelectedBg: '#111b26',
      itemHoverBg: '#1f1f1f',
    },
    Card: {
      colorBgContainer: '#1f1f1f',
    },
    Table: {
      colorBgContainer: '#1f1f1f',
      headerBg: '#141414',
    },
  },
  algorithm: theme.darkAlgorithm,
};
