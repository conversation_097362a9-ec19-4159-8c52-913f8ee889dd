.email-html-content {
  max-width: 100%;
  overflow-x: auto;
  word-break: break-word;
  line-height: 1.6;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Hide unnecessary borders in email content */
.email-html-content table {
  border-collapse: collapse;
}

.email-html-content table:not([border]),
.email-html-content table[border="0"] {
  border: none !important;
}

.email-html-content table:not([border]) td,
.email-html-content table:not([border]) th,
.email-html-content table[border="0"] td,
.email-html-content table[border="0"] th {
  border: none !important;
}

/* Fix nested tables layout */
.email-html-content table table {
  margin: 0 !important;
  padding: 0 !important;
}

.email-html-content img {
  max-width: 100%;
  height: auto;
}

.email-html-content table {
  max-width: 100%;
  border-collapse: collapse;
}

.email-html-content td, .email-html-content th {
  padding: 8px;
  border: 1px solid #ddd;
}

.email-html-content pre {
  white-space: pre-wrap;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

.email-html-content blockquote {
  border-left: 3px solid #ddd;
  padding-left: 10px;
  margin-left: 0;
  color: #666;
}

/* Fix for common email rendering issues */
.email-html-content div {
  max-width: 100% !important;
}

.email-html-content span {
  max-width: 100% !important;
  display: inline;
}

.email-html-content a {
  color: #3182ce;
  text-decoration: underline;
}

.email-html-content ul, .email-html-content ol {
  padding-left: 20px;
  margin: 10px 0;
}

.email-html-content li {
  margin-bottom: 5px;
}

/* Remove visible borders from layout tables */
.email-html-content > table,
.email-html-content > table > tbody > tr > td > table,
.email-html-content > table > tbody > tr > td > table > tbody > tr > td > table {
  border: none !important;
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  margin: 0 auto !important;
}

/* Fix spacing issues */
.email-html-content > table > tbody > tr > td {
  padding: 0 !important;
}

/* Improve email container appearance */
.email-html-content {
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  color: #333;
  font-size: 14px;
  line-height: 1.6;
}

/* Fix for email clients that use fixed widths */
.email-html-content [style*="width"] {
  max-width: 100% !important;
  width: auto !important;
}

/* Fix for Outlook-specific markup */
.email-html-content [class*="MsoNormal"] {
  margin: 0 !important;
  padding: 0 !important;
}

/* Fix for Gmail-specific markup */
.email-html-content .gmail_quote {
  max-width: 100% !important;
  overflow: auto;
  padding: 10px;
  border-left: 3px solid #ddd;
  margin: 10px 0;
}

/* Dark mode styles */
.dark .email-html-content {
  background-color: #1a202c;
  color: #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  border: 1px solid #2d3748;
}

.dark .email-html-content pre {
  background-color: #2d3748;
}

.dark .email-html-content blockquote {
  border-left-color: #4a5568;
  color: #a0aec0;
}

.dark .email-html-content td, .dark .email-html-content th {
  border-color: #4a5568;
  color: #e2e8f0;
}

.dark .email-html-content a {
  color: #63b3ed;
}

.dark .email-html-content .gmail_quote {
  border-left-color: #4a5568;
}

/* Force text color in dark mode */
.dark .email-html-content p,
.dark .email-html-content div,
.dark .email-html-content span,
.dark .email-html-content h1,
.dark .email-html-content h2,
.dark .email-html-content h3,
.dark .email-html-content h4,
.dark .email-html-content h5,
.dark .email-html-content h6 {
  color: #e2e8f0 !important;
}

/* Improve link visibility in dark mode */
.dark .email-html-content a {
  color: #63b3ed !important;
  text-decoration: underline !important;
}

/* Improve table cell visibility in dark mode */
.dark .email-html-content table td,
.dark .email-html-content table th {
  color: #e2e8f0 !important;
  border-color: #4a5568 !important;
}

/* Fix background colors in dark mode */
.dark .email-html-content [style*="background"] {
  background-color: transparent !important;
}
