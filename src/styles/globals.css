@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dark mode styles */
.dark {
  color-scheme: dark;
}

/* Ant Design dark mode overrides */
.dark .ant-card {
  background-color: #1f1f1f;
  border-color: #303030;
}

.dark .ant-card-head {
  color: #ffffff;
  border-color: #303030;
}

.dark .ant-table {
  background-color: #1f1f1f;
  color: #ffffff;
}

.dark .ant-table-thead > tr > th {
  background-color: #141414;
  color: #ffffff;
  border-color: #303030;
}

.dark .ant-table-tbody > tr > td {
  border-color: #303030;
}

.dark .ant-table-tbody > tr:hover > td {
  background-color: #303030;
}

.dark .ant-pagination-item {
  background-color: #1f1f1f;
  border-color: #303030;
}

.dark .ant-pagination-item a {
  color: #ffffff;
}

.dark .ant-pagination-item-active {
  background-color: #177ddc;
  border-color: #177ddc;
}

.dark .ant-pagination-item-active a {
  color: #ffffff;
}

.dark .ant-select-selector {
  background-color: #1f1f1f !important;
  border-color: #303030 !important;
  color: #ffffff !important;
}

.dark .ant-select-arrow {
  color: #ffffff;
}

.dark .ant-input {
  background-color: #1f1f1f;
  border-color: #303030;
  color: #ffffff;
}

.dark .ant-input-affix-wrapper {
  background-color: #1f1f1f;
  border-color: #303030;
}

.dark .ant-input-affix-wrapper > input.ant-input {
  background-color: transparent;
}

.dark .ant-btn {
  border-color: #303030;
}

.dark .ant-btn-default {
  background-color: #1f1f1f;
  color: #ffffff;
}

.dark .ant-modal-content,
.dark .ant-modal-header {
  background-color: #1f1f1f;
  color: #ffffff;
}

.dark .ant-modal-title {
  color: #ffffff;
}

.dark .ant-modal-close-x {
  color: #ffffff;
}

.dark .ant-tabs-tab {
  color: #d9d9d9;
}

.dark .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #177ddc;
}

.dark .ant-tabs-ink-bar {
  background: #177ddc;
}

.dark .ant-tabs-nav::before {
  border-bottom-color: #303030;
}

.dark .ant-divider {
  border-top-color: #303030;
}

.dark .ant-typography {
  color: #ffffff;
}

.dark .ant-typography.ant-typography-secondary {
  color: rgba(255, 255, 255, 0.65);
}

/* Global table styles to prevent overflow */
.ant-table-wrapper {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* Ensure all tables respect container boundaries */
.ant-table {
  width: 100%;
  max-width: 100%;
}

/* Custom component styles */
@import './verification-table.css';
@import './email-preview.css';
