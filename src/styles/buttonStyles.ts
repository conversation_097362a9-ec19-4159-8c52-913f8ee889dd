// Common button styles for consistency across the application
export const buttonStyles = {
  // Primary button (banner blue)
  primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-600',
  
  // Secondary button (lighter blue)
  secondary: 'bg-blue-100 text-blue-600 hover:bg-blue-200 focus:ring-blue-600',
  
  // Outline button
  outline: 'border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-600',
  
  // Danger button
  danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-600',
  
  // Success button
  success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-600',
  
  // Warning button
  warning: 'bg-amber-500 text-white hover:bg-amber-600 focus:ring-amber-500',
  
  // Base styles for all buttons
  base: 'inline-flex items-center justify-center rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2',
  
  // Size variants
  sizes: {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-2.5 py-1.5 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-2 text-base',
    xl: 'px-6 py-3 text-base'
  }
};

// Helper function to combine button styles
export function getButtonClasses(
  variant: 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'warning' = 'primary',
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md',
  additionalClasses: string = ''
): string {
  return `${buttonStyles.base} ${buttonStyles[variant]} ${buttonStyles.sizes[size]} ${additionalClasses}`;
}
