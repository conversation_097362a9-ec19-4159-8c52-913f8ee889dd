/* Custom styles for the DNS verification results table */

/* Ensure tables stay within their containers and expand to full width */
.verification-results-table {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: auto;
}

.verification-results-table .ant-table {
  width: 100% !important;
  table-layout: fixed !important;
}

/* Force table to take full width */
.verification-results-table .ant-table-wrapper {
  width: 100% !important;
}

/* Make sure the container for the verification results takes full width */
.bg-gray-50.dark\:bg-gray-900.p-4.rounded-lg.border.border-gray-200.dark\:border-gray-700.shadow-sm.w-full.max-w-full.overflow-hidden {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: auto !important;
}

/* Ensure the parent div takes full width */
.mb-6 {
  width: 100% !important;
}

.verification-results-table .ant-table-container {
  border-radius: 8px;
  overflow: hidden;
  width: 100% !important;
}

/* Ensure the table cells have appropriate widths */
.verification-results-table .ant-table-thead > tr > th,
.verification-results-table .ant-table-tbody > tr > td {
  white-space: normal;
  word-break: break-word;
}

/* Domain column should be narrower */
.verification-results-table .ant-table-thead > tr > th:nth-child(1),
.verification-results-table .ant-table-tbody > tr > td:nth-child(1) {
  width: 20%;
}

/* Record Type column should be very narrow */
.verification-results-table .ant-table-thead > tr > th:nth-child(2),
.verification-results-table .ant-table-tbody > tr > td:nth-child(2) {
  width: 15%;
}

/* Expected Value column should be wider */
.verification-results-table .ant-table-thead > tr > th:nth-child(3),
.verification-results-table .ant-table-tbody > tr > td:nth-child(3) {
  width: 25%;
}

/* Actual Value column should be wider */
.verification-results-table .ant-table-thead > tr > th:nth-child(4),
.verification-results-table .ant-table-tbody > tr > td:nth-child(4) {
  width: 25%;
}

/* Status column should be narrow */
.verification-results-table .ant-table-thead > tr > th:nth-child(5),
.verification-results-table .ant-table-tbody > tr > td:nth-child(5) {
  width: 15%;
}

.verification-results-table .ant-table-thead > tr > th {
  background-color: #f0f2f5;
  font-weight: 600;
  padding: 12px 16px;
  text-align: left;
}

.dark .verification-results-table .ant-table-thead > tr > th {
  background-color: #1f2937;
  color: #e5e7eb;
  border-bottom: 1px solid #374151;
}

.verification-results-table .ant-table-tbody > tr > td {
  padding: 12px 16px;
  vertical-align: middle;
}

.dark .verification-results-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #374151;
  background-color: #111827;
}

.verification-results-table .ant-table-tbody > tr:hover > td {
  background-color: #f9fafb;
}

.dark .verification-results-table .ant-table-tbody > tr:hover > td {
  background-color: #1e293b;
}

.verification-results-table .ant-typography-ellipsis-multiple-line {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.verification-results-table .ant-tag {
  margin-right: 0;
  border-radius: 4px;
}

.verification-results-table .ant-typography-code {
  padding: 4px 6px;
  border-radius: 4px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  line-height: 1.5;
  max-width: 100%;
  word-break: break-all;
  white-space: pre-wrap;
}

.dark .verification-results-table .ant-typography-code {
  background-color: #1e293b;
  border-color: #374151;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .verification-results-table .ant-table-thead > tr > th,
  .verification-results-table .ant-table-tbody > tr > td {
    padding: 8px 12px;
    font-size: 13px;
  }

  .verification-results-table .ant-typography-code {
    font-size: 11px;
  }
}

/* Status tag styling */
.verification-results-table .ant-tag-success {
  background-color: rgba(82, 196, 26, 0.1);
  border-color: #52c41a;
  color: #52c41a;
}

.verification-results-table .ant-tag-error {
  background-color: rgba(245, 34, 45, 0.1);
  border-color: #f5222d;
  color: #f5222d;
}

.dark .verification-results-table .ant-tag-success {
  background-color: rgba(82, 196, 26, 0.2);
  border-color: #52c41a;
  color: #73d13d;
}

.dark .verification-results-table .ant-tag-error {
  background-color: rgba(245, 34, 45, 0.2);
  border-color: #f5222d;
  color: #ff4d4f;
}
