import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface Organization {
  id: string;
  name: string;
  customerId?: string;
  subscriptionId?: string;
  planId?: string;
}

interface LeadList {
  id: string;
  name: string;
  description?: string;
  source?: string;
  organizationId: string;
  _count?: { leads: number };
}

interface EmailAccount {
  id: string;
  email: string;
  name?: string;
  provider: string;
  status: string;
}

interface AppState {
  // User and organization data
  user: any | null;
  organization: Organization | null;
  
  // Common data
  leadLists: LeadList[];
  emailAccounts: EmailAccount[];
  
  // Loading states
  isLoadingOrganization: boolean;
  isLoadingLeadLists: boolean;
  isLoadingEmailAccounts: boolean;
  
  // Actions
  setUser: (user: any | null) => void;
  setOrganization: (organization: Organization | null) => void;
  setLeadLists: (leadLists: LeadList[]) => void;
  setEmailAccounts: (emailAccounts: EmailAccount[]) => void;
  
  // Loading state actions
  setIsLoadingOrganization: (isLoading: boolean) => void;
  setIsLoadingLeadLists: (isLoading: boolean) => void;
  setIsLoadingEmailAccounts: (isLoading: boolean) => void;
  
  // Reset state
  resetState: () => void;
}

// Create store with persistence
export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      // Initial state
      user: null,
      organization: null,
      leadLists: [],
      emailAccounts: [],
      
      // Loading states
      isLoadingOrganization: false,
      isLoadingLeadLists: false,
      isLoadingEmailAccounts: false,
      
      // Actions
      setUser: (user) => set({ user }),
      setOrganization: (organization) => set({ organization }),
      setLeadLists: (leadLists) => set({ leadLists }),
      setEmailAccounts: (emailAccounts) => set({ emailAccounts }),
      
      // Loading state actions
      setIsLoadingOrganization: (isLoadingOrganization) => set({ isLoadingOrganization }),
      setIsLoadingLeadLists: (isLoadingLeadLists) => set({ isLoadingLeadLists }),
      setIsLoadingEmailAccounts: (isLoadingEmailAccounts) => set({ isLoadingEmailAccounts }),
      
      // Reset state
      resetState: () => set({
        user: null,
        organization: null,
        leadLists: [],
        emailAccounts: [],
        isLoadingOrganization: false,
        isLoadingLeadLists: false,
        isLoadingEmailAccounts: false,
      }),
    }),
    {
      name: 'app-storage', // unique name for localStorage
      partialize: (state) => ({
        // Only persist these fields
        user: state.user,
        organization: state.organization,
        leadLists: state.leadLists,
        emailAccounts: state.emailAccounts,
      }),
    }
  )
);
