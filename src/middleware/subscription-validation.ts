import { NextApiResponse } from 'next';
import { ExtendedNextApiRequest } from '@/lib/apiHandler';
import { prisma } from '@/lib/prisma';
import { EnhancedUsageService } from '@/services/enhanced-usage.service';
import { SubscriptionService } from '@/services/subscription.service';
import { SubscriptionTier, SUBSCRIPTION_PLANS, AIFeatureType } from '@/config/subscription-plans';

/**
 * Middleware to validate if a user can perform an action based on their subscription
 */
export async function validateSubscription(
  req: ExtendedNextApiRequest,
  res: NextApiResponse,
  next: () => void
) {
  try {
    // Skip validation for non-authenticated routes
    if (!req.user?.id) {
      return next();
    }

    // Get the user's organization
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        ownedOrganization: {
          include: {
            subscription: true,
          },
        },
      },
    });

    if (!user?.ownedOrganization) {
      return res.status(403).json({
        error: 'Organization not found',
        requiresAction: 'SETUP_ORGANIZATION',
      });
    }

    const organizationId = user.ownedOrganization.id;

    // Check if the user has a payment method set up
    if (!user.ownedOrganization.customerId) {
      return res.status(403).json({
        error: 'Payment method required',
        requiresAction: 'SETUP_PAYMENT_METHOD',
        message: 'Please add a payment method to continue.',
      });
    }

    // For campaign creation or sending emails, check usage limits
    if (
      req.url?.includes('/api/campaigns/create') ||
      req.url?.includes('/api/campaigns/send') ||
      req.url?.includes('/api/emails/send')
    ) {
      const { withinLimits, limitedBy, currentUsage } = await EnhancedUsageService.checkUsageLimits(organizationId);

      if (!withinLimits) {
        // Get subscription details to provide upgrade information
        const subscriptionDetails = await SubscriptionService.getSubscriptionDetails(organizationId);
        const currentTier = subscriptionDetails.tier;

        // Determine the next tier to suggest for upgrade
        let suggestedTier: SubscriptionTier;
        if (currentTier === SubscriptionTier.FREE) {
          suggestedTier = SubscriptionTier.PRO;
        } else if (currentTier === SubscriptionTier.PRO) {
          suggestedTier = SubscriptionTier.ENTERPRISE;
        } else {
          suggestedTier = SubscriptionTier.ENTERPRISE; // Already at highest tier
        }

        // Create a notification about the limit
        if (limitedBy?.includes('dailyEmails')) {
          const limit = SUBSCRIPTION_PLANS[currentTier].limits.dailyEmails;
          await EnhancedUsageService.createUsageLimitNotification(
            organizationId,
            'daily email sending',
            currentUsage.dailyEmailsSent,
            limit
          );
        }

        return res.status(403).json({
          error: 'Usage limit exceeded',
          requiresAction: 'UPGRADE_SUBSCRIPTION',
          limitedBy,
          currentUsage,
          currentTier,
          suggestedTier,
          message: `You've reached your ${limitedBy?.join(', ')} limit. Please upgrade your subscription to continue.`,
        });
      }
    }

    // For AI features, check if the feature is available for the subscription tier
    if (req.url?.includes('/api/ai/')) {
      // Map URL patterns to AI features that we can check
      let aiFeature: 'aiAgents' | 'knowledgeBase' | null = null;
      let featureName = '';

      if (req.url.includes('/reply') || req.url.includes('/agents')) {
        aiFeature = 'aiAgents';
        featureName = 'AI Agents';
      } else if (req.url.includes('/knowledge')) {
        aiFeature = 'knowledgeBase';
        featureName = 'Knowledge Base';
      }

      if (aiFeature) {
        const isAvailable = await EnhancedUsageService.isAIFeatureAvailable(organizationId, aiFeature);

        if (!isAvailable) {
          return res.status(403).json({
            error: 'Feature not available',
            requiresAction: 'UPGRADE_SUBSCRIPTION',
            feature: aiFeature,
            message: `${featureName} is not available in your current package. Please upgrade to access this feature.`,
            upgradeRequired: true
          });
        }
      }
    }

    // If all checks pass, proceed to the next middleware or handler
    return next();
  } catch (error) {
    console.error('Error in subscription validation middleware:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Higher-order function to apply subscription validation to an API handler
 */
export function withSubscriptionValidation(handler: any) {
  return async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
    return new Promise<void>((resolve) => {
      validateSubscription(req, res, () => {
        resolve(handler(req, res));
      });
    });
  };
}
