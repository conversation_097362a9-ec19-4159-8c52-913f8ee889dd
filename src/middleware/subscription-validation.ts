import { NextApiResponse } from 'next';
import { ExtendedNextApiRequest } from '@/lib/apiHandler';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { EnhancedUsageService } from '@/services/enhanced-usage.service';
import { SubscriptionService } from '@/services/subscription.service';
import { SubscriptionTier, SUBSCRIPTION_PLANS, AIFeatureType } from '@/config/subscription-plans';

/**
 * Middleware to validate if a user can perform an action based on their subscription
 */
export async function validateSubscription(
  req: ExtendedNextApiRequest,
  res: NextApiResponse,
  next: () => void
) {
  try {
    // Skip validation for non-authenticated routes
    if (!req.user?.id) {
      return next();
    }

    // Get the user's organization with package information
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        ownedOrganization: {
          include: {
            subscription: true,
            package: true,
            dailyUsage: {
              where: {
                date: {
                  gte: new Date(new Date().setHours(0, 0, 0, 0))
                }
              },
              orderBy: { date: 'desc' },
              take: 1
            }
          },
        },
      },
    });

    if (!user?.ownedOrganization) {
      return res.status(403).json({
        error: 'Organization not found',
        requiresAction: 'SETUP_ORGANIZATION',
      });
    }

    const organization = user.ownedOrganization;
    const organizationId = organization.id;

    // MANDATORY: Check if organization has a package - NO PACKAGE = NO ACCESS
    if (!organization.package) {
      logger.warn('Organization has no package assigned - access blocked', {
        organizationId,
        userId: req.user.id,
        endpoint: req.url
      });

      return res.status(403).json({
        error: 'No subscription package found',
        requiresAction: 'SELECT_PACKAGE',
        message: 'You must select a subscription package to use this service. Please choose a package to continue.',
      });
    }

    // Check if package is active
    if (organization.package.status !== 'ACTIVE') {
      return res.status(403).json({
        error: 'Package is not active',
        requiresAction: 'ACTIVATE_PACKAGE',
        message: 'Your subscription package is not active. Please contact support or update your subscription.',
      });
    }

    // Check if the user has a payment method set up
    if (!user.ownedOrganization.customerId) {
      return res.status(403).json({
        error: 'Payment method required',
        requiresAction: 'SETUP_PAYMENT_METHOD',
        message: 'Please add a payment method to continue.',
      });
    }

    // For campaign creation or sending emails, check package limits
    if (
      req.url?.includes('/api/campaigns/create') ||
      req.url?.includes('/api/campaigns/send') ||
      req.url?.includes('/api/emails/send')
    ) {
      const packageLimits = organization.package;
      const todayUsage = organization.dailyUsage[0];
      const currentEmailsSent = todayUsage?.emailsSent || 0;

      // Check daily email limit
      if (currentEmailsSent >= packageLimits.dailyEmailLimit) {
        logger.warn('Daily email limit exceeded', {
          organizationId,
          currentUsage: currentEmailsSent,
          dailyLimit: packageLimits.dailyEmailLimit,
          packageName: packageLimits.name
        });

        return res.status(403).json({
          error: 'Daily email limit exceeded',
          requiresAction: 'UPGRADE_PACKAGE',
          currentUsage: currentEmailsSent,
          dailyLimit: packageLimits.dailyEmailLimit,
          packageName: packageLimits.name,
          message: `You've reached your daily email limit of ${packageLimits.dailyEmailLimit} emails. Please upgrade your package to send more emails.`,
        });
      }

      // Check monthly email limit if applicable
      if (packageLimits.monthlyEmailLimit && packageLimits.monthlyEmailLimit > 0) {
        // Get monthly usage (you may need to implement this)
        const monthlyUsage = await EnhancedUsageService.getCurrentUsage(organizationId);
        if (monthlyUsage.emailsSent >= packageLimits.monthlyEmailLimit) {
          return res.status(403).json({
            error: 'Monthly email limit exceeded',
            requiresAction: 'UPGRADE_PACKAGE',
            currentUsage: monthlyUsage.emailsSent,
            monthlyLimit: packageLimits.monthlyEmailLimit,
            packageName: packageLimits.name,
            message: `You've reached your monthly email limit of ${packageLimits.monthlyEmailLimit} emails. Please upgrade your package to send more emails.`,
          });
        }
      }
    }

    // For AI features, check if the feature is available in the package
    if (req.url?.includes('/api/ai/') || req.url?.includes('/knowledge-base/')) {
      const feature = req.url.includes('/reply')
        ? 'REPLY_AGENT'
        : req.url.includes('/template')
          ? 'TEMPLATE_DESIGNER'
          : req.url.includes('/content')
            ? 'CONTENT_GENERATOR'
            : req.url.includes('/image')
              ? 'IMAGE_GENERATOR'
              : req.url.includes('/knowledge-base')
                ? 'KNOWLEDGE_BASE'
                : null;

      if (feature) {
        const packageFeatures = organization.package.aiFeatures as string[];

        // Free package: Block AI and Knowledge Base features
        if (organization.package.name === 'Free' || organization.package.price === 0) {
          logger.warn('AI feature blocked for free package', {
            organizationId,
            feature,
            packageName: organization.package.name
          });

          return res.status(403).json({
            error: 'AI feature not available',
            requiresAction: 'UPGRADE_PACKAGE',
            feature,
            packageName: organization.package.name,
            message: `AI features including ${feature} are not available on the Free package. Please upgrade to access AI capabilities.`,
          });
        }

        // Check if feature is included in package
        if (!packageFeatures.includes(feature)) {
          return res.status(403).json({
            error: 'Feature not included in package',
            requiresAction: 'UPGRADE_PACKAGE',
            feature,
            packageName: organization.package.name,
            availableFeatures: packageFeatures,
            message: `The ${feature} feature is not included in your ${organization.package.name} package. Please upgrade to access this feature.`,
          });
        }
      }
    }

    // If all checks pass, proceed to the next middleware or handler
    return next();
  } catch (error) {
    console.error('Error in subscription validation middleware:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Higher-order function to apply subscription validation to an API handler
 */
export function withSubscriptionValidation(handler: any) {
  return async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
    return new Promise<void>((resolve) => {
      validateSubscription(req, res, () => {
        resolve(handler(req, res));
      });
    });
  };
}
