import { NextApiRequest, NextApiResponse } from 'next'
import { AppError } from '@/lib/errors'
import { logger } from '@/lib/logger'

// Import the ExtendedLogger interface
interface ExtendedLogger extends Logger {
  addRequestContext: (req: any) => any;
}

import { Logger } from 'winston'

export const errorHandler = (
  error: Error,
  req: NextApiRequest,
  res: NextApiResponse
) => {
  if (error instanceof AppError) {
    logger.error('Operational error:', {
      ...(logger as ExtendedLogger).addRequestContext(req),
      error: {
        message: error.message,
        stack: error.stack,
        statusCode: error.statusCode
      }
    })

    return res.status(error.statusCode).json({
      error: error.message
    })
  }

  // Unexpected errors
  logger.error('Unexpected error:', {
    ...(logger as ExtendedLogger).addRequestContext(req),
    error: {
      message: error.message,
      stack: error.stack
    }
  })

  return res.status(500).json({
    error: 'An unexpected error occurred'
  })
}