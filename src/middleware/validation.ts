import { NextApiRequest, NextApiResponse } from 'next'
import { ZodSchema } from 'zod'
import { logger } from '@/lib/logger'

export const validateRequest = async (req: NextApiRequest, schema: ZodSchema) => {
  try {
    const validatedData = await schema.parseAsync(req.body)
    req.body = validatedData
  } catch (error) {
    logger.error('Validation error:', error)
    throw {
      status: 400,
      message: 'Invalid request data',
      details: error.errors
    }
  }
}
