import { NextApiRequest, NextApiResponse } from 'next'
import DOMPurify from 'isomorphic-dompurify'
import { htmlToText } from 'html-to-text'

const sanitizeValue = (value: any): any => {
  if (typeof value === 'string') {
    // Clean HTML if present
    const cleanHtml = DOMPurify.sanitize(value, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
      ALLOWED_ATTR: ['href']
    })
    
    // Convert to plain text if it's not meant to contain HTML
    return value.includes('<') ? cleanHtml : htmlToText(value, {
      wordwrap: false,
      selectors: [
        { selector: 'a', options: { hideLinkHrefIfSameAsText: true } }
      ]
    })
  }
  
  if (Array.isArray(value)) {
    return value.map(sanitizeValue)
  }
  
  if (typeof value === 'object' && value !== null) {
    return Object.keys(value).reduce((acc, key) => ({
      ...acc,
      [key]: sanitizeValue(value[key])
    }), {})
  }
  
  return value
}

export const sanitizeInput = (req: NextApiRequest) => {
  req.body = sanitizeValue(req.body)
  req.query = sanitizeValue(req.query)
  return req
}
