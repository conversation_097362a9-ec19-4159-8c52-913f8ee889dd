import { NextApiRequest, NextApiResponse } from 'next'
import { Ratelimit } from '@upstash/ratelimit'
import redis from '@/lib/redis'

// Create a new ratelimiter that allows 100 requests per 15 minutes
const ratelimit = new Ratelimit({
  redis: redis as any,
  limiter: Ratelimit.slidingWindow(100, '15 m'),
  analytics: true,
})

export const rateLimiter = async (req: NextApiRequest, res: NextApiResponse) => {
  const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress
  const identifier = `${ip}-${req.url}`

  const result = await ratelimit.limit(identifier)

  res.setHeader('X-RateLimit-Limit', result.limit)
  res.setHeader('X-RateLimit-Remaining', result.remaining)

  if (!result.success) {
    res.status(429).json({
      error: 'Too many requests, please try again later.',
      retryAfter: result.reset
    })
    throw new Error('Rate limit exceeded')
  }
}
