import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/pages/api/auth/[...nextauth]';
import { EnhancedUsageService } from '@/services/enhanced-usage.service';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

export interface PackageLimitsMiddlewareOptions {
  checkEmailLimit?: boolean;
  checkAIAgents?: boolean;
  checkKnowledgeBase?: boolean;
  checkCampaigns?: boolean;
  checkLeads?: boolean;
  checkEmailAccounts?: boolean;
}

/**
 * Middleware to enforce package limits
 */
export function withPackageLimits(
  handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void>,
  options: PackageLimitsMiddlewareOptions = {}
) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      const session = await getServerSession(req, res, authOptions);
      
      if (!session?.user?.email) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      // Get user's organization
      const user = await prisma.user.findUnique({
        where: { email: session.user.email },
        include: {
          ownedOrganization: true,
          organizations: {
            include: {
              organization: true
            }
          }
        }
      });

      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Get the organization (either owned or member of)
      const organization = user.ownedOrganization || user.organizations[0]?.organization;
      
      if (!organization) {
        return res.status(404).json({ error: 'Organization not found' });
      }

      // Check package limits
      const limitsCheck = await EnhancedUsageService.checkUsageLimits(organization.id);
      
      if (!limitsCheck.withinLimits) {
        const limitedBy = limitsCheck.limitedBy || [];
        
        // Check specific limits based on options
        if (options.checkEmailLimit && (limitedBy.includes('dailyEmails') || limitedBy.includes('monthlyEmails'))) {
          return res.status(403).json({
            error: 'Email limit exceeded',
            limitedBy: limitedBy.filter(l => l.includes('Email')),
            currentUsage: limitsCheck.currentUsage,
            package: limitsCheck.package
          });
        }

        if (options.checkAIAgents && limitedBy.includes('aiAgents')) {
          return res.status(403).json({
            error: 'AI agents limit exceeded',
            limitedBy: ['aiAgents'],
            package: limitsCheck.package
          });
        }

        if (options.checkKnowledgeBase && limitedBy.includes('knowledgeBases')) {
          return res.status(403).json({
            error: 'Knowledge bases limit exceeded',
            limitedBy: ['knowledgeBases'],
            package: limitsCheck.package
          });
        }

        if (options.checkCampaigns && limitedBy.includes('campaigns')) {
          return res.status(403).json({
            error: 'Campaigns limit exceeded',
            limitedBy: ['campaigns'],
            package: limitsCheck.package
          });
        }

        if (options.checkLeads && limitedBy.includes('leads')) {
          return res.status(403).json({
            error: 'Leads limit exceeded',
            limitedBy: ['leads'],
            package: limitsCheck.package
          });
        }

        if (options.checkEmailAccounts && limitedBy.includes('emailAccounts')) {
          return res.status(403).json({
            error: 'Email accounts limit exceeded',
            limitedBy: ['emailAccounts'],
            package: limitsCheck.package
          });
        }
      }

      // Add organization and package info to request for use in handler
      (req as any).organization = organization;
      (req as any).package = limitsCheck.package;
      (req as any).currentUsage = limitsCheck.currentUsage;

      // Continue to the actual handler
      return await handler(req, res);
    } catch (error) {
      logger.error('Error in package limits middleware', {
        error: error instanceof Error ? error.message : 'Unknown error',
        url: req.url,
        method: req.method
      });
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
}

/**
 * Check if AI features are enabled for the organization
 */
export async function checkAIFeatureAccess(
  organizationId: string,
  feature: 'aiAgents' | 'knowledgeBase'
): Promise<{ allowed: boolean; reason?: string }> {
  try {
    const isAllowed = await EnhancedUsageService.isAIFeatureAvailable(organizationId, feature);
    
    if (!isAllowed) {
      return {
        allowed: false,
        reason: `${feature} feature is not enabled in your current package`
      };
    }

    // Check specific limits
    if (feature === 'aiAgents') {
      const canCreate = await EnhancedUsageService.canCreateAIAgent(organizationId);
      if (!canCreate) {
        return {
          allowed: false,
          reason: 'AI agents limit reached for your current package'
        };
      }
    }

    if (feature === 'knowledgeBase') {
      const canCreate = await EnhancedUsageService.canCreateKnowledgeBase(organizationId);
      if (!canCreate) {
        return {
          allowed: false,
          reason: 'Knowledge bases limit reached for your current package'
        };
      }
    }

    return { allowed: true };
  } catch (error) {
    logger.error('Error checking AI feature access', {
      error: error instanceof Error ? error.message : 'Unknown error',
      organizationId,
      feature
    });
    return {
      allowed: false,
      reason: 'Error checking feature access'
    };
  }
}

/**
 * Middleware specifically for AI features
 */
export function withAIFeatureCheck(
  handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void>,
  feature: 'aiAgents' | 'knowledgeBase'
) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      const session = await getServerSession(req, res, authOptions);
      
      if (!session?.user?.email) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      // Get user's organization
      const user = await prisma.user.findUnique({
        where: { email: session.user.email },
        include: {
          ownedOrganization: true,
          organizations: {
            include: {
              organization: true
            }
          }
        }
      });

      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      const organization = user.ownedOrganization || user.organizations[0]?.organization;
      
      if (!organization) {
        return res.status(404).json({ error: 'Organization not found' });
      }

      // Check AI feature access
      const accessCheck = await checkAIFeatureAccess(organization.id, feature);
      
      if (!accessCheck.allowed) {
        return res.status(403).json({
          error: 'Feature not available',
          reason: accessCheck.reason,
          feature,
          upgradeRequired: true
        });
      }

      // Add organization info to request
      (req as any).organization = organization;

      // Continue to the actual handler
      return await handler(req, res);
    } catch (error) {
      logger.error('Error in AI feature check middleware', {
        error: error instanceof Error ? error.message : 'Unknown error',
        url: req.url,
        method: req.method,
        feature
      });
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
}

/**
 * Get package limits for display in UI
 */
export async function getPackageLimitsForUser(userEmail: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { email: userEmail },
      include: {
        ownedOrganization: true,
        organizations: {
          include: {
            organization: true
          }
        }
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    const organization = user.ownedOrganization || user.organizations[0]?.organization;
    
    if (!organization) {
      throw new Error('Organization not found');
    }

    const limits = await EnhancedUsageService.getPackageLimits(organization.id);
    const currentUsage = await EnhancedUsageService.getCurrentUsage(organization.id);

    return {
      limits,
      currentUsage,
      organizationId: organization.id
    };
  } catch (error) {
    logger.error('Error getting package limits for user', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userEmail
    });
    throw error;
  }
}
