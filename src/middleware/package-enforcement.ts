import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { PackageManagementService } from '@/services/package-management.service';

/**
 * Package Enforcement Middleware
 * 
 * Ensures all organizations have a valid package before accessing email features.
 * This is a MANDATORY requirement - no package = no email access.
 */

export interface PackageEnforcementRequest extends NextApiRequest {
  user?: {
    id: string;
    email: string;
  };
  organization?: {
    id: string;
    name: string;
    package: {
      id: string;
      name: string;
      price: number;
      dailyEmailLimit: number;
      monthlyEmailLimit?: number;
      emailAccountLimit: number;
      aiFeatures: string[];
      status: string;
    };
  };
}

/**
 * Middleware to enforce package requirements
 */
export async function enforcePackageRequirement(
  req: PackageEnforcementRequest,
  res: NextApiResponse,
  next: () => void
) {
  try {
    // Skip enforcement for super admin endpoints
    if (req.url?.includes('/api/super-admin/')) {
      return next();
    }

    // Skip enforcement for non-email related endpoints
    const emailRelatedEndpoints = [
      '/api/campaigns',
      '/api/emails',
      '/api/email-accounts',
      '/api/ai/',
      '/api/knowledge-base',
      '/api/agents'
    ];

    const isEmailRelated = emailRelatedEndpoints.some(endpoint => 
      req.url?.includes(endpoint)
    );

    if (!isEmailRelated) {
      return next();
    }

    // Get user from request (should be set by auth middleware)
    if (!req.user?.id) {
      return res.status(401).json({
        error: 'Authentication required',
        requiresAction: 'LOGIN'
      });
    }

    // Get user's organization with package information
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        ownedOrganization: {
          include: {
            package: true
          }
        }
      }
    });

    if (!user?.ownedOrganization) {
      return res.status(403).json({
        error: 'Organization not found',
        requiresAction: 'SETUP_ORGANIZATION'
      });
    }

    const organization = user.ownedOrganization;

    // CRITICAL: Check if organization has a package
    if (!organization.package) {
      logger.warn('PACKAGE ENFORCEMENT: Organization blocked - no package assigned', {
        organizationId: organization.id,
        organizationName: organization.name,
        userId: req.user.id,
        userEmail: req.user.email,
        endpoint: req.url,
        method: req.method
      });

      // Try to auto-assign free package
      try {
        await PackageManagementService.assignFreePackageToOrganizations();
        
        // Re-fetch organization with package
        const updatedUser = await prisma.user.findUnique({
          where: { id: req.user.id },
          include: {
            ownedOrganization: {
              include: {
                package: true
              }
            }
          }
        });

        if (updatedUser?.ownedOrganization?.package) {
          logger.info('PACKAGE ENFORCEMENT: Auto-assigned free package', {
            organizationId: organization.id,
            packageId: updatedUser.ownedOrganization.package.id,
            packageName: updatedUser.ownedOrganization.package.name
          });

          // Update organization in request
          req.organization = {
            id: organization.id,
            name: organization.name,
            package: updatedUser.ownedOrganization.package
          };

          return next();
        }
      } catch (autoAssignError) {
        logger.error('PACKAGE ENFORCEMENT: Failed to auto-assign free package', {
          error: autoAssignError instanceof Error ? autoAssignError.message : 'Unknown error',
          organizationId: organization.id
        });
      }

      // If auto-assignment failed, block access
      return res.status(403).json({
        error: 'No subscription package found',
        requiresAction: 'SELECT_PACKAGE',
        message: 'You must select a subscription package to use email features. Please choose a package to continue.',
        details: {
          organizationId: organization.id,
          organizationName: organization.name,
          availablePackages: '/api/packages' // Endpoint to get available packages
        }
      });
    }

    // Check if package is active
    if (organization.package.status !== 'ACTIVE') {
      logger.warn('PACKAGE ENFORCEMENT: Organization blocked - inactive package', {
        organizationId: organization.id,
        packageId: organization.package.id,
        packageStatus: organization.package.status,
        userId: req.user.id
      });

      return res.status(403).json({
        error: 'Package is not active',
        requiresAction: 'ACTIVATE_PACKAGE',
        message: 'Your subscription package is not active. Please contact support or update your subscription.',
        details: {
          packageName: organization.package.name,
          packageStatus: organization.package.status
        }
      });
    }

    // Add organization and package info to request for downstream use
    req.organization = {
      id: organization.id,
      name: organization.name,
      package: organization.package
    };

    logger.debug('PACKAGE ENFORCEMENT: Access granted', {
      organizationId: organization.id,
      packageName: organization.package.name,
      packagePrice: organization.package.price,
      endpoint: req.url
    });

    next();

  } catch (error) {
    logger.error('PACKAGE ENFORCEMENT: Middleware error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.user?.id,
      endpoint: req.url
    });

    return res.status(500).json({
      error: 'Package verification failed',
      message: 'Unable to verify your subscription package. Please try again.'
    });
  }
}

/**
 * Check if a specific AI feature is available for the organization
 */
export function checkAIFeatureAccess(
  req: PackageEnforcementRequest,
  featureName: string
): { allowed: boolean; reason?: string } {
  if (!req.organization?.package) {
    return {
      allowed: false,
      reason: 'No package assigned'
    };
  }

  const packageFeatures = req.organization.package.aiFeatures as string[];

  // Free package: Block all AI features
  if (req.organization.package.price === 0 || req.organization.package.name === 'Free') {
    return {
      allowed: false,
      reason: 'AI features not available on free package'
    };
  }

  // Check if feature is included in package
  if (!packageFeatures.includes(featureName)) {
    return {
      allowed: false,
      reason: `Feature ${featureName} not included in ${req.organization.package.name} package`
    };
  }

  return { allowed: true };
}

/**
 * Check daily email limit for the organization
 */
export async function checkDailyEmailLimit(
  req: PackageEnforcementRequest,
  emailsToSend: number = 1
): Promise<{ allowed: boolean; reason?: string; currentUsage?: number; limit?: number }> {
  if (!req.organization?.package) {
    return {
      allowed: false,
      reason: 'No package assigned'
    };
  }

  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayUsage = await prisma.dailyUsage.findFirst({
      where: {
        organizationId: req.organization.id,
        date: {
          gte: today
        }
      }
    });

    const currentUsage = todayUsage?.emailsSent || 0;
    const limit = req.organization.package.dailyEmailLimit;

    if (currentUsage + emailsToSend > limit) {
      return {
        allowed: false,
        reason: 'Daily email limit exceeded',
        currentUsage,
        limit
      };
    }

    return {
      allowed: true,
      currentUsage,
      limit
    };
  } catch (error) {
    logger.error('Error checking daily email limit', {
      error: error instanceof Error ? error.message : 'Unknown error',
      organizationId: req.organization.id
    });

    return {
      allowed: false,
      reason: 'Unable to verify email limit'
    };
  }
}

/**
 * Utility function to get package info from request
 */
export function getPackageInfo(req: PackageEnforcementRequest) {
  return req.organization?.package || null;
}

/**
 * Utility function to check if organization has any package
 */
export function hasValidPackage(req: PackageEnforcementRequest): boolean {
  return !!(req.organization?.package && req.organization.package.status === 'ACTIVE');
}
