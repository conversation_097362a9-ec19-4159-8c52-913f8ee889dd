import { useAppStore } from '@/store';

export function useCommonData() {
  const {
    user,
    organization,
    leadLists,
    emailAccounts,
    isLoadingOrganization,
    isLoadingLeadLists,
    isLoadingEmailAccounts,
  } = useAppStore();

  // Derived loading state
  const isLoading = isLoadingOrganization || isLoadingLeadLists || isLoadingEmailAccounts;

  return {
    user,
    organization,
    leadLists,
    emailAccounts,
    isLoading,
    isLoadingOrganization,
    isLoadingLeadLists,
    isLoadingEmailAccounts,
  };
}
