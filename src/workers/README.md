# Background Workers

This directory contains background workers for processing various tasks asynchronously.

## Sequence Processor Worker

The sequence processor worker (`sequence-processor.worker.ts`) is responsible for processing sequence campaign events that are queued in the `ProcessingQueue` table. This allows tracking events (opens, clicks) to be processed asynchronously without blocking the main request/response cycle.

### How It Works

1. When a tracking event (email open, link click) is received, it's added to the `ProcessingQueue` table
2. The sequence processor worker runs at regular intervals to process these events
3. For each event, it:
   - Retrieves the campaign and lead data
   - Processes the lead in the sequence
   - Updates the event status in the queue

### Configuration

The worker can be configured using environment variables:

- `SEQUENCE_PROCESSOR_INTERVAL`: Interval in milliseconds between processing runs (default: 10000)
- `SEQUENCE_PROCESSOR_BATCH_SIZE`: Maximum number of events to process in one batch (default: 10)

### Starting the Worker

The worker is automatically started by the campaign processor cron job in production environments. In development, it can be started manually for testing:

```typescript
import { startSequenceProcessor } from './workers/sequence-processor.worker';

// Start the worker
const intervalId = startSequenceProcessor();

// To stop the worker
clearInterval(intervalId);
```
