/**
 * Background worker for processing sequence campaign events
 * This worker processes events from the processing queue to handle sequence campaign conditions
 */
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { SequenceCampaignService } from '@/services/sequence-campaign.service';

// Process interval in milliseconds (default: 10 seconds)
const PROCESS_INTERVAL = parseInt(process.env.SEQUENCE_PROCESSOR_INTERVAL || '10000');

// Maximum number of items to process in one batch
const BATCH_SIZE = parseInt(process.env.SEQUENCE_PROCESSOR_BATCH_SIZE || '10');

// Flag to prevent multiple processing runs at the same time
let isProcessing = false;

/**
 * Process the next batch of sequence events from the queue
 */
export async function processSequenceEvents() {
  // If already processing, skip this run
  if (isProcessing) {
    logger.debug('Sequence processor already running, skipping this run');
    return;
  }

  isProcessing = true;
  const startTime = Date.now();
  let processedCount = 0;
  let errorCount = 0;

  try {
    logger.info('Starting sequence event processing');

    // Get the next batch of events to process
    const pendingEvents = await prisma.processingQueue.findMany({
      where: {
        type: 'sequence_condition_check',
        status: 'pending',
      },
      orderBy: [
        { priority: 'desc' }, // Higher priority first
        { createdAt: 'asc' }, // Older events first
      ],
      take: BATCH_SIZE,
    });

    logger.info(`Found ${pendingEvents.length} pending sequence events to process`);

    // Process each event
    for (const event of pendingEvents) {
      try {
        // Mark the event as processing
        await prisma.processingQueue.update({
          where: { id: event.id },
          data: {
            status: 'processing',
            processedAt: new Date(),
          },
        });

        // Extract event data
        const payload = event.payload as any;
        const campaignId = payload.campaignId;
        const leadId = payload.leadId;
        const eventType = payload.eventType;

        logger.info(`Processing sequence event ${event.id}`, {
          campaignId,
          leadId,
          eventType,
          queuedAt: event.createdAt,
        });

        // Get the campaign with all necessary data
        const campaign = await prisma.campaign.findUnique({
          where: { id: campaignId },
          include: {
            steps: {
              orderBy: {
                position: 'asc',
              },
              include: {
                emailAccount: true,
                emailAccounts: {
                  include: {
                    emailAccount: true
                  }
                },
                conditionStep: true,
                conditionedSteps: true,
              },
            },
            leads: {
              where: { id: leadId }, // Only get the specific lead
              include: {
                lead: true,
                currentStep: true,
                stepActivities: {
                  orderBy: {
                    createdAt: 'desc',
                  },
                  include: {
                    step: true,
                  }
                },
              },
            },
          },
        });

        if (!campaign || !campaign.leads || campaign.leads.length === 0) {
          logger.warn(`Campaign or lead not found for event ${event.id}`, {
            campaignId,
            leadId,
            eventType,
          });

          // Mark as completed with error
          await prisma.processingQueue.update({
            where: { id: event.id },
            data: {
              status: 'error',
              error: 'Campaign or lead not found',
              completedAt: new Date(),
            },
          });

          errorCount++;
          continue;
        }

        // Process the lead in the sequence
        const lead = campaign.leads[0];
        logger.info(`Processing lead ${lead.lead.email} for event ${eventType}`);
        
        await SequenceCampaignService.processLeadInSequence(campaign, lead);

        // Mark the event as completed
        await prisma.processingQueue.update({
          where: { id: event.id },
          data: {
            status: 'completed',
            completedAt: new Date(),
          },
        });

        processedCount++;
        logger.info(`Successfully processed sequence event ${event.id}`);
      } catch (eventError) {
        errorCount++;
        logger.error(`Error processing sequence event ${event.id}`, {
          error: eventError instanceof Error ? eventError.message : 'Unknown error',
          stack: eventError instanceof Error ? eventError.stack : 'No stack trace',
          eventId: event.id,
        });

        // Mark as error but don't fail the entire batch
        try {
          await prisma.processingQueue.update({
            where: { id: event.id },
            data: {
              status: 'error',
              error: eventError instanceof Error ? eventError.message : 'Unknown error',
              completedAt: new Date(),
            },
          });
        } catch (updateError) {
          logger.error(`Failed to update event status to error`, {
            eventId: event.id,
            error: updateError instanceof Error ? updateError.message : 'Unknown error',
          });
        }
      }
    }

    // Log processing summary
    const duration = Date.now() - startTime;
    logger.info(`Sequence event processing completed`, {
      processedCount,
      errorCount,
      duration,
      eventsPerSecond: processedCount > 0 ? Math.round((processedCount / duration) * 1000) : 0,
    });
  } catch (error) {
    logger.error('Error in sequence event processor', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
    });
  } finally {
    isProcessing = false;
  }
}

/**
 * Start the sequence processor
 * This function sets up an interval to periodically process sequence events
 */
export function startSequenceProcessor() {
  logger.info('Starting sequence processor worker');

  // Process events immediately on startup
  processSequenceEvents().catch(error => {
    logger.error('Error in initial sequence event processing', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  });

  // Set up interval for regular processing
  const intervalId = setInterval(() => {
    processSequenceEvents().catch(error => {
      logger.error('Error in scheduled sequence event processing', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    });
  }, PROCESS_INTERVAL);

  // Return the interval ID so it can be cleared if needed
  return intervalId;
}

// If this file is run directly (not imported), start the processor
if (require.main === module) {
  startSequenceProcessor();
}
