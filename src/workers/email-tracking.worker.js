const { workerData, parentPort } = require('worker_threads');
const cheerio = require('cheerio');

/**
 * Worker thread for adding tracking to emails
 * This offloads the CPU-intensive DOM manipulation to a separate thread
 */
function addTrackingToEmail(data) {
  try {
    const { html, campaignId, leadId, baseUrl, trackingDomains } = data;
    
    // Check if HTML is empty or not a string
    if (!html || typeof html !== 'string') {
      parentPort.postMessage({ error: 'Invalid HTML content' });
      return;
    }

    // Ensure HTML has a basic structure
    let processedHtml = html;
    if (!html.includes('<body')) {
      processedHtml = `<html><body>${html}</body></html>`;
    }

    // Parse the HTML
    const $ = cheerio.load(processedHtml);

    // Determine the tracking domains to use
    const defaultOpenDomain = process.env.DEFAULT_OPEN_TRACKING_DOMAIN || `${baseUrl}/api/tracking`;
    const defaultClickDomain = process.env.DEFAULT_CLICK_TRACKING_DOMAIN || `${baseUrl}/api/tracking`;

    // Use provided tracking domains or fall back to defaults
    const openDomain = trackingDomains?.openDomain || defaultOpenDomain;
    const clickDomain = trackingDomains?.clickDomain || defaultClickDomain;
    const domainSource = trackingDomains?.source || 'default';

    // Helper function to build tracking URL based on domain type
    const buildTrackingUrl = (domain, path, params) => {
      // If domain includes a full URL path (e.g., localhost:3000/api/tracking)
      if (domain.includes('/')) {
        return `https://${domain}/${path}?${params}`;
      }

      // If it's a custom domain (just the domain name)
      if (domainSource === 'custom' || domainSource === 'sender') {
        // For custom domains, use the standard tracking path structure
        return `https://${domain}/api/tracking/${path}?${params}`;
      }

      // For default domains, use the application URL structure
      return `${baseUrl}/api/tracking/${path}?${params}`;
    };

    // Build the tracking pixel URL
    const trackingParams = `cid=${campaignId}&lid=${leadId}`;
    const timestamp = new Date().getTime();
    const trackingPixelUrl = `${buildTrackingUrl(openDomain, 'pixel', trackingParams)}&t=${timestamp}`;

    // Create tracking pixel HTML
    const trackingPixelHtml = `<img src="${trackingPixelUrl}" width="1" height="1" alt="" style="display:none !important; max-height:1px !important; max-width:1px !important; border:0 !important; margin:0 !important; padding:0 !important; outline:none !important; position:absolute !important; opacity:0 !important;" />`;

    // Add tracking pixels at strategic locations
    // 1. At the beginning of the body
    $('body').prepend(trackingPixelHtml);

    // 2. At the end of the body
    $('body').append(trackingPixelHtml);

    // Replace all links with tracking links
    $('a').each((i, el) => {
      try {
        const originalUrl = $(el).attr('href');
        if (originalUrl && !originalUrl.startsWith('#') && !originalUrl.startsWith('mailto:')) {
          // Check if this is an unsubscribe link
          const isUnsubscribeLink =
            originalUrl.includes('/unsubscribe') ||
            originalUrl.includes('unsubscribe=') ||
            ($(el).text() && $(el).text().toLowerCase().includes('unsubscribe'));

          if (isUnsubscribeLink) {
            // If it's already an unsubscribe link, replace it with our unsubscribe endpoint
            const unsubscribeParams = `cid=${campaignId}&lid=${leadId}`;
            const unsubscribeUrl = buildTrackingUrl(clickDomain, 'unsubscribe', unsubscribeParams);
            $(el).attr('href', unsubscribeUrl);
          } else {
            // Otherwise, add tracking to the link
            try {
              const encodedUrl = encodeURIComponent(originalUrl);
              const linkParams = `cid=${campaignId}&lid=${leadId}&url=${encodedUrl}`;
              const trackingUrl = buildTrackingUrl(clickDomain, 'link', linkParams);
              $(el).attr('href', trackingUrl);
            } catch (encodeError) {
              // Keep the original URL if encoding fails
            }
          }
        }
      } catch (linkError) {
        // Continue with other links
      }
    });

    // Add an unsubscribe footer
    const unsubscribeParams = `cid=${campaignId}&lid=${leadId}`;
    const unsubscribeUrl = buildTrackingUrl(clickDomain, 'unsubscribe', unsubscribeParams);

    $('body').append(`
      <div style="margin-top: 20px; padding-top: 10px; border-top: 1px solid #eee; font-size: 12px; color: #666; font-family: Arial, sans-serif;">
        <p>If you no longer wish to receive these emails, you can <a href="${unsubscribeUrl}" style="color: #666;">unsubscribe here</a>.</p>
      </div>
    `);

    // Return the processed HTML
    parentPort.postMessage({ html: $.html() });
  } catch (error) {
    parentPort.postMessage({ 
      error: error.message || 'Unknown error',
      stack: error.stack || 'No stack trace'
    });
  }
}

// Process the worker data
addTrackingToEmail(workerData);
