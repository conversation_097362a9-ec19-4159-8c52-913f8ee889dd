import Stripe from 'stripe';

// Use the environment variable or a fallback for development
const stripeSecretKey = process.env.STRIPE_SECRET_KEY || 'sk_test_51QJ2GtHys6qIJTYuOWy8yQ06y4NQ1mpiXdhweBqf1gOp93S5alE1UnGUf4cb1p18F0SI7MJ287spAgUaiPaSja5M000OxyF380';

export const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2025-02-24.acacia', // Use a valid API version
  typescript: true,
});

export const USAGE_PRICES = {
  EMAILS: {
    basic: 'price_basic_emails_id', // Create these in Stripe Dashboard
    pro: 'price_pro_emails_id',
    enterprise: 'price_enterprise_emails_id',
  },
  STORAGE: {
    basic: 'price_basic_storage_id',
    pro: 'price_pro_storage_id',
    enterprise: 'price_enterprise_storage_id',
  },
  API_CALLS: {
    basic: 'price_basic_api_id',
    pro: 'price_pro_api_id',
    enterprise: 'price_enterprise_api_id',
  },
};

// Fallback price IDs for development
const BASIC_PRICE_ID = process.env.STRIPE_BASIC_PRICE_ID || 'price_basic';
const PRO_PRICE_ID = process.env.STRIPE_PRO_PRICE_ID || 'price_pro';
const ENTERPRISE_PRICE_ID = process.env.STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise';

export const PLANS = {
  BASIC: {
    name: 'Basic',
    id: BASIC_PRICE_ID,
    priceMonthly: 10,
    features: [
      'Pay as you go emails ($0.001/email)',
      'Storage ($0.02/GB)',
      'API calls ($0.0001/call)',
      'Email support'
    ],
    usagePrices: [
      USAGE_PRICES.EMAILS.basic,
      USAGE_PRICES.STORAGE.basic,
      USAGE_PRICES.API_CALLS.basic,
    ]
  },
  PRO: {
    name: 'Pro',
    id: PRO_PRICE_ID,
    priceMonthly: 29,
    features: [
      'Up to 10,000 emails/month',
      '5 email accounts',
      'Advanced analytics',
      'Priority support',
      'Custom domains'
    ],
    usagePrices: [
      USAGE_PRICES.EMAILS.pro,
      USAGE_PRICES.STORAGE.pro,
      USAGE_PRICES.API_CALLS.pro,
    ]
  },
  ENTERPRISE: {
    name: 'Enterprise',
    id: ENTERPRISE_PRICE_ID,
    priceMonthly: 99,
    features: [
      'Unlimited emails',
      'Unlimited email accounts',
      'Enterprise analytics',
      'Dedicated support',
      'Custom integration',
      'SLA guarantee'
    ],
    usagePrices: [
      USAGE_PRICES.EMAILS.enterprise,
      USAGE_PRICES.STORAGE.enterprise,
      USAGE_PRICES.API_CALLS.enterprise,
    ]
  }
};
