// Mock SpamAssassin client for email quality checking

interface CheckOptions {
  html: string;
  subject: string;
  from?: string;
  to?: string;
}

interface SpamRule {
  rule: string;
  description: string;
  score: number;
}

interface CheckResult {
  score: number;
  details: SpamRule[];
}

export class SpamAssassinClient {
  async check(options: CheckOptions): Promise<CheckResult> {
    // This is a mock implementation that returns random scores
    // In a real implementation, this would call an actual SpamAssassin service
    
    const mockRules: SpamRule[] = [
      {
        rule: 'HTML_MESSAGE',
        description: 'HTML formatting in message',
        score: 0.1,
      },
      {
        rule: 'MIME_HTML_ONLY',
        description: 'Message only has HTML content',
        score: 0.2,
      },
      {
        rule: 'UPPERCASE_25_50',
        description: 'Message has 25-50% uppercase characters',
        score: options.subject.toUpperCase() === options.subject ? 1.5 : 0,
      },
      {
        rule: 'EXCESSIVE_EXCLAMATION',
        description: 'Message contains excessive exclamation marks',
        score: (options.subject.match(/!/g) || []).length > 2 ? 1.2 : 0,
      },
    ];

    // Calculate total score
    const activeRules = mockRules.filter(rule => rule.score > 0);
    const totalScore = activeRules.reduce((sum, rule) => sum + rule.score, 0);

    return {
      score: totalScore,
      details: activeRules,
    };
  }
}
