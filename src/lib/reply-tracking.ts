import { logger } from './logger';
import { TrackingService } from '@/services/tracking.service';
import { prisma } from './prisma';

/**
 * Process an incoming email to check if it's a reply to a tracked email
 * @param email The parsed email object
 * @returns Success status
 */
export async function processIncomingEmailForReplyTracking(email: {
  to?: string | string[];
  from?: string;
  subject?: string;
  messageId?: string;
  inReplyTo?: string;
  text?: string;
  html?: string;
  headers?: Record<string, any>;
}): Promise<boolean> {
  try {
    // Check if this is a reply to a tracked email
    if (!email.to) {
      logger.warn('No recipient in incoming email', { messageId: email.messageId });
      return false;
    }

    // Handle both string and array formats for 'to'
    const recipients = Array.isArray(email.to) ? email.to : [email.to];

    // Check each recipient to see if it's a tracking address
    for (const recipient of recipients) {
      // Try to parse the tracking information from the recipient
      const { campaignId, leadId } = TrackingService.parseReplyTrackingAddress(recipient);

      if (campaignId && leadId) {
        logger.info('Found tracking information in recipient', {
          recipient,
          campaignId,
          leadId,
          messageId: email.messageId
        });

        // Track the reply
        await TrackingService.trackReply(
          campaignId,
          leadId,
          email.messageId || `reply-${Date.now()}`,
          {
            subject: email.subject,
            text: email.text,
            html: email.html,
            from: email.from,
            inReplyTo: email.inReplyTo
          }
        );

        // Store the email in the database for reference
        try {
          // First, find a valid email account to associate with this email
          // We'll try to find an email account associated with the campaign
          const campaign = await prisma.campaign.findUnique({
            where: { id: campaignId },
            include: {
              steps: {
                take: 1,
                include: {
                  emailAccount: true
                }
              }
            }
          });

          // Get the first email account we can find
          const emailAccountId = campaign?.steps[0]?.emailAccount?.id;

          if (!emailAccountId) {
            // If we can't find an email account, we'll try to find any email account in the system
            const anyEmailAccount = await prisma.emailAccount.findFirst({
              select: { id: true }
            });

            if (!anyEmailAccount) {
              logger.warn('No email account found to associate with received email', {
                campaignId,
                leadId,
                messageId: email.messageId
              });
              // Skip storing the email if we can't find an email account
              return true;
            }

            // Use the first email account we found
            await prisma.receivedEmail.create({
              data: {
                emailAccountId: anyEmailAccount.id,
                messageId: email.messageId || `reply-${Date.now()}`,
                inReplyTo: email.inReplyTo,
                from: email.from || '',
                to: Array.isArray(email.to) ? email.to.join(', ') : email.to,
                subject: email.subject || '',
                textBody: email.text || '',
                htmlBody: email.html || '',
                receivedAt: new Date(),
                headers: email.headers || {},
                campaignId,
                isRepliedTo: false,
                leadId
              }
            });
          } else {
            // Use the email account from the campaign
            await prisma.receivedEmail.create({
              data: {
                emailAccountId,
                messageId: email.messageId || `reply-${Date.now()}`,
                inReplyTo: email.inReplyTo,
                from: email.from || '',
                to: Array.isArray(email.to) ? email.to.join(', ') : email.to,
                subject: email.subject || '',
                textBody: email.text || '',
                htmlBody: email.html || '',
                receivedAt: new Date(),
                headers: email.headers || {},
                campaignId,
                isRepliedTo: false,
                leadId
              }
            });
          }

          logger.info('Stored reply email in database', {
            campaignId,
            leadId,
            messageId: email.messageId
          });
        } catch (dbError) {
          logger.error('Error storing reply email in database', {
            error: dbError instanceof Error ? dbError.message : 'Unknown error',
            campaignId,
            leadId,
            messageId: email.messageId,
            stack: dbError instanceof Error ? dbError.stack : undefined
          });
          // Continue processing even if DB storage fails
        }

        return true;
      }
    }

    // No tracking information found
    return false;
  } catch (error) {
    logger.error('Error processing incoming email for reply tracking', {
      error: error instanceof Error ? error.message : 'Unknown error',
      messageId: email.messageId,
      stack: error instanceof Error ? error.stack : undefined
    });
    return false;
  }
}

/**
 * Generate a unique reply-to address for tracking replies
 * @param campaignId The campaign ID
 * @param leadId The lead ID
 * @param baseEmail The base email address (e.g., <EMAIL>)
 * @returns The unique reply-to address
 */
export function generateReplyTrackingAddress(
  campaignId: string,
  leadId: string,
  baseEmail: string
): string {
  return TrackingService.generateReplyTrackingAddress(campaignId, leadId, baseEmail);
}

/**
 * Parse a tracking reply-to address to extract campaign and lead IDs
 * @param replyToEmail The reply-to email address
 * @returns The extracted campaign and lead IDs
 */
export function parseReplyTrackingAddress(replyToEmail: string): { campaignId?: string; leadId?: string } {
  return TrackingService.parseReplyTrackingAddress(replyToEmail);
}
