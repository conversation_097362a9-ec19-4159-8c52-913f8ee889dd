import cheerio from 'cheerio';
import { logger } from './logger';
import {
  getTrackingBaseUrl,
  generateOpenTrackingUrl,
  generateLinkTrackingUrl,
  generateUnsubscribeUrl,
  generateTrackingPixelHtml,
  generateUnsubscribeFooter
} from './trackingUrl';

/**
 * Add tracking pixel, link tracking, and unsubscribe link to an HTML email
 */
export function addTrackingToEmail(
  html: string,
  campaignId: string,
  leadId: string,
  baseUrl: string,
  trackingDomains?: {
    openDomain?: string;
    clickDomain?: string;
    bounceDomain?: string;
    source?: 'custom' | 'sender' | 'default';
  }
): string {
  try {
    // Check if HTML is empty or not a string
    if (!html || typeof html !== 'string') {
      console.error('Invalid HTML content for campaign', campaignId, 'for lead', leadId);
      return '';
    }

    // Ensure HTML has a basic structure
    let processedHtml = html;
    try {
      if (!html.includes('<body')) {
        processedHtml = `<html><body>${html}</body></html>`;
      }
    } catch (error) {
      console.error('Error checking for body tag:', error);
      processedHtml = `<html><body>${html}</body></html>`;
    }

    // Parse the HTML
    const $ = cheerio.load(processedHtml);

    // Get the base URL for tracking
    const trackingBaseUrl = baseUrl || getTrackingBaseUrl();

    // Log the tracking base URL
    logger.info('Using tracking base URL for email', {
      trackingBaseUrl,
      campaignId,
      leadId,
      trackingDomains,
      timestamp: new Date().toISOString()
    });

    // Generate multiple tracking pixels with different formats to increase detection chances
    console.log(`📧 ADDING TRACKING TO EMAIL`);
    console.log(`📧 Campaign ID: ${campaignId}`);
    console.log(`📧 Lead ID: ${leadId}`);
    console.log(`📧 Base URL: ${trackingBaseUrl}`);
    console.log(`📧 Tracking Domains:`, JSON.stringify(trackingDomains, null, 2));
    console.log(`📧 Timestamp: ${new Date().toISOString()}`);

    // Main tracking pixel (standard format)
    const mainTrackingPixelUrl = `${trackingBaseUrl}/api/tracking/pixel?cid=${campaignId}&lid=${leadId}&t=${Date.now()}`;
    const mainTrackingPixelHtml = `<img src="${mainTrackingPixelUrl}" width="1" height="1" alt=""
      style="display:none !important; max-height:1px !important; max-width:1px !important; border:0 !important; margin:0 !important; padding:0 !important; outline:none !important; position:absolute !important; opacity:0 !important;" />`;

    console.log(`📧 Generated main tracking pixel URL: ${mainTrackingPixelUrl}`);

    // Second tracking pixel with different timestamp and style
    const timestamp2 = Date.now() + 1;
    const pixel2Url = `${trackingBaseUrl}/api/tracking/pixel?cid=${campaignId}&lid=${leadId}&t=${timestamp2}&v=2`;
    const pixel2Html = `<img src="${pixel2Url}" width="0" height="0" alt=""
      style="position:absolute; visibility:hidden; display:block; width:0; height:0; padding:0; margin:0; line-height:0;" />`;

    // Third tracking pixel with different format (background image)
    const timestamp3 = Date.now() + 2;
    const pixel3Url = `${trackingBaseUrl}/api/tracking/pixel?cid=${campaignId}&lid=${leadId}&t=${timestamp3}&v=3`;
    const pixel3Html = `<div style="background-image:url('${pixel3Url}'); background-repeat:no-repeat; background-position:-9999px -9999px; height:1px; width:1px;"></div>`;

    // Fourth tracking pixel with different query parameter order
    const timestamp4 = Date.now() + 3;
    const pixel4Url = `${trackingBaseUrl}/api/tracking/pixel?t=${timestamp4}&lid=${leadId}&cid=${campaignId}&v=4`;
    const pixel4Html = `<img src="${pixel4Url}" width="1" height="1" alt=""
      style="display:none !important; position:fixed; left:-100px; top:-100px;" />`;

    // Log the tracking URLs
    logger.info('Adding multiple tracking pixels for improved detection', {
      mainTrackingPixelUrl,
      pixel2Url,
      pixel3Url,
      pixel4Url,
      campaignId,
      leadId,
      timestamp: new Date().toISOString(),
      trackingDomains,
      pixelLocations: [
        'body start (prepend)',
        'body end (append)',
        'after first paragraph/div',
        'before last element'
      ]
    });

    // Add tracking pixels at strategic locations throughout the email

    // 1. At the beginning of the body (most reliable for Gmail)
    $('body').prepend(mainTrackingPixelHtml);

    // 2. At the end of the body
    $('body').append(pixel2Html);

    // 3. After the first paragraph or div if it exists
    const firstParagraph = $('p').first();
    if (firstParagraph.length > 0) {
      firstParagraph.after(pixel3Html);
    } else {
      const firstDiv = $('div').first();
      if (firstDiv.length > 0) {
        firstDiv.after(pixel3Html);
      } else {
        // If no paragraphs or divs, add it to the body
        $('body').append(pixel3Html);
      }
    }

    // 4. Before the last element in the body
    const lastElement = $('body > *').last();
    if (lastElement.length > 0) {
      lastElement.before(pixel4Html);
    } else {
      $('body').append(pixel4Html);
    }

    console.log(`📧 Added ${4} tracking pixels to email`);
    console.log(`📧 Pixel locations: body start, body end, after first paragraph/div, before last element`);

    // Replace all links with tracking links
    let linkCount = 0;
    let unsubscribeLinkCount = 0;
    $('a').each((i, el) => {
      try {
        const originalUrl = $(el).attr('href');
        if (originalUrl && !originalUrl.startsWith('#') && !originalUrl.startsWith('mailto:')) {
          // Check if this is an unsubscribe link
          const isUnsubscribeLink =
            originalUrl.includes('/unsubscribe') ||
            originalUrl.includes('unsubscribe=') ||
            ($(el).text() && $(el).text().toLowerCase().includes('unsubscribe'));

          if (isUnsubscribeLink) {
            // If it's already an unsubscribe link, replace it with our unsubscribe endpoint
            const unsubscribeUrl = generateUnsubscribeUrl(campaignId, leadId);
            unsubscribeLinkCount++;

            console.log(`📧 Replacing unsubscribe link #${unsubscribeLinkCount}:`);
            console.log(`📧 Original URL: ${originalUrl}`);
            console.log(`📧 New URL: ${unsubscribeUrl}`);
            $(el).attr('href', unsubscribeUrl);
          } else {
            // Otherwise, add tracking to the link
            try {
              // Use the main application URL for all tracking to avoid DNS issues
              const trackingUrl = generateLinkTrackingUrl(campaignId, leadId, originalUrl);
              linkCount++;

              console.log(`📧 Adding tracking to link #${linkCount}:`);
              console.log(`📧 Original URL: ${originalUrl}`);
              console.log(`📧 Tracking URL: ${trackingUrl}`);
              logger.info('Adding tracking to link', {
                originalUrl,
                trackingUrl,
                baseUrl: getTrackingBaseUrl(),
                campaignId,
                leadId,
                linkNumber: linkCount,
                timestamp: new Date().toISOString()
              });
              $(el).attr('href', trackingUrl);
            } catch (encodeError) {
              console.error(`❌ Error encoding URL #${linkCount}:`, originalUrl, encodeError);
              logger.error('Error encoding URL for tracking', {
                error: encodeError instanceof Error ? encodeError.message : 'Unknown error',
                originalUrl,
                campaignId,
                leadId,
                linkNumber: linkCount
              });
              // Keep the original URL if encoding fails
            }
          }
        }
      } catch (linkError) {
        console.error(`❌ Error processing link #${linkCount}:`, linkError);
        logger.error('Error processing link for tracking', {
          error: linkError instanceof Error ? linkError.message : 'Unknown error',
          campaignId,
          leadId,
          linkNumber: linkCount
        });
        // Continue with other links
      }
    });

    console.log(`📧 Email tracking summary:`);
    console.log(`📧 Total links processed: ${linkCount + unsubscribeLinkCount}`);
    console.log(`📧 Regular links with tracking: ${linkCount}`);
    console.log(`📧 Unsubscribe links: ${unsubscribeLinkCount}`);
    console.log(`📧 Tracking pixels added: 4`);
    console.log(`📧 Campaign ID: ${campaignId}`);
    console.log(`📧 Lead ID: ${leadId}`);
    console.log(`📧 Timestamp: ${new Date().toISOString()}`);

    logger.info('Email tracking summary', {
      campaignId,
      leadId,
      totalLinks: linkCount + unsubscribeLinkCount,
      regularLinks: linkCount,
      unsubscribeLinks: unsubscribeLinkCount,
      trackingPixels: 4,
      timestamp: new Date().toISOString(),
      trackingDomains
    });

    // Check if the email already has an unsubscribe link
    let hasUnsubscribeLink = false;
    try {
      hasUnsubscribeLink = $('a').toArray().some(el => {
        try {
          const href = $(el).attr('href') || '';
          const text = $(el).text().toLowerCase();
          return href.includes('/unsubscribe') ||
                 href.includes('unsubscribe=') ||
                 text.includes('unsubscribe');
        } catch (error) {
          return false;
        }
      });
    } catch (error) {
      console.error('Error checking for unsubscribe links:', error);
    }

    // Always add an unsubscribe link at the bottom
    try {
      // Generate the unsubscribe footer using the utility function
      const unsubscribeFooter = generateUnsubscribeFooter(campaignId, leadId);
      const unsubscribeUrl = generateUnsubscribeUrl(campaignId, leadId);

      console.log('Adding unsubscribe link:', unsubscribeUrl);
      logger.info('Adding unsubscribe link', { unsubscribeUrl, campaignId, leadId });

      // Add the unsubscribe footer
      $('body').append(unsubscribeFooter);
    } catch (error) {
      console.error('Error adding unsubscribe link:', error);
    }

    return $.html();
  } catch (error) {
    console.error('Error adding tracking to email:', error);
    return html; // Return the original HTML if there's an error
  }
}
