import { logger } from './logger';
import cheerio from 'cheerio';

/**
 * A simplified tracking implementation that uses the static-gif endpoint
 * This is a more reliable approach for email open tracking
 */
export function addTrackingToEmail(
  html: string,
  campaignId: string,
  leadId: string,
  baseUrl: string
): string {
  try {
    logger.info('Adding simplified tracking to email', { campaignId, leadId });
    
    // Load the HTML into cheerio
    const $ = cheerio.load(html);
    
    // Build the tracking pixel URL with cache-busting
    const timestamp = new Date().getTime();
    const trackingPixelUrl = `${baseUrl}/api/tracking/static-gif?cid=${campaignId}&lid=${leadId}&t=${timestamp}`;
    
    // Create tracking pixel HTML
    const trackingPixelHtml = `<img src="${trackingPixelUrl}" width="1" height="1" alt="" style="display:none !important; max-height:1px !important; max-width:1px !important; border:0 !important; margin:0 !important; padding:0 !important; outline:none !important; position:absolute !important; opacity:0 !important;" />`;
    
    // Add tracking pixels at strategic locations for maximum compatibility
    
    // 1. At the beginning of the body
    $('body').prepend(trackingPixelHtml);
    
    // 2. At the end of the body
    $('body').append(trackingPixelHtml);
    
    // 3. Directly before the closing body tag
    const bodyHtml = $('body').html();
    $('body').html(bodyHtml + trackingPixelHtml);
    
    logger.info('Added simplified tracking pixels to email', {
      campaignId,
      leadId,
      pixelUrl: trackingPixelUrl
    });
    
    return $.html();
  } catch (error) {
    logger.error('Error adding simplified tracking to email', {
      error: error instanceof Error ? error.message : 'Unknown error',
      campaignId,
      leadId
    });
    
    // Return the original HTML if there's an error
    return html;
  }
}
