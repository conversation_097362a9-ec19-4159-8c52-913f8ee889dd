import { logger } from './logger';

/**
 * A lightweight tracking implementation that uses string manipulation instead of DOM parsing
 * This is much faster than using Cheerio for HTML manipulation
 */
export function addTrackingToEmail(
  html: string,
  campaignId: string,
  leadId: string,
  baseUrl: string,
  trackingDomains?: {
    openDomain?: string;
    clickDomain?: string;
    bounceDomain?: string;
    source?: 'custom' | 'sender' | 'default';
  }
): string {
  try {
    // Check if HTML is empty or not a string
    if (!html || typeof html !== 'string') {
      logger.error('Invalid HTML content for campaign', { campaignId, leadId });
      return '';
    }

    // Log the original HTML structure for debugging
    logger.info('Original HTML before tracking', {
      campaignId,
      leadId,
      htmlLength: html.length,
      hasBodyTag: html.includes('<body'),
      htmlPreview: html.substring(0, 200) + (html.length > 200 ? '...' : '')
    });

    // Ensure HTML has a basic structure
    let processedHtml = html;
    if (!html.includes('<body')) {
      processedHtml = `<html><body>${html}</body></html>`;
      logger.info('Added body wrapper to HTML', { campaignId, leadId });
    }

    // Determine the tracking domains to use
    const defaultOpenDomain = process.env.DEFAULT_OPEN_TRACKING_DOMAIN || `${baseUrl}/api/tracking`;
    const defaultClickDomain = process.env.DEFAULT_CLICK_TRACKING_DOMAIN || `${baseUrl}/api/tracking`;

    // Use provided tracking domains or fall back to defaults
    const openDomain = trackingDomains?.openDomain || defaultOpenDomain;
    const clickDomain = trackingDomains?.clickDomain || defaultClickDomain;
    const domainSource = trackingDomains?.source || 'default';

    // Helper function to build tracking URL based on domain type
    const buildTrackingUrl = (domain: string, path: string, params: string) => {
      // If domain includes a full URL path (e.g., localhost:3000/api/tracking)
      if (domain.includes('/')) {
        return `https://${domain}/${path}?${params}`;
      }

      // If it's a custom domain (just the domain name)
      if (domainSource === 'custom' || domainSource === 'sender') {
        // For custom domains, use the standard tracking path structure
        return `https://${domain}/api/tracking/${path}?${params}`;
      }

      // For default domains, use the application URL structure
      return `${baseUrl}/api/tracking/${path}?${params}`;
    };

    // Build the tracking pixel URL
    const trackingParams = `cid=${campaignId}&lid=${leadId}`;
    const timestamp = new Date().getTime();
    const trackingPixelUrl = `${buildTrackingUrl(openDomain, 'pixel', trackingParams)}&t=${timestamp}`;

    // Create tracking pixel HTML
    const trackingPixelHtml = `<img src="${trackingPixelUrl}" width="1" height="1" alt="" style="display:none !important; max-height:1px !important; max-width:1px !important; border:0 !important; margin:0 !important; padding:0 !important; outline:none !important; position:absolute !important; opacity:0 !important;" />`;

    // Find the body tag positions
    const bodyStartIndex = processedHtml.indexOf('<body');
    const bodyEndIndex = processedHtml.lastIndexOf('</body>');

    if (bodyStartIndex !== -1 && bodyEndIndex !== -1) {
      // Find the position right after the opening body tag
      const bodyTagEndIndex = processedHtml.indexOf('>', bodyStartIndex) + 1;

      // Insert tracking pixel at the beginning of the body
      processedHtml =
        processedHtml.substring(0, bodyTagEndIndex) +
        trackingPixelHtml +
        processedHtml.substring(bodyTagEndIndex);

      // Insert tracking pixel before the closing body tag
      processedHtml =
        processedHtml.substring(0, bodyEndIndex) +
        trackingPixelHtml +
        processedHtml.substring(bodyEndIndex);
    } else {
      // If no body tags found, just append to the end
      processedHtml += trackingPixelHtml;
    }

    // Process links - this is more complex with string manipulation
    // We'll use a simple regex approach for basic link replacement
    // For more complex cases, we'd need a more sophisticated parser

    // Create the unsubscribe URL
    const unsubscribeParams = `cid=${campaignId}&lid=${leadId}`;
    const unsubscribeUrl = buildTrackingUrl(clickDomain, 'unsubscribe', unsubscribeParams);

    // Add unsubscribe footer
    const unsubscribeFooter = `
      <div style="margin-top: 20px; padding-top: 10px; border-top: 1px solid #eee; font-size: 12px; color: #666; font-family: Arial, sans-serif;">
        <p>If you no longer wish to receive these emails, you can <a href="${unsubscribeUrl}" style="color: #666;">unsubscribe here</a>.</p>
      </div>
    `;

    // Add the unsubscribe footer at the very end of the email content
    // First, let's find the updated body end index after adding tracking pixels
    const updatedBodyEndIndex = processedHtml.lastIndexOf('</body>');

    if (updatedBodyEndIndex !== -1) {
      // Insert the unsubscribe footer just before the closing body tag
      processedHtml =
        processedHtml.substring(0, updatedBodyEndIndex) +
        unsubscribeFooter +
        processedHtml.substring(updatedBodyEndIndex);
    } else {
      // If no body tag, check if we have HTML structure
      const htmlEndIndex = processedHtml.lastIndexOf('</html>');
      if (htmlEndIndex !== -1) {
        // Insert before closing html tag
        processedHtml =
          processedHtml.substring(0, htmlEndIndex) +
          unsubscribeFooter +
          processedHtml.substring(htmlEndIndex);
      } else {
        // No HTML structure, just append to the very end
        processedHtml += unsubscribeFooter;
      }
    }

    logger.info('Added unsubscribe footer to email', {
      campaignId,
      leadId,
      hasBodyTag: updatedBodyEndIndex !== -1,
      htmlLength: processedHtml.length
    });

    logger.info('Added optimized tracking to email', {
      campaignId,
      leadId,
      pixelUrl: trackingPixelUrl,
      unsubscribeUrl
    });

    // Log the final HTML structure for debugging
    logger.info('Final HTML after tracking', {
      campaignId,
      leadId,
      finalHtmlLength: processedHtml.length,
      hasBodyTag: processedHtml.includes('<body'),
      hasUnsubscribeFooter: processedHtml.includes('unsubscribe here'),
      finalHtmlPreview: processedHtml.substring(0, 300) + (processedHtml.length > 300 ? '...' : '')
    });

    return processedHtml;
  } catch (error) {
    logger.error('Error adding optimized tracking to email', {
      error: error instanceof Error ? error.message : 'Unknown error',
      campaignId,
      leadId
    });

    // Return the original HTML if there's an error
    return html;
  }
}
