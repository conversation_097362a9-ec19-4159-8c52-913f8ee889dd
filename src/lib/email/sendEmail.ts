import nodemailer from 'nodemailer';
import { logger } from '../logger';

interface EmailOptions {
  to: string;
  subject: string;
  text: string;
  html: string;
}

export const sendEmail = async ({ to, subject, text, html }: EmailOptions) => {
  logger.info(`Attempting to send email to ${to}`, { subject });

  try {
    // Check if SMTP settings are configured
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
      logger.error('SMTP settings are not properly configured', {
        host: process.env.SMTP_HOST ? 'Set' : 'Missing',
        user: process.env.SMTP_USER ? 'Set' : 'Missing',
        password: process.env.SMTP_PASSWORD ? 'Set' : 'Missing',
      });

      // In development, use a fallback method to log the email content
      if (process.env.NODE_ENV === 'development') {
        logger.info('DEVELOPMENT MODE: Email would have been sent with the following content:', {
          to,
          subject,
          text,
          html: html.substring(0, 200) + '...' // Truncate HTML for logging
        });

        // Return a mock success response
        return {
          messageId: `mock-${Date.now()}@localhost`,
          response: 'Mock email sent in development mode',
          accepted: [to],
          rejected: [],
        };
      } else {
        throw new Error('SMTP settings are not properly configured');
      }
    }

    // Log SMTP settings for debugging (without showing the full password)
    const smtpPassword = process.env.SMTP_PASSWORD || '';
    logger.info('SMTP Configuration', {
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: process.env.SMTP_PORT === '465', // Port 465 should use secure=true
      user: process.env.SMTP_USER,
      from: process.env.SMTP_FROM,
      passwordLength: smtpPassword.length,
      passwordFirstChar: smtpPassword.charAt(0),
      passwordLastChar: smtpPassword.charAt(smtpPassword.length - 1),
    });

    // Create transporter with more robust configuration
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '465'),
      secure: process.env.SMTP_PORT === '465', // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        // Make sure to trim any quotes that might have been added in the .env file
        pass: smtpPassword.replace(/^["'](.+)["']$/, '$1'),
      },
      tls: {
        // Do not fail on invalid certs
        rejectUnauthorized: false
      },
      debug: true, // Enable debug output
      logger: true, // Log to console for better debugging
      pool: true, // Use pooled connections
      maxConnections: 5, // Maximum number of connections to make
      maxMessages: 100, // Maximum number of messages to send per connection
    });

    // Verify connection configuration
    logger.info('Verifying SMTP connection...');
    try {
      await new Promise((resolve, reject) => {
        transporter.verify(function (error, success) {
          if (error) {
            logger.error('SMTP connection verification failed:', error);
            reject(error);
          } else {
            logger.info('SMTP connection verified successfully:', success);
            resolve(success);
          }
        });
      });
    } catch (verifyError) {
      logger.error('Failed to verify SMTP connection:', verifyError);

      // In development, use a fallback method
      if (process.env.NODE_ENV === 'development') {
        logger.info('DEVELOPMENT MODE: Email would have been sent despite SMTP verification failure:', {
          to,
          subject,
          text,
          html: html.substring(0, 200) + '...' // Truncate HTML for logging
        });

        // Return a mock success response
        return {
          messageId: `mock-${Date.now()}@localhost`,
          response: 'Mock email sent in development mode (SMTP verification failed)',
          accepted: [to],
          rejected: [],
        };
      } else {
        throw new Error(`SMTP connection verification failed: ${verifyError instanceof Error ? verifyError.message : 'Unknown error'}`);
      }
    }

    // Send mail with retry logic
    logger.info(`Sending email to ${to}...`);

    let retries = 3;
    let lastError = null;

    while (retries > 0) {
      try {
        const info = await transporter.sendMail({
          from: process.env.SMTP_FROM || '"Avian Email" <<EMAIL>>',
          to,
          subject,
          text,
          html,
          // Add headers for better deliverability
          headers: {
            'X-Priority': '1', // High priority
            'X-MSMail-Priority': 'High',
            'Importance': 'High'
          }
        });

        logger.info('Email sent successfully', {
          messageId: info.messageId,
          response: info.response,
          accepted: info.accepted,
          rejected: info.rejected,
          to,
          subject
        });

        return info;
      } catch (sendError) {
        lastError = sendError;
        retries--;

        if (retries > 0) {
          logger.warn(`Failed to send email, retrying (${retries} attempts left):`, {
            error: sendError instanceof Error ? sendError.message : 'Unknown error',
            to,
            subject
          });

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    // If we get here, all retries failed
    throw lastError;
  } catch (error) {
    logger.error(`Failed to send email to ${to}:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      subject,
    });

    // In development, use a fallback method
    if (process.env.NODE_ENV === 'development') {
      logger.info('DEVELOPMENT MODE: Email would have been sent despite error:', {
        to,
        subject,
        text,
        html: html.substring(0, 200) + '...' // Truncate HTML for logging
      });

      // Return a mock success response
      return {
        messageId: `mock-${Date.now()}@localhost`,
        response: 'Mock email sent in development mode (error occurred)',
        accepted: [to],
        rejected: [],
      };
    }

    throw new Error(`Failed to send email: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};