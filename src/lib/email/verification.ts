import { prisma } from '../prisma';
import { sendEmail } from './sendEmail';
import crypto from 'crypto';
import { logger } from '../logger';

export const emailVerification = {
  sendVerificationEmail: async (userId: string, email: string) => {
    logger.info(`Sending verification email to ${email} for user ${userId}`);

    const token = crypto.randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Check if there's an existing token for this email and delete it
    const existingToken = await prisma.verificationToken.findFirst({
      where: { identifier: email }
    });

    if (existingToken) {
      logger.info(`Deleting existing verification token for ${email}`);
      try {
        await prisma.verificationToken.delete({
          where: { token: existingToken.token }
        });
        logger.info(`Successfully deleted existing token for ${email}`);
      } catch (deleteError) {
        logger.error(`Error deleting existing token for ${email}:`, deleteError);
        // Continue anyway - we'll create a new token
      }
    }

    // Create a new token
    try {
      const newToken = await prisma.verificationToken.create({
        data: {
          identifier: email,
          token,
          expires
        }
      });

      logger.info(`Created new verification token for ${email}`, {
        token: newToken.token.substring(0, 10) + '...',
        expires: newToken.expires
      });
    } catch (createError) {
      logger.error(`Error creating verification token for ${email}:`, createError);
      throw new Error(`Failed to create verification token: ${createError instanceof Error ? createError.message : 'Unknown error'}`);
    }

    const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify?token=${token}`;
    logger.info(`Verification URL for ${email}: ${verificationUrl.replace(token, token.substring(0, 10) + '...')}`);

    try {
      // Send the email and return the result
      const emailResult = await sendEmail({
        to: email,
        subject: 'Verify your email address',
        text: `Please verify your email by clicking: ${verificationUrl}`,
        html: `
          <h1>Email Verification</h1>
          <p>Please verify your email by clicking the link below:</p>
          <a href="${verificationUrl}" style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 15px;">Verify Email</a>
          <p style="margin-top: 20px;">If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p>${verificationUrl}</p>
          <p>This link will expire in 24 hours.</p>
        `
      });

      logger.info(`Verification email sent successfully to ${email}`, {
        messageId: emailResult.messageId,
        response: emailResult.response
      });

      return emailResult;
    } catch (error) {
      logger.error(`Failed to send verification email to ${email}:`, error);

      // In development mode, don't throw an error
      if (process.env.NODE_ENV === 'development') {
        logger.info(`DEVELOPMENT MODE: Returning mock email result for ${email}`);
        return {
          messageId: `mock-${Date.now()}@localhost`,
          response: 'Mock email sent in development mode',
          accepted: [email],
          rejected: [],
          verificationUrl // Include the verification URL for debugging
        };
      }

      throw error;
    }
  },

  verifyEmail: async (token: string) => {
    logger.info(`Verifying email with token: ${token.substring(0, 10)}...`);

    // Log all tokens in the database for debugging
    const allTokens = await prisma.verificationToken.findMany({
      take: 5
    });

    logger.info(`Found ${allTokens.length} tokens in database:`, {
      tokens: allTokens.map(t => ({
        tokenPrefix: t.token.substring(0, 10) + '...',
        identifier: t.identifier,
        expires: t.expires,
        isExpired: t.expires < new Date()
      }))
    });

    const verificationToken = await prisma.verificationToken.findUnique({
      where: { token }
    });

    if (!verificationToken) {
      logger.error(`Invalid verification token: ${token.substring(0, 10)}...`);

      // Try to find the user by extracting email from token (if token contains email)
      let email = null;
      try {
        // Some tokens might contain the email in a specific format
        // This is a heuristic approach to try to find the user
        if (token.includes('@')) {
          email = token.split('@')[0] + '@' + token.split('@')[1].split('.')[0] + '.' + token.split('@')[1].split('.')[1];
          logger.info(`Extracted email from token: ${email}`);

          // Try to find the user by email
          const user = await prisma.user.findUnique({
            where: { email },
            select: {
              id: true,
              email: true,
              emailVerified: true
            }
          });

          if (user) {
            logger.info(`Found user by extracted email: ${user.email}`);

            // If we found the user, verify them directly
            if (!user.emailVerified) {
              logger.info(`User email not verified, verifying directly: ${user.email}`);

              // Update the user's emailVerified field
              const now = new Date();
              const nowIso = now.toISOString();

              await prisma.$executeRawUnsafe(
                `UPDATE "User" SET "emailVerified" = $1 WHERE "id" = $2`,
                nowIso,
                user.id
              );

              logger.info(`User email verified directly: ${user.email}`);

              return user;
            } else {
              logger.info(`User email already verified: ${user.email}`);
              return user;
            }
          }
        }
      } catch (extractError) {
        logger.error(`Error extracting email from token:`, extractError);
      }

      throw new Error('Invalid verification token');
    }

    logger.info(`Found verification token for identifier: ${verificationToken.identifier}`);

    if (verificationToken.expires < new Date()) {
      logger.error(`Verification token has expired: ${token.substring(0, 10)}...`);

      // Even if the token has expired, try to verify the user
      logger.info(`Attempting to verify user with expired token: ${verificationToken.identifier}`);

      // Try to find the user by email
      const user = await prisma.user.findUnique({
        where: { email: verificationToken.identifier },
        select: {
          id: true,
          email: true,
          emailVerified: true
        }
      });

      if (user && !user.emailVerified) {
        logger.info(`Found user with expired token, verifying anyway: ${user.email}`);

        // Update the user's emailVerified field
        const now = new Date();
        const nowIso = now.toISOString();

        await prisma.$executeRawUnsafe(
          `UPDATE "User" SET "emailVerified" = $1 WHERE "id" = $2`,
          nowIso,
          user.id
        );

        logger.info(`User email verified with expired token: ${user.email}`);

        // Delete the expired token
        await prisma.verificationToken.delete({ where: { token } });

        return user;
      }

      await prisma.verificationToken.delete({ where: { token } });
      throw new Error('Verification token has expired');
    }

    try {
      // Get the user first
      const user = await prisma.user.findUnique({
        where: { email: verificationToken.identifier },
        select: {
          id: true,
          email: true,
          emailVerified: true
        }
      });

      if (!user) {
        logger.error(`User not found for email: ${verificationToken.identifier}`);
        throw new Error('User not found');
      }

      logger.info(`Found user to verify: ${user.email}`, {
        userId: user.id,
        currentEmailVerified: user.emailVerified
      });

      // Use a more direct approach to update the emailVerified field
      const now = new Date();

      // Use a raw SQL query to ensure the date is stored correctly
      const nowIso = now.toISOString();
      logger.info(`Setting emailVerified to ISO string: ${nowIso}`);

      // Execute raw SQL to update the emailVerified field
      await prisma.$executeRawUnsafe(
        `UPDATE "User" SET "emailVerified" = $1 WHERE "id" = $2`,
        nowIso,
        user.id
      );

      // Fetch the updated user
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          emailVerified: true
        }
      });

      logger.info(`User email verified successfully: ${updatedUser.email}`, {
        userId: updatedUser.id,
        emailVerified: updatedUser.emailVerified,
        emailVerifiedType: typeof updatedUser.emailVerified,
        emailVerifiedIsDate: updatedUser.emailVerified instanceof Date,
        verificationTime: now.toISOString()
      });

      // Double-check that the update worked
      const verifiedUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          emailVerified: true
        }
      });

      logger.info(`Verification status after update: ${verifiedUser.email}`, {
        userId: verifiedUser.id,
        emailVerified: verifiedUser.emailVerified,
        emailVerifiedType: typeof verifiedUser.emailVerified,
        emailVerifiedIsDate: verifiedUser.emailVerified instanceof Date
      });

      // Delete the verification token
      await prisma.verificationToken.delete({ where: { token } });
      logger.info(`Verification token deleted: ${token.substring(0, 10)}...`);

      return verifiedUser;
    } catch (error) {
      logger.error(`Error updating user verification status:`, error);
      throw new Error('Failed to verify email. Please try again or contact support.');
    }
  }
};
