import Redis, { RedisOptions } from 'ioredis'
import { logger } from './logger'

// Prefer REDIS_URL, otherwise use REDIS_HOST and REDIS_PORT, otherwise fallback to localhost:6379
let redisUrl = process.env.REDIS_URL;
let redisOptions: RedisOptions = {
  // Add resilient connection options
  // BullMQ requires maxRetriesPerRequest to be null
  maxRetriesPerRequest: null,
  retryStrategy: (times) => {
    // Exponential backoff with a maximum of 30 seconds in development
    const maxDelay = process.env.NODE_ENV === 'development' ? 30000 : 5000;
    const delay = Math.min(times * 1000, maxDelay);
    logger.info(`Redis retry attempt ${times} with delay ${delay}ms`);
    return delay;
  },
  // Reconnect after disconnection
  reconnectOnError: (err) => {
    // In development, be more conservative about reconnecting
    if (process.env.NODE_ENV === 'development') {
      return false; // Don't auto-reconnect in development to save requests
    }
    const targetError = 'READONLY';
    if (err.message.includes(targetError)) {
      return true;
    }
    return false;
  },
  // Connection timeout
  connectTimeout: 10000,
  // Keep alive - longer in development to reduce requests
  keepAlive: process.env.NODE_ENV === 'development' ? 60000 : 10000,
  // Enable auto-pipelining to batch requests
  enableAutoPipelining: true,
  // Lazy connect to avoid unnecessary connections
  lazyConnect: true,
  // Reduce command timeout in development
  commandTimeout: process.env.NODE_ENV === 'development' ? 30000 : 5000,
};

if (!redisUrl) {
  const host = process.env.REDIS_HOST || 'localhost';
  const port = parseInt(process.env.REDIS_PORT || '6379');
  logger.info(`Using Redis host: ${host}, port: ${port}`);
  redisOptions.host = host;
  redisOptions.port = port;
} else {
  // If using a URL, check if it is an upstash/cloud URL and set TLS accordingly
  if (redisUrl.includes('upstash') || redisUrl.startsWith('rediss://')) {
    redisOptions.tls = {};
  }
}

const redis = redisUrl ? new Redis(redisUrl, redisOptions) : new Redis(redisOptions);

redis.on('error', (error) => {
  logger.error('Redis connection error:', error)
})

redis.on('connect', () => {
  logger.info('Redis connected successfully')
})

export default redis
