/**
 * Initialize LangChain tracing
 *
 * This function sets up Lang<PERSON>hain tracing for monitoring AI agent activities.
 * It's designed to work in both server and client environments.
 */
export function initLang<PERSON>hain() {
  // Only run on the server side
  if (typeof window !== 'undefined') {
    return;
  }

  try {
    const apiKey = process.env.LANGCHAIN_API_KEY;
    const project = process.env.LANGCHAIN_PROJECT;
    const tracingEnabled = process.env.LANGCHAIN_TRACING_V2 === 'true';

    if (!apiKey) {
      console.warn('LangChain API key is missing. Tracing will not be enabled.', {
        hasApiKey: !!apiKey,
        apiKeyLength: apiKey?.length || 0,
        environment: process.env.NODE_ENV,
      });
      return;
    }

    if (!project) {
      console.warn('LangChain project name is missing. Tracing will not be enabled.', {
        hasProject: !!project,
        environment: process.env.NODE_ENV,
      });
      return;
    }

    if (!tracingEnabled) {
      console.info('<PERSON><PERSON>hain tracing is disabled.', {
        tracingV2: process.env.LANGCHAIN_TRACING_V2,
        environment: process.env.NODE_ENV,
      });
      return;
    }

    // Lang<PERSON>hain tracing is configured via environment variables
    // These should already be set in the environment or next.config.js
    console.info(`LangChain tracing initialized for project: ${project}`, {
      tracingEnabled: true,
      endpoint: 'https://api.smith.langchain.com',
      hasApiKey: !!apiKey,
      project,
    });

    return true;
  } catch (error) {
    console.error('Error initializing LangChain',
      error instanceof Error ? error.message : 'Unknown error'
    );
  }
}
