import { Worker } from 'worker_threads';
import { logger } from './logger';
import path from 'path';

/**
 * Execute a CPU-intensive task in a worker thread
 * This helps prevent blocking the main event loop
 * 
 * @param scriptPath Path to the worker script
 * @param data Data to pass to the worker
 * @returns Promise that resolves with the worker result
 */
export function runWorkerThread<T, R>(scriptPath: string, data: T): Promise<R> {
  return new Promise((resolve, reject) => {
    try {
      // Create a worker with the specified script
      const worker = new Worker(path.resolve(process.cwd(), scriptPath), {
        workerData: data
      });
      
      // Handle worker messages
      worker.on('message', (result) => {
        resolve(result as R);
      });
      
      // Handle worker errors
      worker.on('error', (error) => {
        logger.error('Worker thread error', {
          scriptPath,
          error: error.message,
          stack: error.stack
        });
        reject(error);
      });
      
      // Handle worker exit
      worker.on('exit', (code) => {
        if (code !== 0) {
          const error = new Error(`Worker stopped with exit code ${code}`);
          logger.error('Worker thread exited with non-zero code', {
            scriptPath,
            exitCode: code
          });
          reject(error);
        }
      });
    } catch (error) {
      logger.error('Error creating worker thread', {
        scriptPath,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      reject(error);
    }
  });
}
