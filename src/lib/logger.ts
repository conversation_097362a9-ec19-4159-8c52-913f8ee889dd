import winston, { Logger } from 'winston'

// Create the logger instance
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
})

// Add file transports only in development environment
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error'
  }))

  logger.add(new winston.transports.File({
    filename: 'logs/combined.log'
  }))
}

// Add request context helper
// Extend the logger type
interface ExtendedLogger extends Logger {
  addRequestContext: (req: any) => any;
}

(logger as ExtendedLogger).addRequestContext = (req: any) => ({
  requestId: req.headers['x-request-id'],
  userId: req.user?.id,
  path: req.url,
  method: req.method,
})
