import * as Sentry from '@sentry/nextjs';
import { logger } from '@/lib/logger';

export const initSentry = () => {
  if (process.env.NEXT_PUBLIC_SENTRY_DSN) {
    Sentry.init({
      dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
      environment: process.env.NODE_ENV,
      tracesSampleRate: 1.0,
      beforeSend(event) {
        // Don't send errors in development
        if (process.env.NODE_ENV === 'development') {
          return null;
        }
        return event;
      },
    });
  } else {
    logger.warn('Sentry DSN not configured');
  }
};

export const captureException = (error: Error, context?: Record<string, any>) => {
  logger.error(error.message, { error, context });
  
  if (process.env.NEXT_PUBLIC_SENTRY_DSN) {
    Sentry.captureException(error, {
      extra: context,
    });
  }
};