/**
 * Utility functions for generating tracking URLs
 */

/**
 * Get the base URL for tracking endpoints
 * Uses environment variables if available, falls back to localhost for development
 */
export function getTrackingBaseUrl(): string {
  // First try to use the environment variable
  if (process.env.NEXT_PUBLIC_APP_URL) {
    // Add protocol if not present
    const envUrl = process.env.NEXT_PUBLIC_APP_URL;
    if (envUrl.startsWith('http')) {
      return envUrl;
    } else {
      return `https://${envUrl}`;
    }
  }

  // Fall back to default for development
  return process.env.NODE_ENV === 'production'
    ? 'https://avian-mail.wattlesol.com'
    : 'http://localhost:3000';
}

/**
 * Generate a tracking pixel URL for email opens
 */
export function generateOpenTrackingUrl(campaignId: string, leadId: string, version: number = 1): string {
  // Add a random component to help bypass email security filters
  const randomId = Math.random().toString(36).substring(2, 8);

  return `${getTrackingBaseUrl()}/api/tracking/pixel?cid=${campaignId}&lid=${leadId}&t=${Date.now()}&v=${version}&r=${randomId}`;
}

/**
 * Generate a tracking URL for link clicks
 *
 * Note: We're now using the main application URL for all tracking to avoid DNS issues
 */
export function generateLinkTrackingUrl(campaignId: string, leadId: string, url: string, clickDomain?: string): string {
  // Always use the base URL for tracking to avoid DNS issues
  const baseUrl = getTrackingBaseUrl();

  // Add a random component to help bypass email security filters
  const randomId = Math.random().toString(36).substring(2, 8);

  return `${baseUrl}/api/tracking/link?cid=${campaignId}&lid=${leadId}&url=${encodeURIComponent(url)}&r=${randomId}`;
}

/**
 * Generate an unsubscribe URL
 */
export function generateUnsubscribeUrl(campaignId: string, leadId: string): string {
  return `${getTrackingBaseUrl()}/api/tracking/unsubscribe?cid=${campaignId}&lid=${leadId}`;
}

/**
 * Generate HTML for a tracking pixel
 * @param campaignId The campaign ID
 * @param leadId The lead ID
 * @param version Optional version number for the pixel
 * @param style Optional style type (1-4) for different pixel styles
 */
export function generateTrackingPixelHtml(campaignId: string, leadId: string, version: number = 1, style: number = 1): string {
  const trackingUrl = generateOpenTrackingUrl(campaignId, leadId, version);

  // Use different styles to increase chances of detection
  switch (style) {
    case 1:
      // Standard invisible pixel
      return `<img src="${trackingUrl}" width="1" height="1" alt="" style="display:none !important; max-height:1px !important; max-width:1px !important; border:0 !important; margin:0 !important; padding:0 !important; outline:none !important; position:absolute !important; opacity:0 !important;" />`;

    case 2:
      // Zero-size pixel with visibility:hidden
      return `<img src="${trackingUrl}" width="0" height="0" alt="" style="position:absolute; visibility:hidden; display:block; width:0; height:0; padding:0; margin:0; line-height:0;" />`;

    case 3:
      // Background image approach
      return `<div style="background-image:url('${trackingUrl}'); background-repeat:no-repeat; background-position:-9999px -9999px; height:1px; width:1px;"></div>`;

    case 4:
      // Fixed position off-screen
      return `<img src="${trackingUrl}" width="1" height="1" alt="" style="display:none !important; position:fixed; left:-100px; top:-100px;" />`;

    default:
      // Default to standard invisible pixel
      return `<img src="${trackingUrl}" width="1" height="1" alt="" style="display:none !important; max-height:1px !important; max-width:1px !important; border:0 !important; margin:0 !important; padding:0 !important; outline:none !important; position:absolute !important; opacity:0 !important;" />`;
  }
}

/**
 * Wrap a URL with tracking
 *
 * @param url The original URL to wrap with tracking
 * @param campaignId The campaign ID
 * @param leadId The lead ID
 * @returns The tracking URL that will redirect to the original URL
 */
export function wrapUrlWithTracking(url: string, campaignId: string, leadId: string): string {
  // Always use the main application URL for tracking to avoid DNS issues
  const baseUrl = getTrackingBaseUrl();

  // Add a random component to help bypass email security filters
  const randomId = Math.random().toString(36).substring(2, 8);

  return `${baseUrl}/api/tracking/link?cid=${campaignId}&lid=${leadId}&url=${encodeURIComponent(url)}&r=${randomId}`;
}

/**
 * Generate unsubscribe HTML footer for emails
 */
export function generateUnsubscribeFooter(campaignId: string, leadId: string): string {
  const unsubscribeUrl = generateUnsubscribeUrl(campaignId, leadId);
  return `<div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; font-family: Arial, sans-serif;">
    <p>If you no longer wish to receive these emails, you can <a href="${unsubscribeUrl}" style="color: #666; text-decoration: underline;">unsubscribe here</a>.</p>
  </div>`;
}
