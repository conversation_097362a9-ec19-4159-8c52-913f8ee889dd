import { prisma } from './prisma'
import { logger } from './logger'

export interface AuditLogData {
  userId: string
  action: string
  resourceType: string
  resourceId?: string
  oldData?: any
  newData?: any
  metadata?: Record<string, any>
}

export const auditLog = async (data: AuditLogData) => {
  try {
    const log = await prisma.auditLog.create({
      data: {
        userId: data.userId,
        action: data.action,
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        oldData: data.oldData ? JSON.stringify(data.oldData) : null,
        newData: data.newData ? JSON.stringify(data.newData) : null,
        metadata: data.metadata ? JSON.stringify(data.metadata) : null,
        ipAddress: data.metadata?.ipAddress,
        userAgent: data.metadata?.userAgent,
        timestamp: new Date()
      }
    })

    logger.info('Audit log created', { logId: log.id, ...data })
    return log
  } catch (error) {
    logger.error('Failed to create audit log', { error, data })
    throw error
  }
}