import { NextApiRequest, NextApiResponse } from 'next'

interface RateLimitStore {
  [key: string]: {
    count: number
    resetTime: number
  }
}

const store: RateLimitStore = {}
const WINDOW_SIZE_IN_SECONDS = 60
const MAX_REQUESTS_PER_WINDOW = 100

export function rateLimit(req: NextApiRequest, res: NextApiResponse): boolean {
  // Convert potential array of IPs to single string
  const ipAddress = Array.isArray(req.headers['x-forwarded-for']) 
    ? req.headers['x-forwarded-for'][0] 
    : (req.headers['x-forwarded-for'] as string | undefined)
  const ip = ipAddress || req.socket.remoteAddress || 'unknown'
  
  const now = Date.now()
  const windowStart = now - (WINDOW_SIZE_IN_SECONDS * 1000)

  // Clean up expired entries
  Object.keys(store).forEach(key => {
    if (store[key].resetTime < now) {
      delete store[key]
    }
  })

  // Initialize or get existing window
  if (!store[ip] || store[ip].resetTime < now) {
    store[ip] = {
      count: 0,
      resetTime: now + (WINDOW_SIZE_IN_SECONDS * 1000)
    }
  }

  // Increment request count
  store[ip].count++

  // Set rate limit headers
  res.setHeader('X-RateLimit-Limit', MAX_REQUESTS_PER_WINDOW)
  res.setHeader('X-RateLimit-Remaining', Math.max(0, MAX_REQUESTS_PER_WINDOW - store[ip].count))
  res.setHeader('X-RateLimit-Reset', Math.ceil(store[ip].resetTime / 1000))

  // Check if over limit
  if (store[ip].count > MAX_REQUESTS_PER_WINDOW) {
    return false
  }

  return true
}
