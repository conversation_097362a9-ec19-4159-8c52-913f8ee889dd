import { EmailService } from '@/services/email.service';
import { logger } from './logger';

/**
 * Send an email using the EmailService
 */
export async function sendEmail({
  to,
  subject,
  html,
  emailAccountId,
  from,
  overridePassword,
}: {
  to: string;
  subject: string;
  html: string;
  emailAccountId: string;
  from?: string;
  overridePassword?: string;
}) {
  try {
    logger.info(`Sending email to ${to} using account ${emailAccountId}`);

    // Get the email service to send the email
    const result = await EmailService.sendEmail({
      to,
      subject,
      html,
      emailAccountId,
      from,
      overridePassword,
    });

    logger.info(`Email sent successfully to ${to}`);
    return result;
  } catch (error) {
    logger.error(`Failed to send email to ${to}`, {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
}
