import { logger } from './logger';
import NodeCache from 'node-cache';

/**
 * Simple in-memory cache for frequently accessed data
 * This helps reduce database queries and improves performance
 */
class CacheService {
  private cache: NodeCache;
  
  constructor() {
    // Initialize cache with standard TTL of 5 minutes and check period of 1 minute
    this.cache = new NodeCache({ 
      stdTTL: 300, 
      checkperiod: 60,
      useClones: false // For better performance
    });
    
    logger.info('Cache service initialized');
  }
  
  /**
   * Get a value from the cache
   * @param key The cache key
   * @returns The cached value or undefined if not found
   */
  get<T>(key: string): T | undefined {
    try {
      return this.cache.get<T>(key);
    } catch (error) {
      logger.error('Error getting value from cache', {
        key,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return undefined;
    }
  }
  
  /**
   * Set a value in the cache
   * @param key The cache key
   * @param value The value to cache
   * @param ttl Time to live in seconds (optional, defaults to standard TTL)
   * @returns true if successful, false otherwise
   */
  set<T>(key: string, value: T, ttl?: number): boolean {
    try {
      return this.cache.set(key, value, ttl);
    } catch (error) {
      logger.error('Error setting value in cache', {
        key,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }
  
  /**
   * Delete a value from the cache
   * @param key The cache key
   * @returns true if successful, false otherwise
   */
  delete(key: string): boolean {
    try {
      return this.cache.del(key) > 0;
    } catch (error) {
      logger.error('Error deleting value from cache', {
        key,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }
  
  /**
   * Clear the entire cache
   */
  clear(): void {
    try {
      this.cache.flushAll();
      logger.info('Cache cleared');
    } catch (error) {
      logger.error('Error clearing cache', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
  
  /**
   * Get a value from the cache or compute it if not found
   * @param key The cache key
   * @param fn Function to compute the value if not in cache
   * @param ttl Time to live in seconds (optional)
   * @returns The cached or computed value
   */
  async getOrSet<T>(key: string, fn: () => Promise<T>, ttl?: number): Promise<T> {
    const cachedValue = this.get<T>(key);
    
    if (cachedValue !== undefined) {
      return cachedValue;
    }
    
    try {
      const value = await fn();
      this.set(key, value, ttl);
      return value;
    } catch (error) {
      logger.error('Error computing value for cache', {
        key,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}

// Export a singleton instance
export const cacheService = new CacheService();
