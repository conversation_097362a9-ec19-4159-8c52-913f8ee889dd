import { prisma } from './prisma'
import { auditLog } from './audit'
// import { sendEmail } from './email'
import { logger } from './logger'

export const gdprService = {
  // Export user data
  exportUserData: async (userId: string) => {
    const userData = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        campaigns: true,
        emailAccounts: true,
        auditLogs: true,
        consent: true  // Include the consent relation instead of settings
      }
    })

    await auditLog({
      userId,
      action: 'EXPORT_DATA',
      resourceType: 'USER',
      resourceId: userId
    })

    return userData
  },

  // Delete user data
  deleteUserData: async (userId: string) => {
    // Start a transaction to ensure all data is deleted
    await prisma.$transaction(async (tx) => {
      // Delete related data first
      await tx.campaign.deleteMany({ where: { userId } })
      await tx.emailAccount.deleteMany({ where: { userId } })
      await tx.auditLog.deleteMany({ where: { userId } })
      await tx.userConsent.deleteMany({ where: { userId } })
      
      // Finally delete the user
      await tx.user.delete({ where: { id: userId } })
    })

    await auditLog({
      userId,
      action: 'DELETE_DATA',
      resourceType: 'USER',
      resourceId: userId
    })
  },

  // Update user consent
  updateConsent: async (userId: string, consents: Record<string, boolean>) => {
    try {
      // Log incoming data
      logger.debug('Updating consent', { userId, consents });

      // Verify user exists
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        logger.error('User not found during consent update', { userId });
        throw new Error('User not found');
      }

      // Validate consent data
      const validConsentFields = ['marketingEmails', 'analyticsTracking', 'thirdPartySharing'];
      const invalidFields = Object.keys(consents).filter(key => !validConsentFields.includes(key));
      
      if (invalidFields.length > 0) {
        logger.error('Invalid consent fields', { invalidFields });
        throw new Error(`Invalid consent fields: ${invalidFields.join(', ')}`);
      }

      // Ensure all consent values are boolean
      const sanitizedConsents = Object.entries(consents).reduce((acc, [key, value]) => ({
        ...acc,
        [key]: Boolean(value)
      }), {});

      // Update or create consent record
      const updated = await prisma.userConsent.upsert({
        where: { userId },
        create: {
          userId,
          ...sanitizedConsents,
          updatedAt: new Date()
        },
        update: {
          ...sanitizedConsents,
          updatedAt: new Date()
        }
      });

      logger.debug('Consent updated successfully', { userId, updated });

      await auditLog({
        userId,
        action: 'UPDATE_CONSENT',
        resourceType: 'USER_CONSENT',
        resourceId: userId,
        newData: sanitizedConsents
      });

      return updated;
    } catch (error) {
      logger.error('Error updating consent', {
        userId,
        consents,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }
}
