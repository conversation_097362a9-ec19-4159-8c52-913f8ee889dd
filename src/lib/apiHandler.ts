import { NextApiRequest, NextApiResponse } from 'next'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/pages/api/auth/[...nextauth]'
import { AuthenticationError } from './errors'
import { sanitizeInput } from '@/middleware/sanitizer'
import { rateLimit } from './rateLimit'

export interface ExtendedNextApiRequest extends NextApiRequest {
  user?: any
}

interface ApiHandlerOptions {
  requireAuth?: boolean
  audit?: {
    action: string
    resourceType: string
  }
  schema?: any
  rateLimit?: boolean
}

export const apiHandler = (
  handler: (req: ExtendedNextApiRequest, res: NextApiResponse) => Promise<void | NextApiResponse>,
  options: ApiHandlerOptions = {}
) => {
  return async (req: ExtendedNextApiRequest, res: NextApiResponse) => {
    try {
      // Apply rate limiting
      if (!rateLimit(req, res)) {
        return res.status(429).json({ error: 'Too many requests' })
      }

      // Sanitize input
      sanitizeInput(req)

      // Check authentication if required
      if (options.requireAuth) {
        const session = await getServerSession(req, res, authOptions)
        if (!session?.user) {
          return res.status(401).json({ error: 'Unauthorized' })
        }
        req.user = session.user
      }

      await handler(req, res)
    } catch (err: unknown) {
      const error = err instanceof Error ? err : new Error(String(err))
      console.error('API Error:', error)

      const status = error instanceof AuthenticationError ? 401 : 500
      return res.status(status).json({ error: error.message })
    }
  }
}
