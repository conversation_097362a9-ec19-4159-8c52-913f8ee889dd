# 🔐 Super Admin System Guide

## Overview

The Super Admin System provides secure, backend-only access for application owners to manage the entire platform. This system is completely separate from regular user access and includes:

- **Package Management**: Create and manage subscription packages
- **Business Analytics**: Comprehensive revenue and usage metrics  
- **User Management**: Monitor organizations and subscriptions
- **System Administration**: Initialize and configure the platform

## 🚀 Quick Setup

### 1. Run the Setup Tool
```bash
node scripts/setup-super-admin.js
```

### 2. Update Database
```bash
npx prisma db push
```

### 3. Configure Environment Variables
Add to your `.env` file:
```env
SUPER_ADMIN_SECRET=your-super-secret-key-change-in-production
SUPER_ADMIN_JWT_SECRET=your-jwt-secret-change-in-production
SUPER_ADMIN_ALLOWED_IPS=127.0.0.1,your.server.ip  # Optional IP whitelist
```

## 🔑 Authentication

### Initial Setup (First Time Only)
```bash
curl -X POST http://localhost:3002/api/super-admin/auth \
  -H "Content-Type: application/json" \
  -d '{
    "action": "setup",
    "email": "<EMAIL>",
    "password": "your-secure-password",
    "name": "Super Admin",
    "adminSecret": "your-super-secret-key-change-in-production"
  }'
```

### Login
```bash
curl -X POST http://localhost:3002/api/super-admin/auth \
  -H "Content-Type: application/json" \
  -d '{
    "action": "login",
    "email": "<EMAIL>",
    "password": "your-secure-password"
  }'
```

Response:
```json
{
  "message": "Authentication successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "admin": {
    "id": "admin_123",
    "email": "<EMAIL>",
    "name": "Super Admin",
    "role": "super_admin"
  }
}
```

## 📦 Package Management

### Create Package
```bash
curl -X POST http://localhost:3002/api/super-admin/packages \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Starter",
    "description": "Perfect for small businesses",
    "price": 19.99,
    "billingCycle": "MONTHLY",
    "dailyEmailLimit": 500,
    "monthlyEmailLimit": 15000,
    "emailAccountLimit": 3,
    "aiFeatures": ["REPLY_AGENT", "TEMPLATE_DESIGNER"],
    "status": "ACTIVE"
  }'
```

### Get All Packages
```bash
curl -X GET http://localhost:3002/api/super-admin/packages \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Update Package
```bash
curl -X PUT http://localhost:3002/api/super-admin/packages?id=PACKAGE_ID \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "price": 24.99,
    "dailyEmailLimit": 750
  }'
```

### Delete Package (Archive)
```bash
curl -X DELETE http://localhost:3002/api/super-admin/packages?id=PACKAGE_ID \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 Business Analytics

### Overview Metrics
```bash
curl -X GET http://localhost:3002/api/super-admin/analytics \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Organization Analytics
```bash
curl -X GET http://localhost:3002/api/super-admin/analytics?type=organizations \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Detailed Analytics
```bash
curl -X GET http://localhost:3002/api/super-admin/analytics?type=detailed \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🛠️ System Initialization

### Full System Setup
```bash
curl -X POST http://localhost:3002/api/super-admin/initialize \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "full-setup"
  }'
```

This creates:
- **Free Package**: $0/month, 100 emails/day, no AI features
- **Pro Package**: $29.99/month, 1000 emails/day, basic AI features  
- **Enterprise Package**: $99.99/month, 10000 emails/day, all AI features
- Assigns free package to all existing organizations

### Setup Only Packages
```bash
curl -X POST http://localhost:3002/api/super-admin/initialize \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "setup-packages"
  }'
```

### Assign Free Packages
```bash
curl -X POST http://localhost:3002/api/super-admin/initialize \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "assign-free-packages"
  }'
```

## 🔒 Security Features

### IP Whitelisting
Configure allowed IPs in environment variables:
```env
SUPER_ADMIN_ALLOWED_IPS=127.0.0.1,*************,your.office.ip
```

### JWT Token Security
- Tokens expire in 24 hours
- Include admin ID, email, and role
- Verified on every request

### Audit Logging
All admin actions are automatically logged with:
- Admin ID and email
- Action performed
- Timestamp
- IP address
- Request details

## 📋 Package System Features

### Mandatory Package Requirement
- **ALL organizations must have a package to send emails**
- No package = no email sending access
- Free package assigned automatically to existing users

### AI Feature Control
- **Free Package**: No AI features (blocked)
- **Paid Packages**: Configurable AI features array
- Features: `REPLY_AGENT`, `TEMPLATE_DESIGNER`, `CONTENT_GENERATOR`, `IMAGE_GENERATOR`, `KNOWLEDGE_BASE`

### Email Limits
- **Daily Limits**: Enforced per organization
- **Monthly Limits**: Optional additional restriction
- **Account Limits**: Maximum email accounts per organization

### Stripe Integration
- Automatic Stripe product/price creation for paid packages
- Synchronized billing and subscription management
- Webhook support for subscription updates

## 🎯 Default Package Configuration

| Package | Price | Daily Emails | Monthly Emails | Accounts | AI Features |
|---------|-------|--------------|----------------|----------|-------------|
| **Free** | $0 | 100 | 3,000 | 1 | None |
| **Pro** | $29.99 | 1,000 | 30,000 | 5 | Reply Agent, Template Designer, Content Generator |
| **Enterprise** | $99.99 | 10,000 | 300,000 | 20 | All Features |

## 🚨 Important Notes

### Security
- **Never expose super admin endpoints publicly**
- **Use HTTPS in production**
- **Change default secrets immediately**
- **Monitor admin access logs**
- **Consider VPN access for production**

### Database
- Super admin users have `isAdmin: true` flag
- Regular organization owners do NOT have admin access
- Package enforcement is mandatory - no exceptions

### Deployment
- Super admin system works independently of main app
- Can be deployed on separate subdomain for extra security
- Requires same database access as main application

## 🔧 Troubleshooting

### Common Issues

1. **"No token provided"**
   - Include `Authorization: Bearer TOKEN` header

2. **"Invalid or expired token"**
   - Re-authenticate to get new token

3. **"Package not found"**
   - Run initialization to create default packages

4. **"Database connection error"**
   - Ensure database is running and accessible
   - Check Prisma connection string

### Debug Commands

Check setup status:
```bash
curl -X GET http://localhost:3002/api/super-admin/auth \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Check initialization status:
```bash
curl -X GET http://localhost:3002/api/super-admin/initialize \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📞 Support

For issues with the super admin system:
1. Check logs for detailed error messages
2. Verify environment variables are set correctly
3. Ensure database migrations are up to date
4. Test with curl commands first before building UI

---

**⚠️ SECURITY WARNING**: This system provides full administrative access to your application. Protect these credentials and endpoints as you would protect your database access.
