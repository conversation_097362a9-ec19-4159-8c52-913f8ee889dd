#!/bin/bash
set -e

# Configuration
IMAGE_NAME="zainsyed1234/avian-email"
IMAGE_TAG="v1.0.1-9" # Updated tag - removed health check for Coolify compatibility
DOCKERFILE="Dockerfile"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Building Docker image for Linux/amd64 platform using simplified approach...${NC}"

# Make sure we have the latest code
echo -e "${YELLOW}Ensuring we have the latest code...${NC}"
git status

# Generate Prisma client before building (for local development)
echo -e "${YELLOW}Generating Prisma client locally...${NC}"
npx prisma generate

# Build using the simplified approach
echo -e "${YELLOW}Building with ${DOCKERFILE}...${NC}"
if docker build --no-cache -t ${IMAGE_NAME}:${IMAGE_TAG} -f ${DOCKERFILE} --platform linux/amd64 --network=host .; then
    echo -e "${GREEN}Build successful!${NC}"
    echo -e "${YELLOW}Note: Using simplified Dockerfile that installs dependencies at runtime.${NC}"
else
    echo -e "${RED}Build failed. Please check the logs for details.${NC}"
    exit 1
fi

# Push the image to Docker Hub
echo -e "${YELLOW}Pushing image to Docker Hub...${NC}"
if docker push ${IMAGE_NAME}:${IMAGE_TAG}; then
    echo -e "${GREEN}Image successfully pushed to Docker Hub!${NC}"
else
    echo -e "${RED}Push failed! Make sure you're logged in to Docker Hub:${NC}"
    echo -e "docker login"
    exit 1
fi

echo -e "${GREEN}Image successfully built and pushed to Docker Hub!${NC}"
echo -e "${GREEN}Image: ${IMAGE_NAME}:${IMAGE_TAG}${NC}"

echo -e "${YELLOW}Next steps:${NC}"
echo -e "1. Log in to your server or Portainer"
echo -e "2. Pull the image: ${IMAGE_NAME}:${IMAGE_TAG}"
echo -e "3. Create a .env file on your server using the provided .env.server template"
echo -e "4. Deploy using the docker-compose.yml file"
echo -e "5. Make sure the traefik-public network exists"
echo -e ""
echo -e "${GREEN}Simplified build approach used - dependencies will be installed at container startup.${NC}"
echo -e ""
echo -e "${YELLOW}Important environment variables:${NC}"
echo -e "- DATABASE_URL: Your remote PostgreSQL connection string"
echo -e "- DIRECT_URL: Your direct PostgreSQL connection string"
echo -e "- REDIS_URL: Your remote Redis connection string"
echo -e "- NEXTAUTH_URL: Set to https://avian-mail.wattlesol.com"
echo -e "- NEXTAUTH_SECRET: Secret for NextAuth.js authentication"
echo -e "- ENCRYPTION_KEY: Key for encrypting sensitive data"
echo -e "- CRON_SECRET: Secret for securing cron job endpoints"
echo -e ""
echo -e "${YELLOW}Note:${NC} All environment variables from your .env file are included in the docker-compose.yml file."
echo -e "You can use the .env.server file as a template for your server deployment."
