# AI Agent API Documentation

This document provides detailed information about the API endpoints for the AI Agent Automation Service.

## Base URL

All API endpoints are relative to the base URL of your application.

## Authentication

All API endpoints require authentication. You need to include a valid session cookie in your requests.

## Error Handling

All API endpoints return appropriate HTTP status codes and error messages in case of failure.

## Agent Endpoints

### Get All Agents

```
GET /api/agents
```

Returns a list of all agents for the current organization.

#### Query Parameters

- **isActive** (optional): Filter by active status (true/false).

#### Response

```json
[
  {
    "id": "agent_123",
    "name": "Sales Assistant",
    "description": "AI agent for sales outreach",
    "type": "email",
    "capabilities": ["web_scraping", "personalization"],
    "config": {
      "tone": "professional",
      "emailTemplate": "Hello {{name}}, I noticed you work at {{company}}...",
      "followUpDays": 3,
      "maxFollowUps": 2
    },
    "isActive": true,
    "createdAt": "2023-05-01T00:00:00.000Z",
    "updatedAt": "2023-05-01T00:00:00.000Z"
  }
]
```

### Get Agent by ID

```
GET /api/agents/:id
```

Returns a specific agent by ID.

#### Response

```json
{
  "id": "agent_123",
  "name": "Sales Assistant",
  "description": "AI agent for sales outreach",
  "type": "email",
  "capabilities": ["web_scraping", "personalization"],
  "config": {
    "tone": "professional",
    "emailTemplate": "Hello {{name}}, I noticed you work at {{company}}...",
    "followUpDays": 3,
    "maxFollowUps": 2
  },
  "isActive": true,
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Create Agent

```
POST /api/agents
```

Creates a new agent.

#### Request Body

```json
{
  "name": "Sales Assistant",
  "description": "AI agent for sales outreach",
  "type": "email",
  "capabilities": ["web_scraping", "personalization"],
  "config": {
    "tone": "professional",
    "emailTemplate": "Hello {{name}}, I noticed you work at {{company}}...",
    "followUpDays": 3,
    "maxFollowUps": 2
  },
  "isActive": true
}
```

#### Response

```json
{
  "id": "agent_123",
  "name": "Sales Assistant",
  "description": "AI agent for sales outreach",
  "type": "email",
  "capabilities": ["web_scraping", "personalization"],
  "config": {
    "tone": "professional",
    "emailTemplate": "Hello {{name}}, I noticed you work at {{company}}...",
    "followUpDays": 3,
    "maxFollowUps": 2
  },
  "isActive": true,
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Update Agent

```
PUT /api/agents/:id
```

Updates an existing agent.

#### Request Body

```json
{
  "name": "Sales Assistant",
  "description": "AI agent for sales outreach",
  "type": "email",
  "capabilities": ["web_scraping", "personalization", "appointment_scheduling"],
  "config": {
    "tone": "friendly",
    "emailTemplate": "Hi {{name}}, I noticed you work at {{company}}...",
    "followUpDays": 5,
    "maxFollowUps": 3
  },
  "isActive": true
}
```

#### Response

```json
{
  "id": "agent_123",
  "name": "Sales Assistant",
  "description": "AI agent for sales outreach",
  "type": "email",
  "capabilities": ["web_scraping", "personalization", "appointment_scheduling"],
  "config": {
    "tone": "friendly",
    "emailTemplate": "Hi {{name}}, I noticed you work at {{company}}...",
    "followUpDays": 5,
    "maxFollowUps": 3
  },
  "isActive": true,
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Delete Agent

```
DELETE /api/agents/:id
```

Deletes an agent.

#### Response

```json
{
  "message": "Agent deleted successfully"
}
```

### Web Search

```
POST /api/agents/web-search
```

Performs a web search.

#### Request Body

```json
{
  "query": "AI email automation",
  "numResults": 5
}
```

#### Response

```json
{
  "results": [
    {
      "title": "AI Email Automation: The Ultimate Guide",
      "link": "https://example.com/ai-email-automation",
      "snippet": "Learn how AI can automate your email marketing campaigns...",
      "position": 1
    }
  ]
}
```

### Research Topic

```
POST /api/agents/research
```

Researches a topic and provides a comprehensive summary.

#### Request Body

```json
{
  "topic": "AI email automation"
}
```

#### Response

```json
{
  "summary": "# AI Email Automation\n\nAI email automation is a technology that uses artificial intelligence to automate various aspects of email marketing...\n\n## Benefits\n\n- Increased efficiency\n- Better personalization\n- Higher conversion rates\n\n## Sources\n\n- https://example.com/ai-email-automation\n- https://example.com/email-marketing-ai"
}
```

## Knowledge Base Endpoints

### Get All Knowledge Bases

```
GET /api/knowledge-bases
```

Returns a list of all knowledge bases for the current organization.

#### Query Parameters

- **agentId** (optional): Filter by agent ID.

#### Response

```json
[
  {
    "id": "kb_123",
    "name": "Product Information",
    "description": "Information about our products",
    "content": "Our product is a cutting-edge email automation platform...",
    "agentId": "agent_123",
    "createdAt": "2023-05-01T00:00:00.000Z",
    "updatedAt": "2023-05-01T00:00:00.000Z"
  }
]
```

### Get Knowledge Base by ID

```
GET /api/knowledge-bases/:id
```

Returns a specific knowledge base by ID.

#### Response

```json
{
  "id": "kb_123",
  "name": "Product Information",
  "description": "Information about our products",
  "content": "Our product is a cutting-edge email automation platform...",
  "agentId": "agent_123",
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Create Knowledge Base

```
POST /api/knowledge-bases
```

Creates a new knowledge base.

#### Request Body

```json
{
  "name": "Product Information",
  "description": "Information about our products",
  "content": "Our product is a cutting-edge email automation platform...",
  "agentId": "agent_123"
}
```

#### Response

```json
{
  "id": "kb_123",
  "name": "Product Information",
  "description": "Information about our products",
  "content": "Our product is a cutting-edge email automation platform...",
  "agentId": "agent_123",
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Update Knowledge Base

```
PUT /api/knowledge-bases/:id
```

Updates an existing knowledge base.

#### Request Body

```json
{
  "name": "Product Information",
  "description": "Updated information about our products",
  "content": "Our product is a cutting-edge email automation platform with advanced AI capabilities..."
}
```

#### Response

```json
{
  "id": "kb_123",
  "name": "Product Information",
  "description": "Updated information about our products",
  "content": "Our product is a cutting-edge email automation platform with advanced AI capabilities...",
  "agentId": "agent_123",
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Delete Knowledge Base

```
DELETE /api/knowledge-bases/:id
```

Deletes a knowledge base.

#### Response

```json
{
  "message": "Knowledge base deleted successfully"
}
```

### Query Knowledge Base

```
POST /api/knowledge-bases/query
```

Queries a knowledge base or generates a response using RAG.

#### Request Body

```json
{
  "knowledgeBaseId": "kb_123",
  "query": "What are the features of your product?"
}
```

OR

```json
{
  "agentId": "agent_123",
  "query": "What are the features of your product?"
}
```

#### Response

```json
{
  "results": [
    {
      "content": "Our product is a cutting-edge email automation platform with the following features: AI-powered personalization, automated follow-ups, appointment scheduling, and analytics.",
      "score": 0.95
    }
  ]
}
```

OR

```json
{
  "response": "Our product offers several key features:\n\n1. AI-powered personalization\n2. Automated follow-ups\n3. Appointment scheduling\n4. Comprehensive analytics\n\nThese features help you streamline your email outreach and improve conversion rates."
}
```

## Appointment Endpoints

### Get All Appointments

```
GET /api/appointments
```

Returns a list of all appointments for the current organization.

#### Query Parameters

- **campaignId** (optional): Filter by campaign ID.
- **leadId** (optional): Filter by lead ID.
- **status** (optional): Filter by status.
- **upcoming** (optional): Filter by upcoming status (true/false).

#### Response

```json
[
  {
    "id": "appointment_123",
    "title": "Product Demo",
    "description": "Demo of our email automation platform",
    "startTime": "2023-06-01T10:00:00.000Z",
    "endTime": "2023-06-01T11:00:00.000Z",
    "status": "scheduled",
    "leadId": "lead_123",
    "campaignId": "campaign_123",
    "agentId": "agent_123",
    "createdAt": "2023-05-01T00:00:00.000Z",
    "updatedAt": "2023-05-01T00:00:00.000Z"
  }
]
```

### Get Appointment by ID

```
GET /api/appointments/:id
```

Returns a specific appointment by ID.

#### Response

```json
{
  "id": "appointment_123",
  "title": "Product Demo",
  "description": "Demo of our email automation platform",
  "startTime": "2023-06-01T10:00:00.000Z",
  "endTime": "2023-06-01T11:00:00.000Z",
  "status": "scheduled",
  "leadId": "lead_123",
  "campaignId": "campaign_123",
  "agentId": "agent_123",
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Create Appointment

```
POST /api/appointments
```

Creates a new appointment.

#### Request Body

```json
{
  "title": "Product Demo",
  "description": "Demo of our email automation platform",
  "startTime": "2023-06-01T10:00:00.000Z",
  "endTime": "2023-06-01T11:00:00.000Z",
  "status": "scheduled",
  "leadId": "lead_123",
  "campaignId": "campaign_123",
  "agentId": "agent_123"
}
```

#### Response

```json
{
  "id": "appointment_123",
  "title": "Product Demo",
  "description": "Demo of our email automation platform",
  "startTime": "2023-06-01T10:00:00.000Z",
  "endTime": "2023-06-01T11:00:00.000Z",
  "status": "scheduled",
  "leadId": "lead_123",
  "campaignId": "campaign_123",
  "agentId": "agent_123",
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Update Appointment

```
PUT /api/appointments/:id
```

Updates an existing appointment.

#### Request Body

```json
{
  "title": "Product Demo",
  "description": "Updated demo of our email automation platform",
  "startTime": "2023-06-02T10:00:00.000Z",
  "endTime": "2023-06-02T11:00:00.000Z",
  "status": "rescheduled"
}
```

#### Response

```json
{
  "id": "appointment_123",
  "title": "Product Demo",
  "description": "Updated demo of our email automation platform",
  "startTime": "2023-06-02T10:00:00.000Z",
  "endTime": "2023-06-02T11:00:00.000Z",
  "status": "rescheduled",
  "leadId": "lead_123",
  "campaignId": "campaign_123",
  "agentId": "agent_123",
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Delete Appointment

```
DELETE /api/appointments/:id
```

Deletes an appointment.

#### Response

```json
{
  "message": "Appointment deleted successfully"
}
```

## Reminder Endpoints

### Get All Reminders

```
GET /api/reminders
```

Returns a list of all reminders for the current organization.

#### Query Parameters

- **appointmentId** (optional): Filter by appointment ID.
- **status** (optional): Filter by status.

#### Response

```json
[
  {
    "id": "reminder_123",
    "appointmentId": "appointment_123",
    "timeBeforeAppointment": 24,
    "status": "pending",
    "content": "Reminder: Product Demo tomorrow at 10:00 AM",
    "createdAt": "2023-05-01T00:00:00.000Z",
    "updatedAt": "2023-05-01T00:00:00.000Z"
  }
]
```

### Get Reminder by ID

```
GET /api/reminders/:id
```

Returns a specific reminder by ID.

#### Response

```json
{
  "id": "reminder_123",
  "appointmentId": "appointment_123",
  "timeBeforeAppointment": 24,
  "status": "pending",
  "content": "Reminder: Product Demo tomorrow at 10:00 AM",
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Create Reminder

```
POST /api/reminders
```

Creates a new reminder.

#### Request Body

```json
{
  "appointmentId": "appointment_123",
  "timeBeforeAppointment": 24,
  "status": "pending",
  "content": "Reminder: Product Demo tomorrow at 10:00 AM"
}
```

#### Response

```json
{
  "id": "reminder_123",
  "appointmentId": "appointment_123",
  "timeBeforeAppointment": 24,
  "status": "pending",
  "content": "Reminder: Product Demo tomorrow at 10:00 AM",
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Update Reminder

```
PUT /api/reminders/:id
```

Updates an existing reminder.

#### Request Body

```json
{
  "timeBeforeAppointment": 48,
  "status": "pending",
  "content": "Reminder: Product Demo in 2 days at 10:00 AM"
}
```

#### Response

```json
{
  "id": "reminder_123",
  "appointmentId": "appointment_123",
  "timeBeforeAppointment": 48,
  "status": "pending",
  "content": "Reminder: Product Demo in 2 days at 10:00 AM",
  "createdAt": "2023-05-01T00:00:00.000Z",
  "updatedAt": "2023-05-01T00:00:00.000Z"
}
```

### Delete Reminder

```
DELETE /api/reminders/:id
```

Deletes a reminder.

#### Response

```json
{
  "message": "Reminder deleted successfully"
}
```
