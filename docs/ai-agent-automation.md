# AI Agent Automation Service Documentation

## Overview

The AI Agent Automation Service is a powerful feature that allows you to create AI agents that can automate various tasks in your email campaigns. These agents can gather data from leads' profiles, write personalized emails, follow up until conversion, and send dynamic appointment reminders.

## Features

- **Web Scraping**: Agents can scrape data from websites to gather information about leads and their companies.
- **LinkedIn Research**: Agents can extract structured information from LinkedIn profiles.
- **Personalization**: Agents can generate highly personalized email content based on scraped data.
- **Appointment Scheduling**: Agents can schedule and manage appointments with leads.
- **Knowledge Base**: Agents can use knowledge bases to answer questions and provide information.
- **Web Search**: Agents can search the web for information to enhance their responses.

## Components

### 1. Agent Model

The Agent model represents an AI agent with specific capabilities and configuration:

- **Name**: The name of the agent.
- **Description**: A description of what the agent does.
- **Type**: The type of agent (email, chat, etc.).
- **Capabilities**: The capabilities of the agent (web_scraping, linkedin_research, personalization, appointment_scheduling, knowledge_base, web_search).
- **Config**: Configuration options for the agent (tone, email template, follow-up days, etc.).
- **KnowledgeBases**: Knowledge bases associated with the agent.

### 2. Knowledge Base Model

The Knowledge Base model represents a collection of information that an agent can use to answer questions:

- **Name**: The name of the knowledge base.
- **Description**: A description of the knowledge base.
- **Content**: The content of the knowledge base.
- **AgentId**: The ID of the agent that owns the knowledge base.

### 3. Appointment Model

The Appointment model represents a scheduled meeting between a lead and an agent:

- **Title**: The title of the appointment.
- **Description**: A description of the appointment.
- **StartTime**: The start time of the appointment.
- **EndTime**: The end time of the appointment.
- **Status**: The status of the appointment (scheduled, completed, cancelled, rescheduled).
- **LeadId**: The ID of the lead.
- **CampaignId**: The ID of the campaign.
- **AgentId**: The ID of the agent.

### 4. Reminder Model

The Reminder model represents a reminder for an appointment:

- **AppointmentId**: The ID of the appointment.
- **TimeBeforeAppointment**: The time before the appointment to send the reminder (in hours).
- **Status**: The status of the reminder (pending, sent, failed).
- **Content**: The content of the reminder.

## Services

### 1. AgentEnhancementService

The `AgentEnhancementService` provides methods for enhancing AI agents with web scraping, data processing, and personalized email generation:

- **scrapeWebsite(url)**: Scrapes data from a website.
- **scrapeLinkedInProfile(url)**: Scrapes data from a LinkedIn profile.
- **generatePersonalizedEmail(leadId, agentId, scrapedData)**: Generates a personalized email based on lead data and scraped information.

### 2. KnowledgeBaseService

The `KnowledgeBaseService` provides methods for managing knowledge bases for AI agents:

- **createKnowledgeBase(agentId, name, content)**: Creates a knowledge base for an agent.
- **queryKnowledgeBase(knowledgeBaseId, query)**: Queries a knowledge base with a specific query.
- **generateResponse(agentId, query)**: Generates a response using RAG (Retrieval Augmented Generation).

### 3. WebSearchService

The `WebSearchService` provides methods for web search and research capabilities:

- **webSearch(query, numResults)**: Performs a web search.
- **researchTopic(topic)**: Researches a topic using web search and summarizes the results.

### 4. AppointmentService

The `AppointmentService` provides methods for managing appointments:

- **createAppointment(data)**: Creates an appointment.
- **updateAppointment(id, data)**: Updates an appointment.
- **deleteAppointment(id)**: Deletes an appointment.
- **getAppointment(id)**: Gets an appointment by ID.
- **getAppointments(filters)**: Gets appointments based on filters.

### 5. ReminderService

The `ReminderService` provides methods for managing reminders:

- **createReminder(data)**: Creates a reminder.
- **updateReminder(id, data)**: Updates a reminder.
- **deleteReminder(id)**: Deletes a reminder.
- **getReminder(id)**: Gets a reminder by ID.
- **getReminders(filters)**: Gets reminders based on filters.
- **sendReminder(id)**: Sends a reminder.

## Cron Jobs

### 1. Agent Processor

The agent processor runs periodically to process AI agent tasks:

- **processAgents()**: Processes all active agents.
- **processAgent(agentId)**: Processes a specific agent.
- **processLeads(agentId, campaignId)**: Processes leads for a specific agent and campaign.

### 2. Reminder Processor

The reminder processor runs periodically to send appointment reminders:

- **processReminders()**: Processes all pending reminders.
- **processReminder(reminderId)**: Processes a specific reminder.
- **sendReminder(reminderId)**: Sends a reminder.

## API Endpoints

### Agent Endpoints

- **GET /api/agents**: Gets all agents.
- **GET /api/agents/:id**: Gets an agent by ID.
- **POST /api/agents**: Creates a new agent.
- **PUT /api/agents/:id**: Updates an agent.
- **DELETE /api/agents/:id**: Deletes an agent.
- **POST /api/agents/web-search**: Performs a web search.
- **POST /api/agents/research**: Researches a topic.

### Knowledge Base Endpoints

- **GET /api/knowledge-bases**: Gets all knowledge bases.
- **GET /api/knowledge-bases/:id**: Gets a knowledge base by ID.
- **POST /api/knowledge-bases**: Creates a new knowledge base.
- **PUT /api/knowledge-bases/:id**: Updates a knowledge base.
- **DELETE /api/knowledge-bases/:id**: Deletes a knowledge base.
- **POST /api/knowledge-bases/query**: Queries a knowledge base.

### Appointment Endpoints

- **GET /api/appointments**: Gets all appointments.
- **GET /api/appointments/:id**: Gets an appointment by ID.
- **POST /api/appointments**: Creates a new appointment.
- **PUT /api/appointments/:id**: Updates an appointment.
- **DELETE /api/appointments/:id**: Deletes an appointment.

### Reminder Endpoints

- **GET /api/reminders**: Gets all reminders.
- **GET /api/reminders/:id**: Gets a reminder by ID.
- **POST /api/reminders**: Creates a new reminder.
- **PUT /api/reminders/:id**: Updates a reminder.
- **DELETE /api/reminders/:id**: Deletes a reminder.

## Usage Examples

### Creating an Agent

```javascript
// Create an agent with web scraping and personalization capabilities
const agent = await prisma.agent.create({
  data: {
    name: 'Sales Assistant',
    description: 'AI agent for sales outreach',
    type: 'email',
    capabilities: ['web_scraping', 'personalization'],
    config: {
      tone: 'professional',
      emailTemplate: 'Hello {{name}}, I noticed you work at {{company}}...',
      followUpDays: 3,
      maxFollowUps: 2,
    },
    organizationId: 'org_123',
  },
});
```

### Creating a Knowledge Base

```javascript
// Create a knowledge base for an agent
const knowledgeBase = await KnowledgeBaseService.createKnowledgeBase(
  'agent_123',
  'Product Information',
  'Our product is a cutting-edge email automation platform...'
);
```

### Generating a Personalized Email

```javascript
// Generate a personalized email based on lead data and scraped information
const email = await AgentEnhancementService.generatePersonalizedEmail(
  'lead_123',
  'agent_123',
  {
    name: 'John Doe',
    title: 'CEO',
    company: 'Acme Inc.',
    website: 'https://acme.com',
    about: 'Acme Inc. is a leading provider of...',
  }
);
```

### Creating an Appointment

```javascript
// Create an appointment
const appointment = await prisma.appointment.create({
  data: {
    title: 'Product Demo',
    description: 'Demo of our email automation platform',
    startTime: new Date('2023-06-01T10:00:00Z'),
    endTime: new Date('2023-06-01T11:00:00Z'),
    status: 'scheduled',
    leadId: 'lead_123',
    campaignId: 'campaign_123',
    agentId: 'agent_123',
  },
});
```

### Creating a Reminder

```javascript
// Create a reminder for an appointment
const reminder = await prisma.reminder.create({
  data: {
    appointmentId: 'appointment_123',
    timeBeforeAppointment: 24, // 24 hours before
    status: 'pending',
    content: 'Reminder: Product Demo tomorrow at 10:00 AM',
  },
});
```

## Best Practices

1. **Use Specific Capabilities**: Only enable the capabilities that your agent needs to perform its tasks.
2. **Provide Clear Instructions**: When configuring an agent, provide clear instructions and templates.
3. **Monitor Agent Performance**: Regularly check the performance of your agents and adjust their configuration as needed.
4. **Keep Knowledge Bases Updated**: Regularly update your knowledge bases with the latest information.
5. **Test Before Deployment**: Always test your agents with a small group of leads before deploying them to a larger audience.
6. **Respect Privacy**: Ensure that your agents respect the privacy of your leads and comply with relevant regulations.
7. **Set Reasonable Limits**: Set reasonable limits for the number of follow-ups and the frequency of reminders.
8. **Provide Fallbacks**: Always provide fallback options in case the agent cannot complete a task.

## Troubleshooting

1. **Agent Not Processing Leads**: Check that the agent is active and has the necessary capabilities.
2. **Knowledge Base Not Working**: Check that the knowledge base content is properly formatted and contains relevant information.
3. **Web Scraping Not Working**: Check that the website is accessible and does not block scraping.
4. **Personalization Not Working**: Check that the lead data and scraped information are available and properly formatted.
5. **Appointments Not Being Created**: Check that the agent has the appointment_scheduling capability and the necessary permissions.
6. **Reminders Not Being Sent**: Check that the reminder processor is running and the reminders have the correct status.

## Conclusion

The AI Agent Automation Service provides a powerful way to automate various tasks in your email campaigns. By leveraging AI agents with specific capabilities, you can gather data, generate personalized content, schedule appointments, and send reminders, all while providing a seamless experience for your leads.
