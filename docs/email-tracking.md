# Email Tracking System Documentation

This document explains how the email tracking system works in Avian Email, including how to set up and verify tracking domains, how tracking data is collected, and how to troubleshoot common issues.

## Overview

The Avian Email tracking system allows you to track:

1. **Email Opens**: When recipients open your emails
2. **Link Clicks**: When recipients click links in your emails
3. **Bounces**: When emails cannot be delivered to recipients

This data is collected and displayed in campaign analytics to help you understand the performance of your email campaigns.

## How Tracking Works

### Email Open Tracking

Email opens are tracked using a 1x1 pixel image (tracking pixel) that is inserted into your emails. When a recipient opens your email, their email client loads this image, which sends a request to our server. This request is recorded as an email open.

### Link Click Tracking

Link clicks are tracked by replacing all links in your emails with tracking links that redirect through our server. When a recipient clicks a link, they are first sent to our server, which records the click, and then redirected to the original destination.

### Bounce Tracking

Bounces are tracked in two ways:
1. Through bounce notifications from your email provider
2. By setting up a bounce tracking domain that receives bounce emails

## Setting Up Tracking Domains

For optimal deliverability and tracking accuracy, you should set up custom tracking domains. This involves creating subdomains and configuring DNS records.

### Required Domains

1. **Open Tracking Domain**: Used for tracking email opens (e.g., `open.yourdomain.com`)
2. **Click Tracking Domain**: Used for tracking link clicks (e.g., `click.yourdomain.com`)
3. **Bounce Tracking Domain**: Used for receiving bounce notifications (e.g., `bounce.yourdomain.com`)

### DNS Configuration

For each tracking domain, you need to add specific DNS records:

#### Open Tracking Domain (CNAME Record)
```
Type: CNAME
Name: open
Value: avian-mail.wattlesol.com
```

#### Click Tracking Domain (CNAME Record)
```
Type: CNAME
Name: click
Value: avian-mail.wattlesol.com
```

#### Bounce Tracking Domain (MX and SPF Records)
```
Type: MX
Name: bounce
Priority: 10
Value: avian-mail.wattlesol.com

Type: TXT
Name: bounce
Value: v=spf1 include:avian-mail.wattlesol.com ~all
```

### Verifying Your Domains

After adding the DNS records, you need to verify them in the Avian Email dashboard:

1. Go to **Settings > DNS Tracking**
2. Enter your tracking domains
3. Click **Verify DNS**

The system will check if your DNS records are correctly configured. If verification is successful, your custom tracking domains will be used for all your campaigns.

## Troubleshooting

### Email Opens Not Being Tracked

1. **Check if images are being loaded**: Email opens are only tracked if the recipient's email client loads images. Some clients block images by default.
2. **Verify tracking domain**: Make sure your open tracking domain is correctly configured and verified.
3. **Check for tracking pixel in emails**: Inspect your sent emails to ensure the tracking pixel is present.

### Link Clicks Not Being Tracked

1. **Verify link wrapping**: Inspect your sent emails to ensure links are wrapped with tracking URLs.
2. **Check click tracking domain**: Make sure your click tracking domain is correctly configured and verified.
3. **Test redirect**: Try clicking a tracking link to see if the redirect works properly.

### Bounces Not Being Tracked

1. **Verify bounce domain setup**: Make sure your bounce tracking domain has the correct MX and SPF records.
2. **Check email provider settings**: Ensure your email provider is configured to forward bounce notifications.

## Testing the Tracking System

You can test the tracking system using the built-in testing tools:

1. Go to **Settings > Tracking Test**
2. Select a campaign and lead
3. Generate a test email
4. Use the test actions to simulate opens, clicks, and bounces

## Advanced Configuration

### Environment Variables

The tracking system can be configured using the following environment variables:

```
DEFAULT_OPEN_TRACKING_DOMAIN="open.yourdomain.com"
DEFAULT_CLICK_TRACKING_DOMAIN="click.yourdomain.com"
DEFAULT_BOUNCE_TRACKING_DOMAIN="bounce.yourdomain.com"
NEXT_PUBLIC_APP_URL="yourdomain.com"
ENABLE_TRACKING_LOGS="true"
```

### Tracking Data Storage

Tracking data is stored in the following database tables:

- `StepActivity`: Records individual tracking events (opens, clicks, bounces)
- `Campaign`: Contains aggregate metrics for campaigns
- `CampaignLead`: Tracks the status of each lead in a campaign

## Best Practices

1. **Always use custom tracking domains**: This improves deliverability and tracking accuracy.
2. **Regularly verify your tracking domains**: DNS configurations can sometimes change.
3. **Include both text and HTML versions**: This ensures tracking works even if HTML is disabled.
4. **Test tracking before sending campaigns**: Use the testing tools to verify tracking is working.
5. **Monitor bounce rates**: High bounce rates can affect your sender reputation.

## Technical Details

### Tracking Pixel Implementation

The tracking pixel is a 1x1 transparent GIF image that is inserted at both the beginning and end of the email body. It includes cache-busting parameters to ensure it is loaded every time the email is opened.

### Link Tracking Implementation

All links in emails are replaced with tracking links that include the campaign ID, lead ID, and original URL. When a recipient clicks a link, they are redirected through our server, which records the click and then redirects them to the original destination.

### Bounce Handling

Bounces are processed through a dedicated API endpoint that records the bounce type, reason, and updates the campaign metrics. Hard bounces automatically add the recipient to a suppression list to prevent future sends.

## Support

If you encounter any issues with the tracking system, please contact <NAME_EMAIL> with the following information:

1. Campaign ID
2. Example recipient email
3. Tracking domains configuration
4. Any error messages or unexpected behavior

Our support team will help you diagnose and resolve the issue.
