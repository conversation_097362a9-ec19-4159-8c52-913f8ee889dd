const path = require('path');

// Load environment variables explicitly
require('dotenv').config();

/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    // Explicitly expose environment variables to the runtime
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    OPENAI_MODEL: process.env.OPENAI_MODEL,
    AI_PROVIDER: process.env.AI_PROVIDER,
    OLLAMA_BASE_URL: process.env.OLLAMA_BASE_URL,
    OLLAMA_MODEL: process.env.OLLAMA_MODEL,
    OLLAMA_EMBEDDING_MODEL: process.env.OLLAMA_EMBEDDING_MODEL,
    CHROMA_API_URL: process.env.CHROMA_API_URL,
    LANGCHAIN_API_KEY: process.env.LANGCHAIN_API_KEY,
    LANGCHAIN_PROJECT: process.env.LANGCHAIN_PROJECT,
    LANGCHAIN_TRACING_V2: process.env.LANGCHAIN_TRACING_V2,
    LANGCHAIN_ENDPOINT: process.env.LANGCHAIN_ENDPOINT || 'https://api.smith.langchain.com',
    // R1 Optimization variables
    R1_OPTIMIZATION_LEVEL: process.env.R1_OPTIMIZATION_LEVEL,
    R1_MAX_TOKENS: process.env.R1_MAX_TOKENS,
    R1_TEMPERATURE: process.env.R1_TEMPERATURE,
  },
  reactStrictMode: true,
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          }
        ]
      }
    ]
  },
  webpack: (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname, 'src'),
    };

    // Fix for ES modules in Ant Design
    config.module.rules.push({
      test: /\.m?js$/,
      type: 'javascript/auto',
      resolve: {
        fullySpecified: false,
      },
    });

    return config;
  },
  // Add transpilePackages to handle ES modules
  transpilePackages: ['@ant-design/icons', '@ant-design/icons-svg', 'antd', 'rc-util', 'rc-pagination', 'rc-picker', 'rc-input', 'rc-field-form', 'rc-textarea', 'rc-select', 'rc-dropdown', 'rc-menu', 'rc-virtual-list', 'rc-tree', 'rc-table', 'rc-tooltip', 'rc-tabs', '@rc-component', 'rc-drawer', 'rc-dialog', 'rc-motion', 'rc-notification', 'rc-switch', 'rc-slider', 'rc-progress', 'rc-collapse', 'rc-checkbox', 'rc-rate', 'rc-mentions', 'rc-steps', 'rc-upload'],
}

module.exports = nextConfig
