FROM node:18-alpine

WORKDIR /app

# Update package repository and install dependencies
RUN apk update && \
    apk add --no-cache libc6-compat && \
    apk add --no-cache bash curl && \
    apk add --no-cache jq openssl && \
    apk add --no-cache python3 make g++

# Copy the entire application
COPY . .

# Copy the fixed sequence processor script
RUN cp scripts/start-sequence-processor.fixed.js scripts/start-sequence-processor.js

# Create entrypoint script
RUN echo '#!/bin/bash' > /app/entrypoint.sh && \
    echo 'set -e' >> /app/entrypoint.sh && \
    echo 'echo "Installing dependencies..."' >> /app/entrypoint.sh && \
    echo 'npm install' >> /app/entrypoint.sh && \
    echo 'echo "Generating Prisma client..."' >> /app/entrypoint.sh && \
    echo 'npx prisma generate' >> /app/entrypoint.sh && \
    echo 'echo "Building application..."' >> /app/entrypoint.sh && \
    echo 'npm run build' >> /app/entrypoint.sh && \
    echo 'echo "Starting sequence processor..."' >> /app/entrypoint.sh && \
    echo 'node scripts/start-sequence-processor.js &' >> /app/entrypoint.sh && \
    echo 'echo "Starting application on port 3002..."' >> /app/entrypoint.sh && \
    echo 'exec npm start -- -p 3002' >> /app/entrypoint.sh && \
    chmod +x /app/entrypoint.sh

# Create log directory
RUN mkdir -p logs

# Expose ports
EXPOSE 3002
EXPOSE 9000

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:9000/health || exit 1

# Start the application
CMD ["/app/entrypoint.sh"]
