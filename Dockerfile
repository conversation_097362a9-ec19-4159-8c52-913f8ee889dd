FROM node:18-alpine

WORKDIR /app

# Update package repository and install dependencies
RUN apk update && \
    apk add --no-cache libc6-compat && \
    apk add --no-cache bash curl && \
    apk add --no-cache jq openssl && \
    apk add --no-cache python3 make g++

# Copy package files first for better caching
COPY package*.json ./
COPY prisma ./prisma/

# Install dependencies
RUN npm ci --only=production

# Copy the rest of the application
COPY . .

# Copy the fixed sequence processor script
RUN cp scripts/start-sequence-processor.fixed.js scripts/start-sequence-processor.js

# Generate Prisma client and build application during image build
RUN npx prisma generate && \
    npm run build

# Create optimized entrypoint script
RUN echo '#!/bin/bash' > /app/entrypoint.sh && \
    echo 'set -e' >> /app/entrypoint.sh && \
    echo 'echo "Starting Avian Email Application..."' >> /app/entrypoint.sh && \
    echo 'echo "Environment: $NODE_ENV"' >> /app/entrypoint.sh && \
    echo 'echo "Port: ${PORT:-3002}"' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# Run database migrations if needed' >> /app/entrypoint.sh && \
    echo 'if [ "$NODE_ENV" = "production" ]; then' >> /app/entrypoint.sh && \
    echo '  echo "Running database migrations..."' >> /app/entrypoint.sh && \
    echo '  npx prisma migrate deploy || echo "Migration failed or not needed"' >> /app/entrypoint.sh && \
    echo 'fi' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# Start sequence processor in background' >> /app/entrypoint.sh && \
    echo 'echo "Starting sequence processor..."' >> /app/entrypoint.sh && \
    echo 'node scripts/start-sequence-processor.js > logs/sequence-processor.log 2>&1 &' >> /app/entrypoint.sh && \
    echo 'SEQUENCE_PID=$!' >> /app/entrypoint.sh && \
    echo 'echo "Sequence processor started with PID $SEQUENCE_PID"' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# Cleanup function' >> /app/entrypoint.sh && \
    echo 'cleanup() {' >> /app/entrypoint.sh && \
    echo '  echo "Shutting down..."' >> /app/entrypoint.sh && \
    echo '  kill $SEQUENCE_PID 2>/dev/null || true' >> /app/entrypoint.sh && \
    echo '  exit 0' >> /app/entrypoint.sh && \
    echo '}' >> /app/entrypoint.sh && \
    echo 'trap cleanup SIGTERM SIGINT' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# Start the main application' >> /app/entrypoint.sh && \
    echo 'echo "Starting Next.js application on port ${PORT:-3002}..."' >> /app/entrypoint.sh && \
    echo 'exec npm start -- -p ${PORT:-3002}' >> /app/entrypoint.sh && \
    chmod +x /app/entrypoint.sh

# Create log directory
RUN mkdir -p logs

# Expose ports
EXPOSE 3002

# Add health check for Coolify compatibility
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3002/api/health-check || exit 1

# Start the application
CMD ["/app/entrypoint.sh"]
