generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                String               @id @default(cuid())
  name              String?
  email             String               @unique
  password          String
  emailVerified     DateTime?
  image             String?
  accounts          Account[]
  auditLogs         AuditLog[]
  campaigns         Campaign[]
  emailAccounts     EmailAccount[]
  notifications     Notification[]
  ownedOrganization Organization?        @relation("OwnedOrganization")
  organizations     OrganizationMember[]
  sessions          Session[]
  consent           UserConsent?
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model EmailAccount {
  id             String         @id @default(cuid())
  userId         String
  email          String
  provider       String
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  lastUsed       DateTime?
  name           String?
  status         String         @default("pending")
  warmupStatus   String?        // null, IN_PROGRESS, COMPLETED, FAILED
  reputationScore Float?        // 0-100 score representing email reputation
  lastChecked    DateTime?      // Last time reputation was checked
  smtpHost       String?
  smtpPort       Int?
  smtpUsername   String?
  smtpPassword   String?
  imapHost       String?
  imapPort       Int?
  imapUsername   String?
  imapPassword   String?
  imapEnabled    Boolean        @default(false)
  lastImapSync   DateTime?
  accessToken    String?
  refreshToken   String?
  // AI Agent Integration
  aiAgentEnabled Boolean        @default(false)
  aiAgentId      String?
  aiReplyEnabled Boolean        @default(false)
  aiReplyDelay   Int?           @default(0) // Delay in minutes before AI replies
  aiReplyPrompt  String?        @db.Text // Custom prompt for this inbox
  // Email Signature
  signatureEnabled Boolean      @default(false)
  signatureName    String?      // Name to use in signature (e.g., "John Smith", "Sales Team")
  signatureTitle   String?      // Job title (e.g., "Sales Manager", "Customer Success")
  signatureCompany String?      // Company name override
  signaturePhone   String?      // Phone number
  signatureEmail   String?      // Email override (defaults to account email)
  signatureWebsite String?      // Website URL
  signatureCustom  String?      @db.Text // Custom signature text
  agent          Agent?         @relation("EmailAccountAgent", fields: [aiAgentId], references: [id])
  user           User           @relation(fields: [userId], references: [id])
  campaignSteps   CampaignStep[]
  campaignStepEmailAccounts CampaignStepEmailAccount[]
  testVariants    ABTestVariant[]
  warmupActivities WarmupActivity[]
  reputationChecks ReputationCheck[]
  receivedEmails  ReceivedEmail[]

  @@index([userId])
  @@index([aiAgentId])
}

model Campaign {
  id             String         @id @default(cuid())
  userId         String
  name           String
  description    String?
  type           String         @default("standard") // standard, sequence, drip
  status         String         @default("draft") // draft, active, paused, completed, completed_with_errors, failed, in_progress
  startDate      DateTime?
  endDate        DateTime?
  recipientType  String?        // individual, list
  listId         String?        // Reference to a lead list if recipientType is 'list'
  errorMessage   String?        // Detailed error message when campaign fails or has issues
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  user           User           @relation(fields: [userId], references: [id])
  organizationId String
  organization   Organization   @relation(fields: [organizationId], references: [id])
  steps          CampaignStep[]
  leads          CampaignLead[]
  agentId        String?
  agent          Agent?         @relation(fields: [agentId], references: [id])
  abTests        ABTest[]
  isAbTest       Boolean        @default(false)
  receivedEmails ReceivedEmail[]
  agentReplies   AgentReply[]
  standardTracking StandardCampaignTracking[]
  sequenceTracking SequenceCampaignTracking[]
  emailOpenEvents EmailOpenEvent[]
  linkClickEvents LinkClickEvent[]

  // Metrics
  sentCount      Int            @default(0)
  openedCount    Int            @default(0)
  clickedCount   Int            @default(0)
  repliedCount   Int            @default(0)
  pendingCount   Int            @default(0)
  errorCount     Int            @default(0)

  // Relations
  appointments   Appointment[]

  @@index([organizationId])
  @@index([userId])
  @@index([agentId])
}

model Recipient {
  id         String    @id @default(cuid())
  campaignId String
  email      String
  status     String
  error      String?
  sentAt     DateTime?
  openedAt   DateTime?
  clickedAt  DateTime?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@index([campaignId])
  @@index([email])
}

model SuppressionList {
  id        String   @id @default(cuid())
  email     String   @unique
  reason    String
  createdAt DateTime @default(now())
}

model Usage {
  id             String       @id @default(cuid())
  organizationId String
  period         DateTime
  emailsSent     Int          @default(0)
  emailAccounts  Int          @default(0)
  storageUsed    Int          @default(0)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id])

  @@unique([organizationId, period])
}

model Organization {
  id             String               @id @default(cuid())
  name           String
  customerId     String?              @unique
  subscriptionId String?              @unique
  planId         String?
  subscriptionTier SubscriptionTier   @default(FREE)
  packageId      String?              // Reference to custom package
  createdAt      DateTime             @default(now())
  updatedAt      DateTime             @updatedAt
  isPersonal     Boolean              @default(false)
  ownerId        String               @unique
  owner          User                 @relation("OwnedOrganization", fields: [ownerId], references: [id])
  subscription   Subscription?        @relation(fields: [subscriptionId], references: [id])
  package        Package?             @relation(fields: [packageId], references: [id])
  members        OrganizationMember[]
  notifications  Notification[]
  usage          Usage[]
  templates      EmailTemplate[]
  leads          Lead[]
  leadLists      LeadList[]
  dnsTracking    DnsTrackingConfig?
  campaigns      Campaign[]
  agents         Agent[]
  dailyUsage     DailyUsage[]
  knowledgeBases KnowledgeBase[]
}

model OrganizationMember {
  id             String       @id @default(cuid())
  organizationId String
  userId         String
  role           Role         @default(MEMBER)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([organizationId, userId])
}

model Subscription {
  id                   String             @id @default(cuid())
  stripeSubscriptionId String             @unique
  stripePriceId        String
  stripeCustomerId     String
  tier                 SubscriptionTier   @default(FREE)
  status               SubscriptionStatus
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  cancelAtPeriodEnd    Boolean            @default(false)
  trialEnd             DateTime?
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt
  organization         Organization?
}

model AuditLog {
  id           String   @id @default(cuid())
  timestamp    DateTime @default(now())
  userId       String
  action       String
  resourceType String
  resourceId   String?
  oldData      String?
  newData      String?
  metadata     String?
  ipAddress    String?
  userAgent    String?
  user         User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([timestamp])
  @@index([resourceType, resourceId])
}

model UserConsent {
  id                String   @id @default(cuid())
  userId            String   @unique
  marketingEmails   Boolean  @default(false)
  analyticsTracking Boolean  @default(false)
  thirdPartySharing Boolean  @default(false)
  updatedAt         DateTime @default(now())
  user              User     @relation(fields: [userId], references: [id])
}

enum Role {
  OWNER
  ADMIN
  MEMBER
}

enum SubscriptionTier {
  FREE
  PRO
  ENTERPRISE
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  INCOMPLETE
  INCOMPLETE_EXPIRED
  PAST_DUE
  TRIALING
  UNPAID
}

enum PackageStatus {
  ACTIVE
  INACTIVE
  ARCHIVED
}

enum BillingCycle {
  DAILY
  WEEKLY
  BIWEEKLY
  MONTHLY
  QUARTERLY
  YEARLY
}

model EmailTemplate {
  id             String         @id @default(cuid())
  name           String
  subject        String
  content        String
  organizationId String
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  organization   Organization   @relation(fields: [organizationId], references: [id])
  campaignSteps  CampaignStep[]
  testVariants   ABTestVariant[]
}

model Notification {
  id             String       @id @default(cuid())
  title          String
  message        String
  type           String       @default("info") // info, success, warning, error
  isRead         Boolean      @default(false)
  organizationId String
  userId         String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id])
  user           User?        @relation(fields: [userId], references: [id])

  @@index([organizationId])
  @@index([userId])
}

model LeadList {
  id             String       @id @default(cuid())
  name           String
  description    String?
  source         String       @default("manual") // manual, import, api, website
  organizationId String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id])
  leads          Lead[]

  @@unique([name, organizationId])
  @@index([organizationId])
}

model Lead {
  id             String       @id @default(cuid())
  email          String
  firstName      String?
  lastName       String?
  company        String?
  phone          String?
  status         String       @default("active") // active, inactive, unsubscribed
  source         String       @default("manual") // manual, import, api, website
  tags           String[]     @default([])
  customFields   Json?        // For storing additional custom fields
  organizationId String
  listId         String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id])
  list           LeadList?    @relation(fields: [listId], references: [id])
  campaigns      CampaignLead[]
  appointments   Appointment[]
  receivedEmails ReceivedEmail[]
  agentReplies   AgentReply[]
  standardTracking StandardCampaignTracking[]
  sequenceTracking SequenceCampaignTracking[]
  emailOpenEvents EmailOpenEvent[]
  linkClickEvents LinkClickEvent[]

  @@unique([email, organizationId])
  @@index([organizationId])
  @@index([listId])
  @@index([email])
  @@index([status])
}

model CampaignLead {
  id                String             @id @default(cuid())
  campaignId        String
  leadId            String
  currentStepId     String?
  status            String             @default("active") // active, completed, unsubscribed, bounced
  joinedAt          DateTime           @default(now())
  completedAt       DateTime?
  unsubscribedAt    DateTime?
  lastInteractionAt DateTime?
  campaign          Campaign           @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  lead              Lead               @relation(fields: [leadId], references: [id], onDelete: Cascade)
  currentStep       CampaignStep?      @relation("CurrentStep", fields: [currentStepId], references: [id])
  stepActivities    StepActivity[]
  conversations     AgentConversation[]

  @@unique([campaignId, leadId])
  @@index([campaignId])
  @@index([leadId])
  @@index([currentStepId])
}

model CampaignStep {
  id                 String          @id @default(cuid())
  campaignId         String
  name               String
  description        String?
  type               String          @default("email") // email, wait, condition, action
  position           Int
  emailAccountId     String?         // Primary email account (for backward compatibility)
  subject            String?
  content            String?
  templateId         String?         // Reference to an email template
  waitDuration       Int?            // in hours
  waitUntil          DateTime?
  conditionType      String?         // opened, clicked, replied, not_opened, not_clicked, not_replied
  conditionStepId    String?
  conditionTimeframe Int?            // in hours
  actionType         String?         // update_lead, add_tag, remove_tag, exit_campaign
  actionValue        String?
  useMultipleSenders Boolean         @default(false) // Whether to use multiple senders
  agentId            String?         // Reference to an AI agent
  createdAt          DateTime        @default(now())
  updatedAt          DateTime        @updatedAt
  campaign           Campaign        @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  emailAccount       EmailAccount?   @relation(fields: [emailAccountId], references: [id])
  emailAccounts      CampaignStepEmailAccount[] // Multiple email accounts for this step
  template           EmailTemplate?  @relation(fields: [templateId], references: [id])
  agent              Agent?          @relation(fields: [agentId], references: [id])
  conditionStep      CampaignStep?   @relation("StepCondition", fields: [conditionStepId], references: [id])
  conditionedSteps   CampaignStep[]  @relation("StepCondition")
  currentLeads       CampaignLead[]  @relation("CurrentStep")
  activities         StepActivity[]
  receivedEmails     ReceivedEmail[]
  sequenceCurrentSteps SequenceCampaignTracking[] @relation("CurrentSequenceStep")
  sequenceStepEvents SequenceStepEvent[]

  @@index([campaignId])
  @@index([emailAccountId])
  @@index([conditionStepId])
  @@index([agentId])
}

model StepActivity {
  id             String       @id @default(cuid())
  campaignLeadId String
  stepId         String
  status         String       @default("pending") // pending, sent, opened, clicked, replied, bounced, unsubscribed
  type           String?      // email_sent, email_opened, link_clicked, email_replied, email_bounced
  metadata       Json?        // Additional data like URL clicked, user agent, etc.
  sentAt         DateTime?
  openedAt       DateTime?
  clickedAt      DateTime?
  repliedAt      DateTime?
  bouncedAt      DateTime?
  error          String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  campaignLead   CampaignLead @relation(fields: [campaignLeadId], references: [id], onDelete: Cascade)
  step           CampaignStep @relation(fields: [stepId], references: [id], onDelete: Cascade)

  @@index([campaignLeadId])
  @@index([stepId])
}

model CampaignStepEmailAccount {
  id             String       @id @default(cuid())
  campaignStepId String
  emailAccountId String
  weight         Int         @default(1) // Weight for distribution (higher = more emails)
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  campaignStep   CampaignStep @relation(fields: [campaignStepId], references: [id], onDelete: Cascade)
  emailAccount   EmailAccount @relation(fields: [emailAccountId], references: [id], onDelete: Cascade)

  @@index([campaignStepId])
  @@index([emailAccountId])
}

model Agent {
  id             String              @id @default(cuid())
  name           String
  description    String?
  type           String              @default("email") // email, chat, etc.
  config         Json?
  systemPrompt   String?             @db.Text // Custom system prompt for email generation
  isActive       Boolean             @default(true)
  capabilities   String[]            @default([]) // web_scraping, linkedin_research, personalization, appointment_scheduling, calendar_integration
  organizationId String
  // Calendar Integration
  calendarEnabled Boolean             @default(false)
  calendarProvider String?           // google, cal_com, outlook
  calendarConfig Json?               // Provider-specific configuration
  defaultMeetingDuration Int?        @default(30) // Default meeting duration in minutes
  availableTimeSlots Json?           // Available time slots configuration
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  organization   Organization        @relation(fields: [organizationId], references: [id])
  campaigns      Campaign[]
  campaignSteps  CampaignStep[]
  conversations  AgentConversation[]
  replies        AgentReply[]
  appointments   Appointment[]
  reminders      Reminder[]
  knowledgeBases KnowledgeBaseAgent[]
  emailAccounts  EmailAccount[] @relation("EmailAccountAgent")

  @@index([organizationId])
}

// Model for knowledge bases used by AI agents
model KnowledgeBase {
  id             String                    @id @default(cuid())
  name           String                    @default("Organization Knowledge Base")
  description    String?
  chromaCollectionId String?               // Chroma collection ID
  vectorCount    Int                       @default(0) // Number of vectors stored
  organizationId String                    @unique // One knowledge base per organization
  createdAt      DateTime                  @default(now())
  updatedAt      DateTime                  @updatedAt
  organization   Organization              @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  agents         KnowledgeBaseAgent[]      // Many-to-many relationship with agents
  documents      KnowledgeBaseDocument[]   // Individual documents/chunks

  @@index([organizationId])
  @@index([chromaCollectionId])
}

model KnowledgeBaseAgent {
  id              String        @id @default(cuid())
  knowledgeBaseId String
  agentId         String
  createdAt       DateTime      @default(now())
  knowledgeBase   KnowledgeBase @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  agent           Agent         @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@unique([knowledgeBaseId, agentId])
  @@index([knowledgeBaseId])
  @@index([agentId])
}

model KnowledgeBaseDocument {
  id              String        @id @default(cuid())
  knowledgeBaseId String
  title           String?
  content         String        @db.Text
  type            KnowledgeBaseDocumentType @default(TEXT)
  sourceUrl       String?       // For web scraping
  filePath        String?       // For uploaded files
  fileName        String?       // Original file name
  fileSize        Int?          // File size in bytes
  mimeType        String?       // File MIME type
  metadata        Json?         // Additional metadata
  vectorId        String?       // Chroma vector ID
  pageNumber      Int?          // For PDF pages
  chunkIndex      Int?          // For text chunks
  createdAt       DateTime      @default(now())
  knowledgeBase   KnowledgeBase @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)

  @@index([knowledgeBaseId])
  @@index([vectorId])
  @@index([type])
}

enum KnowledgeBaseDocumentType {
  TEXT
  PDF
  IMAGE
  WEB_URL
  DOCUMENT
}

model AgentConversation {
  id             String       @id @default(cuid())
  campaignLeadId String
  agentId        String
  status         String       @default("active") // active, closed
  lastMessageAt  DateTime?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  campaignLead   CampaignLead @relation(fields: [campaignLeadId], references: [id], onDelete: Cascade)
  agent          Agent        @relation(fields: [agentId], references: [id])
  messages       AgentMessage[]

  @@index([campaignLeadId])
  @@index([agentId])
}

model AgentMessage {
  id                  String            @id @default(cuid())
  conversationId      String
  direction           String            @default("incoming") // incoming, outgoing
  content             String
  metadata            Json?
  createdAt           DateTime          @default(now())
  conversation        AgentConversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@index([conversationId])
}

model ABTest {
  id             String          @id @default(cuid())
  name           String
  campaignId     String
  status         String          @default("active") // active, completed, paused
  testType       String          @default("subject") // subject, content, sender, time
  splitPercent   Int             @default(50) // Percentage for variant A (remaining goes to B)
  winnerMetric   String          @default("openRate") // openRate, clickRate, replyRate, conversionRate
  startDate      DateTime        @default(now())
  endDate        DateTime?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  campaign       Campaign        @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  variants       ABTestVariant[]

  @@index([campaignId])
}

model ABTestVariant {
  id             String    @id @default(cuid())
  testId         String
  name           String    // e.g., "Variant A", "Variant B"
  subject        String?
  content        String?
  templateId     String?
  emailAccountId String?
  sendTime       DateTime?
  recipientCount Int       @default(0)
  opens          Int       @default(0)
  clicks         Int       @default(0)
  replies        Int       @default(0)
  conversions    Int       @default(0)
  bounces        Int       @default(0)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  test           ABTest    @relation(fields: [testId], references: [id], onDelete: Cascade)
  template       EmailTemplate? @relation(fields: [templateId], references: [id])
  emailAccount   EmailAccount? @relation(fields: [emailAccountId], references: [id])

  @@index([testId])
  @@index([templateId])
  @@index([emailAccountId])
}

model WarmupActivity {
  id             String       @id @default(cuid())
  emailAccountId String
  targetEmail    String
  status         String       // SUCCESS, FAILED
  error          String?
  sentAt         DateTime     @default(now())
  openedAt       DateTime?
  repliedAt      DateTime?
  emailAccount   EmailAccount @relation(fields: [emailAccountId], references: [id], onDelete: Cascade)

  @@index([emailAccountId])
}

model ReputationCheck {
  id             String       @id @default(cuid())
  emailAccountId String
  score          Float
  provider       String       // The service used to check reputation
  details        Json?
  createdAt      DateTime     @default(now())
  emailAccount   EmailAccount @relation(fields: [emailAccountId], references: [id], onDelete: Cascade)

  @@index([emailAccountId])
}

model ReceivedEmail {
  id             String       @id @default(cuid())
  emailAccountId String
  messageId      String       // Original email Message-ID header
  inReplyTo      String?      // Message-ID of the email this is replying to
  references     String?      // Thread references
  from           String
  to             String
  cc             String?
  subject        String
  textBody       String?
  htmlBody       String?
  receivedAt     DateTime
  headers        Json?
  isProcessed    Boolean      @default(false)
  isRepliedTo    Boolean      @default(false)
  isRead         Boolean      @default(false)
  readAt         DateTime?    // When the email was first read
  campaignId     String?
  campaignStepId String?
  leadId         String?
  agentReplyId   String?      @unique
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  emailAccount   EmailAccount @relation(fields: [emailAccountId], references: [id], onDelete: Cascade)
  campaign       Campaign?    @relation(fields: [campaignId], references: [id])
  campaignStep   CampaignStep? @relation(fields: [campaignStepId], references: [id])
  lead           Lead?        @relation(fields: [leadId], references: [id])
  agentReply     AgentReply?

  @@index([emailAccountId])
  @@index([messageId])
  @@index([inReplyTo])
  @@index([campaignId])
  @@index([campaignStepId])
  @@index([leadId])
  @@index([agentReplyId])
}

model AgentReply {
  id             String         @id @default(cuid())
  agentId        String
  receivedEmailId String?      @unique // The email this is replying to
  campaignId     String?
  leadId         String?
  subject        String
  textContent    String
  htmlContent    String?
  sentAt         DateTime?
  status         String         @default("PENDING") // PENDING, SENT, FAILED
  error          String?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  agent          Agent          @relation(fields: [agentId], references: [id])
  receivedEmail  ReceivedEmail? @relation(fields: [receivedEmailId], references: [id])
  campaign       Campaign?      @relation(fields: [campaignId], references: [id])
  lead           Lead?          @relation(fields: [leadId], references: [id])

  @@index([agentId])
  @@index([campaignId])
  @@index([leadId])
}

model DnsTrackingConfig {
  id                   String       @id @default(cuid())
  organizationId       String       @unique
  openTrackingDomain   String?
  clickTrackingDomain  String?
  bounceTrackingDomain String?
  isVerified           Boolean      @default(false)
  verifiedAt           DateTime?
  createdAt            DateTime     @default(now())
  updatedAt            DateTime     @updatedAt
  organization         Organization @relation(fields: [organizationId], references: [id])

  @@index([organizationId])
}

model ProcessingQueue {
  id          String    @id @default(cuid())
  type        String    // sequence_condition_check, email_reply_check, etc.
  status      String    @default("pending") // pending, processing, completed, error
  payload     Json      // Contains data needed for processing
  priority    Int       @default(0) // Higher numbers = higher priority
  error       String?   // Error message if processing failed
  retryCount  Int       @default(0) // Number of times this has been retried
  maxRetries  Int       @default(3) // Maximum number of retries
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  processedAt DateTime? // When processing started
  completedAt DateTime? // When processing completed

  @@index([type, status]) // For finding pending items of a specific type
  @@index([status, priority, createdAt]) // For finding next items to process
  @@index([createdAt]) // For cleanup of old items
}

// Model for appointments created by AI agents
model Appointment {
  id             String       @id @default(cuid())
  leadId         String
  campaignId     String
  agentId        String
  title          String
  description    String?
  startTime      DateTime
  endTime        DateTime
  status         String       @default("scheduled") // scheduled, completed, cancelled, rescheduled
  location       String?      // Can be physical address or virtual meeting link
  meetingLink    String?      // URL for virtual meetings
  notes          String?
  metadata       Json?        // Additional data about the appointment
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  lead           Lead         @relation(fields: [leadId], references: [id], onDelete: Cascade)
  campaign       Campaign     @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  agent          Agent        @relation(fields: [agentId], references: [id])
  reminders      Reminder[]

  @@index([leadId])
  @@index([campaignId])
  @@index([agentId])
  @@index([startTime]) // For querying upcoming appointments
  @@index([status]) // For filtering by status
}

// Model for appointment reminders
model Reminder {
  id             String       @id @default(cuid())
  appointmentId  String
  agentId        String
  type           String       // email, notification
  timeBeforeAppointment Int   // Minutes before appointment
  status         String       @default("pending") // pending, sent, failed
  sentAt         DateTime?
  content        String?      // Custom reminder message
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  appointment    Appointment  @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  agent          Agent        @relation(fields: [agentId], references: [id])

  @@index([appointmentId])
  @@index([agentId])
  @@index([status]) // For finding pending reminders
}

// Model for tracking daily usage (rolling 24-hour window)
model DailyUsage {
  id             String       @id @default(cuid())
  organizationId String
  date           DateTime     @default(now())
  emailsSent     Int          @default(0)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id])

  @@index([organizationId, date])
}

// Custom Package Management System
model Package {
  id             String          @id @default(cuid())
  name           String
  description    String?
  price          Float           @default(0)
  billingCycle   BillingCycle    @default(MONTHLY)
  status         PackageStatus   @default(ACTIVE)

  // Email limits
  dailyEmailLimit     Int?        // null means unlimited
  monthlyEmailLimit   Int?        // null means unlimited

  // Feature flags
  aiAgentsEnabled     Boolean     @default(false)
  knowledgeBaseEnabled Boolean    @default(false)
  emailTrackingEnabled Boolean    @default(true)
  customDomainsEnabled Boolean    @default(false)
  apiAccessEnabled    Boolean     @default(false)
  prioritySupportEnabled Boolean  @default(false)

  // AI specific limits
  maxAiAgents         Int?        // null means unlimited
  maxKnowledgeBases   Int?        // null means unlimited

  // Other limits
  maxEmailAccounts    Int?        // null means unlimited
  maxLeads           Int?         // null means unlimited
  maxCampaigns       Int?         // null means unlimited
  storageLimit       Int?         // in bytes, null means unlimited

  // Metadata
  isDefault          Boolean      @default(false)
  sortOrder          Int          @default(0)
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt
  createdBy          String?      // Admin user who created this package

  // Relations
  organizations      Organization[]
  subscriptions      PackageSubscription[]

  @@index([status])
  @@index([isDefault])
}

// Track organization subscriptions to packages
model PackageSubscription {
  id             String       @id @default(cuid())
  organizationId String
  packageId      String
  startDate      DateTime     @default(now())
  endDate        DateTime?    // null means active indefinitely
  isActive       Boolean      @default(true)
  autoRenew      Boolean      @default(true)

  // Payment tracking
  stripeSubscriptionId String?
  lastPaymentDate     DateTime?
  nextPaymentDate     DateTime?

  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  package        Package      @relation(fields: [packageId], references: [id])

  @@index([organizationId])
  @@index([packageId])
  @@index([isActive])
}

// Admin analytics and business metrics
model BusinessAnalytics {
  id                    String   @id @default(cuid())
  date                  DateTime @default(now())

  // Revenue metrics
  totalRevenue          Float    @default(0)
  newSubscriptions      Int      @default(0)
  canceledSubscriptions Int      @default(0)
  upgrades              Int      @default(0)
  downgrades            Int      @default(0)

  // Usage metrics
  totalEmailsSent       Int      @default(0)
  totalActiveUsers      Int      @default(0)
  totalOrganizations    Int      @default(0)

  // Feature usage
  aiAgentsUsage         Int      @default(0)
  knowledgeBaseUsage    Int      @default(0)
  campaignsCreated      Int      @default(0)

  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@index([date])
}

// New models for campaign-specific tracking

// For standard campaigns (single-email campaigns)
model StandardCampaignTracking {
  id             String       @id @default(cuid())
  campaignId     String
  leadId         String
  emailSent      Boolean      @default(false)
  emailOpened    Boolean      @default(false)
  linkClicked    Boolean      @default(false)
  replied        Boolean      @default(false)
  unsubscribed   Boolean      @default(false)
  bounced        Boolean      @default(false)
  sentAt         DateTime?
  openedAt       DateTime?
  clickedAt      DateTime?
  repliedAt      DateTime?
  unsubscribedAt DateTime?
  bouncedAt      DateTime?
  lastUrl        String?      // Last URL clicked
  metadata       Json?        // Additional tracking data
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  campaign       Campaign     @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  lead           Lead         @relation(fields: [leadId], references: [id], onDelete: Cascade)

  @@unique([campaignId, leadId])
  @@index([campaignId])
  @@index([leadId])
}

// For sequence campaigns (multi-step campaigns)
model SequenceCampaignTracking {
  id                String       @id @default(cuid())
  campaignId        String
  leadId            String
  currentStepId     String?
  completedStepIds  String[]     @default([])
  status            String       @default("active") // active, completed, unsubscribed, bounced
  lastInteractionAt DateTime?
  metadata          Json?        // Additional tracking data
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt
  campaign          Campaign     @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  lead              Lead         @relation(fields: [leadId], references: [id], onDelete: Cascade)
  currentStep       CampaignStep? @relation("CurrentSequenceStep", fields: [currentStepId], references: [id])
  stepEvents        SequenceStepEvent[]

  @@unique([campaignId, leadId])
  @@index([campaignId])
  @@index([leadId])
  @@index([currentStepId])
}

// For tracking individual step events in sequence campaigns
model SequenceStepEvent {
  id                      String                 @id @default(cuid())
  sequenceCampaignTrackingId String
  stepId                  String
  eventType               String                 // email_sent, email_opened, link_clicked, email_replied, email_bounced, step_entered, step_completed
  status                  String                 @default("pending")
  metadata                Json?                  // Additional event data
  timestamp               DateTime               @default(now())
  createdAt               DateTime               @default(now())
  updatedAt               DateTime               @updatedAt
  sequenceCampaignTracking SequenceCampaignTracking @relation(fields: [sequenceCampaignTrackingId], references: [id], onDelete: Cascade)
  step                    CampaignStep           @relation(fields: [stepId], references: [id], onDelete: Cascade)

  @@index([sequenceCampaignTrackingId])
  @@index([stepId])
  @@index([eventType])
}

// For tracking email opens across all campaign types
model EmailOpenEvent {
  id             String    @id @default(cuid())
  campaignId     String
  leadId         String
  campaignType   String    // standard, sequence
  userAgent      String?
  ipAddress      String?
  timestamp      DateTime  @default(now())
  metadata       Json?
  createdAt      DateTime  @default(now())
  campaign       Campaign  @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  lead           Lead      @relation(fields: [leadId], references: [id], onDelete: Cascade)

  @@index([campaignId])
  @@index([leadId])
  @@index([timestamp])
}

// For tracking link clicks across all campaign types
model LinkClickEvent {
  id             String    @id @default(cuid())
  campaignId     String
  leadId         String
  campaignType   String    // standard, sequence
  url            String
  userAgent      String?
  ipAddress      String?
  timestamp      DateTime  @default(now())
  metadata       Json?
  createdAt      DateTime  @default(now())
  campaign       Campaign  @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  lead           Lead      @relation(fields: [leadId], references: [id], onDelete: Cascade)

  @@index([campaignId])
  @@index([leadId])
  @@index([timestamp])
}
