-- AlterTable
ALTER TABLE "EmailAccount" ADD COLUMN     "aiAgentEnabled" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "aiAgentId" TEXT,
ADD COLUMN     "aiReplyDelay" INTEGER DEFAULT 0,
ADD COLUMN     "aiReplyEnabled" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "aiReplyPrompt" TEXT;

-- CreateIndex
CREATE INDEX "EmailAccount_aiAgentId_idx" ON "EmailAccount"("aiAgentId");

-- AddForeignKey
ALTER TABLE "EmailAccount" ADD CONSTRAINT "EmailAccount_aiAgentId_fkey" FOREIGN KEY ("aiAgentId") REFERENCES "Agent"("id") ON DELETE SET NULL ON UPDATE CASCADE;
