-- CreateTable
CREATE TABLE "ProcessingQueue" (
    "id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "payload" JSONB NOT NULL,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "error" TEXT,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "maxRetries" INTEGER NOT NULL DEFAULT 3,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "processedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "ProcessingQueue_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ProcessingQueue_type_status_idx" ON "ProcessingQueue"("type", "status");

-- CreateIndex
CREATE INDEX "ProcessingQueue_status_priority_createdAt_idx" ON "ProcessingQueue"("status", "priority", "createdAt");

-- CreateIndex
CREATE INDEX "ProcessingQueue_createdAt_idx" ON "ProcessingQueue"("createdAt");
