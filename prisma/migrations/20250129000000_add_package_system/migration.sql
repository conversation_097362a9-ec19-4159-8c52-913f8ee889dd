-- Create<PERSON><PERSON>
CREATE TYPE "PackageStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'ARCHIVED');

-- CreateEnum
CREATE TYPE "BillingCycle" AS ENUM ('DAILY', 'WEEKLY', 'BIWEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY');

-- CreateTable
CREATE TABLE "Package" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "price" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "billingCycle" "BillingCycle" NOT NULL DEFAULT 'MONTHLY',
    "status" "PackageStatus" NOT NULL DEFAULT 'ACTIVE',
    "dailyEmailLimit" INTEGER,
    "monthlyEmailLimit" INTEGER,
    "aiAgentsEnabled" BOOLEAN NOT NULL DEFAULT false,
    "knowledgeBaseEnabled" BOOLEAN NOT NULL DEFAULT false,
    "emailTrackingEnabled" BOOLEAN NOT NULL DEFAULT true,
    "customDomainsEnabled" BOOLEAN NOT NULL DEFAULT false,
    "apiAccessEnabled" BOOLEAN NOT NULL DEFAULT false,
    "prioritySupportEnabled" BOOLEAN NOT NULL DEFAULT false,
    "maxAiAgents" INTEGER,
    "maxKnowledgeBases" INTEGER,
    "maxEmailAccounts" INTEGER,
    "maxLeads" INTEGER,
    "maxCampaigns" INTEGER,
    "storageLimit" INTEGER,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,

    CONSTRAINT "Package_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PackageSubscription" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "packageId" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endDate" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "autoRenew" BOOLEAN NOT NULL DEFAULT true,
    "stripeSubscriptionId" TEXT,
    "lastPaymentDate" TIMESTAMP(3),
    "nextPaymentDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PackageSubscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BusinessAnalytics" (
    "id" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "totalRevenue" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "newSubscriptions" INTEGER NOT NULL DEFAULT 0,
    "canceledSubscriptions" INTEGER NOT NULL DEFAULT 0,
    "upgrades" INTEGER NOT NULL DEFAULT 0,
    "downgrades" INTEGER NOT NULL DEFAULT 0,
    "totalEmailsSent" INTEGER NOT NULL DEFAULT 0,
    "totalActiveUsers" INTEGER NOT NULL DEFAULT 0,
    "totalOrganizations" INTEGER NOT NULL DEFAULT 0,
    "aiAgentsUsage" INTEGER NOT NULL DEFAULT 0,
    "knowledgeBaseUsage" INTEGER NOT NULL DEFAULT 0,
    "campaignsCreated" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BusinessAnalytics_pkey" PRIMARY KEY ("id")
);

-- AlterTable
ALTER TABLE "Organization" ADD COLUMN "packageId" TEXT;

-- CreateIndex
CREATE INDEX "Package_status_idx" ON "Package"("status");

-- CreateIndex
CREATE INDEX "Package_isDefault_idx" ON "Package"("isDefault");

-- CreateIndex
CREATE INDEX "PackageSubscription_organizationId_idx" ON "PackageSubscription"("organizationId");

-- CreateIndex
CREATE INDEX "PackageSubscription_packageId_idx" ON "PackageSubscription"("packageId");

-- CreateIndex
CREATE INDEX "PackageSubscription_isActive_idx" ON "PackageSubscription"("isActive");

-- CreateIndex
CREATE INDEX "BusinessAnalytics_date_idx" ON "BusinessAnalytics"("date");

-- AddForeignKey
ALTER TABLE "Organization" ADD CONSTRAINT "Organization_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "Package"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PackageSubscription" ADD CONSTRAINT "PackageSubscription_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "Package"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Insert default free package
INSERT INTO "Package" (
    "id",
    "name",
    "description",
    "price",
    "billingCycle",
    "status",
    "dailyEmailLimit",
    "monthlyEmailLimit",
    "aiAgentsEnabled",
    "knowledgeBaseEnabled",
    "emailTrackingEnabled",
    "customDomainsEnabled",
    "apiAccessEnabled",
    "prioritySupportEnabled",
    "maxAiAgents",
    "maxKnowledgeBases",
    "maxEmailAccounts",
    "maxLeads",
    "maxCampaigns",
    "storageLimit",
    "isDefault",
    "sortOrder",
    "createdAt",
    "updatedAt"
) VALUES (
    'free-package-default',
    'Free',
    'Free package with basic features - 100 emails per day, no AI features',
    0,
    'MONTHLY',
    'ACTIVE',
    100,
    3000,
    false,
    false,
    true,
    false,
    false,
    false,
    0,
    0,
    1,
    1000,
    10,
    **********, -- 1GB in bytes
    true,
    0,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Assign free package to all existing organizations that don't have a package
UPDATE "Organization" 
SET "packageId" = 'free-package-default' 
WHERE "packageId" IS NULL;
