/*
  Warnings:

  - You are about to drop the column `agentId` on the `KnowledgeBase` table. All the data in the column will be lost.
  - Added the required column `organizationId` to the `KnowledgeBase` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "KnowledgeBaseType" AS ENUM ('TEXT', 'PDF', 'IMAGE', 'WEB_URL', 'DOCUMENT');

-- DropForeignKey
ALTER TABLE "KnowledgeBase" DROP CONSTRAINT "KnowledgeBase_agentId_fkey";

-- DropIndex
DROP INDEX "KnowledgeBase_agentId_idx";

-- AlterTable
ALTER TABLE "KnowledgeBase" DROP COLUMN "agentId",
ADD COLUMN     "chromaCollectionId" TEXT,
ADD COLUMN     "fileName" TEXT,
ADD COLUMN     "filePath" TEXT,
ADD COLUMN     "fileSize" INTEGER,
ADD COLUMN     "mimeType" TEXT,
ADD COLUMN     "organizationId" TEXT NOT NULL,
ADD COLUMN     "sourceUrl" TEXT,
ADD COLUMN     "type" "KnowledgeBaseType" NOT NULL DEFAULT 'TEXT',
ADD COLUMN     "vectorCount" INTEGER NOT NULL DEFAULT 0,
ALTER COLUMN "content" DROP NOT NULL;

-- CreateTable
CREATE TABLE "KnowledgeBaseAgent" (
    "id" TEXT NOT NULL,
    "knowledgeBaseId" TEXT NOT NULL,
    "agentId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "KnowledgeBaseAgent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KnowledgeBaseDocument" (
    "id" TEXT NOT NULL,
    "knowledgeBaseId" TEXT NOT NULL,
    "title" TEXT,
    "content" TEXT NOT NULL,
    "metadata" JSONB,
    "vectorId" TEXT,
    "pageNumber" INTEGER,
    "chunkIndex" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "KnowledgeBaseDocument_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "KnowledgeBaseAgent_knowledgeBaseId_idx" ON "KnowledgeBaseAgent"("knowledgeBaseId");

-- CreateIndex
CREATE INDEX "KnowledgeBaseAgent_agentId_idx" ON "KnowledgeBaseAgent"("agentId");

-- CreateIndex
CREATE UNIQUE INDEX "KnowledgeBaseAgent_knowledgeBaseId_agentId_key" ON "KnowledgeBaseAgent"("knowledgeBaseId", "agentId");

-- CreateIndex
CREATE INDEX "KnowledgeBaseDocument_knowledgeBaseId_idx" ON "KnowledgeBaseDocument"("knowledgeBaseId");

-- CreateIndex
CREATE INDEX "KnowledgeBaseDocument_vectorId_idx" ON "KnowledgeBaseDocument"("vectorId");

-- CreateIndex
CREATE INDEX "KnowledgeBase_organizationId_idx" ON "KnowledgeBase"("organizationId");

-- CreateIndex
CREATE INDEX "KnowledgeBase_chromaCollectionId_idx" ON "KnowledgeBase"("chromaCollectionId");

-- AddForeignKey
ALTER TABLE "KnowledgeBase" ADD CONSTRAINT "KnowledgeBase_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseAgent" ADD CONSTRAINT "KnowledgeBaseAgent_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseAgent" ADD CONSTRAINT "KnowledgeBaseAgent_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "Agent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseDocument" ADD CONSTRAINT "KnowledgeBaseDocument_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE CASCADE ON UPDATE CASCADE;
