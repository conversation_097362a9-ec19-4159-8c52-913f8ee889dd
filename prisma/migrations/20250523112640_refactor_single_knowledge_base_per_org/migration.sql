/*
  Warnings:

  - You are about to drop the column `content` on the `KnowledgeBase` table. All the data in the column will be lost.
  - You are about to drop the column `fileName` on the `KnowledgeBase` table. All the data in the column will be lost.
  - You are about to drop the column `filePath` on the `KnowledgeBase` table. All the data in the column will be lost.
  - You are about to drop the column `fileSize` on the `KnowledgeBase` table. All the data in the column will be lost.
  - You are about to drop the column `mimeType` on the `KnowledgeBase` table. All the data in the column will be lost.
  - You are about to drop the column `sourceUrl` on the `KnowledgeBase` table. All the data in the column will be lost.
  - You are about to drop the column `type` on the `KnowledgeBase` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[organizationId]` on the table `KnowledgeBase` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "KnowledgeBaseDocumentType" AS ENUM ('TEXT', 'PDF', 'IMAGE', 'WEB_URL', 'DOCUMENT');

-- AlterTable
ALTER TABLE "KnowledgeBase" DROP COLUMN "content",
DROP COLUMN "fileName",
DROP COLUMN "filePath",
DROP COLUMN "fileSize",
DROP COLUMN "mimeType",
DROP COLUMN "sourceUrl",
DROP COLUMN "type",
ALTER COLUMN "name" SET DEFAULT 'Organization Knowledge Base';

-- AlterTable
ALTER TABLE "KnowledgeBaseDocument" ADD COLUMN     "fileName" TEXT,
ADD COLUMN     "filePath" TEXT,
ADD COLUMN     "fileSize" INTEGER,
ADD COLUMN     "mimeType" TEXT,
ADD COLUMN     "sourceUrl" TEXT,
ADD COLUMN     "type" "KnowledgeBaseDocumentType" NOT NULL DEFAULT 'TEXT';

-- DropEnum
DROP TYPE "KnowledgeBaseType";

-- CreateIndex
CREATE UNIQUE INDEX "KnowledgeBase_organizationId_key" ON "KnowledgeBase"("organizationId");

-- CreateIndex
CREATE INDEX "KnowledgeBaseDocument_type_idx" ON "KnowledgeBaseDocument"("type");
