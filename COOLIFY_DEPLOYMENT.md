# Coolify Direct Build Deployment Guide

This guide will help you deploy the Avian Email application using Coolify's direct build feature for seamless CI/CD.

## Prerequisites

1. **Coolify Instance**: Running Coolify server
2. **Git Repository**: Your code pushed to a Git repository (GitHub, GitLab, etc.)
3. **Database**: PostgreSQL database accessible from Coolify
4. **Redis**: Redis instance accessible from Coolify

## Deployment Steps

### 1. Create New Application in Coolify

1. Go to your Coolify dashboard
2. Click "New Application"
3. Choose "Git Repository"
4. Connect your repository containing this code

### 2. Configure Build Settings

**Build Configuration:**
- **Build Type**: Dockerfile
- **Dockerfile Path**: `Dockerfile` (in root directory)
- **Build Context**: `.` (root directory)
- **Port**: `3002`

### 3. Set Environment Variables

**Required Variables:**
```bash
# Application
NODE_ENV=production
PORT=3002

# Database
DATABASE_URL=postgresql://user:password@host:port/database
DIRECT_URL=postgresql://user:password@host:port/database

# Redis
REDIS_URL=redis://user:password@host:port

# Security
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-random-secret-string
ENCRYPTION_KEY=your-32-character-encryption-key
CRON_SECRET=your-cron-secret

# AI Provider (OpenAI)
AI_PROVIDER=openai
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4

# OR AI Provider (Ollama)
AI_PROVIDER=ollama
OLLAMA_BASE_URL=https://your-ollama-instance.com
OLLAMA_MODEL=deepseek-r1:latest
OLLAMA_EMBEDDING_MODEL=nomic-embed-text:latest

# Vector Database
CHROMA_API_URL=https://chroma.wattlesol.com/
```

**Optional Variables:**
```bash
# Monitoring
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn
SENTRY_AUTH_TOKEN=your-sentry-token

# Email Services
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASSWORD=your-smtp-password

# Additional APIs
SERPER_API_KEY=your-serper-key
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### 4. Configure Health Check

**Health Check Settings:**
- **Path**: `/api/health-check`
- **Port**: `3002`
- **Interval**: `30s`
- **Timeout**: `10s`
- **Retries**: `3`
- **Start Period**: `60s`

### 5. Resource Configuration

**Recommended Resources:**
- **Memory**: 2GB (minimum 1GB)
- **CPU**: 1 vCPU
- **Storage**: 10GB

### 6. Domain Configuration

1. Set your custom domain in Coolify
2. Update `NEXTAUTH_URL` environment variable to match your domain
3. Configure SSL certificate (Coolify handles this automatically)

## Build Process

When you deploy, Coolify will:

1. **Clone Repository**: Pull latest code from your Git repository
2. **Build Image**: Use your Dockerfile to build the application
3. **Install Dependencies**: Run `npm ci --only=production`
4. **Generate Prisma Client**: Run `npx prisma generate`
5. **Build Application**: Run `npm run build`
6. **Start Container**: Run the application with your environment variables

## Automatic Deployments

### Git Integration
- **Auto-deploy on push**: Enable in Coolify settings
- **Branch**: Set to your main branch (e.g., `main` or `master`)
- **Build on PR**: Optional, for testing

### Webhook Setup
Coolify will automatically set up webhooks in your Git repository for automatic deployments.

## Monitoring and Logs

### Application Logs
- View real-time logs in Coolify dashboard
- Logs include application startup, database connections, and errors

### Health Monitoring
- Coolify monitors `/api/health-check` endpoint
- Automatic restart if health checks fail
- Email notifications on deployment failures

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check environment variables are set correctly
   - Verify database and Redis connectivity
   - Review build logs in Coolify

2. **Database Connection Issues**
   - Ensure `DATABASE_URL` is correct
   - Check if database is accessible from Coolify
   - Verify database credentials

3. **Memory Issues**
   - Increase memory allocation in Coolify
   - AI features require more memory (2GB recommended)

4. **Startup Timeouts**
   - Increase health check start period
   - Check if all services are starting properly

### Debug Commands

Access container shell in Coolify:
```bash
# Check application status
ps aux

# Check logs
tail -f logs/sequence-processor.log

# Test database connection
npx prisma db ping

# Check environment variables
env | grep -E "(DATABASE|REDIS|AI_)"
```

## CI/CD Workflow

1. **Development**: Make changes locally
2. **Commit**: Push changes to Git repository
3. **Auto-Deploy**: Coolify automatically builds and deploys
4. **Monitor**: Check deployment status in Coolify dashboard
5. **Verify**: Test application functionality

## Security Considerations

1. **Environment Variables**: Never commit secrets to Git
2. **Database Access**: Use strong passwords and limit access
3. **API Keys**: Rotate keys regularly
4. **SSL/TLS**: Ensure HTTPS is enabled
5. **Firewall**: Limit access to necessary ports only

## Performance Optimization

1. **Resource Allocation**: Monitor and adjust based on usage
2. **Database Optimization**: Use connection pooling
3. **Redis Caching**: Ensure Redis is properly configured
4. **CDN**: Consider using a CDN for static assets

Your application is now ready for seamless CI/CD with Coolify! 🚀
