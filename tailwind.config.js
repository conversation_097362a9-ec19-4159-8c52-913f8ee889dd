/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: 'class', // Enable dark mode with class strategy
  theme: {
    extend: {
      maxWidth: {
        '7xl': '80rem',
      },
      colors: {
        // Light mode colors
        'light-bg': '#f0f2f5',
        'light-card': '#ffffff',
        'light-text': '#000000',
        'light-border': '#e8e8e8',

        // Dark mode colors
        'dark-bg': '#000000',
        'dark-card': '#1f1f1f',
        'dark-header': '#141414',
        'dark-text': '#ffffff',
        'dark-border': '#303030',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}
