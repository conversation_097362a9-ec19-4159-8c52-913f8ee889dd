# 🔐 Super Admin Dashboard

A secure, separate frontend for managing the Avian Email platform. This dashboard provides comprehensive administrative controls for application owners.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- The main Avian Email backend running on port 3002
- Super admin credentials configured

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

The dashboard will be available at: `http://localhost:3001`

## 🔑 First Time Setup

### 1. Configure Environment Variables
The `.env.local` file is already configured with:
```env
NEXT_PUBLIC_API_URL=http://localhost:3002
SUPER_ADMIN_SECRET=super-secret-admin-key-change-in-production-2024
SUPER_ADMIN_JWT_SECRET=super-admin-jwt-secret-change-in-production-2024
```

### 2. Initialize Super Admin
On first visit, you'll be prompted to create the first super admin account using:
- Email address
- Secure password
- Admin secret key: `super-secret-admin-key-change-in-production-2024`

### 3. Initialize Package System
After login, click "Initialize System" to:
- Create default packages (Free, Pro, Enterprise)
- Assign free packages to existing organizations
- Set up Stripe integration

## 📊 Features

### Dashboard Overview
- Revenue metrics and growth tracking
- Subscription analytics by package
- User activity and engagement stats
- System status and health checks

### Package Management
- Create/edit/delete subscription packages
- Configure pricing and billing cycles
- Set email limits and account restrictions
- Manage AI feature permissions
- Stripe integration for payments

### Business Analytics
- Real-time revenue tracking
- Subscription conversion metrics
- Organization usage statistics
- Package popularity insights

## 🔒 Security Features

### Authentication
- JWT-based secure authentication
- Token expiration and refresh
- Session management
- Automatic logout on token expiry

### Access Control
- Super admin only access
- IP whitelisting support
- Secure API communication
- Protected routes

## 🛠️ Development

### Available Scripts
```bash
npm run dev     # Start development server (port 3001)
npm run build   # Build for production
npm run start   # Start production server
npm run lint    # Run ESLint
```

### API Integration
The dashboard communicates with the main backend via:
- `POST /api/super-admin/auth` - Authentication
- `GET /api/super-admin/analytics` - Business metrics
- `CRUD /api/super-admin/packages` - Package management
- `POST /api/super-admin/initialize` - System setup

## 🚀 Usage

1. **Start the main backend**: Make sure your main Avian Email app is running on port 3002
2. **Start the admin dashboard**: Run `npm run dev` (will start on port 3001)
3. **First login**: Use the admin secret to create your super admin account
4. **Initialize system**: Set up the package system and assign packages to organizations
5. **Manage platform**: Use the dashboard to manage packages, view analytics, and monitor the system

---

**⚠️ Security Warning**: This dashboard provides full administrative access. Protect credentials and limit access to authorized personnel only.
