(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[201],{842:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var t=a(5155),r=a(2115),i=a(5731),l=a(9074),n=a(5868),c=a(4870),d=a(8515),o=a(3109),m=a(9946);let x=(0,m.A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var h=a(7108);let u=(0,m.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);function p(){let[e,s]=(0,r.useState)(null),[a,m]=(0,r.useState)(!0),[p,g]=(0,r.useState)(null),[j,y]=(0,r.useState)("30d");(0,r.useEffect)(()=>{v()},[j]);let v=async()=>{try{m(!0);let e=await i.FH.get("/super-admin/analytics?type=overview");s(e.data.data||e.data)}catch(s){var e,a;g((null==(a=s.response)||null==(e=a.data)?void 0:e.error)||"Failed to fetch revenue data")}finally{m(!1)}},N=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),b=e=>new Intl.NumberFormat("en-US").format(e);if(a)return(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(p)return(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:[(0,t.jsx)("p",{className:"text-red-800",children:p}),(0,t.jsx)("button",{onClick:v,className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Try again"})]});if(!e)return(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No revenue data available"})});let f=e.subscriptions.active>0?e.revenue.total/e.subscriptions.active:0;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Revenue Analytics"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Track revenue performance and subscription metrics"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("select",{value:j,onChange:e=>y(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"7d",children:"Last 7 days"}),(0,t.jsx)("option",{value:"30d",children:"Last 30 days"}),(0,t.jsx)("option",{value:"90d",children:"Last 90 days"}),(0,t.jsx)("option",{value:"1y",children:"Last year"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:N(e.revenue.total)})]}),(0,t.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,t.jsx)(n.A,{className:"h-6 w-6 text-green-600"})})]}),(0,t.jsxs)("div",{className:"mt-4 flex items-center",children:[e.revenue.growth>=0?(0,t.jsx)(c.A,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)(d.A,{className:"h-4 w-4 text-red-500"}),(0,t.jsxs)("span",{className:"text-sm font-medium ".concat(e.revenue.growth>=0?"text-green-600":"text-red-600"),children:[Math.abs(e.revenue.growth).toFixed(1),"%"]}),(0,t.jsx)("span",{className:"text-sm text-gray-500 ml-2",children:"vs last period"})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Monthly Revenue"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:N(e.revenue.monthly)})]}),(0,t.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,t.jsx)(o.A,{className:"h-6 w-6 text-blue-600"})})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Current month"})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Subscriptions"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:b(e.subscriptions.active)})]}),(0,t.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,t.jsx)(x,{className:"h-6 w-6 text-purple-600"})})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:[b(e.subscriptions.cancelled)," cancelled"]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg Revenue/User"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:N(f)})]}),(0,t.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,t.jsx)(h.A,{className:"h-6 w-6 text-orange-600"})})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Per subscription"})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Revenue by Package"})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"space-y-6",children:e.subscriptions.byPackage.map((s,a)=>{let r=e.revenue.total>0?s.revenue/e.revenue.total*100:0;return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:s.packageName}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[b(s.count)," subscribers"]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:N(s.revenue)}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[r.toFixed(1),"% of total"]})]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(r,"%")}})})]},a)})})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Subscription Health"})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Subscriptions"}),(0,t.jsx)("span",{className:"font-medium",children:b(e.subscriptions.total)})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Active Subscriptions"}),(0,t.jsx)("span",{className:"font-medium text-green-600",children:b(e.subscriptions.active)})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Cancelled Subscriptions"}),(0,t.jsx)("span",{className:"font-medium text-red-600",children:b(e.subscriptions.cancelled)})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Retention Rate"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.subscriptions.total>0?(e.subscriptions.active/e.subscriptions.total*100).toFixed(1):0,"%"]})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Package Performance"})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Packages"}),(0,t.jsx)("span",{className:"font-medium",children:b(e.packages.total)})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Active Packages"}),(0,t.jsx)("span",{className:"font-medium",children:b(e.packages.active)})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Most Popular"}),(0,t.jsx)("span",{className:"font-medium",children:e.packages.mostPopular})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Avg Revenue/Package"}),(0,t.jsx)("span",{className:"font-medium",children:N(e.packages.active>0?e.revenue.total/e.packages.active:0)})]})]})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Growth Indicators"})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-2",children:e.revenue.growth>=0?(0,t.jsx)(o.A,{className:"h-8 w-8 text-green-500"}):(0,t.jsx)(u,{className:"h-8 w-8 text-red-500"})}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[Math.abs(e.revenue.growth).toFixed(1),"%"]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Revenue Growth"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,t.jsx)(x,{className:"h-8 w-8 text-blue-500"})}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.subscriptions.total>0?(e.subscriptions.active/e.subscriptions.total*100).toFixed(1):0,"%"]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Retention Rate"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,t.jsx)(n.A,{className:"h-8 w-8 text-green-500"})}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:N(f)}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"ARPU"})]})]})})]})]})}},3109:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},4870:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},5716:(e,s,a)=>{Promise.resolve().then(a.bind(a,842))},5731:(e,s,a)=>{"use strict";a.d(s,{FH:()=>t,I5:()=>d,O5:()=>r,Pt:()=>l,R2:()=>n,Tp:()=>i,mq:()=>o,rh:()=>c});let t=a(3464).A.create({baseURL:"http://localhost:3000",headers:{"Content-Type":"application/json"}}),r=e=>{t.defaults.headers.common.Authorization="Bearer ".concat(e),localStorage.setItem("super_admin_token",e)},i=()=>{delete t.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},l=()=>localStorage.getItem("super_admin_token");{let e=l();e&&r(e)}t.interceptors.response.use(e=>e,e=>{var s;return(null==(s=e.response)?void 0:s.status)===401&&(i(),window.location.href="/login"),Promise.reject(e)});let n={login:async(e,s)=>(await t.post("/api/super-admin/auth",{action:"login",email:e,password:s})).data,setup:async(e,s,a,r)=>(await t.post("/api/super-admin/auth",{action:"setup",email:e,password:s,name:a,adminSecret:r})).data,checkSetup:async()=>(await t.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await t.get("/api/super-admin/auth")).data},c={getOverview:async()=>(await t.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await t.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await t.get("/api/super-admin/analytics?type=detailed")).data},d={getAll:async()=>(await t.get("/api/super-admin/packages")).data,getById:async e=>(await t.get("/api/super-admin/packages?id=".concat(e))).data,create:async e=>(await t.post("/api/super-admin/packages",e)).data,update:async(e,s)=>(await t.put("/api/super-admin/packages?id=".concat(e),s)).data,delete:async e=>(await t.delete("/api/super-admin/packages?id=".concat(e))).data},o={initialize:async e=>(await t.post("/api/super-admin/initialize",{action:e})).data,getInitializationStatus:async()=>(await t.get("/api/super-admin/initialize")).data}},5868:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7108:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},8515:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]])},9074:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9946:(e,s,a)=>{"use strict";a.d(s,{A:()=>m});var t=a(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,a)=>a?a.toUpperCase():s.toLowerCase()),l=e=>{let s=i(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=function(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return s.filter((e,s,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===s).join(" ").trim()},c=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,t.forwardRef)((e,s)=>{let{color:a="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:o="",children:m,iconNode:x,...h}=e;return(0,t.createElement)("svg",{ref:s,...d,width:r,height:r,stroke:a,strokeWidth:l?24*Number(i)/Number(r):i,className:n("lucide",o),...!m&&!c(h)&&{"aria-hidden":"true"},...h},[...x.map(e=>{let[s,a]=e;return(0,t.createElement)(s,a)}),...Array.isArray(m)?m:[m]])}),m=(e,s)=>{let a=(0,t.forwardRef)((a,i)=>{let{className:c,...d}=a;return(0,t.createElement)(o,{ref:i,iconNode:s,className:n("lucide-".concat(r(l(e))),"lucide-".concat(e),c),...d})});return a.displayName=l(e),a}}},e=>{var s=s=>e(e.s=s);e.O(0,[464,441,684,358],()=>s(5716)),_N_E=e.O()}]);