(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{283:(e,a,s)=>{"use strict";s.d(a,{A:()=>r,AuthProvider:()=>c});var t=s(5155),i=s(2115),n=s(5731);let l=(0,i.createContext)(void 0),r=()=>{let e=(0,i.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:a}=e,[s,r]=(0,i.useState)(null),[c,d]=(0,i.useState)(!0);(0,i.useEffect)(()=>{(async()=>{let e=(0,n.Pt)();if(e)try{let a=await n.R2.verifyToken();a.valid&&a.admin?(r(a.admin),(0,n.O5)(e)):(0,n.Tp)()}catch(e){console.error("Token verification failed:",e),(0,n.Tp)()}d(!1)})()},[]);let o=async(e,a)=>{try{let s=await n.R2.login(e,a);if(s.token&&s.admin)(0,n.O5)(s.token),r(s.admin);else throw Error("Invalid response from server")}catch(e){var s,t;throw Error((null==(t=e.response)||null==(s=t.data)?void 0:s.error)||"Login failed")}},m=async(e,a,s,t)=>{try{let i=await n.R2.setup(e,a,s,t);if(i.token&&i.admin)(0,n.O5)(i.token),r(i.admin);else throw Error("Invalid response from server")}catch(e){var i,l;throw Error((null==(l=e.response)||null==(i=l.data)?void 0:i.error)||"Setup failed")}};return(0,t.jsx)(l.Provider,{value:{admin:s,isLoading:c,isAuthenticated:!!s,login:o,logout:()=>{(0,n.Tp)(),r(null)},setup:m},children:a})}},1600:(e,a,s)=>{Promise.resolve().then(s.bind(s,9896))},2871:(e,a,s)=>{"use strict";s.d(a,{A:()=>j});var t=s(5155),i=s(2115),n=s(5695),l=s(283),r=s(6874),c=s.n(r),d=s(2713),o=s(9397),m=s(7108),u=s(7580),x=s(5868),h=s(381),p=s(5525),g=s(4835),v=s(2596);let y=[{name:"Dashboard",href:"/dashboard",icon:d.A},{name:"Analytics",href:"/analytics",icon:o.A},{name:"Packages",href:"/packages",icon:m.A},{name:"Organizations",href:"/organizations",icon:u.A},{name:"Revenue",href:"/revenue",icon:x.A},{name:"System",href:"/system",icon:h.A}];function f(e){var a;let{children:s}=e,{admin:i,logout:r}=(0,l.A)(),d=(0,n.usePathname)();return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsxs)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-gray-900",children:[(0,t.jsx)("div",{className:"flex h-16 items-center justify-center px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-8 w-8 text-blue-400"}),(0,t.jsx)("span",{className:"text-xl font-bold text-white",children:"Super Admin"})]})}),(0,t.jsx)("nav",{className:"mt-8 px-4",children:(0,t.jsx)("ul",{className:"space-y-2",children:y.map(e=>{let a=d===e.href;return(0,t.jsx)("li",{children:(0,t.jsxs)(c(),{href:e.href,className:(0,v.A)("flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",a?"bg-blue-600 text-white":"text-gray-300 hover:bg-gray-800 hover:text-white"),children:[(0,t.jsx)(e.icon,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:e.name})]})},e.name)})})}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:(0,t.jsx)("div",{className:"rounded-lg bg-gray-800 p-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-white",children:null==i?void 0:i.name}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:null==i?void 0:i.email})]}),(0,t.jsx)("button",{onClick:r,className:"rounded-md p-1 text-gray-400 hover:text-white",title:"Logout",children:(0,t.jsx)(g.A,{className:"h-5 w-5"})})]})})})]}),(0,t.jsxs)("div",{className:"pl-64",children:[(0,t.jsx)("div",{className:"sticky top-0 z-40 bg-white shadow-sm",children:(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between px-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:(null==(a=y.find(e=>e.href===d))?void 0:a.name)||"Super Admin"}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsx)("div",{className:"rounded-full bg-green-100 px-3 py-1",children:(0,t.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Production"})})})]})}),(0,t.jsx)("main",{className:"p-6",children:s})]})]})}function j(e){let{children:a}=e,{isAuthenticated:s,isLoading:r}=(0,l.A)(),c=(0,n.useRouter)();return((0,i.useEffect)(()=>{r||s||c.push("/login")},[s,r,c]),r)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):s?(0,t.jsx)(f,{children:a}):null}},5339:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5731:(e,a,s)=>{"use strict";s.d(a,{I5:()=>d,O5:()=>i,Pt:()=>l,R2:()=>r,Tp:()=>n,mq:()=>o,rh:()=>c});let t=s(3464).A.create({baseURL:"http://localhost:3002",headers:{"Content-Type":"application/json"}}),i=e=>{t.defaults.headers.common.Authorization="Bearer ".concat(e),localStorage.setItem("super_admin_token",e)},n=()=>{delete t.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},l=()=>localStorage.getItem("super_admin_token");{let e=l();e&&i(e)}t.interceptors.response.use(e=>e,e=>{var a;return(null==(a=e.response)?void 0:a.status)===401&&(n(),window.location.href="/login"),Promise.reject(e)});let r={login:async(e,a)=>(await t.post("/api/super-admin/auth",{action:"login",email:e,password:a})).data,setup:async(e,a,s,i)=>(await t.post("/api/super-admin/auth",{action:"setup",email:e,password:a,name:s,adminSecret:i})).data,checkSetup:async()=>(await t.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await t.get("/api/super-admin/auth")).data},c={getOverview:async()=>(await t.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await t.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await t.get("/api/super-admin/analytics?type=detailed")).data},d={getAll:async()=>(await t.get("/api/super-admin/packages")).data,getById:async e=>(await t.get("/api/super-admin/packages?id=".concat(e))).data,create:async e=>(await t.post("/api/super-admin/packages",e)).data,update:async(e,a)=>(await t.put("/api/super-admin/packages?id=".concat(e),a)).data,delete:async e=>(await t.delete("/api/super-admin/packages?id=".concat(e))).data},o={initialize:async e=>(await t.post("/api/super-admin/initialize",{action:e})).data,getInitializationStatus:async()=>(await t.get("/api/super-admin/initialize")).data}},9896:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>p});var t=s(5155),i=s(2115),n=s(5731),l=s(2871),r=s(5339),c=s(5868),d=s(7108),o=s(7580),m=s(9397),u=s(9946);let x=(0,u.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),h=(0,u.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);function p(){var e;let[a,s]=(0,i.useState)(null),[u,p]=(0,i.useState)(null),[g,v]=(0,i.useState)(!0),[y,f]=(0,i.useState)("");(0,i.useEffect)(()=>{(async()=>{try{let[e,a]=await Promise.all([n.rh.getOverview(),n.mq.getInitializationStatus()]);s(e.data),p(a)}catch(s){var e,a;f((null==(a=s.response)||null==(e=a.data)?void 0:e.error)||"Failed to load dashboard data")}finally{v(!1)}})()},[]);let j=async()=>{try{v(!0),await n.mq.initialize("full-setup");let[e,a]=await Promise.all([n.rh.getOverview(),n.mq.getInitializationStatus()]);s(e.data),p(a)}catch(s){var e,a;f((null==(a=s.response)||null==(e=a.data)?void 0:e.error)||"Failed to initialize system")}finally{v(!1)}};if(g)return(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(y)return(0,t.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)(r.A,{className:"h-5 w-5 text-red-400"}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-red-700",children:y})]})]})});let N=[{name:"Total Revenue",value:"$".concat((null==a?void 0:a.revenue.total.toFixed(2))||"0.00"),change:"".concat(((null==a?void 0:a.revenue.growth)||0)>=0?"+":"").concat(((null==a?void 0:a.revenue.growth)||0).toFixed(1),"%"),changeType:((null==a?void 0:a.revenue.growth)||0)>=0?"positive":"negative",icon:c.A},{name:"Active Subscriptions",value:(null==a?void 0:a.subscriptions.active)||0,change:"".concat((null==a?void 0:a.subscriptions.total)||0," total"),changeType:"neutral",icon:d.A},{name:"Total Users",value:(null==a?void 0:a.users.total)||0,change:"".concat((null==a?void 0:a.users.newThisMonth)||0," new this month"),changeType:"positive",icon:o.A},{name:"Active Packages",value:(null==a?void 0:a.packages.active)||0,change:"Most popular: ".concat((null==a?void 0:a.packages.mostPopular)||"None"),changeType:"neutral",icon:m.A}];return(0,t.jsx)(l.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[u&&!u.initialized&&(0,t.jsx)("div",{className:"rounded-md bg-yellow-50 p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)(r.A,{className:"h-5 w-5 text-yellow-400"}),(0,t.jsxs)("div",{className:"ml-3 flex-1",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"System Setup Required"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-yellow-700",children:"The package system needs to be initialized. This will create default packages and assign them to existing organizations."}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("button",{onClick:j,className:"bg-yellow-100 px-3 py-2 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-200",children:"Initialize System"})})]})]})}),u&&u.initialized&&(0,t.jsx)("div",{className:"rounded-md bg-green-50 p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)(x,{className:"h-5 w-5 text-green-400"}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"System Initialized"}),(0,t.jsxs)("div",{className:"mt-2 text-sm text-green-700",children:["Package system is active with ",(null==(e=u.packages)?void 0:e.total)||0," packages configured."]})]})]})}),(0,t.jsx)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:N.map(e=>(0,t.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(e.icon,{className:"h-6 w-6 text-gray-400"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:e.name}),(0,t.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.value})]})})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("div",{className:"flex items-center text-sm",children:(0,t.jsx)("span",{className:"".concat("positive"===e.changeType?"text-green-600":"negative"===e.changeType?"text-red-600":"text-gray-600"),children:e.change})})})]})},e.name))}),(0,t.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,t.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,t.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"mt-5 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsxs)("button",{className:"relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)(d.A,{className:"mx-auto h-8 w-8 text-gray-400"}),(0,t.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Create New Package"})]}),(0,t.jsxs)("button",{className:"relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)(h,{className:"mx-auto h-8 w-8 text-gray-400"}),(0,t.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"View Analytics"})]}),(0,t.jsxs)("button",{className:"relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)(o.A,{className:"mx-auto h-8 w-8 text-gray-400"}),(0,t.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Manage Organizations"})]})]})]})}),(0,t.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,t.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,t.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"System Overview"}),(0,t.jsx)("div",{className:"mt-5",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Monthly Revenue"}),(0,t.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:["$",(null==a?void 0:a.revenue.monthly.toFixed(2))||"0.00"]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Active Users"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900",children:(null==a?void 0:a.users.active)||0})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Cancelled Subscriptions"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900",children:(null==a?void 0:a.subscriptions.cancelled)||0})]})]})})]})})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[464,700,441,684,358],()=>a(1600)),_N_E=e.O()}]);