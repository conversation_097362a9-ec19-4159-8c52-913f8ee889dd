(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{283:(a,e,t)=>{"use strict";t.d(e,{A:()=>o,AuthProvider:()=>u});var i=t(5155),n=t(2115),r=t(5731);let s=(0,n.createContext)(void 0),o=()=>{let a=(0,n.useContext)(s);if(void 0===a)throw Error("useAuth must be used within an AuthProvider");return a},u=a=>{let{children:e}=a,[t,o]=(0,n.useState)(null),[u,d]=(0,n.useState)(!0);(0,n.useEffect)(()=>{(async()=>{let a=(0,r.Pt)();if(a)try{let e=await r.R2.verifyToken();e.valid&&e.admin?(o(e.admin),(0,r.O5)(a)):(0,r.Tp)()}catch(a){console.error("Token verification failed:",a),(0,r.Tp)()}d(!1)})()},[]);let l=async(a,e)=>{try{let t=await r.R2.login(a,e);if(t.token&&t.admin)(0,r.O5)(t.token),o(t.admin);else throw Error("Invalid response from server")}catch(a){var t,i;throw Error((null==(i=a.response)||null==(t=i.data)?void 0:t.error)||"Login failed")}},c=async(a,e,t,i)=>{try{let n=await r.R2.setup(a,e,t,i);if(n.token&&n.admin)(0,r.O5)(n.token),o(n.admin);else throw Error("Invalid response from server")}catch(a){var n,s;throw Error((null==(s=a.response)||null==(n=s.data)?void 0:n.error)||"Setup failed")}};return(0,i.jsx)(s.Provider,{value:{admin:t,isLoading:u,isAuthenticated:!!t,login:l,logout:()=>{(0,r.Tp)(),o(null)},setup:c},children:e})}},1111:(a,e,t)=>{Promise.resolve().then(t.bind(t,3792))},3792:(a,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>o});var i=t(5155),n=t(2115),r=t(5695),s=t(283);function o(){let{isAuthenticated:a,isLoading:e}=(0,s.A)(),t=(0,r.useRouter)();return((0,n.useEffect)(()=>{e||(a?t.push("/dashboard"):t.push("/login"))},[a,e,t]),e)?(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,i.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):null}},5695:(a,e,t)=>{"use strict";var i=t(8999);t.o(i,"usePathname")&&t.d(e,{usePathname:function(){return i.usePathname}}),t.o(i,"useRouter")&&t.d(e,{useRouter:function(){return i.useRouter}})},5731:(a,e,t)=>{"use strict";t.d(e,{FH:()=>i,I5:()=>d,O5:()=>n,Pt:()=>s,R2:()=>o,Tp:()=>r,mq:()=>l,rh:()=>u});let i=t(3464).A.create({baseURL:"http://localhost:3000",headers:{"Content-Type":"application/json"}}),n=a=>{i.defaults.headers.common.Authorization="Bearer ".concat(a),localStorage.setItem("super_admin_token",a)},r=()=>{delete i.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},s=()=>localStorage.getItem("super_admin_token");{let a=s();a&&n(a)}i.interceptors.response.use(a=>a,a=>{var e;return(null==(e=a.response)?void 0:e.status)===401&&(r(),window.location.href="/login"),Promise.reject(a)});let o={login:async(a,e)=>(await i.post("/api/super-admin/auth",{action:"login",email:a,password:e})).data,setup:async(a,e,t,n)=>(await i.post("/api/super-admin/auth",{action:"setup",email:a,password:e,name:t,adminSecret:n})).data,checkSetup:async()=>(await i.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await i.get("/api/super-admin/auth")).data},u={getOverview:async()=>(await i.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await i.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await i.get("/api/super-admin/analytics?type=detailed")).data},d={getAll:async()=>(await i.get("/api/super-admin/packages")).data,getById:async a=>(await i.get("/api/super-admin/packages?id=".concat(a))).data,create:async a=>(await i.post("/api/super-admin/packages",a)).data,update:async(a,e)=>(await i.put("/api/super-admin/packages?id=".concat(a),e)).data,delete:async a=>(await i.delete("/api/super-admin/packages?id=".concat(a))).data},l={initialize:async a=>(await i.post("/api/super-admin/initialize",{action:a})).data,getInitializationStatus:async()=>(await i.get("/api/super-admin/initialize")).data}}},a=>{var e=e=>a(a.s=e);a.O(0,[464,441,684,358],()=>e(1111)),_N_E=a.O()}]);