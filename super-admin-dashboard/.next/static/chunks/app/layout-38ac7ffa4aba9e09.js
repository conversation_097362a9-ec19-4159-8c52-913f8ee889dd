(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{157:(a,e,t)=>{Promise.resolve().then(t.t.bind(t,8346,23)),Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,283))},283:(a,e,t)=>{"use strict";t.d(e,{A:()=>o,AuthProvider:()=>l});var i=t(5155),n=t(2115),r=t(5731);let s=(0,n.createContext)(void 0),o=()=>{let a=(0,n.useContext)(s);if(void 0===a)throw Error("useAuth must be used within an AuthProvider");return a},l=a=>{let{children:e}=a,[t,o]=(0,n.useState)(null),[l,d]=(0,n.useState)(!0);(0,n.useEffect)(()=>{(async()=>{let a=(0,r.Pt)();if(a)try{let e=await r.R2.verifyToken();e.valid&&e.admin?(o(e.admin),(0,r.O5)(a)):(0,r.Tp)()}catch(a){console.error("Token verification failed:",a),(0,r.Tp)()}d(!1)})()},[]);let c=async(a,e)=>{try{let t=await r.R2.login(a,e);if(t.token&&t.admin)(0,r.O5)(t.token),o(t.admin);else throw Error("Invalid response from server")}catch(a){var t,i;throw Error((null==(i=a.response)||null==(t=i.data)?void 0:t.error)||"Login failed")}},p=async(a,e,t,i)=>{try{let n=await r.R2.setup(a,e,t,i);if(n.token&&n.admin)(0,r.O5)(n.token),o(n.admin);else throw Error("Invalid response from server")}catch(a){var n,s;throw Error((null==(s=a.response)||null==(n=s.data)?void 0:n.error)||"Setup failed")}};return(0,i.jsx)(s.Provider,{value:{admin:t,isLoading:l,isAuthenticated:!!t,login:c,logout:()=>{(0,r.Tp)(),o(null)},setup:p},children:e})}},347:()=>{},5731:(a,e,t)=>{"use strict";t.d(e,{I5:()=>d,O5:()=>n,Pt:()=>s,R2:()=>o,Tp:()=>r,mq:()=>c,rh:()=>l});let i=t(3464).A.create({baseURL:"http://localhost:3002",headers:{"Content-Type":"application/json"}}),n=a=>{i.defaults.headers.common.Authorization="Bearer ".concat(a),localStorage.setItem("super_admin_token",a)},r=()=>{delete i.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},s=()=>localStorage.getItem("super_admin_token");{let a=s();a&&n(a)}i.interceptors.response.use(a=>a,a=>{var e;return(null==(e=a.response)?void 0:e.status)===401&&(r(),window.location.href="/login"),Promise.reject(a)});let o={login:async(a,e)=>(await i.post("/api/super-admin/auth",{action:"login",email:a,password:e})).data,setup:async(a,e,t,n)=>(await i.post("/api/super-admin/auth",{action:"setup",email:a,password:e,name:t,adminSecret:n})).data,checkSetup:async()=>(await i.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await i.get("/api/super-admin/auth")).data},l={getOverview:async()=>(await i.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await i.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await i.get("/api/super-admin/analytics?type=detailed")).data},d={getAll:async()=>(await i.get("/api/super-admin/packages")).data,getById:async a=>(await i.get("/api/super-admin/packages?id=".concat(a))).data,create:async a=>(await i.post("/api/super-admin/packages",a)).data,update:async(a,e)=>(await i.put("/api/super-admin/packages?id=".concat(a),e)).data,delete:async a=>(await i.delete("/api/super-admin/packages?id=".concat(a))).data},c={initialize:async a=>(await i.post("/api/super-admin/initialize",{action:a})).data,getInitializationStatus:async()=>(await i.get("/api/super-admin/initialize")).data}},8346:a=>{a.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}}},a=>{var e=e=>a(a.s=e);a.O(0,[838,464,441,684,358],()=>e(157)),_N_E=a.O()}]);