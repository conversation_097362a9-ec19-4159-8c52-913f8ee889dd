(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{283:(e,a,t)=>{"use strict";t.d(a,{A:()=>l,AuthProvider:()=>o});var r=t(5155),s=t(2115),i=t(5731);let n=(0,s.createContext)(void 0),l=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},o=e=>{let{children:a}=e,[t,l]=(0,s.useState)(null),[o,d]=(0,s.useState)(!0);(0,s.useEffect)(()=>{(async()=>{let e=(0,i.Pt)();if(e)try{let a=await i.R2.verifyToken();a.valid&&a.admin?(l(a.admin),(0,i.O5)(e)):(0,i.Tp)()}catch(e){console.error("Token verification failed:",e),(0,i.Tp)()}d(!1)})()},[]);let c=async(e,a)=>{try{let t=await i.R2.login(e,a);if(t.token&&t.admin)(0,i.O5)(t.token),l(t.admin);else throw Error("Invalid response from server")}catch(e){var t,r;throw Error((null==(r=e.response)||null==(t=r.data)?void 0:t.error)||"Login failed")}},u=async(e,a,t,r)=>{try{let s=await i.R2.setup(e,a,t,r);if(s.token&&s.admin)(0,i.O5)(s.token),l(s.admin);else throw Error("Invalid response from server")}catch(e){var s,n;throw Error((null==(n=e.response)||null==(s=n.data)?void 0:s.error)||"Setup failed")}};return(0,r.jsx)(n.Provider,{value:{admin:t,isLoading:o,isAuthenticated:!!t,login:c,logout:()=>{(0,i.Tp)(),l(null)},setup:u},children:a})}},302:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>p});var r=t(5155),s=t(2115),i=t(5695),n=t(283),l=t(5731),o=t(5525),d=t(5339),c=t(9946);let u=(0,c.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),m=(0,c.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);function p(){let[e,a]=(0,s.useState)(!1),[t,c]=(0,s.useState)(!1),[p,h]=(0,s.useState)(!1),[x,y]=(0,s.useState)(""),[f,g]=(0,s.useState)({email:"",password:"",name:"",adminSecret:""}),{login:w,setup:b,isAuthenticated:v}=(0,n.A)(),k=(0,i.useRouter)();(0,s.useEffect)(()=>{v&&k.push("/dashboard")},[v,k]),(0,s.useEffect)(()=>{(async()=>{try{let e=await l.R2.checkSetup();a(e.setupRequired)}catch(e){console.error("Failed to check setup status:",e)}})()},[]);let j=async a=>{a.preventDefault(),c(!0),y("");try{e?await b(f.email,f.password,f.name,f.adminSecret):await w(f.email,f.password),k.push("/dashboard")}catch(e){y(e.message)}finally{c(!1)}},N=e=>{g({...f,[e.target.name]:e.target.value})};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100",children:(0,r.jsx)(o.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:e?"Setup Super Admin":"Super Admin Login"}),(0,r.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:e?"Create the first super admin account":"Sign in to your super admin account"})]}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:j,children:[x&&(0,r.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 text-red-400"}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-red-700",children:x})]})]})}),(0,r.jsxs)("div",{className:"space-y-4",children:[e&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,r.jsx)("input",{id:"name",name:"name",type:"text",required:!0,value:f.name,onChange:N,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your full name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:f.email,onChange:N,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your email address"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsxs)("div",{className:"mt-1 relative",children:[(0,r.jsx)("input",{id:"password",name:"password",type:p?"text":"password",autoComplete:e?"new-password":"current-password",required:!0,value:f.password,onChange:N,className:"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your password"}),(0,r.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>h(!p),children:p?(0,r.jsx)(u,{className:"h-5 w-5 text-gray-400"}):(0,r.jsx)(m,{className:"h-5 w-5 text-gray-400"})})]})]}),e&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"adminSecret",className:"block text-sm font-medium text-gray-700",children:"Admin Secret"}),(0,r.jsx)("input",{id:"adminSecret",name:"adminSecret",type:"password",required:!0,value:f.adminSecret,onChange:N,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter the admin secret key"}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"This is the SUPER_ADMIN_SECRET from your environment variables"})]})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:t,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:t?(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):e?"Create Super Admin":"Sign In"})})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["Super Admin Dashboard v","1.0.0"]})})]})})}},3423:(e,a,t)=>{Promise.resolve().then(t.bind(t,302))},5339:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5525:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,a,t)=>{"use strict";var r=t(8999);t.o(r,"usePathname")&&t.d(a,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(a,{useRouter:function(){return r.useRouter}})},5731:(e,a,t)=>{"use strict";t.d(a,{I5:()=>d,O5:()=>s,Pt:()=>n,R2:()=>l,Tp:()=>i,mq:()=>c,rh:()=>o});let r=t(3464).A.create({baseURL:"http://localhost:3002",headers:{"Content-Type":"application/json"}}),s=e=>{r.defaults.headers.common.Authorization="Bearer ".concat(e),localStorage.setItem("super_admin_token",e)},i=()=>{delete r.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},n=()=>localStorage.getItem("super_admin_token");{let e=n();e&&s(e)}r.interceptors.response.use(e=>e,e=>{var a;return(null==(a=e.response)?void 0:a.status)===401&&(i(),window.location.href="/login"),Promise.reject(e)});let l={login:async(e,a)=>(await r.post("/api/super-admin/auth",{action:"login",email:e,password:a})).data,setup:async(e,a,t,s)=>(await r.post("/api/super-admin/auth",{action:"setup",email:e,password:a,name:t,adminSecret:s})).data,checkSetup:async()=>(await r.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await r.get("/api/super-admin/auth")).data},o={getOverview:async()=>(await r.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await r.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await r.get("/api/super-admin/analytics?type=detailed")).data},d={getAll:async()=>(await r.get("/api/super-admin/packages")).data,getById:async e=>(await r.get("/api/super-admin/packages?id=".concat(e))).data,create:async e=>(await r.post("/api/super-admin/packages",e)).data,update:async(e,a)=>(await r.put("/api/super-admin/packages?id=".concat(e),a)).data,delete:async e=>(await r.delete("/api/super-admin/packages?id=".concat(e))).data},c={initialize:async e=>(await r.post("/api/super-admin/initialize",{action:e})).data,getInitializationStatus:async()=>(await r.get("/api/super-admin/initialize")).data}},9946:(e,a,t)=>{"use strict";t.d(a,{A:()=>u});var r=t(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,a,t)=>t?t.toUpperCase():a.toLowerCase()),n=e=>{let a=i(e);return a.charAt(0).toUpperCase()+a.slice(1)},l=function(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return a.filter((e,a,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===a).join(" ").trim()},o=e=>{for(let a in e)if(a.startsWith("aria-")||"role"===a||"title"===a)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,a)=>{let{color:t="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:n,className:c="",children:u,iconNode:m,...p}=e;return(0,r.createElement)("svg",{ref:a,...d,width:s,height:s,stroke:t,strokeWidth:n?24*Number(i)/Number(s):i,className:l("lucide",c),...!u&&!o(p)&&{"aria-hidden":"true"},...p},[...m.map(e=>{let[a,t]=e;return(0,r.createElement)(a,t)}),...Array.isArray(u)?u:[u]])}),u=(e,a)=>{let t=(0,r.forwardRef)((t,i)=>{let{className:o,...d}=t;return(0,r.createElement)(c,{ref:i,iconNode:a,className:l("lucide-".concat(s(n(e))),"lucide-".concat(e),o),...d})});return t.displayName=n(e),t}}},e=>{var a=a=>e(e.s=a);e.O(0,[464,441,684,358],()=>a(3423)),_N_E=e.O()}]);