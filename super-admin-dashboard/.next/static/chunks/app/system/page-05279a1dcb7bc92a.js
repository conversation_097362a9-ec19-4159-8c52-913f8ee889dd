(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[186],{381:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},646:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5731:(e,a,s)=>{"use strict";s.d(a,{FH:()=>t,I5:()=>d,O5:()=>i,Pt:()=>l,R2:()=>n,Tp:()=>r,mq:()=>m,rh:()=>c});let t=s(3464).A.create({baseURL:"http://localhost:3000",headers:{"Content-Type":"application/json"}}),i=e=>{t.defaults.headers.common.Authorization="Bearer ".concat(e),localStorage.setItem("super_admin_token",e)},r=()=>{delete t.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},l=()=>localStorage.getItem("super_admin_token");{let e=l();e&&i(e)}t.interceptors.response.use(e=>e,e=>{var a;return(null==(a=e.response)?void 0:a.status)===401&&(r(),window.location.href="/login"),Promise.reject(e)});let n={login:async(e,a)=>(await t.post("/api/super-admin/auth",{action:"login",email:e,password:a})).data,setup:async(e,a,s,i)=>(await t.post("/api/super-admin/auth",{action:"setup",email:e,password:a,name:s,adminSecret:i})).data,checkSetup:async()=>(await t.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await t.get("/api/super-admin/auth")).data},c={getOverview:async()=>(await t.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await t.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await t.get("/api/super-admin/analytics?type=detailed")).data},d={getAll:async()=>(await t.get("/api/super-admin/packages")).data,getById:async e=>(await t.get("/api/super-admin/packages?id=".concat(e))).data,create:async e=>(await t.post("/api/super-admin/packages",e)).data,update:async(e,a)=>(await t.put("/api/super-admin/packages?id=".concat(e),a)).data,delete:async e=>(await t.delete("/api/super-admin/packages?id=".concat(e))).data},m={initialize:async e=>(await t.post("/api/super-admin/initialize",{action:e})).data,getInitializationStatus:async()=>(await t.get("/api/super-admin/initialize")).data}},5837:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>u});var t=s(5155),i=s(2115),r=s(5731),l=s(646),n=s(9946);let c=(0,n.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),d=(0,n.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),m=(0,n.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),o=(0,n.A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);var x=s(7108),h=s(381),p=s(7580),g=s(9397);function u(){let[e,a]=(0,i.useState)({database:"healthy",api:"healthy",packages:"healthy"}),[s,n]=(0,i.useState)(null),[u,y]=(0,i.useState)(!0),[j,N]=(0,i.useState)(!1),[f,w]=(0,i.useState)(null);(0,i.useEffect)(()=>{b(),v()},[]);let b=async()=>{try{await r.FH.get("/super-admin/analytics"),a(e=>({...e,api:"healthy",database:"healthy"}))}catch(e){a(e=>({...e,api:"error",database:"error"}))}},v=async()=>{try{y(!0);let e=await r.FH.get("/super-admin/initialize");n(e.data),e.data.initialized?a(e=>({...e,packages:"healthy"})):a(e=>({...e,packages:"warning"}))}catch(t){var e,s;w((null==(s=t.response)||null==(e=s.data)?void 0:e.error)||"Failed to check system status"),a(e=>({...e,packages:"error"}))}finally{y(!1)}},k=async()=>{try{N(!0),w(null),await r.FH.post("/super-admin/initialize",{action:"full-setup"}),await v(),await b()}catch(s){var e,a;w((null==(a=s.response)||null==(e=a.data)?void 0:e.error)||"Failed to initialize system")}finally{N(!1)}},A=e=>{switch(e){case"healthy":return(0,t.jsx)(l.A,{className:"h-5 w-5 text-green-500"});case"warning":return(0,t.jsx)(c,{className:"h-5 w-5 text-yellow-500"});case"error":return(0,t.jsx)(c,{className:"h-5 w-5 text-red-500"})}},z=e=>{switch(e){case"healthy":return"text-green-600 bg-green-100";case"warning":return"text-yellow-600 bg-yellow-100";case"error":return"text-red-600 bg-red-100"}};return u?(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"System Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Monitor and manage system health and configuration"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"System Status"}),(0,t.jsxs)("button",{onClick:()=>{b(),v()},className:"flex items-center space-x-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[(0,t.jsx)(d,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Refresh"})]})]})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,t.jsx)(m,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:"Database"}),A(e.database)]}),(0,t.jsx)("p",{className:"text-sm px-2 py-1 rounded-full inline-block mt-1 ".concat(z(e.database)),children:"healthy"===e.database?"Connected":"warning"===e.database?"Warning":"Disconnected"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,t.jsx)(o,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:"API"}),A(e.api)]}),(0,t.jsx)("p",{className:"text-sm px-2 py-1 rounded-full inline-block mt-1 ".concat(z(e.api)),children:"healthy"===e.api?"Operational":"warning"===e.api?"Warning":"Error"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,t.jsx)(x.A,{className:"h-6 w-6 text-purple-600"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:"Package System"}),A(e.packages)]}),(0,t.jsx)("p",{className:"text-sm px-2 py-1 rounded-full inline-block mt-1 ".concat(z(e.packages)),children:"healthy"===e.packages?"Initialized":"warning"===e.packages?"Setup Required":"Error"})]})]})]})})]}),s&&(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"System Initialization"})}),(0,t.jsxs)("div",{className:"p-6",children:[s.setupRequired?(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(c,{className:"h-5 w-5 text-yellow-400 mr-3"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"System Setup Required"}),(0,t.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"The package system needs to be initialized before the platform can be used."})]})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)("button",{onClick:k,disabled:j,className:"flex items-center space-x-2 px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50",children:[j?(0,t.jsx)(d,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:j?"Initializing...":"Initialize System"})]})})]}):(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(l.A,{className:"h-5 w-5 text-green-400 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"System Initialized"}),(0,t.jsx)("p",{className:"text-sm text-green-700 mt-1",children:"The package system is properly configured and ready to use."})]})]})}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Package Configuration"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-md",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Total Packages"}),(0,t.jsx)("span",{className:"font-medium",children:s.packages.total})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-md",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Free Package"}),(0,t.jsx)("span",{className:"text-sm font-medium ".concat(s.packages.hasFree?"text-green-600":"text-red-600"),children:s.packages.hasFree?"Configured":"Missing"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-md",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Premium Packages"}),(0,t.jsx)("span",{className:"text-sm font-medium ".concat(s.packages.hasPro||s.packages.hasEnterprise?"text-green-600":"text-yellow-600"),children:s.packages.hasPro&&s.packages.hasEnterprise?"Complete":s.packages.hasPro||s.packages.hasEnterprise?"Partial":"None"})]})]})]})]})]}),f&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(c,{className:"h-5 w-5 text-red-400 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"System Error"}),(0,t.jsx)("p",{className:"text-sm text-red-700 mt-1",children:f})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"System Information"})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Environment"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Mode"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Production"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Version"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"1.0.0"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"API URL"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"http://localhost:3000"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Security"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Authentication"}),(0,t.jsx)("span",{className:"text-sm font-medium text-green-600",children:"JWT Enabled"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"CORS"}),(0,t.jsx)("span",{className:"text-sm font-medium text-green-600",children:"Configured"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Admin Access"}),(0,t.jsx)("span",{className:"text-sm font-medium text-green-600",children:"Protected"})]})]})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("button",{onClick:()=>window.location.href="/packages",className:"flex items-center space-x-3 p-4 border border-gray-200 rounded-md hover:bg-gray-50",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:"Manage Packages"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Configure subscription packages"})]})]}),(0,t.jsxs)("button",{onClick:()=>window.location.href="/organizations",className:"flex items-center space-x-3 p-4 border border-gray-200 rounded-md hover:bg-gray-50",children:[(0,t.jsx)(p.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:"View Organizations"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Monitor user organizations"})]})]}),(0,t.jsxs)("button",{onClick:()=>window.location.href="/analytics",className:"flex items-center space-x-3 p-4 border border-gray-200 rounded-md hover:bg-gray-50",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 text-purple-600"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:"View Analytics"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Business metrics and insights"})]})]})]})})]})]})}},7108:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7547:(e,a,s)=>{Promise.resolve().then(s.bind(s,5837))},7580:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9397:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},9946:(e,a,s)=>{"use strict";s.d(a,{A:()=>o});var t=s(2115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,a,s)=>s?s.toUpperCase():a.toLowerCase()),l=e=>{let a=r(e);return a.charAt(0).toUpperCase()+a.slice(1)},n=function(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return a.filter((e,a,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===a).join(" ").trim()},c=e=>{for(let a in e)if(a.startsWith("aria-")||"role"===a||"title"===a)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,t.forwardRef)((e,a)=>{let{color:s="currentColor",size:i=24,strokeWidth:r=2,absoluteStrokeWidth:l,className:m="",children:o,iconNode:x,...h}=e;return(0,t.createElement)("svg",{ref:a,...d,width:i,height:i,stroke:s,strokeWidth:l?24*Number(r)/Number(i):r,className:n("lucide",m),...!o&&!c(h)&&{"aria-hidden":"true"},...h},[...x.map(e=>{let[a,s]=e;return(0,t.createElement)(a,s)}),...Array.isArray(o)?o:[o]])}),o=(e,a)=>{let s=(0,t.forwardRef)((s,r)=>{let{className:c,...d}=s;return(0,t.createElement)(m,{ref:r,iconNode:a,className:n("lucide-".concat(i(l(e))),"lucide-".concat(e),c),...d})});return s.displayName=l(e),s}}},e=>{var a=a=>e(e.s=a);e.O(0,[464,441,684,358],()=>a(7547)),_N_E=e.O()}]);