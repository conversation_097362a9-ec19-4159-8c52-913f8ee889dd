(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[425],{1888:(e,a,t)=>{Promise.resolve().then(t.bind(t,5007))},5007:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>h});var s=t(5155),r=t(2115),i=t(5731),l=t(7580),n=t(9946);let d=(0,n.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var c=t(7108),o=t(9074);let m=(0,n.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),x=(0,n.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),p=(0,n.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);function h(){let[e,a]=(0,r.useState)([]),[t,n]=(0,r.useState)(!0),[h,u]=(0,r.useState)(null),[g,y]=(0,r.useState)(""),[j,v]=(0,r.useState)("created");(0,r.useEffect)(()=>{w()},[]);let w=async()=>{try{n(!0);let e=await i.FH.get("/super-admin/analytics?type=detailed"),t=e.data.data||e.data;a(t.organizations||[])}catch(a){var e,t;u((null==(t=a.response)||null==(e=t.data)?void 0:e.error)||"Failed to fetch organizations")}finally{n(!1)}},N=e.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())||e.owner.toLowerCase().includes(g.toLowerCase())).sort((e,a)=>{switch(j){case"name":return e.name.localeCompare(a.name);case"emails":return a.totalEmailsSent-e.totalEmailsSent;case"revenue":var t,s;return((null==(t=a.package)?void 0:t.price)||0)-((null==(s=e.package)?void 0:s.price)||0);default:return new Date(a.createdAt).getTime()-new Date(e.createdAt).getTime()}}),f=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),k=e=>new Intl.NumberFormat("en-US").format(e),b=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return t?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):h?(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:[(0,s.jsx)("p",{className:"text-red-800",children:h}),(0,s.jsx)("button",{onClick:w,className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Try again"})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Organizations"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage and monitor all organizations"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,s.jsx)(l.A,{className:"h-6 w-6 text-blue-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Organizations"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k(e.length)})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,s.jsx)(d,{className:"h-6 w-6 text-green-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Emails Sent"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k(e.reduce((e,a)=>e+a.totalEmailsSent,0))})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,s.jsx)(c.A,{className:"h-6 w-6 text-purple-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Subscriptions"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k(e.filter(e=>e.package).length)})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,s.jsx)(o.A,{className:"h-6 w-6 text-orange-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"This Month"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k(e.filter(e=>{let a=new Date(e.createdAt),t=new Date;return a.getMonth()===t.getMonth()&&a.getFullYear()===t.getFullYear()}).length)})]})]})})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search organizations...",value:g,onChange:e=>y(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(x,{className:"h-4 w-4 text-gray-400"}),(0,s.jsxs)("select",{value:j,onChange:e=>v(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"created",children:"Sort by Created Date"}),(0,s.jsx)("option",{value:"name",children:"Sort by Name"}),(0,s.jsx)("option",{value:"emails",children:"Sort by Emails Sent"}),(0,s.jsx)("option",{value:"revenue",children:"Sort by Revenue"})]})]})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Organization"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Package"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Usage"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Activity"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:N.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.owner})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.package?(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.package.name}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[b(e.package.price),"/month"]})]}):(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800",children:"No Package"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[k(e.totalEmailsSent)," total"]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[k(e.emailsSentLast30Days)," last 30 days"]})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,s.jsxs)("div",{children:[k(e.campaignCount)," campaigns"]}),(0,s.jsxs)("div",{children:[k(e.leadCount)," leads"]}),(0,s.jsxs)("div",{children:[k(e.agentCount)," agents"]})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:f(e.createdAt)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("button",{className:"text-blue-600 hover:text-blue-900 flex items-center",children:[(0,s.jsx)(p,{className:"h-4 w-4 mr-1"}),"View Details"]})})]},e.id))})]})}),0===N.length&&(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(l.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,s.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No organizations found"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:g?"Try adjusting your search terms.":"No organizations have been created yet."})]})]})]})}},5731:(e,a,t)=>{"use strict";t.d(a,{FH:()=>s,I5:()=>c,O5:()=>r,Pt:()=>l,R2:()=>n,Tp:()=>i,mq:()=>o,rh:()=>d});let s=t(3464).A.create({baseURL:"http://localhost:3000",headers:{"Content-Type":"application/json"}}),r=e=>{s.defaults.headers.common.Authorization="Bearer ".concat(e),localStorage.setItem("super_admin_token",e)},i=()=>{delete s.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},l=()=>localStorage.getItem("super_admin_token");{let e=l();e&&r(e)}s.interceptors.response.use(e=>e,e=>{var a;return(null==(a=e.response)?void 0:a.status)===401&&(i(),window.location.href="/login"),Promise.reject(e)});let n={login:async(e,a)=>(await s.post("/api/super-admin/auth",{action:"login",email:e,password:a})).data,setup:async(e,a,t,r)=>(await s.post("/api/super-admin/auth",{action:"setup",email:e,password:a,name:t,adminSecret:r})).data,checkSetup:async()=>(await s.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await s.get("/api/super-admin/auth")).data},d={getOverview:async()=>(await s.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await s.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await s.get("/api/super-admin/analytics?type=detailed")).data},c={getAll:async()=>(await s.get("/api/super-admin/packages")).data,getById:async e=>(await s.get("/api/super-admin/packages?id=".concat(e))).data,create:async e=>(await s.post("/api/super-admin/packages",e)).data,update:async(e,a)=>(await s.put("/api/super-admin/packages?id=".concat(e),a)).data,delete:async e=>(await s.delete("/api/super-admin/packages?id=".concat(e))).data},o={initialize:async e=>(await s.post("/api/super-admin/initialize",{action:e})).data,getInitializationStatus:async()=>(await s.get("/api/super-admin/initialize")).data}},7108:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7580:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9074:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9946:(e,a,t)=>{"use strict";t.d(a,{A:()=>m});var s=t(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,a,t)=>t?t.toUpperCase():a.toLowerCase()),l=e=>{let a=i(e);return a.charAt(0).toUpperCase()+a.slice(1)},n=function(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return a.filter((e,a,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===a).join(" ").trim()},d=e=>{for(let a in e)if(a.startsWith("aria-")||"role"===a||"title"===a)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)((e,a)=>{let{color:t="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:o="",children:m,iconNode:x,...p}=e;return(0,s.createElement)("svg",{ref:a,...c,width:r,height:r,stroke:t,strokeWidth:l?24*Number(i)/Number(r):i,className:n("lucide",o),...!m&&!d(p)&&{"aria-hidden":"true"},...p},[...x.map(e=>{let[a,t]=e;return(0,s.createElement)(a,t)}),...Array.isArray(m)?m:[m]])}),m=(e,a)=>{let t=(0,s.forwardRef)((t,i)=>{let{className:d,...c}=t;return(0,s.createElement)(o,{ref:i,iconNode:a,className:n("lucide-".concat(r(l(e))),"lucide-".concat(e),d),...c})});return t.displayName=l(e),t}}},e=>{var a=a=>e(e.s=a);e.O(0,[464,441,684,358],()=>a(1888)),_N_E=e.O()}]);