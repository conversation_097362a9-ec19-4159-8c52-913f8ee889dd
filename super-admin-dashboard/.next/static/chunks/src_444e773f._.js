(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "analyticsAPI": (()=>analyticsAPI),
    "authAPI": (()=>authAPI),
    "clearAuthToken": (()=>clearAuthToken),
    "default": (()=>__TURBOPACK__default__export__),
    "getStoredToken": (()=>getStoredToken),
    "packagesAPI": (()=>packagesAPI),
    "setAuthToken": (()=>setAuthToken),
    "systemAPI": (()=>systemAPI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
const API_URL = ("TURBOPACK compile-time value", "http://localhost:3002") || 'http://localhost:3002';
// Create axios instance
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_URL,
    headers: {
        'Content-Type': 'application/json'
    }
});
// Token management
let authToken = null;
const setAuthToken = (token)=>{
    authToken = token;
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    localStorage.setItem('super_admin_token', token);
};
const clearAuthToken = ()=>{
    authToken = null;
    delete api.defaults.headers.common['Authorization'];
    localStorage.removeItem('super_admin_token');
};
const getStoredToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        return localStorage.getItem('super_admin_token');
    }
    "TURBOPACK unreachable";
};
// Initialize token from localStorage
if ("TURBOPACK compile-time truthy", 1) {
    const storedToken = getStoredToken();
    if (storedToken) {
        setAuthToken(storedToken);
    }
}
// Response interceptor for handling auth errors
api.interceptors.response.use((response)=>response, (error)=>{
    if (error.response?.status === 401) {
        clearAuthToken();
        window.location.href = '/login';
    }
    return Promise.reject(error);
});
const authAPI = {
    login: async (email, password)=>{
        const response = await api.post('/api/super-admin/auth', {
            action: 'login',
            email,
            password
        });
        return response.data;
    },
    setup: async (email, password, name, adminSecret)=>{
        const response = await api.post('/api/super-admin/auth', {
            action: 'setup',
            email,
            password,
            name,
            adminSecret
        });
        return response.data;
    },
    checkSetup: async ()=>{
        const response = await api.post('/api/super-admin/auth', {
            action: 'check-setup'
        });
        return response.data;
    },
    verifyToken: async ()=>{
        const response = await api.get('/api/super-admin/auth');
        return response.data;
    }
};
const analyticsAPI = {
    getOverview: async ()=>{
        const response = await api.get('/api/super-admin/analytics');
        return response.data;
    },
    getOrganizations: async ()=>{
        const response = await api.get('/api/super-admin/analytics?type=organizations');
        return response.data;
    },
    getDetailed: async ()=>{
        const response = await api.get('/api/super-admin/analytics?type=detailed');
        return response.data;
    }
};
const packagesAPI = {
    getAll: async ()=>{
        const response = await api.get('/api/super-admin/packages');
        return response.data;
    },
    getById: async (id)=>{
        const response = await api.get(`/api/super-admin/packages?id=${id}`);
        return response.data;
    },
    create: async (packageData)=>{
        const response = await api.post('/api/super-admin/packages', packageData);
        return response.data;
    },
    update: async (id, packageData)=>{
        const response = await api.put(`/api/super-admin/packages?id=${id}`, packageData);
        return response.data;
    },
    delete: async (id)=>{
        const response = await api.delete(`/api/super-admin/packages?id=${id}`);
        return response.data;
    }
};
const systemAPI = {
    initialize: async (action)=>{
        const response = await api.post('/api/super-admin/initialize', {
            action
        });
        return response.data;
    },
    getInitializationStatus: async ()=>{
        const response = await api.get('/api/super-admin/initialize');
        return response.data;
    }
};
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useAuth = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
_s(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const AuthProvider = ({ children })=>{
    _s1();
    const [admin, setAdmin] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const initializeAuth = {
                "AuthProvider.useEffect.initializeAuth": async ()=>{
                    const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStoredToken"])();
                    if (token) {
                        try {
                            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].verifyToken();
                            if (response.valid && response.admin) {
                                setAdmin(response.admin);
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAuthToken"])(token);
                            } else {
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearAuthToken"])();
                            }
                        } catch (error) {
                            console.error('Token verification failed:', error);
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearAuthToken"])();
                        }
                    }
                    setIsLoading(false);
                }
            }["AuthProvider.useEffect.initializeAuth"];
            initializeAuth();
        }
    }["AuthProvider.useEffect"], []);
    const login = async (email, password)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].login(email, password);
            if (response.token && response.admin) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAuthToken"])(response.token);
                setAdmin(response.admin);
            } else {
                throw new Error('Invalid response from server');
            }
        } catch (error) {
            throw new Error(error.response?.data?.error || 'Login failed');
        }
    };
    const logout = ()=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearAuthToken"])();
        setAdmin(null);
    };
    const setup = async (email, password, name, adminSecret)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].setup(email, password, name, adminSecret);
            if (response.token && response.admin) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAuthToken"])(response.token);
                setAdmin(response.admin);
            } else {
                throw new Error('Invalid response from server');
            }
        } catch (error) {
            throw new Error(error.response?.data?.error || 'Setup failed');
        }
    };
    const value = {
        admin,
        isLoading,
        isAuthenticated: !!admin,
        login,
        logout,
        setup
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 101,
        columnNumber: 10
    }, this);
};
_s1(AuthProvider, "6t39ZjYaGYsHEMLnBAmfmuK7Cbg=");
_c = AuthProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_444e773f._.js.map