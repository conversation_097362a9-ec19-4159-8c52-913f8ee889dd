(()=>{var e={};e.id=262,e.ids=[262],e.modules={220:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(687),r=t(3210),i=t(2185),l=t(6640),n=t(2688);let d=(0,n.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var c=t(9080);let o=(0,n.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),x=(0,n.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var p=t(3928),u=t(1312);let m=(0,n.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);function h(){let[e,s]=(0,r.useState)([]),[t,n]=(0,r.useState)(!0),[h,g]=(0,r.useState)(""),b=async()=>{try{n(!0);let e=await i.I5.getAll();s(e)}catch(e){g(e.response?.data?.error||"Failed to load packages")}finally{n(!1)}},y=async e=>{if(confirm("Are you sure you want to delete this package?"))try{await i.I5.delete(e),await b()}catch(e){alert(e.response?.data?.error||"Failed to delete package")}};return t?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Packages"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage subscription packages and pricing"})]}),(0,a.jsxs)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2",children:[(0,a.jsx)(d,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Create Package"})]})]}),h&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsx)("p",{className:"text-red-800",children:h})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,a.jsxs)("div",{className:`bg-white rounded-lg shadow-md border-2 ${e.isDefault?"border-blue-500":"border-gray-200"} p-6`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.A,{className:"h-6 w-6 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),e.isDefault&&(0,a.jsx)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full",children:"Default"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-blue-600",children:(0,a.jsx)(o,{className:"h-4 w-4"})}),!e.isDefault&&(0,a.jsx)("button",{onClick:()=>y(e.id),className:"text-gray-400 hover:text-red-600",children:(0,a.jsx)(x,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex items-baseline space-x-2",children:[(0,a.jsxs)("span",{className:"text-3xl font-bold text-gray-900",children:["$",e.price.toFixed(2)]}),(0,a.jsxs)("span",{className:"text-gray-500",children:["/",e.billingCycle.toLowerCase()]})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:e.description})]}),(0,a.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:[e.dailyEmailLimit.toLocaleString()," emails/day"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:[e.emailAccountLimit," email accounts"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m,{className:"h-4 w-4 text-purple-600"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.aiFeatures.length>0?`${e.aiFeatures.length} AI features`:"No AI features"})]})]}),e.aiFeatures.length>0&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"AI Features:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.aiFeatures.map(e=>(0,a.jsx)("span",{className:"bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded",children:e.replace("_"," ")},e))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"ACTIVE"===e.status?"bg-green-100 text-green-800":"INACTIVE"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:e.status}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[e.organizations?.length||0," organizations"]})]})]},e.id))}),0===e.length&&!t&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(c.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No packages"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by creating a new package."}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 mx-auto",children:[(0,a.jsx)(d,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Create Package"})]})})]})]})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1820:e=>{"use strict";e.exports=require("os")},2412:e=>{"use strict";e.exports=require("assert")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4735:e=>{"use strict";e.exports=require("events")},5493:(e,s,t)=>{Promise.resolve().then(t.bind(t,7442))},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},7442:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/packages/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/packages/page.tsx","default")},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},8887:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>c});var a=t(5239),r=t(8088),i=t(8170),l=t.n(i),n=t(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["packages",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7442)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/packages/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/packages/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/packages/page",pathname:"/packages",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9021:e=>{"use strict";e.exports=require("fs")},9061:(e,s,t)=>{Promise.resolve().then(t.bind(t,220))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,682,934,502,967],()=>t(8887));module.exports=a})();