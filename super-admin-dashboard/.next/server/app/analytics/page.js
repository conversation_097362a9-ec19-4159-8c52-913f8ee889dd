(()=>{var e={};e.id=745,e.ids=[745],e.modules={60:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},644:(e,t,s)=>{Promise.resolve().then(s.bind(s,1185))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1031:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/analytics/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/analytics/page.tsx","default")},1135:()=>{},1185:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(687),r=s(3210),i=s(2185),n=s(3928),l=s(60),d=s(2737),o=s(9080),c=s(1312),m=s(8559),u=s(5541);function p(){let[e,t]=(0,r.useState)(null),[s,p]=(0,r.useState)(!0),[h,x]=(0,r.useState)(null),v=async()=>{try{p(!0);let e=await i.FH.get("/super-admin/analytics?type=overview");t(e.data.data||e.data)}catch(e){x(e.response?.data?.error||"Failed to fetch analytics")}finally{p(!1)}};if(s)return(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(h)return(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:[(0,a.jsx)("p",{className:"text-red-800",children:h}),(0,a.jsx)("button",{onClick:v,className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Try again"})]});if(!e)return(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No analytics data available"})});let g=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),y=e=>new Intl.NumberFormat("en-US").format(e);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Business Analytics"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Comprehensive business metrics and insights"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g(e.revenue.total)})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(n.A,{className:"h-6 w-6 text-green-600"})})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center",children:[e.revenue.growth>=0?(0,a.jsx)(l.A,{className:"h-4 w-4 text-green-500"}):(0,a.jsx)(d.A,{className:"h-4 w-4 text-red-500"}),(0,a.jsxs)("span",{className:`text-sm font-medium ${e.revenue.growth>=0?"text-green-600":"text-red-600"}`,children:[Math.abs(e.revenue.growth).toFixed(1),"%"]}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-2",children:"vs last month"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Subscriptions"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:y(e.subscriptions.active)})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(o.A,{className:"h-6 w-6 text-blue-600"})})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[y(e.subscriptions.total)," total subscriptions"]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:y(e.users.total)})]}),(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(c.A,{className:"h-6 w-6 text-purple-600"})})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[y(e.users.newThisMonth)," new this month"]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Emails Sent"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:y(e.usage.totalEmailsSent)})]}),(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-full",children:(0,a.jsx)(m.A,{className:"h-6 w-6 text-orange-600"})})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[y(e.usage.emailsSentThisMonth)," this month"]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Package Performance"})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"space-y-4",children:e.subscriptions.byPackage.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.packageName}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[y(e.count)," subscribers"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:g(e.revenue)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"revenue"})]})]},t))})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Usage Statistics"})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Average emails per user"}),(0,a.jsx)("span",{className:"font-medium",children:y(e.usage.averageEmailsPerUser)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Active packages"}),(0,a.jsx)("span",{className:"font-medium",children:e.packages.active})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Most popular package"}),(0,a.jsx)("span",{className:"font-medium",children:e.packages.mostPopular})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Monthly Revenue"})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:g(e.revenue.monthly)}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"This month"}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-center",children:[e.revenue.growth>=0?(0,a.jsx)(u.A,{className:"h-5 w-5 text-green-500 mr-2"}):(0,a.jsx)(d.A,{className:"h-5 w-5 text-red-500 mr-2"}),(0,a.jsxs)("span",{className:`font-medium ${e.revenue.growth>=0?"text-green-600":"text-red-600"}`,children:[Math.abs(e.revenue.growth).toFixed(1),"% growth"]})]})]})})]})]})]})}},1312:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},1630:e=>{"use strict";e.exports=require("http")},1820:e=>{"use strict";e.exports=require("os")},2185:(e,t,s)=>{"use strict";s.d(t,{FH:()=>a,I5:()=>o,O5:()=>r,Pt:()=>n,R2:()=>l,Tp:()=>i,mq:()=>c,rh:()=>d});let a=s(1060).A.create({baseURL:"http://localhost:3000",headers:{"Content-Type":"application/json"}}),r=e=>{a.defaults.headers.common.Authorization=`Bearer ${e}`,localStorage.setItem("super_admin_token",e)},i=()=>{delete a.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},n=()=>null;a.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(i(),window.location.href="/login"),Promise.reject(e)));let l={login:async(e,t)=>(await a.post("/api/super-admin/auth",{action:"login",email:e,password:t})).data,setup:async(e,t,s,r)=>(await a.post("/api/super-admin/auth",{action:"setup",email:e,password:t,name:s,adminSecret:r})).data,checkSetup:async()=>(await a.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await a.get("/api/super-admin/auth")).data},d={getOverview:async()=>(await a.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await a.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await a.get("/api/super-admin/analytics?type=detailed")).data},o={getAll:async()=>(await a.get("/api/super-admin/packages")).data,getById:async e=>(await a.get(`/api/super-admin/packages?id=${e}`)).data,create:async e=>(await a.post("/api/super-admin/packages",e)).data,update:async(e,t)=>(await a.put(`/api/super-admin/packages?id=${e}`,t)).data,delete:async e=>(await a.delete(`/api/super-admin/packages?id=${e}`)).data},c={initialize:async e=>(await a.post("/api/super-admin/initialize",{action:e})).data,getInitializationStatus:async()=>(await a.get("/api/super-admin/initialize")).data}},2412:e=>{"use strict";e.exports=require("assert")},2688:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var a=s(3210);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:r,className:i="",children:n,iconNode:c,...m},u)=>(0,a.createElement)("svg",{ref:u,...o,width:t,height:t,stroke:e,strokeWidth:r?24*Number(s)/Number(t):s,className:l("lucide",i),...!n&&!d(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(n)?n:[n]])),m=(e,t)=>{let s=(0,a.forwardRef)(({className:s,...i},d)=>(0,a.createElement)(c,{ref:d,iconNode:t,className:l(`lucide-${r(n(e))}`,`lucide-${e}`,s),...i}));return s.displayName=n(e),s}},2737:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,AuthProvider:()=>d});var a=s(687),r=s(3210),i=s(2185);let n=(0,r.createContext)(void 0),l=()=>{let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=({children:e})=>{let[t,s]=(0,r.useState)(null),[l,d]=(0,r.useState)(!0);(0,r.useEffect)(()=>{(async()=>{let e=(0,i.Pt)();if(e)try{let t=await i.R2.verifyToken();t.valid&&t.admin?(s(t.admin),(0,i.O5)(e)):(0,i.Tp)()}catch(e){console.error("Token verification failed:",e),(0,i.Tp)()}d(!1)})()},[]);let o=async(e,t)=>{try{let a=await i.R2.login(e,t);if(a.token&&a.admin)(0,i.O5)(a.token),s(a.admin);else throw Error("Invalid response from server")}catch(e){throw Error(e.response?.data?.error||"Login failed")}},c=async(e,t,a,r)=>{try{let n=await i.R2.setup(e,t,a,r);if(n.token&&n.admin)(0,i.O5)(n.token),s(n.admin);else throw Error("Invalid response from server")}catch(e){throw Error(e.response?.data?.error||"Setup failed")}};return(0,a.jsx)(n.Provider,{value:{admin:t,isLoading:l,isAuthenticated:!!t,login:o,logout:()=>{(0,i.Tp)(),s(null)},setup:c},children:e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3928:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>l});var a=s(7413),r=s(7339),i=s.n(r);s(1135);var n=s(9131);let l={title:"Avian Email - Super Admin Dashboard",description:"Super Admin Dashboard for Avian Email Platform"};function d({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:i().className,children:(0,a.jsx)(n.AuthProvider,{children:e})})})}},4587:(e,t,s)=>{Promise.resolve().then(s.bind(s,9131))},4675:(e,t,s)=>{Promise.resolve().then(s.bind(s,3213))},4735:e=>{"use strict";e.exports=require("events")},4858:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=s(5239),r=s(8088),i=s(8170),n=s.n(i),l=s(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1031)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/analytics/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/analytics/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/analytics/page",pathname:"/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},5511:e=>{"use strict";e.exports=require("crypto")},5541:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},5591:e=>{"use strict";e.exports=require("https")},7910:e=>{"use strict";e.exports=require("stream")},8116:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},8354:e=>{"use strict";e.exports=require("util")},8559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},9021:e=>{"use strict";e.exports=require("fs")},9080:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>r});var a=s(2907);(0,a.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx","useAuth");let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx","AuthProvider")},9204:(e,t,s)=>{Promise.resolve().then(s.bind(s,1031))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9556:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,682,658],()=>s(4858));module.exports=a})();