(()=>{var e={};e.id=186,e.ids=[186],e.modules={440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},656:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>c});var a=t(5239),r=t(8088),i=t(8170),n=t.n(i),l=t(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["system",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8394)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/system/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/system/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/system/page",pathname:"/system",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1101:(e,s,t)=>{Promise.resolve().then(t.bind(t,8394))},1135:()=>{},1312:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},1630:e=>{"use strict";e.exports=require("http")},1820:e=>{"use strict";e.exports=require("os")},2185:(e,s,t)=>{"use strict";t.d(s,{FH:()=>a,I5:()=>c,O5:()=>r,Pt:()=>n,R2:()=>l,Tp:()=>i,mq:()=>o,rh:()=>d});let a=t(1060).A.create({baseURL:"http://localhost:3000",headers:{"Content-Type":"application/json"}}),r=e=>{a.defaults.headers.common.Authorization=`Bearer ${e}`,localStorage.setItem("super_admin_token",e)},i=()=>{delete a.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},n=()=>null;a.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(i(),window.location.href="/login"),Promise.reject(e)));let l={login:async(e,s)=>(await a.post("/api/super-admin/auth",{action:"login",email:e,password:s})).data,setup:async(e,s,t,r)=>(await a.post("/api/super-admin/auth",{action:"setup",email:e,password:s,name:t,adminSecret:r})).data,checkSetup:async()=>(await a.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await a.get("/api/super-admin/auth")).data},d={getOverview:async()=>(await a.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await a.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await a.get("/api/super-admin/analytics?type=detailed")).data},c={getAll:async()=>(await a.get("/api/super-admin/packages")).data,getById:async e=>(await a.get(`/api/super-admin/packages?id=${e}`)).data,create:async e=>(await a.post("/api/super-admin/packages",e)).data,update:async(e,s)=>(await a.put(`/api/super-admin/packages?id=${e}`,s)).data,delete:async e=>(await a.delete(`/api/super-admin/packages?id=${e}`)).data},o={initialize:async e=>(await a.post("/api/super-admin/initialize",{action:e})).data,getInitializationStatus:async()=>(await a.get("/api/super-admin/initialize")).data}},2412:e=>{"use strict";e.exports=require("assert")},2688:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var a=t(3210);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),n=e=>{let s=i(e);return s.charAt(0).toUpperCase()+s.slice(1)},l=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),d=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:r,className:i="",children:n,iconNode:o,...m},h)=>(0,a.createElement)("svg",{ref:h,...c,width:s,height:s,stroke:e,strokeWidth:r?24*Number(t)/Number(s):t,className:l("lucide",i),...!n&&!d(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,s])=>(0,a.createElement)(e,s)),...Array.isArray(n)?n:[n]])),m=(e,s)=>{let t=(0,a.forwardRef)(({className:t,...i},d)=>(0,a.createElement)(o,{ref:d,iconNode:s,className:l(`lucide-${r(n(e))}`,`lucide-${e}`,t),...i}));return t.displayName=n(e),t}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,s,t)=>{"use strict";t.d(s,{A:()=>l,AuthProvider:()=>d});var a=t(687),r=t(3210),i=t(2185);let n=(0,r.createContext)(void 0),l=()=>{let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=({children:e})=>{let[s,t]=(0,r.useState)(null),[l,d]=(0,r.useState)(!0);(0,r.useEffect)(()=>{(async()=>{let e=(0,i.Pt)();if(e)try{let s=await i.R2.verifyToken();s.valid&&s.admin?(t(s.admin),(0,i.O5)(e)):(0,i.Tp)()}catch(e){console.error("Token verification failed:",e),(0,i.Tp)()}d(!1)})()},[]);let c=async(e,s)=>{try{let a=await i.R2.login(e,s);if(a.token&&a.admin)(0,i.O5)(a.token),t(a.admin);else throw Error("Invalid response from server")}catch(e){throw Error(e.response?.data?.error||"Login failed")}},o=async(e,s,a,r)=>{try{let n=await i.R2.setup(e,s,a,r);if(n.token&&n.admin)(0,i.O5)(n.token),t(n.admin);else throw Error("Invalid response from server")}catch(e){throw Error(e.response?.data?.error||"Setup failed")}};return(0,a.jsx)(n.Provider,{value:{admin:s,isLoading:l,isAuthenticated:!!s,login:c,logout:()=>{(0,i.Tp)(),t(null)},setup:o},children:e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4027:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4075:e=>{"use strict";e.exports=require("zlib")},4431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>l});var a=t(7413),r=t(7339),i=t.n(r);t(1135);var n=t(9131);let l={title:"Avian Email - Super Admin Dashboard",description:"Super Admin Dashboard for Avian Email Platform"};function d({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:i().className,children:(0,a.jsx)(n.AuthProvider,{children:e})})})}},4587:(e,s,t)=>{Promise.resolve().then(t.bind(t,9131))},4675:(e,s,t)=>{Promise.resolve().then(t.bind(t,3213))},4735:e=>{"use strict";e.exports=require("events")},5069:(e,s,t)=>{Promise.resolve().then(t.bind(t,6652))},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6652:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(687),r=t(3210),i=t(2185),n=t(5336),l=t(2688);let d=(0,l.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),c=(0,l.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),o=(0,l.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),m=(0,l.A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);var h=t(9080),p=t(4027),x=t(1312),u=t(8559);function g(){let[e,s]=(0,r.useState)({database:"healthy",api:"healthy",packages:"healthy"}),[t,l]=(0,r.useState)(null),[g,y]=(0,r.useState)(!0),[v,b]=(0,r.useState)(!1),[f,j]=(0,r.useState)(null),w=async()=>{try{await i.FH.get("/super-admin/analytics"),s(e=>({...e,api:"healthy",database:"healthy"}))}catch(e){s(e=>({...e,api:"error",database:"error"}))}},N=async()=>{try{y(!0);let e=await i.FH.get("/super-admin/initialize");l(e.data),e.data.initialized?s(e=>({...e,packages:"healthy"})):s(e=>({...e,packages:"warning"}))}catch(e){j(e.response?.data?.error||"Failed to check system status"),s(e=>({...e,packages:"error"}))}finally{y(!1)}},k=async()=>{try{b(!0),j(null),await i.FH.post("/super-admin/initialize",{action:"full-setup"}),await N(),await w()}catch(e){j(e.response?.data?.error||"Failed to initialize system")}finally{b(!1)}},A=e=>{switch(e){case"healthy":return(0,a.jsx)(n.A,{className:"h-5 w-5 text-green-500"});case"warning":return(0,a.jsx)(d,{className:"h-5 w-5 text-yellow-500"});case"error":return(0,a.jsx)(d,{className:"h-5 w-5 text-red-500"})}},P=e=>{switch(e){case"healthy":return"text-green-600 bg-green-100";case"warning":return"text-yellow-600 bg-yellow-100";case"error":return"text-red-600 bg-red-100"}};return g?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"System Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Monitor and manage system health and configuration"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"System Status"}),(0,a.jsxs)("button",{onClick:()=>{w(),N()},className:"flex items-center space-x-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[(0,a.jsx)(c,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Refresh"})]})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(o,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Database"}),A(e.database)]}),(0,a.jsx)("p",{className:`text-sm px-2 py-1 rounded-full inline-block mt-1 ${P(e.database)}`,children:"healthy"===e.database?"Connected":"warning"===e.database?"Warning":"Disconnected"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(m,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"API"}),A(e.api)]}),(0,a.jsx)("p",{className:`text-sm px-2 py-1 rounded-full inline-block mt-1 ${P(e.api)}`,children:"healthy"===e.api?"Operational":"warning"===e.api?"Warning":"Error"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Package System"}),A(e.packages)]}),(0,a.jsx)("p",{className:`text-sm px-2 py-1 rounded-full inline-block mt-1 ${P(e.packages)}`,children:"healthy"===e.packages?"Initialized":"warning"===e.packages?"Setup Required":"Error"})]})]})]})})]}),t&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"System Initialization"})}),(0,a.jsxs)("div",{className:"p-6",children:[t.setupRequired?(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d,{className:"h-5 w-5 text-yellow-400 mr-3"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"System Setup Required"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"The package system needs to be initialized before the platform can be used."})]})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("button",{onClick:k,disabled:v,className:"flex items-center space-x-2 px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50",children:[v?(0,a.jsx)(c,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:v?"Initializing...":"Initialize System"})]})})]}):(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.A,{className:"h-5 w-5 text-green-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"System Initialized"}),(0,a.jsx)("p",{className:"text-sm text-green-700 mt-1",children:"The package system is properly configured and ready to use."})]})]})}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Package Configuration"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-md",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Total Packages"}),(0,a.jsx)("span",{className:"font-medium",children:t.packages.total})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-md",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Free Package"}),(0,a.jsx)("span",{className:`text-sm font-medium ${t.packages.hasFree?"text-green-600":"text-red-600"}`,children:t.packages.hasFree?"Configured":"Missing"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-md",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Premium Packages"}),(0,a.jsx)("span",{className:`text-sm font-medium ${t.packages.hasPro||t.packages.hasEnterprise?"text-green-600":"text-yellow-600"}`,children:t.packages.hasPro&&t.packages.hasEnterprise?"Complete":t.packages.hasPro||t.packages.hasEnterprise?"Partial":"None"})]})]})]})]})]}),f&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d,{className:"h-5 w-5 text-red-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"System Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:f})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"System Information"})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Environment"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Mode"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Production"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Version"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"1.0.0"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"API URL"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"http://localhost:3000"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Security"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Authentication"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-600",children:"JWT Enabled"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"CORS"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-600",children:"Configured"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Admin Access"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-600",children:"Protected"})]})]})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("button",{onClick:()=>window.location.href="/packages",className:"flex items-center space-x-3 p-4 border border-gray-200 rounded-md hover:bg-gray-50",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Manage Packages"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Configure subscription packages"})]})]}),(0,a.jsxs)("button",{onClick:()=>window.location.href="/organizations",className:"flex items-center space-x-3 p-4 border border-gray-200 rounded-md hover:bg-gray-50",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"View Organizations"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Monitor user organizations"})]})]}),(0,a.jsxs)("button",{onClick:()=>window.location.href="/analytics",className:"flex items-center space-x-3 p-4 border border-gray-200 rounded-md hover:bg-gray-50",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"View Analytics"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Business metrics and insights"})]})]})]})})]})]})}},7910:e=>{"use strict";e.exports=require("stream")},8116:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},8354:e=>{"use strict";e.exports=require("util")},8394:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/system/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/system/page.tsx","default")},8559:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},9021:e=>{"use strict";e.exports=require("fs")},9080:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>r});var a=t(2907);(0,a.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx","useAuth");let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx","AuthProvider")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9556:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,682,658],()=>t(656));module.exports=a})();