(()=>{var e={};e.id=974,e.ids=[974],e.modules={345:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},617:(e,t,r)=>{Promise.resolve().then(r.bind(r,5694))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},906:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>c,tree:()=>u});var a=r(5239),s=r(8088),i=r(8170),n=r.n(i),o=r(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},c=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},1135:()=>{},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/page.tsx","default")},1630:e=>{"use strict";e.exports=require("http")},1820:e=>{"use strict";e.exports=require("os")},2185:(e,t,r)=>{"use strict";r.d(t,{FH:()=>a,I5:()=>u,O5:()=>s,Pt:()=>n,R2:()=>o,Tp:()=>i,mq:()=>l,rh:()=>d});let a=r(1060).A.create({baseURL:"http://localhost:3000",headers:{"Content-Type":"application/json"}}),s=e=>{a.defaults.headers.common.Authorization=`Bearer ${e}`,localStorage.setItem("super_admin_token",e)},i=()=>{delete a.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},n=()=>null;a.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(i(),window.location.href="/login"),Promise.reject(e)));let o={login:async(e,t)=>(await a.post("/api/super-admin/auth",{action:"login",email:e,password:t})).data,setup:async(e,t,r,s)=>(await a.post("/api/super-admin/auth",{action:"setup",email:e,password:t,name:r,adminSecret:s})).data,checkSetup:async()=>(await a.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await a.get("/api/super-admin/auth")).data},d={getOverview:async()=>(await a.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await a.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await a.get("/api/super-admin/analytics?type=detailed")).data},u={getAll:async()=>(await a.get("/api/super-admin/packages")).data,getById:async e=>(await a.get(`/api/super-admin/packages?id=${e}`)).data,create:async e=>(await a.post("/api/super-admin/packages",e)).data,update:async(e,t)=>(await a.put(`/api/super-admin/packages?id=${e}`,t)).data,delete:async e=>(await a.delete(`/api/super-admin/packages?id=${e}`)).data},l={initialize:async e=>(await a.post("/api/super-admin/initialize",{action:e})).data,getInitializationStatus:async()=>(await a.get("/api/super-admin/initialize")).data}},2412:e=>{"use strict";e.exports=require("assert")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,AuthProvider:()=>d});var a=r(687),s=r(3210),i=r(2185);let n=(0,s.createContext)(void 0),o=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=({children:e})=>{let[t,r]=(0,s.useState)(null),[o,d]=(0,s.useState)(!0);(0,s.useEffect)(()=>{(async()=>{let e=(0,i.Pt)();if(e)try{let t=await i.R2.verifyToken();t.valid&&t.admin?(r(t.admin),(0,i.O5)(e)):(0,i.Tp)()}catch(e){console.error("Token verification failed:",e),(0,i.Tp)()}d(!1)})()},[]);let u=async(e,t)=>{try{let a=await i.R2.login(e,t);if(a.token&&a.admin)(0,i.O5)(a.token),r(a.admin);else throw Error("Invalid response from server")}catch(e){throw Error(e.response?.data?.error||"Login failed")}},l=async(e,t,a,s)=>{try{let n=await i.R2.setup(e,t,a,s);if(n.token&&n.admin)(0,i.O5)(n.token),r(n.admin);else throw Error("Invalid response from server")}catch(e){throw Error(e.response?.data?.error||"Setup failed")}};return(0,a.jsx)(n.Provider,{value:{admin:t,isLoading:o,isAuthenticated:!!t,login:u,logout:()=>{(0,i.Tp)(),r(null)},setup:l},children:e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>o});var a=r(7413),s=r(7339),i=r.n(s);r(1135);var n=r(9131);let o={title:"Avian Email - Super Admin Dashboard",description:"Super Admin Dashboard for Avian Email Platform"};function d({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:i().className,children:(0,a.jsx)(n.AuthProvider,{children:e})})})}},4587:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},4675:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5694:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(687);r(3210);var s=r(6189),i=r(3213);function n(){let{isAuthenticated:e,isLoading:t}=(0,i.A)();return((0,s.useRouter)(),t)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):null}},6189:(e,t,r)=>{"use strict";var a=r(5773);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},7910:e=>{"use strict";e.exports=require("stream")},8116:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var a=r(2907);(0,a.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx","useAuth");let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx","AuthProvider")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9556:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,682,658],()=>r(906));module.exports=a})();