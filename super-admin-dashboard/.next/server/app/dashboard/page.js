(()=>{var e={};e.id=105,e.ids=[105],e.modules={559:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/dashboard/page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1806:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>c});var r=t(5239),a=t(8088),i=t(8170),n=t.n(i),l=t(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,559)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/dashboard/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/dashboard/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1820:e=>{"use strict";e.exports=require("os")},2412:e=>{"use strict";e.exports=require("assert")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3592:(e,s,t)=>{Promise.resolve().then(t.bind(t,559))},3613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4735:e=>{"use strict";e.exports=require("events")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5511:e=>{"use strict";e.exports=require("crypto")},5541:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},5591:e=>{"use strict";e.exports=require("https")},7144:(e,s,t)=>{Promise.resolve().then(t.bind(t,8061))},7910:e=>{"use strict";e.exports=require("stream")},8061:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(687),a=t(3210),i=t(2185),n=t(6640),l=t(3613),d=t(3928),c=t(9080),o=t(1312),m=t(8559),u=t(5336),x=t(5541);function h(){let[e,s]=(0,a.useState)(null),[t,h]=(0,a.useState)(null),[p,g]=(0,a.useState)(!0),[v,b]=(0,a.useState)(""),y=async()=>{try{g(!0),await i.mq.initialize("full-setup");let[e,t]=await Promise.all([i.rh.getOverview(),i.mq.getInitializationStatus()]);s(e.data),h(t)}catch(e){b(e.response?.data?.error||"Failed to initialize system")}finally{g(!1)}};if(p)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(v)return(0,r.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(l.A,{className:"h-5 w-5 text-red-400"}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-red-700",children:v})]})]})});let f=[{name:"Total Revenue",value:`$${e?.revenue.total.toFixed(2)||"0.00"}`,change:`${(e?.revenue.growth||0)>=0?"+":""}${(e?.revenue.growth||0).toFixed(1)}%`,changeType:(e?.revenue.growth||0)>=0?"positive":"negative",icon:d.A},{name:"Active Subscriptions",value:e?.subscriptions.active||0,change:`${e?.subscriptions.total||0} total`,changeType:"neutral",icon:c.A},{name:"Total Users",value:e?.users.total||0,change:`${e?.users.newThisMonth||0} new this month`,changeType:"positive",icon:o.A},{name:"Active Packages",value:e?.packages.active||0,change:`Most popular: ${e?.packages.mostPopular||"None"}`,changeType:"neutral",icon:m.A}];return(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[t&&!t.initialized&&(0,r.jsx)("div",{className:"rounded-md bg-yellow-50 p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(l.A,{className:"h-5 w-5 text-yellow-400"}),(0,r.jsxs)("div",{className:"ml-3 flex-1",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"System Setup Required"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-yellow-700",children:"The package system needs to be initialized. This will create default packages and assign them to existing organizations."}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)("button",{onClick:y,className:"bg-yellow-100 px-3 py-2 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-200",children:"Initialize System"})})]})]})}),t&&t.initialized&&(0,r.jsx)("div",{className:"rounded-md bg-green-50 p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-green-400"}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"System Initialized"}),(0,r.jsxs)("div",{className:"mt-2 text-sm text-green-700",children:["Package system is active with ",t.packages?.total||0," packages configured."]})]})]})}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:f.map(e=>(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsxs)("div",{className:"p-5",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(e.icon,{className:"h-6 w-6 text-gray-400"})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:e.name}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.value})]})})]}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)("div",{className:"flex items-center text-sm",children:(0,r.jsx)("span",{className:`${"positive"===e.changeType?"text-green-600":"negative"===e.changeType?"text-red-600":"text-gray-600"}`,children:e.change})})})]})},e.name))}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"mt-5 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)("button",{className:"relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)(c.A,{className:"mx-auto h-8 w-8 text-gray-400"}),(0,r.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Create New Package"})]}),(0,r.jsxs)("button",{className:"relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)(x.A,{className:"mx-auto h-8 w-8 text-gray-400"}),(0,r.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"View Analytics"})]}),(0,r.jsxs)("button",{className:"relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)(o.A,{className:"mx-auto h-8 w-8 text-gray-400"}),(0,r.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Manage Organizations"})]})]})]})}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"System Overview"}),(0,r.jsx)("div",{className:"mt-5",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Monthly Revenue"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:["$",e?.revenue.monthly.toFixed(2)||"0.00"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Active Users"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e?.users.active||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Cancelled Subscriptions"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e?.subscriptions.cancelled||0})]})]})})]})})]})})}},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,682,658,466,967],()=>t(1806));module.exports=r})();