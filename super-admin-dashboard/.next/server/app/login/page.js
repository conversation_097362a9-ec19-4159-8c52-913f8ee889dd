(()=>{var e={};e.id=520,e.ids=[520],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1630:e=>{"use strict";e.exports=require("http")},1820:e=>{"use strict";e.exports=require("os")},1934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>d});var a=r(5239),s=r(8088),i=r(8170),n=r.n(i),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4934)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/login/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/login/page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2185:(e,t,r)=>{"use strict";r.d(t,{FH:()=>a,I5:()=>d,O5:()=>s,Pt:()=>n,R2:()=>o,Tp:()=>i,mq:()=>u,rh:()=>l});let a=r(1060).A.create({baseURL:"http://localhost:3000",headers:{"Content-Type":"application/json"}}),s=e=>{a.defaults.headers.common.Authorization=`Bearer ${e}`,localStorage.setItem("super_admin_token",e)},i=()=>{delete a.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},n=()=>null;a.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(i(),window.location.href="/login"),Promise.reject(e)));let o={login:async(e,t)=>(await a.post("/api/super-admin/auth",{action:"login",email:e,password:t})).data,setup:async(e,t,r,s)=>(await a.post("/api/super-admin/auth",{action:"setup",email:e,password:t,name:r,adminSecret:s})).data,checkSetup:async()=>(await a.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await a.get("/api/super-admin/auth")).data},l={getOverview:async()=>(await a.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await a.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await a.get("/api/super-admin/analytics?type=detailed")).data},d={getAll:async()=>(await a.get("/api/super-admin/packages")).data,getById:async e=>(await a.get(`/api/super-admin/packages?id=${e}`)).data,create:async e=>(await a.post("/api/super-admin/packages",e)).data,update:async(e,t)=>(await a.put(`/api/super-admin/packages?id=${e}`,t)).data,delete:async e=>(await a.delete(`/api/super-admin/packages?id=${e}`)).data},u={initialize:async e=>(await a.post("/api/super-admin/initialize",{action:e})).data,getInitializationStatus:async()=>(await a.get("/api/super-admin/initialize")).data}},2412:e=>{"use strict";e.exports=require("assert")},2688:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var a=r(3210);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:i="",children:n,iconNode:u,...c},m)=>(0,a.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:o("lucide",i),...!n&&!l(c)&&{"aria-hidden":"true"},...c},[...u.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(n)?n:[n]])),c=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...i},l)=>(0,a.createElement)(u,{ref:l,iconNode:t,className:o(`lucide-${s(n(e))}`,`lucide-${e}`,r),...i}));return r.displayName=n(e),r}},2881:(e,t,r)=>{Promise.resolve().then(r.bind(r,4934))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,AuthProvider:()=>l});var a=r(687),s=r(3210),i=r(2185);let n=(0,s.createContext)(void 0),o=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},l=({children:e})=>{let[t,r]=(0,s.useState)(null),[o,l]=(0,s.useState)(!0);(0,s.useEffect)(()=>{(async()=>{let e=(0,i.Pt)();if(e)try{let t=await i.R2.verifyToken();t.valid&&t.admin?(r(t.admin),(0,i.O5)(e)):(0,i.Tp)()}catch(e){console.error("Token verification failed:",e),(0,i.Tp)()}l(!1)})()},[]);let d=async(e,t)=>{try{let a=await i.R2.login(e,t);if(a.token&&a.admin)(0,i.O5)(a.token),r(a.admin);else throw Error("Invalid response from server")}catch(e){throw Error(e.response?.data?.error||"Login failed")}},u=async(e,t,a,s)=>{try{let n=await i.R2.setup(e,t,a,s);if(n.token&&n.admin)(0,i.O5)(n.token),r(n.admin);else throw Error("Invalid response from server")}catch(e){throw Error(e.response?.data?.error||"Setup failed")}};return(0,a.jsx)(n.Provider,{value:{admin:t,isLoading:o,isAuthenticated:!!t,login:d,logout:()=>{(0,i.Tp)(),r(null)},setup:u},children:e})}},3251:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var a=r(687),s=r(3210),i=r(6189),n=r(3213);r(2185);var o=r(9891),l=r(3613),d=r(2688);let u=(0,d.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),c=(0,d.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);function m(){let[e,t]=(0,s.useState)(!1),[r,d]=(0,s.useState)(!1),[m,p]=(0,s.useState)(!1),[h,x]=(0,s.useState)(""),[b,f]=(0,s.useState)({email:"",password:"",name:"",adminSecret:""}),{login:v,setup:y,isAuthenticated:g}=(0,n.A)(),w=(0,i.useRouter)(),k=async t=>{t.preventDefault(),d(!0),x("");try{e?await y(b.email,b.password,b.name,b.adminSecret):await v(b.email,b.password),w.push("/dashboard")}catch(e){x(e.message)}finally{d(!1)}},j=e=>{f({...b,[e.target.name]:e.target.value})};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100",children:(0,a.jsx)(o.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:e?"Setup Super Admin":"Super Admin Login"}),(0,a.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:e?"Create the first super admin account":"Sign in to your super admin account"})]}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:k,children:[h&&(0,a.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(l.A,{className:"h-5 w-5 text-red-400"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-red-700",children:h})]})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,a.jsx)("input",{id:"name",name:"name",type:"text",required:!0,value:b.name,onChange:j,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:b.email,onChange:j,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your email address"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsxs)("div",{className:"mt-1 relative",children:[(0,a.jsx)("input",{id:"password",name:"password",type:m?"text":"password",autoComplete:e?"new-password":"current-password",required:!0,value:b.password,onChange:j,className:"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your password"}),(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>p(!m),children:m?(0,a.jsx)(u,{className:"h-5 w-5 text-gray-400"}):(0,a.jsx)(c,{className:"h-5 w-5 text-gray-400"})})]})]}),e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"adminSecret",className:"block text-sm font-medium text-gray-700",children:"Admin Secret"}),(0,a.jsx)("input",{id:"adminSecret",name:"adminSecret",type:"password",required:!0,value:b.adminSecret,onChange:j,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter the admin secret key"}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"This is the SUPER_ADMIN_SECRET from your environment variables"})]})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:r,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:r?(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):e?"Create Super Admin":"Sign In"})})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Super Admin Dashboard v","1.0.0"]})})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3613:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>o});var a=r(7413),s=r(7339),i=r.n(s);r(1135);var n=r(9131);let o={title:"Avian Email - Super Admin Dashboard",description:"Super Admin Dashboard for Avian Email Platform"};function l({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:i().className,children:(0,a.jsx)(n.AuthProvider,{children:e})})})}},4587:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},4675:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},4735:e=>{"use strict";e.exports=require("events")},4934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/login/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/login/page.tsx","default")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6189:(e,t,r)=>{"use strict";var a=r(5773);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},7910:e=>{"use strict";e.exports=require("stream")},8033:(e,t,r)=>{Promise.resolve().then(r.bind(r,3251))},8116:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var a=r(2907);(0,a.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx","useAuth");let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx","AuthProvider")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9556:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9891:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,682,658],()=>r(1934));module.exports=a})();