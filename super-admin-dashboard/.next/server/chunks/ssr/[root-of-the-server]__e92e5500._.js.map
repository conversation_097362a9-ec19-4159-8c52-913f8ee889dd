{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Token management\nlet authToken: string | null = null;\n\nexport const setAuthToken = (token: string) => {\n  authToken = token;\n  api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n  localStorage.setItem('super_admin_token', token);\n};\n\nexport const clearAuthToken = () => {\n  authToken = null;\n  delete api.defaults.headers.common['Authorization'];\n  localStorage.removeItem('super_admin_token');\n};\n\nexport const getStoredToken = () => {\n  if (typeof window !== 'undefined') {\n    return localStorage.getItem('super_admin_token');\n  }\n  return null;\n};\n\n// Initialize token from localStorage\nif (typeof window !== 'undefined') {\n  const storedToken = getStoredToken();\n  if (storedToken) {\n    setAuthToken(storedToken);\n  }\n}\n\n// Response interceptor for handling auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      clearAuthToken();\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  login: async (email: string, password: string) => {\n    const response = await api.post('/api/super-admin/auth', {\n      action: 'login',\n      email,\n      password,\n    });\n    return response.data;\n  },\n\n  setup: async (email: string, password: string, name: string, adminSecret: string) => {\n    const response = await api.post('/api/super-admin/auth', {\n      action: 'setup',\n      email,\n      password,\n      name,\n      adminSecret,\n    });\n    return response.data;\n  },\n\n  checkSetup: async () => {\n    const response = await api.post('/api/super-admin/auth', {\n      action: 'check-setup',\n    });\n    return response.data;\n  },\n\n  verifyToken: async () => {\n    const response = await api.get('/api/super-admin/auth');\n    return response.data;\n  },\n};\n\n// Analytics API\nexport const analyticsAPI = {\n  getOverview: async () => {\n    const response = await api.get('/api/super-admin/analytics');\n    return response.data;\n  },\n\n  getOrganizations: async () => {\n    const response = await api.get('/api/super-admin/analytics?type=organizations');\n    return response.data;\n  },\n\n  getDetailed: async () => {\n    const response = await api.get('/api/super-admin/analytics?type=detailed');\n    return response.data;\n  },\n};\n\n// Packages API\nexport const packagesAPI = {\n  getAll: async () => {\n    const response = await api.get('/api/super-admin/packages');\n    return response.data;\n  },\n\n  getById: async (id: string) => {\n    const response = await api.get(`/api/super-admin/packages?id=${id}`);\n    return response.data;\n  },\n\n  create: async (packageData: any) => {\n    const response = await api.post('/api/super-admin/packages', packageData);\n    return response.data;\n  },\n\n  update: async (id: string, packageData: any) => {\n    const response = await api.put(`/api/super-admin/packages?id=${id}`, packageData);\n    return response.data;\n  },\n\n  delete: async (id: string) => {\n    const response = await api.delete(`/api/super-admin/packages?id=${id}`);\n    return response.data;\n  },\n};\n\n// System API\nexport const systemAPI = {\n  initialize: async (action: string) => {\n    const response = await api.post('/api/super-admin/initialize', { action });\n    return response.data;\n  },\n\n  getInitializationStatus: async () => {\n    const response = await api.get('/api/super-admin/initialize');\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAEA,MAAM,UAAU,6DAAmC;AAEnD,wBAAwB;AACxB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,mBAAmB;AACnB,IAAI,YAA2B;AAExB,MAAM,eAAe,CAAC;IAC3B,YAAY;IACZ,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IAChE,aAAa,OAAO,CAAC,qBAAqB;AAC5C;AAEO,MAAM,iBAAiB;IAC5B,YAAY;IACZ,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB;IACnD,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,iBAAiB;IAC5B,uCAAmC;;IAEnC;IACA,OAAO;AACT;AAEA,qCAAqC;AACrC,uCAAmC;;AAKnC;AAEA,gDAAgD;AAChD,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,OAAO,OAAO,OAAe;QAC3B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,yBAAyB;YACvD,QAAQ;YACR;YACA;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO,OAAO,OAAe,UAAkB,MAAc;QAC3D,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,yBAAyB;YACvD,QAAQ;YACR;YACA;YACA;YACA;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;QACV,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,yBAAyB;YACvD,QAAQ;QACV;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,6BAA6B;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,EAAE;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACtE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,+BAA+B;YAAE;QAAO;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { authAPI, setAuthToken, clearAuthToken, getStoredToken } from '@/lib/api';\n\ninterface Admin {\n  id: string;\n  email: string;\n  name: string;\n  role: string;\n}\n\ninterface AuthContextType {\n  admin: Admin | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  login: (email: string, password: string) => Promise<void>;\n  logout: () => void;\n  setup: (email: string, password: string, name: string, adminSecret: string) => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [admin, setAdmin] = useState<Admin | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = getStoredToken();\n      if (token) {\n        try {\n          const response = await authAPI.verifyToken();\n          if (response.valid && response.admin) {\n            setAdmin(response.admin);\n            setAuthToken(token);\n          } else {\n            clearAuthToken();\n          }\n        } catch (error) {\n          console.error('Token verification failed:', error);\n          clearAuthToken();\n        }\n      }\n      setIsLoading(false);\n    };\n\n    initializeAuth();\n  }, []);\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await authAPI.login(email, password);\n      if (response.token && response.admin) {\n        setAuthToken(response.token);\n        setAdmin(response.admin);\n      } else {\n        throw new Error('Invalid response from server');\n      }\n    } catch (error: any) {\n      throw new Error(error.response?.data?.error || 'Login failed');\n    }\n  };\n\n  const logout = () => {\n    clearAuthToken();\n    setAdmin(null);\n  };\n\n  const setup = async (email: string, password: string, name: string, adminSecret: string) => {\n    try {\n      const response = await authAPI.setup(email, password, name, adminSecret);\n      if (response.token && response.admin) {\n        setAuthToken(response.token);\n        setAdmin(response.admin);\n      } else {\n        throw new Error('Invalid response from server');\n      }\n    } catch (error: any) {\n      throw new Error(error.response?.data?.error || 'Setup failed');\n    }\n  };\n\n  const value = {\n    admin,\n    isLoading,\n    isAuthenticated: !!admin,\n    login,\n    logout,\n    setup,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,MAAM,QAAQ,CAAA,GAAA,iHAAA,CAAA,iBAAc,AAAD;YAC3B,IAAI,OAAO;gBACT,IAAI;oBACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW;oBAC1C,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,EAAE;wBACpC,SAAS,SAAS,KAAK;wBACvB,CAAA,GAAA,iHAAA,CAAA,eAAY,AAAD,EAAE;oBACf,OAAO;wBACL,CAAA,GAAA,iHAAA,CAAA,iBAAc,AAAD;oBACf;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,CAAA,GAAA,iHAAA,CAAA,iBAAc,AAAD;gBACf;YACF;YACA,aAAa;QACf;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,KAAK,CAAC,OAAO;YAC5C,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,EAAE;gBACpC,CAAA,GAAA,iHAAA,CAAA,eAAY,AAAD,EAAE,SAAS,KAAK;gBAC3B,SAAS,SAAS,KAAK;YACzB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,SAAS;QACjD;IACF;IAEA,MAAM,SAAS;QACb,CAAA,GAAA,iHAAA,CAAA,iBAAc,AAAD;QACb,SAAS;IACX;IAEA,MAAM,QAAQ,OAAO,OAAe,UAAkB,MAAc;QAClE,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,KAAK,CAAC,OAAO,UAAU,MAAM;YAC5D,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,EAAE;gBACpC,CAAA,GAAA,iHAAA,CAAA,eAAY,AAAD,EAAE,SAAS,KAAK;gBAC3B,SAAS,SAAS,KAAK;YACzB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,SAAS;QACjD;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C", "debugId": null}}]}