exports.id=967,exports.ids=[967],exports.modules={440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},2185:(e,t,a)=>{"use strict";a.d(t,{I5:()=>d,O5:()=>i,Pt:()=>r,R2:()=>o,Tp:()=>n,mq:()=>c,rh:()=>l});let s=a(1060).A.create({baseURL:"http://localhost:3002",headers:{"Content-Type":"application/json"}}),i=e=>{s.defaults.headers.common.Authorization=`Bearer ${e}`,localStorage.setItem("super_admin_token",e)},n=()=>{delete s.defaults.headers.common.Authorization,localStorage.removeItem("super_admin_token")},r=()=>null;s.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(n(),window.location.href="/login"),Promise.reject(e)));let o={login:async(e,t)=>(await s.post("/api/super-admin/auth",{action:"login",email:e,password:t})).data,setup:async(e,t,a,i)=>(await s.post("/api/super-admin/auth",{action:"setup",email:e,password:t,name:a,adminSecret:i})).data,checkSetup:async()=>(await s.post("/api/super-admin/auth",{action:"check-setup"})).data,verifyToken:async()=>(await s.get("/api/super-admin/auth")).data},l={getOverview:async()=>(await s.get("/api/super-admin/analytics")).data,getOrganizations:async()=>(await s.get("/api/super-admin/analytics?type=organizations")).data,getDetailed:async()=>(await s.get("/api/super-admin/analytics?type=detailed")).data},d={getAll:async()=>(await s.get("/api/super-admin/packages")).data,getById:async e=>(await s.get(`/api/super-admin/packages?id=${e}`)).data,create:async e=>(await s.post("/api/super-admin/packages",e)).data,update:async(e,t)=>(await s.put(`/api/super-admin/packages?id=${e}`,t)).data,delete:async e=>(await s.delete(`/api/super-admin/packages?id=${e}`)).data},c={initialize:async e=>(await s.post("/api/super-admin/initialize",{action:e})).data,getInitializationStatus:async()=>(await s.get("/api/super-admin/initialize")).data}},3213:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>l});var s=a(687),i=a(3210),n=a(2185);let r=(0,i.createContext)(void 0),o=()=>{let e=(0,i.useContext)(r);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},l=({children:e})=>{let[t,a]=(0,i.useState)(null),[o,l]=(0,i.useState)(!0);(0,i.useEffect)(()=>{(async()=>{let e=(0,n.Pt)();if(e)try{let t=await n.R2.verifyToken();t.valid&&t.admin?(a(t.admin),(0,n.O5)(e)):(0,n.Tp)()}catch(e){console.error("Token verification failed:",e),(0,n.Tp)()}l(!1)})()},[]);let d=async(e,t)=>{try{let s=await n.R2.login(e,t);if(s.token&&s.admin)(0,n.O5)(s.token),a(s.admin);else throw Error("Invalid response from server")}catch(e){throw Error(e.response?.data?.error||"Login failed")}},c=async(e,t,s,i)=>{try{let r=await n.R2.setup(e,t,s,i);if(r.token&&r.admin)(0,n.O5)(r.token),a(r.admin);else throw Error("Invalid response from server")}catch(e){throw Error(e.response?.data?.error||"Setup failed")}};return(0,s.jsx)(r.Provider,{value:{admin:t,isLoading:o,isAuthenticated:!!t,login:d,logout:()=>{(0,n.Tp)(),a(null)},setup:c},children:e})}},4431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l,metadata:()=>o});var s=a(7413),i=a(7339),n=a.n(i);a(1135);var r=a(9131);let o={title:"Avian Email - Super Admin Dashboard",description:"Super Admin Dashboard for Avian Email Platform"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:n().className,children:(0,s.jsx)(r.AuthProvider,{children:e})})})}},4587:(e,t,a)=>{Promise.resolve().then(a.bind(a,9131))},4675:(e,t,a)=>{Promise.resolve().then(a.bind(a,3213))},6640:(e,t,a)=>{"use strict";a.d(t,{A:()=>b});var s=a(687);a(3210);var i=a(6189),n=a(3213),r=a(5814),o=a.n(r),l=a(3411),d=a(8559),c=a(9080),m=a(1312),h=a(3928),u=a(4027),p=a(9891),x=a(83),v=a(9384);let f=[{name:"Dashboard",href:"/dashboard",icon:l.A},{name:"Analytics",href:"/analytics",icon:d.A},{name:"Packages",href:"/packages",icon:c.A},{name:"Organizations",href:"/organizations",icon:m.A},{name:"Revenue",href:"/revenue",icon:h.A},{name:"System",href:"/system",icon:u.A}];function g({children:e}){let{admin:t,logout:a}=(0,n.A)(),r=(0,i.usePathname)();return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsxs)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-gray-900",children:[(0,s.jsx)("div",{className:"flex h-16 items-center justify-center px-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(p.A,{className:"h-8 w-8 text-blue-400"}),(0,s.jsx)("span",{className:"text-xl font-bold text-white",children:"Super Admin"})]})}),(0,s.jsx)("nav",{className:"mt-8 px-4",children:(0,s.jsx)("ul",{className:"space-y-2",children:f.map(e=>{let t=r===e.href;return(0,s.jsx)("li",{children:(0,s.jsxs)(o(),{href:e.href,className:(0,v.A)("flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",t?"bg-blue-600 text-white":"text-gray-300 hover:bg-gray-800 hover:text-white"),children:[(0,s.jsx)(e.icon,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:e.name})]})},e.name)})})}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:(0,s.jsx)("div",{className:"rounded-lg bg-gray-800 p-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-white",children:t?.name}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:t?.email})]}),(0,s.jsx)("button",{onClick:a,className:"rounded-md p-1 text-gray-400 hover:text-white",title:"Logout",children:(0,s.jsx)(x.A,{className:"h-5 w-5"})})]})})})]}),(0,s.jsxs)("div",{className:"pl-64",children:[(0,s.jsx)("div",{className:"sticky top-0 z-40 bg-white shadow-sm",children:(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between px-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:f.find(e=>e.href===r)?.name||"Super Admin"}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsx)("div",{className:"rounded-full bg-green-100 px-3 py-1",children:(0,s.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Production"})})})]})}),(0,s.jsx)("main",{className:"p-6",children:e})]})]})}function b({children:e}){let{isAuthenticated:t,isLoading:a}=(0,n.A)();return((0,i.useRouter)(),a)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):t?(0,s.jsx)(g,{children:e}):null}},8116:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,6444,23)),Promise.resolve().then(a.t.bind(a,6042,23)),Promise.resolve().then(a.t.bind(a,8170,23)),Promise.resolve().then(a.t.bind(a,9477,23)),Promise.resolve().then(a.t.bind(a,9345,23)),Promise.resolve().then(a.t.bind(a,2089,23)),Promise.resolve().then(a.t.bind(a,6577,23)),Promise.resolve().then(a.t.bind(a,1307,23))},9131:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>i});var s=a(2907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx","useAuth");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx","AuthProvider")},9556:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,6346,23)),Promise.resolve().then(a.t.bind(a,7924,23)),Promise.resolve().then(a.t.bind(a,5656,23)),Promise.resolve().then(a.t.bind(a,99,23)),Promise.resolve().then(a.t.bind(a,8243,23)),Promise.resolve().then(a.t.bind(a,8827,23)),Promise.resolve().then(a.t.bind(a,2763,23)),Promise.resolve().then(a.t.bind(a,7173,23))}};