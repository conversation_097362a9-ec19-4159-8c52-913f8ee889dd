[{"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/dashboard/page.tsx": "1", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/layout.tsx": "2", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/login/page.tsx": "3", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/packages/page.tsx": "4", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/page.tsx": "5", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/components/Layout.tsx": "6", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/components/ProtectedRoute.tsx": "7", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx": "8", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/lib/api.ts": "9"}, {"size": 9528, "mtime": 1748562373465, "results": "10", "hashOfConfig": "11"}, {"size": 631, "mtime": 1748561967303, "results": "12", "hashOfConfig": "11"}, {"size": 7921, "mtime": 1748561897010, "results": "13", "hashOfConfig": "11"}, {"size": 7726, "mtime": 1748562167007, "results": "14", "hashOfConfig": "11"}, {"size": 723, "mtime": 1748562049299, "results": "15", "hashOfConfig": "11"}, {"size": 3791, "mtime": 1748561865470, "results": "16", "hashOfConfig": "11"}, {"size": 863, "mtime": 1748562085941, "results": "17", "hashOfConfig": "11"}, {"size": 2794, "mtime": 1748561844768, "results": "18", "hashOfConfig": "11"}, {"size": 3667, "mtime": 1748561828695, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "o9ewph", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/dashboard/page.tsx", ["47", "48", "49"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/login/page.tsx", ["50"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/packages/page.tsx", ["51", "52"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/components/Layout.tsx", [], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx", ["53", "54"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/lib/api.ts", ["55", "56", "57"], [], {"ruleId": "58", "severity": 1, "message": "59", "line": 41, "column": 52, "nodeType": "60", "messageId": "61", "endLine": 41, "endColumn": 55, "suggestions": "62"}, {"ruleId": "58", "severity": 1, "message": "59", "line": 55, "column": 23, "nodeType": "60", "messageId": "61", "endLine": 55, "endColumn": 26, "suggestions": "63"}, {"ruleId": "58", "severity": 1, "message": "59", "line": 76, "column": 21, "nodeType": "60", "messageId": "61", "endLine": 76, "endColumn": 24, "suggestions": "64"}, {"ruleId": "58", "severity": 1, "message": "59", "line": 56, "column": 21, "nodeType": "60", "messageId": "61", "endLine": 56, "endColumn": 24, "suggestions": "65"}, {"ruleId": "58", "severity": 1, "message": "59", "line": 37, "column": 21, "nodeType": "60", "messageId": "61", "endLine": 37, "endColumn": 24, "suggestions": "66"}, {"ruleId": "58", "severity": 1, "message": "59", "line": 52, "column": 21, "nodeType": "60", "messageId": "61", "endLine": 52, "endColumn": 24, "suggestions": "67"}, {"ruleId": "58", "severity": 1, "message": "59", "line": 68, "column": 21, "nodeType": "60", "messageId": "61", "endLine": 68, "endColumn": 24, "suggestions": "68"}, {"ruleId": "58", "severity": 1, "message": "59", "line": 87, "column": 21, "nodeType": "60", "messageId": "61", "endLine": 87, "endColumn": 24, "suggestions": "69"}, {"ruleId": "70", "severity": 1, "message": "71", "line": 14, "column": 5, "nodeType": null, "messageId": "72", "endLine": 14, "endColumn": 14}, {"ruleId": "58", "severity": 1, "message": "59", "line": 120, "column": 31, "nodeType": "60", "messageId": "61", "endLine": 120, "endColumn": 34, "suggestions": "73"}, {"ruleId": "58", "severity": 1, "message": "59", "line": 125, "column": 43, "nodeType": "60", "messageId": "61", "endLine": 125, "endColumn": 46, "suggestions": "74"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["75", "76"], ["77", "78"], ["79", "80"], ["81", "82"], ["83", "84"], ["85", "86"], ["87", "88"], ["89", "90"], "@typescript-eslint/no-unused-vars", "'authToken' is assigned a value but never used.", "unusedVar", ["91", "92"], ["93", "94"], {"messageId": "95", "fix": "96", "desc": "97"}, {"messageId": "98", "fix": "99", "desc": "100"}, {"messageId": "95", "fix": "101", "desc": "97"}, {"messageId": "98", "fix": "102", "desc": "100"}, {"messageId": "95", "fix": "103", "desc": "97"}, {"messageId": "98", "fix": "104", "desc": "100"}, {"messageId": "95", "fix": "105", "desc": "97"}, {"messageId": "98", "fix": "106", "desc": "100"}, {"messageId": "95", "fix": "107", "desc": "97"}, {"messageId": "98", "fix": "108", "desc": "100"}, {"messageId": "95", "fix": "109", "desc": "97"}, {"messageId": "98", "fix": "110", "desc": "100"}, {"messageId": "95", "fix": "111", "desc": "97"}, {"messageId": "98", "fix": "112", "desc": "100"}, {"messageId": "95", "fix": "113", "desc": "97"}, {"messageId": "98", "fix": "114", "desc": "100"}, {"messageId": "95", "fix": "115", "desc": "97"}, {"messageId": "98", "fix": "116", "desc": "100"}, {"messageId": "95", "fix": "117", "desc": "97"}, {"messageId": "98", "fix": "118", "desc": "100"}, "suggestUnknown", {"range": "119", "text": "120"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "121", "text": "122"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "123", "text": "120"}, {"range": "124", "text": "122"}, {"range": "125", "text": "120"}, {"range": "126", "text": "122"}, {"range": "127", "text": "120"}, {"range": "128", "text": "122"}, {"range": "129", "text": "120"}, {"range": "130", "text": "122"}, {"range": "131", "text": "120"}, {"range": "132", "text": "122"}, {"range": "133", "text": "120"}, {"range": "134", "text": "122"}, {"range": "135", "text": "120"}, {"range": "136", "text": "122"}, {"range": "137", "text": "120"}, {"range": "138", "text": "122"}, {"range": "139", "text": "120"}, {"range": "140", "text": "122"}, [825, 828], "unknown", [825, 828], "never", [1273, 1276], [1273, 1276], [1864, 1867], [1864, 1867], [1558, 1561], [1558, 1561], [1002, 1005], [1002, 1005], [1411, 1414], [1411, 1414], [1942, 1945], [1942, 1945], [2508, 2511], [2508, 2511], [2871, 2874], [2871, 2874], [3034, 3037], [3034, 3037]]