[{"/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/analytics/page.tsx": "1", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/dashboard/page.tsx": "2", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/layout.tsx": "3", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/login/page.tsx": "4", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/organizations/page.tsx": "5", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/packages/page.tsx": "6", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/page.tsx": "7", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/revenue/page.tsx": "8", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/system/page.tsx": "9", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/components/Layout.tsx": "10", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/components/ProtectedRoute.tsx": "11", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx": "12", "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/lib/api.ts": "13"}, {"size": 9595, "mtime": 1748635307085, "results": "14", "hashOfConfig": "15"}, {"size": 9528, "mtime": 1748562373465, "results": "16", "hashOfConfig": "15"}, {"size": 631, "mtime": 1748561967303, "results": "17", "hashOfConfig": "15"}, {"size": 7921, "mtime": 1748561897010, "results": "18", "hashOfConfig": "15"}, {"size": 11571, "mtime": 1748635329131, "results": "19", "hashOfConfig": "15"}, {"size": 7726, "mtime": 1748562167007, "results": "20", "hashOfConfig": "15"}, {"size": 723, "mtime": 1748562049299, "results": "21", "hashOfConfig": "15"}, {"size": 14003, "mtime": 1748635357249, "results": "22", "hashOfConfig": "15"}, {"size": 15677, "mtime": 1748635193388, "results": "23", "hashOfConfig": "15"}, {"size": 3791, "mtime": 1748561865470, "results": "24", "hashOfConfig": "15"}, {"size": 863, "mtime": 1748562085941, "results": "25", "hashOfConfig": "15"}, {"size": 2794, "mtime": 1748561844768, "results": "26", "hashOfConfig": "15"}, {"size": 3716, "mtime": 1748636571446, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "o9ewph", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/analytics/page.tsx", ["67", "68"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/dashboard/page.tsx", ["69", "70", "71"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/login/page.tsx", ["72"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/organizations/page.tsx", ["73", "74"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/packages/page.tsx", ["75", "76"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/revenue/page.tsx", ["77", "78"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/app/system/page.tsx", ["79", "80", "81", "82"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/components/Layout.tsx", [], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/contexts/AuthContext.tsx", ["83", "84"], [], "/Users/<USER>/Documents/GitHub/avian-email-backend-nodemailer/super-admin-dashboard/src/lib/api.ts", ["85", "86", "87"], [], {"ruleId": "88", "severity": 1, "message": "89", "line": 6, "column": 3, "nodeType": null, "messageId": "90", "endLine": 6, "endColumn": 12}, {"ruleId": "91", "severity": 1, "message": "92", "line": 63, "column": 19, "nodeType": "93", "messageId": "94", "endLine": 63, "endColumn": 22, "suggestions": "95"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 41, "column": 52, "nodeType": "93", "messageId": "94", "endLine": 41, "endColumn": 55, "suggestions": "96"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 55, "column": 23, "nodeType": "93", "messageId": "94", "endLine": 55, "endColumn": 26, "suggestions": "97"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 76, "column": 21, "nodeType": "93", "messageId": "94", "endLine": 76, "endColumn": 24, "suggestions": "98"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 56, "column": 21, "nodeType": "93", "messageId": "94", "endLine": 56, "endColumn": 24, "suggestions": "99"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 48, "column": 19, "nodeType": "93", "messageId": "94", "endLine": 48, "endColumn": 22, "suggestions": "100"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 203, "column": 60, "nodeType": "93", "messageId": "94", "endLine": 203, "endColumn": 63, "suggestions": "101"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 37, "column": 21, "nodeType": "93", "messageId": "94", "endLine": 37, "endColumn": 24, "suggestions": "102"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 52, "column": 21, "nodeType": "93", "messageId": "94", "endLine": 52, "endColumn": 24, "suggestions": "103"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 54, "column": 19, "nodeType": "93", "messageId": "94", "endLine": 54, "endColumn": 22, "suggestions": "104"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 118, "column": 61, "nodeType": "93", "messageId": "94", "endLine": 118, "endColumn": 64, "suggestions": "105"}, {"ruleId": "88", "severity": 1, "message": "106", "line": 9, "column": 3, "nodeType": null, "messageId": "90", "endLine": 9, "endColumn": 9}, {"ruleId": "88", "severity": 1, "message": "107", "line": 56, "column": 14, "nodeType": null, "messageId": "90", "endLine": 56, "endColumn": 17}, {"ruleId": "91", "severity": 1, "message": "92", "line": 72, "column": 19, "nodeType": "93", "messageId": "94", "endLine": 72, "endColumn": 22, "suggestions": "108"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 92, "column": 19, "nodeType": "93", "messageId": "94", "endLine": 92, "endColumn": 22, "suggestions": "109"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 68, "column": 21, "nodeType": "93", "messageId": "94", "endLine": 68, "endColumn": 24, "suggestions": "110"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 87, "column": 21, "nodeType": "93", "messageId": "94", "endLine": 87, "endColumn": 24, "suggestions": "111"}, {"ruleId": "88", "severity": 1, "message": "112", "line": 14, "column": 5, "nodeType": null, "messageId": "90", "endLine": 14, "endColumn": 14}, {"ruleId": "91", "severity": 1, "message": "92", "line": 120, "column": 31, "nodeType": "93", "messageId": "94", "endLine": 120, "endColumn": 34, "suggestions": "113"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 125, "column": 43, "nodeType": "93", "messageId": "94", "endLine": 125, "endColumn": 46, "suggestions": "114"}, "@typescript-eslint/no-unused-vars", "'BarChart3' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["115", "116"], ["117", "118"], ["119", "120"], ["121", "122"], ["123", "124"], ["125", "126"], ["127", "128"], ["129", "130"], ["131", "132"], ["133", "134"], ["135", "136"], "'Shield' is defined but never used.", "'err' is defined but never used.", ["137", "138"], ["139", "140"], ["141", "142"], ["143", "144"], "'authToken' is assigned a value but never used.", ["145", "146"], ["147", "148"], {"messageId": "149", "fix": "150", "desc": "151"}, {"messageId": "152", "fix": "153", "desc": "154"}, {"messageId": "149", "fix": "155", "desc": "151"}, {"messageId": "152", "fix": "156", "desc": "154"}, {"messageId": "149", "fix": "157", "desc": "151"}, {"messageId": "152", "fix": "158", "desc": "154"}, {"messageId": "149", "fix": "159", "desc": "151"}, {"messageId": "152", "fix": "160", "desc": "154"}, {"messageId": "149", "fix": "161", "desc": "151"}, {"messageId": "152", "fix": "162", "desc": "154"}, {"messageId": "149", "fix": "163", "desc": "151"}, {"messageId": "152", "fix": "164", "desc": "154"}, {"messageId": "149", "fix": "165", "desc": "151"}, {"messageId": "152", "fix": "166", "desc": "154"}, {"messageId": "149", "fix": "167", "desc": "151"}, {"messageId": "152", "fix": "168", "desc": "154"}, {"messageId": "149", "fix": "169", "desc": "151"}, {"messageId": "152", "fix": "170", "desc": "154"}, {"messageId": "149", "fix": "171", "desc": "151"}, {"messageId": "152", "fix": "172", "desc": "154"}, {"messageId": "149", "fix": "173", "desc": "151"}, {"messageId": "152", "fix": "174", "desc": "154"}, {"messageId": "149", "fix": "175", "desc": "151"}, {"messageId": "152", "fix": "176", "desc": "154"}, {"messageId": "149", "fix": "177", "desc": "151"}, {"messageId": "152", "fix": "178", "desc": "154"}, {"messageId": "149", "fix": "179", "desc": "151"}, {"messageId": "152", "fix": "180", "desc": "154"}, {"messageId": "149", "fix": "181", "desc": "151"}, {"messageId": "152", "fix": "182", "desc": "154"}, {"messageId": "149", "fix": "183", "desc": "151"}, {"messageId": "152", "fix": "184", "desc": "154"}, {"messageId": "149", "fix": "185", "desc": "151"}, {"messageId": "152", "fix": "186", "desc": "154"}, "suggestUnknown", {"range": "187", "text": "188"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "189", "text": "190"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "191", "text": "188"}, {"range": "192", "text": "190"}, {"range": "193", "text": "188"}, {"range": "194", "text": "190"}, {"range": "195", "text": "188"}, {"range": "196", "text": "190"}, {"range": "197", "text": "188"}, {"range": "198", "text": "190"}, {"range": "199", "text": "188"}, {"range": "200", "text": "190"}, {"range": "201", "text": "188"}, {"range": "202", "text": "190"}, {"range": "203", "text": "188"}, {"range": "204", "text": "190"}, {"range": "205", "text": "188"}, {"range": "206", "text": "190"}, {"range": "207", "text": "188"}, {"range": "208", "text": "190"}, {"range": "209", "text": "188"}, {"range": "210", "text": "190"}, {"range": "211", "text": "188"}, {"range": "212", "text": "190"}, {"range": "213", "text": "188"}, {"range": "214", "text": "190"}, {"range": "215", "text": "188"}, {"range": "216", "text": "190"}, {"range": "217", "text": "188"}, {"range": "218", "text": "190"}, {"range": "219", "text": "188"}, {"range": "220", "text": "190"}, {"range": "221", "text": "188"}, {"range": "222", "text": "190"}, [1312, 1315], "unknown", [1312, 1315], "never", [825, 828], [825, 828], [1273, 1276], [1273, 1276], [1864, 1867], [1864, 1867], [1558, 1561], [1558, 1561], [1204, 1207], [1204, 1207], [6758, 6761], [6758, 6761], [1002, 1005], [1002, 1005], [1411, 1414], [1411, 1414], [1224, 1227], [1224, 1227], [3052, 3055], [3052, 3055], [1857, 1860], [1857, 1860], [2402, 2405], [2402, 2405], [1942, 1945], [1942, 1945], [2508, 2511], [2508, 2511], [2871, 2874], [2871, 2874], [3034, 3037], [3034, 3037]]