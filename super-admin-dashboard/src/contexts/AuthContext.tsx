'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { authAPI, setAuthToken, clearAuthToken, getStoredToken } from '@/lib/api';

interface Admin {
  id: string;
  email: string;
  name: string;
  role: string;
}

interface AuthContextType {
  admin: Admin | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  setup: (email: string, password: string, name: string, adminSecret: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      const token = getStoredToken();
      if (token) {
        try {
          const response = await authAPI.verifyToken();
          if (response.valid && response.admin) {
            setAdmin(response.admin);
            setAuthToken(token);
          } else {
            clearAuthToken();
          }
        } catch (error) {
          console.error('Token verification failed:', error);
          clearAuthToken();
        }
      }
      setIsLoading(false);
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const response = await authAPI.login(email, password);
      if (response.token && response.admin) {
        setAuthToken(response.token);
        setAdmin(response.admin);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Login failed');
    }
  };

  const logout = () => {
    clearAuthToken();
    setAdmin(null);
  };

  const setup = async (email: string, password: string, name: string, adminSecret: string) => {
    try {
      const response = await authAPI.setup(email, password, name, adminSecret);
      if (response.token && response.admin) {
        setAuthToken(response.token);
        setAdmin(response.admin);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Setup failed');
    }
  };

  const value = {
    admin,
    isLoading,
    isAuthenticated: !!admin,
    login,
    logout,
    setup,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
