import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
let authToken: string | null = null;

export const setAuthToken = (token: string) => {
  authToken = token;
  api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  localStorage.setItem('super_admin_token', token);
};

export const clearAuthToken = () => {
  authToken = null;
  delete api.defaults.headers.common['Authorization'];
  localStorage.removeItem('super_admin_token');
};

export const getStoredToken = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('super_admin_token');
  }
  return null;
};

// Initialize token from localStorage
if (typeof window !== 'undefined') {
  const storedToken = getStoredToken();
  if (storedToken) {
    setAuthToken(storedToken);
  }
}

// Response interceptor for handling auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      clearAuthToken();
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (email: string, password: string) => {
    const response = await api.post('/api/super-admin/auth', {
      action: 'login',
      email,
      password,
    });
    return response.data;
  },

  setup: async (email: string, password: string, name: string, adminSecret: string) => {
    const response = await api.post('/api/super-admin/auth', {
      action: 'setup',
      email,
      password,
      name,
      adminSecret,
    });
    return response.data;
  },

  checkSetup: async () => {
    const response = await api.post('/api/super-admin/auth', {
      action: 'check-setup',
    });
    return response.data;
  },

  verifyToken: async () => {
    const response = await api.get('/api/super-admin/auth');
    return response.data;
  },
};

// Analytics API
export const analyticsAPI = {
  getOverview: async () => {
    const response = await api.get('/api/super-admin/analytics');
    return response.data;
  },

  getOrganizations: async () => {
    const response = await api.get('/api/super-admin/analytics?type=organizations');
    return response.data;
  },

  getDetailed: async () => {
    const response = await api.get('/api/super-admin/analytics?type=detailed');
    return response.data;
  },
};

// Packages API
export const packagesAPI = {
  getAll: async () => {
    const response = await api.get('/api/super-admin/packages');
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/api/super-admin/packages?id=${id}`);
    return response.data;
  },

  create: async (packageData: any) => {
    const response = await api.post('/api/super-admin/packages', packageData);
    return response.data;
  },

  update: async (id: string, packageData: any) => {
    const response = await api.put(`/api/super-admin/packages?id=${id}`, packageData);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/api/super-admin/packages?id=${id}`);
    return response.data;
  },
};

// System API
export const systemAPI = {
  initialize: async (action: string) => {
    const response = await api.post('/api/super-admin/initialize', { action });
    return response.data;
  },

  getInitializationStatus: async () => {
    const response = await api.get('/api/super-admin/initialize');
    return response.data;
  },
};

// Export both named and default
export { api };
export default api;
