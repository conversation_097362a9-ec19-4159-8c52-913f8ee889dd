'use client';

import React, { useState, useEffect } from 'react';
import { analyticsAPI, systemAPI } from '@/lib/api';
import ProtectedRoute from '@/components/ProtectedRoute';
import {
  DollarSign,
  Users,
  Package,
  TrendingUp,
  Activity,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';

interface BusinessMetrics {
  revenue: {
    total: number;
    monthly: number;
    growth: number;
  };
  subscriptions: {
    total: number;
    active: number;
    cancelled: number;
  };
  users: {
    total: number;
    active: number;
    newThisMonth: number;
  };
  packages: {
    total: number;
    active: number;
    mostPopular: string;
  };
}

export default function DashboardPage() {
  const [metrics, setMetrics] = useState<BusinessMetrics | null>(null);
  const [systemStatus, setSystemStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [metricsResponse, statusResponse] = await Promise.all([
          analyticsAPI.getOverview(),
          systemAPI.getInitializationStatus(),
        ]);

        setMetrics(metricsResponse.data);
        setSystemStatus(statusResponse);
      } catch (error: any) {
        setError(error.response?.data?.error || 'Failed to load dashboard data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleInitializeSystem = async () => {
    try {
      setIsLoading(true);
      await systemAPI.initialize('full-setup');
      // Refresh data
      const [metricsResponse, statusResponse] = await Promise.all([
        analyticsAPI.getOverview(),
        systemAPI.getInitializationStatus(),
      ]);
      setMetrics(metricsResponse.data);
      setSystemStatus(statusResponse);
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to initialize system');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-red-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  const statCards = [
    {
      name: 'Total Revenue',
      value: `$${metrics?.revenue.total.toFixed(2) || '0.00'}`,
      change: `${(metrics?.revenue.growth || 0) >= 0 ? '+' : ''}${(metrics?.revenue.growth || 0).toFixed(1)}%`,
      changeType: (metrics?.revenue.growth || 0) >= 0 ? 'positive' : 'negative',
      icon: DollarSign,
    },
    {
      name: 'Active Subscriptions',
      value: metrics?.subscriptions.active || 0,
      change: `${metrics?.subscriptions.total || 0} total`,
      changeType: 'neutral',
      icon: Package,
    },
    {
      name: 'Total Users',
      value: metrics?.users.total || 0,
      change: `${metrics?.users.newThisMonth || 0} new this month`,
      changeType: 'positive',
      icon: Users,
    },
    {
      name: 'Active Packages',
      value: metrics?.packages.active || 0,
      change: `Most popular: ${metrics?.packages.mostPopular || 'None'}`,
      changeType: 'neutral',
      icon: Activity,
    },
  ];

  return (
    <ProtectedRoute>
      <div className="space-y-6">
      {/* System Status */}
      {systemStatus && !systemStatus.initialized && (
        <div className="rounded-md bg-yellow-50 p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-yellow-400" />
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-yellow-800">System Setup Required</h3>
              <div className="mt-2 text-sm text-yellow-700">
                The package system needs to be initialized. This will create default packages and
                assign them to existing organizations.
              </div>
              <div className="mt-4">
                <button
                  onClick={handleInitializeSystem}
                  className="bg-yellow-100 px-3 py-2 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-200"
                >
                  Initialize System
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {systemStatus && systemStatus.initialized && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="flex">
            <CheckCircle className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">System Initialized</h3>
              <div className="mt-2 text-sm text-green-700">
                Package system is active with {systemStatus.packages?.total || 0} packages
                configured.
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => (
          <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">{stat.name}</dt>
                    <dd className="text-lg font-medium text-gray-900">{stat.value}</dd>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <div className="flex items-center text-sm">
                  <span
                    className={`${
                      stat.changeType === 'positive'
                        ? 'text-green-600'
                        : stat.changeType === 'negative'
                        ? 'text-red-600'
                        : 'text-gray-600'
                    }`}
                  >
                    {stat.change}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Quick Actions</h3>
          <div className="mt-5 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <button className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <Package className="mx-auto h-8 w-8 text-gray-400" />
              <span className="mt-2 block text-sm font-medium text-gray-900">
                Create New Package
              </span>
            </button>

            <button className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <TrendingUp className="mx-auto h-8 w-8 text-gray-400" />
              <span className="mt-2 block text-sm font-medium text-gray-900">View Analytics</span>
            </button>

            <button className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <Users className="mx-auto h-8 w-8 text-gray-400" />
              <span className="mt-2 block text-sm font-medium text-gray-900">
                Manage Organizations
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">System Overview</h3>
          <div className="mt-5">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Monthly Revenue</span>
                <span className="text-sm font-medium text-gray-900">
                  ${metrics?.revenue.monthly.toFixed(2) || '0.00'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Active Users</span>
                <span className="text-sm font-medium text-gray-900">
                  {metrics?.users.active || 0}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Cancelled Subscriptions</span>
                <span className="text-sm font-medium text-gray-900">
                  {metrics?.subscriptions.cancelled || 0}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </ProtectedRoute>
  );
}
