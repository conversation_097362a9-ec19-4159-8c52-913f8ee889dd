'use client';

import React, { useState, useEffect } from 'react';
import { api } from '@/lib/api';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard,
  Package,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
} from 'lucide-react';

interface RevenueData {
  revenue: {
    total: number;
    monthly: number;
    growth: number;
  };
  subscriptions: {
    total: number;
    active: number;
    cancelled: number;
    byPackage: Array<{
      packageName: string;
      count: number;
      revenue: number;
    }>;
  };
  packages: {
    total: number;
    active: number;
    mostPopular: string;
  };
}

export default function RevenuePage() {
  const [revenueData, setRevenueData] = useState<RevenueData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');

  useEffect(() => {
    fetchRevenueData();
  }, [timeRange]);

  const fetchRevenueData = async () => {
    try {
      setLoading(true);
      const response = await api.get('/super-admin/analytics?type=overview');
      setRevenueData(response.data.data || response.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch revenue data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-800">{error}</p>
        <button
          onClick={fetchRevenueData}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  if (!revenueData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No revenue data available</p>
      </div>
    );
  }

  const averageRevenuePerSubscription = revenueData.subscriptions.active > 0
    ? revenueData.revenue.total / revenueData.subscriptions.active
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Revenue Analytics</h1>
          <p className="text-gray-600">Track revenue performance and subscription metrics</p>
        </div>
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4 text-gray-400" />
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Revenue Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Revenue */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(revenueData.revenue.total)}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            {revenueData.revenue.growth >= 0 ? (
              <ArrowUpRight className="h-4 w-4 text-green-500" />
            ) : (
              <ArrowDownRight className="h-4 w-4 text-red-500" />
            )}
            <span className={`text-sm font-medium ${
              revenueData.revenue.growth >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {Math.abs(revenueData.revenue.growth).toFixed(1)}%
            </span>
            <span className="text-sm text-gray-500 ml-2">vs last period</span>
          </div>
        </div>

        {/* Monthly Revenue */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(revenueData.revenue.monthly)}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <TrendingUp className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">Current month</span>
          </div>
        </div>

        {/* Active Subscriptions */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Subscriptions</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatNumber(revenueData.subscriptions.active)}
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <CreditCard className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">
              {formatNumber(revenueData.subscriptions.cancelled)} cancelled
            </span>
          </div>
        </div>

        {/* Average Revenue Per User */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Revenue/User</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(averageRevenuePerSubscription)}
              </p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <Package className="h-6 w-6 text-orange-600" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">Per subscription</span>
          </div>
        </div>
      </div>

      {/* Revenue by Package */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Revenue by Package</h2>
        </div>
        <div className="p-6">
          <div className="space-y-6">
            {revenueData.subscriptions.byPackage.map((pkg, index) => {
              const percentage = revenueData.revenue.total > 0
                ? (pkg.revenue / revenueData.revenue.total) * 100
                : 0;

              return (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900">{pkg.packageName}</h3>
                      <p className="text-sm text-gray-500">
                        {formatNumber(pkg.count)} subscribers
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">{formatCurrency(pkg.revenue)}</p>
                      <p className="text-sm text-gray-500">{percentage.toFixed(1)}% of total</p>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Revenue Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Subscription Health */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Subscription Health</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total Subscriptions</span>
                <span className="font-medium">{formatNumber(revenueData.subscriptions.total)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Active Subscriptions</span>
                <span className="font-medium text-green-600">
                  {formatNumber(revenueData.subscriptions.active)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Cancelled Subscriptions</span>
                <span className="font-medium text-red-600">
                  {formatNumber(revenueData.subscriptions.cancelled)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Retention Rate</span>
                <span className="font-medium">
                  {revenueData.subscriptions.total > 0
                    ? ((revenueData.subscriptions.active / revenueData.subscriptions.total) * 100).toFixed(1)
                    : 0}%
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Package Performance */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Package Performance</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total Packages</span>
                <span className="font-medium">{formatNumber(revenueData.packages.total)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Active Packages</span>
                <span className="font-medium">{formatNumber(revenueData.packages.active)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Most Popular</span>
                <span className="font-medium">{revenueData.packages.mostPopular}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Avg Revenue/Package</span>
                <span className="font-medium">
                  {formatCurrency(
                    revenueData.packages.active > 0
                      ? revenueData.revenue.total / revenueData.packages.active
                      : 0
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Growth Indicators */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Growth Indicators</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                {revenueData.revenue.growth >= 0 ? (
                  <TrendingUp className="h-8 w-8 text-green-500" />
                ) : (
                  <TrendingDown className="h-8 w-8 text-red-500" />
                )}
              </div>
              <p className="text-2xl font-bold text-gray-900">
                {Math.abs(revenueData.revenue.growth).toFixed(1)}%
              </p>
              <p className="text-sm text-gray-600">Revenue Growth</p>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <CreditCard className="h-8 w-8 text-blue-500" />
              </div>
              <p className="text-2xl font-bold text-gray-900">
                {revenueData.subscriptions.total > 0
                  ? ((revenueData.subscriptions.active / revenueData.subscriptions.total) * 100).toFixed(1)
                  : 0}%
              </p>
              <p className="text-sm text-gray-600">Retention Rate</p>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(averageRevenuePerSubscription)}
              </p>
              <p className="text-sm text-gray-600">ARPU</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
