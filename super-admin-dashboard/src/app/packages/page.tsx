'use client';

import React, { useState, useEffect } from 'react';
import { packagesAPI } from '@/lib/api';
import ProtectedRoute from '@/components/ProtectedRoute';
import { Package, Plus, Edit, Trash2, DollarSign, Users, Zap } from 'lucide-react';

interface PackageData {
  id: string;
  name: string;
  description: string;
  price: number;
  billingCycle: string;
  dailyEmailLimit: number;
  monthlyEmailLimit?: number;
  emailAccountLimit: number;
  aiFeatures: string[];
  status: string;
  isDefault: boolean;
  organizations?: Array<{ id: string; name: string }>;
}

export default function PackagesPage() {
  const [packages, setPackages] = useState<PackageData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchPackages();
  }, []);

  const fetchPackages = async () => {
    try {
      setIsLoading(true);
      const response = await packagesAPI.getAll();
      setPackages(response);
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to load packages');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeletePackage = async (packageId: string) => {
    if (!confirm('Are you sure you want to delete this package?')) {
      return;
    }

    try {
      await packagesAPI.delete(packageId);
      await fetchPackages(); // Refresh the list
    } catch (error: any) {
      alert(error.response?.data?.error || 'Failed to delete package');
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Packages</h1>
            <p className="text-gray-600">Manage subscription packages and pricing</p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
            <Plus className="h-5 w-5" />
            <span>Create Package</span>
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Packages Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {packages.map((pkg) => (
            <div
              key={pkg.id}
              className={`bg-white rounded-lg shadow-md border-2 ${
                pkg.isDefault ? 'border-blue-500' : 'border-gray-200'
              } p-6`}
            >
              {/* Package Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <Package className="h-6 w-6 text-blue-600" />
                  <h3 className="text-lg font-semibold text-gray-900">{pkg.name}</h3>
                  {pkg.isDefault && (
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                      Default
                    </span>
                  )}
                </div>
                <div className="flex space-x-2">
                  <button className="text-gray-400 hover:text-blue-600">
                    <Edit className="h-4 w-4" />
                  </button>
                  {!pkg.isDefault && (
                    <button
                      onClick={() => handleDeletePackage(pkg.id)}
                      className="text-gray-400 hover:text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>

              {/* Price */}
              <div className="mb-4">
                <div className="flex items-baseline space-x-2">
                  <span className="text-3xl font-bold text-gray-900">
                    ${pkg.price.toFixed(2)}
                  </span>
                  <span className="text-gray-500">/{pkg.billingCycle.toLowerCase()}</span>
                </div>
                <p className="text-gray-600 text-sm mt-1">{pkg.description}</p>
              </div>

              {/* Features */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-gray-700">
                    {pkg.dailyEmailLimit.toLocaleString()} emails/day
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-gray-700">
                    {pkg.emailAccountLimit} email accounts
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Zap className="h-4 w-4 text-purple-600" />
                  <span className="text-sm text-gray-700">
                    {pkg.aiFeatures.length > 0
                      ? `${pkg.aiFeatures.length} AI features`
                      : 'No AI features'}
                  </span>
                </div>
              </div>

              {/* AI Features List */}
              {pkg.aiFeatures.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">AI Features:</h4>
                  <div className="flex flex-wrap gap-1">
                    {pkg.aiFeatures.map((feature) => (
                      <span
                        key={feature}
                        className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded"
                      >
                        {feature.replace('_', ' ')}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Status */}
              <div className="flex items-center justify-between">
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    pkg.status === 'ACTIVE'
                      ? 'bg-green-100 text-green-800'
                      : pkg.status === 'INACTIVE'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {pkg.status}
                </span>
                <span className="text-xs text-gray-500">
                  {pkg.organizations?.length || 0} organizations
                </span>
              </div>
            </div>
          ))}
        </div>

        {packages.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No packages</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by creating a new package.</p>
            <div className="mt-6">
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 mx-auto">
                <Plus className="h-5 w-5" />
                <span>Create Package</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}
