'use client';

import React, { useState, useEffect } from 'react';
import { api } from '@/lib/api';
import {
  BarChart3,
  TrendingUp,
  Users,
  DollarSign,
  Package,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
} from 'lucide-react';

interface BusinessMetrics {
  revenue: {
    total: number;
    monthly: number;
    growth: number;
  };
  subscriptions: {
    total: number;
    active: number;
    cancelled: number;
    byPackage: Array<{
      packageName: string;
      count: number;
      revenue: number;
    }>;
  };
  users: {
    total: number;
    active: number;
    newThisMonth: number;
  };
  usage: {
    totalEmailsSent: number;
    emailsSentThisMonth: number;
    averageEmailsPerUser: number;
  };
  packages: {
    total: number;
    active: number;
    mostPopular: string;
  };
}

export default function AnalyticsPage() {
  const [metrics, setMetrics] = useState<BusinessMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await api.get('/super-admin/analytics?type=overview');
      setMetrics(response.data.data || response.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch analytics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-800">{error}</p>
        <button
          onClick={fetchAnalytics}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No analytics data available</p>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Business Analytics</h1>
        <p className="text-gray-600">Comprehensive business metrics and insights</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Revenue */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(metrics.revenue.total)}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            {metrics.revenue.growth >= 0 ? (
              <ArrowUpRight className="h-4 w-4 text-green-500" />
            ) : (
              <ArrowDownRight className="h-4 w-4 text-red-500" />
            )}
            <span className={`text-sm font-medium ${
              metrics.revenue.growth >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {Math.abs(metrics.revenue.growth).toFixed(1)}%
            </span>
            <span className="text-sm text-gray-500 ml-2">vs last month</span>
          </div>
        </div>

        {/* Active Subscriptions */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Subscriptions</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatNumber(metrics.subscriptions.active)}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Package className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">
              {formatNumber(metrics.subscriptions.total)} total subscriptions
            </span>
          </div>
        </div>

        {/* Total Users */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatNumber(metrics.users.total)}
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">
              {formatNumber(metrics.users.newThisMonth)} new this month
            </span>
          </div>
        </div>

        {/* Emails Sent */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Emails Sent</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatNumber(metrics.usage.totalEmailsSent)}
              </p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <Activity className="h-6 w-6 text-orange-600" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">
              {formatNumber(metrics.usage.emailsSentThisMonth)} this month
            </span>
          </div>
        </div>
      </div>

      {/* Package Performance */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Package Performance</h2>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {metrics.subscriptions.byPackage.map((pkg, index) => (
              <div key={index} className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">{pkg.packageName}</p>
                  <p className="text-sm text-gray-500">{formatNumber(pkg.count)} subscribers</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900">{formatCurrency(pkg.revenue)}</p>
                  <p className="text-sm text-gray-500">revenue</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Usage Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Usage Statistics</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Average emails per user</span>
                <span className="font-medium">{formatNumber(metrics.usage.averageEmailsPerUser)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Active packages</span>
                <span className="font-medium">{metrics.packages.active}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Most popular package</span>
                <span className="font-medium">{metrics.packages.mostPopular}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Monthly Revenue</h2>
          </div>
          <div className="p-6">
            <div className="text-center">
              <p className="text-3xl font-bold text-gray-900">
                {formatCurrency(metrics.revenue.monthly)}
              </p>
              <p className="text-gray-600 mt-2">This month</p>
              <div className="mt-4 flex items-center justify-center">
                {metrics.revenue.growth >= 0 ? (
                  <TrendingUp className="h-5 w-5 text-green-500 mr-2" />
                ) : (
                  <ArrowDownRight className="h-5 w-5 text-red-500 mr-2" />
                )}
                <span className={`font-medium ${
                  metrics.revenue.growth >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {Math.abs(metrics.revenue.growth).toFixed(1)}% growth
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
