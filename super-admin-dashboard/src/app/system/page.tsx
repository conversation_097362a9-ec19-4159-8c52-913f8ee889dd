'use client';

import React, { useState, useEffect } from 'react';
import { api } from '@/lib/api';
import {
  Settings,
  Database,
  Server,
  Shield,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Package,
  Users,
  Activity,
} from 'lucide-react';

interface SystemStatus {
  database: 'healthy' | 'warning' | 'error';
  api: 'healthy' | 'warning' | 'error';
  packages: 'healthy' | 'warning' | 'error';
}

interface InitializationStatus {
  initialized: boolean;
  packages: {
    total: number;
    hasFree: boolean;
    hasPro: boolean;
    hasEnterprise: boolean;
  };
  setupRequired: boolean;
}

export default function SystemPage() {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    database: 'healthy',
    api: 'healthy',
    packages: 'healthy',
  });
  const [initStatus, setInitStatus] = useState<InitializationStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [initializing, setInitializing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkSystemStatus();
    checkInitializationStatus();
  }, []);

  const checkSystemStatus = async () => {
    try {
      // Check API health
      await api.get('/super-admin/analytics');
      setSystemStatus(prev => ({ ...prev, api: 'healthy', database: 'healthy' }));
    } catch (err) {
      setSystemStatus(prev => ({ ...prev, api: 'error', database: 'error' }));
    }
  };

  const checkInitializationStatus = async () => {
    try {
      setLoading(true);
      const response = await api.get('/super-admin/initialize');
      setInitStatus(response.data);
      
      if (response.data.initialized) {
        setSystemStatus(prev => ({ ...prev, packages: 'healthy' }));
      } else {
        setSystemStatus(prev => ({ ...prev, packages: 'warning' }));
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to check system status');
      setSystemStatus(prev => ({ ...prev, packages: 'error' }));
    } finally {
      setLoading(false);
    }
  };

  const initializeSystem = async () => {
    try {
      setInitializing(true);
      setError(null);
      
      await api.post('/super-admin/initialize', {
        action: 'full-setup'
      });
      
      // Refresh status after initialization
      await checkInitializationStatus();
      await checkSystemStatus();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to initialize system');
    } finally {
      setInitializing(false);
    }
  };

  const getStatusIcon = (status: 'healthy' | 'warning' | 'error') => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
    }
  };

  const getStatusColor = (status: 'healthy' | 'warning' | 'error') => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-red-600 bg-red-100';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">System Management</h1>
        <p className="text-gray-600">Monitor and manage system health and configuration</p>
      </div>

      {/* System Status */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">System Status</h2>
            <button
              onClick={() => {
                checkSystemStatus();
                checkInitializationStatus();
              }}
              className="flex items-center space-x-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
          </div>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Database Status */}
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-full">
                <Database className="h-6 w-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="font-medium text-gray-900">Database</p>
                  {getStatusIcon(systemStatus.database)}
                </div>
                <p className={`text-sm px-2 py-1 rounded-full inline-block mt-1 ${getStatusColor(systemStatus.database)}`}>
                  {systemStatus.database === 'healthy' ? 'Connected' : 
                   systemStatus.database === 'warning' ? 'Warning' : 'Disconnected'}
                </p>
              </div>
            </div>

            {/* API Status */}
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-full">
                <Server className="h-6 w-6 text-green-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="font-medium text-gray-900">API</p>
                  {getStatusIcon(systemStatus.api)}
                </div>
                <p className={`text-sm px-2 py-1 rounded-full inline-block mt-1 ${getStatusColor(systemStatus.api)}`}>
                  {systemStatus.api === 'healthy' ? 'Operational' : 
                   systemStatus.api === 'warning' ? 'Warning' : 'Error'}
                </p>
              </div>
            </div>

            {/* Package System Status */}
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-full">
                <Package className="h-6 w-6 text-purple-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="font-medium text-gray-900">Package System</p>
                  {getStatusIcon(systemStatus.packages)}
                </div>
                <p className={`text-sm px-2 py-1 rounded-full inline-block mt-1 ${getStatusColor(systemStatus.packages)}`}>
                  {systemStatus.packages === 'healthy' ? 'Initialized' : 
                   systemStatus.packages === 'warning' ? 'Setup Required' : 'Error'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* System Initialization */}
      {initStatus && (
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">System Initialization</h2>
          </div>
          <div className="p-6">
            {initStatus.setupRequired ? (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 text-yellow-400 mr-3" />
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-yellow-800">
                      System Setup Required
                    </h3>
                    <p className="text-sm text-yellow-700 mt-1">
                      The package system needs to be initialized before the platform can be used.
                    </p>
                  </div>
                </div>
                <div className="mt-4">
                  <button
                    onClick={initializeSystem}
                    disabled={initializing}
                    className="flex items-center space-x-2 px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50"
                  >
                    {initializing ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Settings className="h-4 w-4" />
                    )}
                    <span>{initializing ? 'Initializing...' : 'Initialize System'}</span>
                  </button>
                </div>
              </div>
            ) : (
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-400 mr-3" />
                  <div>
                    <h3 className="text-sm font-medium text-green-800">
                      System Initialized
                    </h3>
                    <p className="text-sm text-green-700 mt-1">
                      The package system is properly configured and ready to use.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Package Status */}
            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Package Configuration</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <span className="text-sm text-gray-600">Total Packages</span>
                  <span className="font-medium">{initStatus.packages.total}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <span className="text-sm text-gray-600">Free Package</span>
                  <span className={`text-sm font-medium ${initStatus.packages.hasFree ? 'text-green-600' : 'text-red-600'}`}>
                    {initStatus.packages.hasFree ? 'Configured' : 'Missing'}
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <span className="text-sm text-gray-600">Premium Packages</span>
                  <span className={`text-sm font-medium ${(initStatus.packages.hasPro || initStatus.packages.hasEnterprise) ? 'text-green-600' : 'text-yellow-600'}`}>
                    {initStatus.packages.hasPro && initStatus.packages.hasEnterprise ? 'Complete' : 
                     initStatus.packages.hasPro || initStatus.packages.hasEnterprise ? 'Partial' : 'None'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-400 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-red-800">System Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* System Information */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">System Information</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Environment</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Mode</span>
                  <span className="text-sm font-medium">
                    {process.env.NODE_ENV === 'development' ? 'Development' : 'Production'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Version</span>
                  <span className="text-sm font-medium">1.0.0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">API URL</span>
                  <span className="text-sm font-medium">{process.env.NEXT_PUBLIC_API_URL}</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Security</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Authentication</span>
                  <span className="text-sm font-medium text-green-600">JWT Enabled</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">CORS</span>
                  <span className="text-sm font-medium text-green-600">Configured</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Admin Access</span>
                  <span className="text-sm font-medium text-green-600">Protected</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Quick Actions</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => window.location.href = '/packages'}
              className="flex items-center space-x-3 p-4 border border-gray-200 rounded-md hover:bg-gray-50"
            >
              <Package className="h-5 w-5 text-blue-600" />
              <div className="text-left">
                <p className="font-medium text-gray-900">Manage Packages</p>
                <p className="text-sm text-gray-500">Configure subscription packages</p>
              </div>
            </button>

            <button
              onClick={() => window.location.href = '/organizations'}
              className="flex items-center space-x-3 p-4 border border-gray-200 rounded-md hover:bg-gray-50"
            >
              <Users className="h-5 w-5 text-green-600" />
              <div className="text-left">
                <p className="font-medium text-gray-900">View Organizations</p>
                <p className="text-sm text-gray-500">Monitor user organizations</p>
              </div>
            </button>

            <button
              onClick={() => window.location.href = '/analytics'}
              className="flex items-center space-x-3 p-4 border border-gray-200 rounded-md hover:bg-gray-50"
            >
              <Activity className="h-5 w-5 text-purple-600" />
              <div className="text-left">
                <p className="font-medium text-gray-900">View Analytics</p>
                <p className="text-sm text-gray-500">Business metrics and insights</p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
