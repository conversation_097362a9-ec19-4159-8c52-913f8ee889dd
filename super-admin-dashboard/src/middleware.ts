import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Simple middleware that doesn't interfere with the admin dashboard
export function middleware(request: NextRequest) {
  // Just pass through all requests without any authentication checks
  return NextResponse.next();
}

// Don't apply middleware to any paths - let the admin dashboard handle its own auth
export const config = {
  matcher: []
};
