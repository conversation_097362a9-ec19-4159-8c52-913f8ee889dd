/** @type {import('next').NextConfig} */
const nextConfig = {
  // Disable middleware inheritance from parent directory
  experimental: {
    // Ensure we don't inherit middleware from parent
  },
  // Security headers
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          }
        ]
      }
    ]
  },
  // Disable static optimization for auth pages
  async redirects() {
    return []
  }
}

module.exports = nextConfig
