/** @type {import('next').NextConfig} */
const nextConfig = {
  // Completely isolate this Next.js app from parent
  experimental: {
    externalDir: false,
  },
  // Security headers
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          }
        ]
      }
    ]
  },
  // Disable any redirects
  async redirects() {
    return []
  },
  // Disable any rewrites
  async rewrites() {
    return []
  }
}

module.exports = nextConfig
