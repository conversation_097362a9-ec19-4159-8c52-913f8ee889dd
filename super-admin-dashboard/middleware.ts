import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Super Admin Dashboard Middleware - NO AUTHENTICATION REQUIRED
// This completely overrides any parent middleware
export function middleware(request: NextRequest) {
  // Allow ALL requests to pass through without any checks
  // No authentication, no redirects, no interference
  return NextResponse.next();
}

// Apply to NO paths - effectively disabling middleware
export const config = {
  matcher: []
};
