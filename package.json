{"name": "avian-email-backend-nodemailer", "version": "1.0.0", "description": "SaaS email campaign management system", "main": "index.js", "scripts": {"dev": "next dev", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate", "process-campaigns": "node scripts/cron-jobs/process-campaigns.js", "sync-emails": "node scripts/cron-jobs/sync-emails.js", "check-reputation": "node scripts/cron-jobs/check-reputation.js", "monitor-system": "node scripts/cron-jobs/monitor-system.js", "process-agents": "node scripts/cron-jobs/process-agents.js", "process-reminders": "node scripts/cron-jobs/process-reminders.js"}, "dependencies": {"@headlessui/react": "^1.7.19", "@heroicons/react": "^2.2.0", "@langchain/community": "^0.3.44", "@langchain/core": "^0.3.57", "@langchain/langgraph": "^0.2.73", "@langchain/openai": "^0.5.11", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.4.2", "@sentry/nextjs": "^9.10.1", "@stripe/react-stripe-js": "^3.5.1", "@stripe/stripe-js": "^6.1.0", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.69.0", "@types/antd": "^1.0.4", "@types/imap": "^0.8.42", "@types/mailparser": "^3.4.5", "@types/micro": "^10.0.0", "@types/node-cron": "^3.0.11", "@types/recharts": "^1.8.29", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.7", "@vercel/speed-insights": "^1.2.0", "antd": "^5.24.5", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "bullmq": "^4.12.4", "cheerio": "^1.0.0", "crypto-js": "^4.1.1", "csrf": "^3.1.0", "csv-parse": "^5.6.0", "csv-stringify": "^6.5.2", "date-fns": "^4.1.0", "dns-lookup": "^0.1.0", "formidable": "^3.5.2", "html-to-text": "^9.0.5", "imap": "^0.8.19", "isomorphic-dompurify": "^2.22.0", "langchain": "^0.3.27", "langsmith": "^0.3.29", "mailparser": "^3.7.2", "micro": "^10.0.1", "next": "^14.0.3", "next-auth": "^4.23.2", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "openai": "^4.91.1", "postcss": "^8.5.3", "puppeteer": "^24.9.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-email-editor": "^1.7.11", "react-hook-form": "^7.55.0", "react-quill": "^2.0.0", "recharts": "^2.15.2", "stripe": "^17.7.0", "tailwindcss": "^3.4.17", "winston": "^3.17.0", "zod": "^3.22.4", "zustand": "^5.0.3"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/crypto-js": "^4.2.2", "@types/html-to-text": "^9.0.4", "@types/multer": "^1.4.12", "@types/node": "^20.8.4", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.27", "@types/tesseract.js": "^0.0.2", "@types/winston": "^2.4.4", "eslint": "^8.51.0", "eslint-config-next": "13.5.4", "prisma": "^5.4.2", "typescript": "^5.2.2"}}