# Git
.git
.gitignore

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Next.js
.next
out

#folders
./super-admin-dashboard

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Logs
logs
*.log

# OS specific
.DS_Store
Thumbs.db

# IDE specific
.idea
.vscode
*.swp
*.swo

# Build files
dist
build

# Don't ignore these files
!.env.docker
!prisma/schema.prisma
