/**
 * <PERSON><PERSON><PERSON> to start the sequence processor worker
 * This script can be run directly in production to process sequence campaign events
 *
 * This is a critical component for sequence campaigns to work automatically.
 * It must run continuously as a background process.
 */
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Set up proper logging
let logger;
try {
  // Try to import the application logger
  const { logger: appLogger } = require('../src/lib/logger');
  logger = appLogger;
} catch (error) {
  // Fall back to console if app logger is not available
  logger = console;
  logger.info('Using console logger for sequence processor');
}

// Process interval in milliseconds (default: 5 seconds for more responsive processing)
const PROCESS_INTERVAL = parseInt(process.env.SEQUENCE_PROCESSOR_INTERVAL || '5000');

// Maximum number of items to process in one batch
const BATCH_SIZE = parseInt(process.env.SEQUENCE_PROCESSOR_BATCH_SIZE || '20');

// Also periodically check for active sequence campaigns that need processing
const CAMPAIGN_CHECK_INTERVAL = parseInt(process.env.CAMPAIGN_CHECK_INTERVAL || '60000'); // 1 minute

logger.info(`Sequence processor configured with:
- Process interval: ${PROCESS_INTERVAL}ms
- Batch size: ${BATCH_SIZE}
- Campaign check interval: ${CAMPAIGN_CHECK_INTERVAL}ms`);

// Track worker health
let lastSuccessfulRun = Date.now();
let processedCount = 0;
let errorCount = 0;

// Flag to prevent multiple processing runs at the same time
let isProcessing = false;

/**
 * Process the next batch of sequence events from the queue
 */
async function processSequenceEvents() {
  // If already processing, skip this run
  if (isProcessing) {
    logger.debug('Sequence processor already running, skipping this run');
    return;
  }

  isProcessing = true;
  const startTime = Date.now();
  let processedCount = 0;
  let errorCount = 0;

  try {
    logger.info('Starting sequence event processing');

    // Get the next batch of events to process
    const pendingEvents = await prisma.processingQueue.findMany({
      where: {
        type: 'sequence_condition_check',
        status: 'pending',
      },
      orderBy: [
        { priority: 'desc' }, // Higher priority first
        { createdAt: 'asc' }, // Older events first
      ],
      take: BATCH_SIZE,
    });

    logger.info(`Found ${pendingEvents.length} pending sequence events to process`);

    // Process each event
    for (const event of pendingEvents) {
      try {
        // Mark the event as processing
        await prisma.processingQueue.update({
          where: { id: event.id },
          data: {
            status: 'processing',
            processedAt: new Date(),
          },
        });

        // Extract event data
        const payload = event.payload;
        const campaignId = payload.campaignId;
        const leadId = payload.leadId;
        const eventType = payload.eventType;

        logger.info(`Processing sequence event ${event.id}`, {
          campaignId,
          leadId,
          eventType,
          queuedAt: event.createdAt,
        });

        // Get the campaign with all necessary data
        const campaign = await prisma.campaign.findUnique({
          where: { id: campaignId },
          include: {
            steps: {
              orderBy: {
                position: 'asc',
              },
              include: {
                emailAccount: true,
                emailAccounts: {
                  include: {
                    emailAccount: true
                  }
                },
                conditionStep: true,
                conditionedSteps: true,
              },
            },
            leads: {
              where: { id: leadId }, // Only get the specific lead
              include: {
                lead: true,
                currentStep: true,
                stepActivities: {
                  orderBy: {
                    createdAt: 'desc',
                  },
                  include: {
                    step: true,
                  }
                },
              },
            },
          },
        });

        if (!campaign || !campaign.leads || campaign.leads.length === 0) {
          logger.warn(`Campaign or lead not found for event ${event.id}`, {
            campaignId,
            leadId,
            eventType,
          });

          // Mark as completed with error
          await prisma.processingQueue.update({
            where: { id: event.id },
            data: {
              status: 'error',
              error: 'Campaign or lead not found',
              completedAt: new Date(),
            },
          });

          errorCount++;
          continue;
        }

        // Process the lead in the sequence
        const lead = campaign.leads[0];
        logger.info(`Processing lead ${lead.lead.email} for event ${eventType}`);

        // Import the SequenceCampaignService dynamically
        // Try multiple paths to handle different environments
        let SequenceCampaignService;
        try {
          // First try the direct import (works in production build)
          const service = require('../src/services/sequence-campaign.service');
          SequenceCampaignService = service.SequenceCampaignService;
        } catch (importError) {
          try {
            // Try without src prefix (Docker environment)
            const service = require('../services/sequence-campaign.service');
            SequenceCampaignService = service.SequenceCampaignService;
          } catch (secondError) {
            try {
              // Try absolute path (Docker environment)
              const service = require('/app/src/services/sequence-campaign.service');
              SequenceCampaignService = service.SequenceCampaignService;
            } catch (thirdError) {
              // If all attempts fail, throw the original error
              logger.error('Failed to import SequenceCampaignService', {
                originalError: importError.message,
                secondError: secondError.message,
                thirdError: thirdError.message
              });
              throw importError;
            }
          }
        }

        // Process the lead
        await SequenceCampaignService.processLeadInSequence(campaign, lead);

        // Mark the event as completed
        await prisma.processingQueue.update({
          where: { id: event.id },
          data: {
            status: 'completed',
            completedAt: new Date(),
          },
        });

        processedCount++;
        logger.info(`Successfully processed sequence event ${event.id}`);
      } catch (eventError) {
        errorCount++;
        logger.error(`Error processing sequence event ${event.id}`, {
          error: eventError instanceof Error ? eventError.message : 'Unknown error',
          stack: eventError instanceof Error ? eventError.stack : 'No stack trace',
          eventId: event.id,
        });

        // Mark as error but don't fail the entire batch
        try {
          await prisma.processingQueue.update({
            where: { id: event.id },
            data: {
              status: 'error',
              error: eventError instanceof Error ? eventError.message : 'Unknown error',
              completedAt: new Date(),
            },
          });
        } catch (updateError) {
          logger.error(`Failed to update event status to error`, {
            eventId: event.id,
            error: updateError instanceof Error ? updateError.message : 'Unknown error',
          });
        }
      }
    }

    // Log processing summary
    const duration = Date.now() - startTime;
    logger.info(`Sequence event processing completed`, {
      processedCount,
      errorCount,
      duration,
      eventsPerSecond: processedCount > 0 ? Math.round((processedCount / duration) * 1000) : 0,
    });
  } catch (error) {
    logger.error('Error in sequence event processor', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
    });
  } finally {
    isProcessing = false;
  }
}

/**
 * Proactively check for active sequence campaigns that need processing
 * This ensures campaigns progress even if tracking events aren't being triggered
 */
async function checkActiveCampaigns() {
  if (isProcessing) {
    logger.debug('Skipping active campaign check - processor already running');
    return;
  }

  const startTime = Date.now();
  let campaignsChecked = 0;
  let leadsProcessed = 0;

  try {
    logger.info('Checking active sequence campaigns for processing');

    // Find all active sequence campaigns
    const activeCampaigns = await prisma.campaign.findMany({
      where: {
        type: 'sequence',
        status: { in: ['active', 'in_progress'] }
      },
      select: {
        id: true,
        name: true,
        _count: {
          select: {
            leads: true
          }
        }
      }
    });

    logger.info(`Found ${activeCampaigns.length} active sequence campaigns`);
    campaignsChecked = activeCampaigns.length;

    // For each campaign, check if there are leads that need processing
    for (const campaign of activeCampaigns) {
      try {
        // Find leads in condition steps that might need processing
        const leadsInConditionSteps = await prisma.campaignLead.findMany({
          where: {
            campaignId: campaign.id,
            status: 'active',
            currentStep: {
              type: 'condition'
            }
          },
          select: {
            id: true,
            leadId: true
          },
          take: 5 // Process a few leads at a time to avoid overloading
        });

        if (leadsInConditionSteps.length > 0) {
          logger.info(`Found ${leadsInConditionSteps.length} leads in condition steps for campaign ${campaign.name} (${campaign.id})`);

          // Queue these leads for processing
          for (const lead of leadsInConditionSteps) {
            await prisma.processingQueue.create({
              data: {
                type: 'sequence_condition_check',
                status: 'pending',
                payload: {
                  campaignId: campaign.id,
                  leadId: lead.id,
                  eventType: 'system_check',
                  timestamp: new Date().toISOString()
                },
                priority: 0, // Lower priority than user-triggered events
                createdAt: new Date()
              }
            });
            leadsProcessed++;
          }
        }

        // Also check for leads in wait steps that might have completed their wait period
        const leadsInWaitSteps = await prisma.campaignLead.findMany({
          where: {
            campaignId: campaign.id,
            status: 'active',
            currentStep: {
              type: 'wait'
            },
            stepActivities: {
              some: {
                type: 'wait_started',
                createdAt: {
                  lt: new Date(Date.now() - 60 * 60 * 1000) // At least 1 hour old
                }
              }
            }
          },
          select: {
            id: true,
            leadId: true
          },
          take: 5
        });

        if (leadsInWaitSteps.length > 0) {
          logger.info(`Found ${leadsInWaitSteps.length} leads in wait steps for campaign ${campaign.name} (${campaign.id})`);

          // Queue these leads for processing
          for (const lead of leadsInWaitSteps) {
            await prisma.processingQueue.create({
              data: {
                type: 'sequence_condition_check',
                status: 'pending',
                payload: {
                  campaignId: campaign.id,
                  leadId: lead.id,
                  eventType: 'wait_check',
                  timestamp: new Date().toISOString()
                },
                priority: 0,
                createdAt: new Date()
              }
            });
            leadsProcessed++;
          }
        }
      } catch (campaignError) {
        logger.error(`Error checking campaign ${campaign.id}`, {
          error: campaignError instanceof Error ? campaignError.message : 'Unknown error',
          stack: campaignError instanceof Error ? campaignError.stack : undefined
        });
      }
    }

    const duration = Date.now() - startTime;
    logger.info(`Completed active campaign check in ${duration}ms`, {
      campaignsChecked,
      leadsQueued: leadsProcessed,
      duration
    });

    lastSuccessfulRun = Date.now();
  } catch (error) {
    logger.error('Error checking active campaigns', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}

/**
 * Start the sequence processor
 * This function sets up intervals to periodically process sequence events
 * and check for active campaigns
 */
function startSequenceProcessor() {
  logger.info('Starting sequence processor worker');

  // Process events immediately on startup
  processSequenceEvents().catch(error => {
    logger.error('Error in initial sequence event processing', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  });

  // Also check active campaigns on startup
  checkActiveCampaigns().catch(error => {
    logger.error('Error in initial active campaign check', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  });

  // Set up interval for regular queue processing
  const queueIntervalId = setInterval(() => {
    processSequenceEvents().catch(error => {
      logger.error('Error in scheduled sequence event processing', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    });
  }, PROCESS_INTERVAL);

  // Set up interval for checking active campaigns
  const campaignIntervalId = setInterval(() => {
    checkActiveCampaigns().catch(error => {
      logger.error('Error in scheduled active campaign check', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    });
  }, CAMPAIGN_CHECK_INTERVAL);

  // Return the interval IDs so they can be cleared if needed
  return { queueIntervalId, campaignIntervalId };
}

/**
 * Health check function to monitor the worker's status
 * This can be used by monitoring tools to check if the worker is healthy
 */
function getWorkerHealth() {
  const now = Date.now();
  const timeSinceLastRun = now - lastSuccessfulRun;

  // Worker is considered unhealthy if it hasn't run successfully in 5 minutes
  const isHealthy = timeSinceLastRun < 5 * 60 * 1000;

  return {
    status: isHealthy ? 'healthy' : 'unhealthy',
    lastSuccessfulRun: new Date(lastSuccessfulRun).toISOString(),
    timeSinceLastRunMs: timeSinceLastRun,
    processedCount,
    errorCount,
    uptime: process.uptime(),
    memoryUsage: process.memoryUsage(),
    timestamp: new Date().toISOString()
  };
}

// If this file is run directly (not imported), start the processor
if (require.main === module) {
  logger.info('Starting sequence processor worker in standalone mode');
  const intervals = startSequenceProcessor();

  // Health check server removed for Coolify compatibility
  logger.info('Health check server disabled for Coolify deployment');

  // Keep the process running
  process.on('SIGINT', () => {
    logger.info('Received SIGINT signal, shutting down sequence processor');
    clearInterval(intervals.queueIntervalId);
    clearInterval(intervals.campaignIntervalId);
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    logger.info('Received SIGTERM signal, shutting down sequence processor');
    clearInterval(intervals.queueIntervalId);
    clearInterval(intervals.campaignIntervalId);
    process.exit(0);
  });

  // Log that we're running
  logger.info('Sequence processor worker is running. Press Ctrl+C to stop.');

  // Periodically log stats
  setInterval(() => {
    const health = getWorkerHealth();
    logger.info('Sequence processor worker status', {
      status: health.status,
      processedCount,
      errorCount,
      timeSinceLastRun: `${Math.round(health.timeSinceLastRunMs / 1000)}s`,
      memoryUsageMB: Math.round(health.memoryUsage.rss / 1024 / 1024)
    });
  }, 60000); // Log stats every minute
} else {
  // Export functions for use in other modules
  module.exports = {
    processSequenceEvents,
    checkActiveCampaigns,
    startSequenceProcessor,
    getWorkerHealth
  };
}
