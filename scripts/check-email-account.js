const { PrismaClient } = require('@prisma/client');
const CryptoJS = require('crypto-js');
const dotenv = require('dotenv');

dotenv.config();

const prisma = new PrismaClient();

async function checkEmailAccount() {
  try {
    const emailAccount = await prisma.emailAccount.findUnique({
      where: {
        id: 'cm93giqfb000110tqvrh0yzzg'
      }
    });

    if (!emailAccount) {
      console.log('Email account not found');
      return;
    }

    // Decrypt the password if it exists
    let decryptedPassword = null;
    if (emailAccount.smtpPassword) {
      try {
        decryptedPassword = CryptoJS.AES.decrypt(
          emailAccount.smtpPassword,
          process.env.ENCRYPTION_KEY
        ).toString(CryptoJS.enc.Utf8);
      } catch (error) {
        console.log('Failed to decrypt password:', error.message);
      }
    }

    console.log('Email Account Details:');
    console.log('ID:', emailAccount.id);
    console.log('Email:', emailAccount.email);
    console.log('Name:', emailAccount.name);
    console.log('Provider:', emailAccount.provider);
    console.log('Status:', emailAccount.status);
    console.log('SMTP Host:', emailAccount.smtpHost || 'Not set');
    console.log('SMTP Port:', emailAccount.smtpPort || 'Not set');
    console.log('SMTP Username:', emailAccount.smtpUsername || 'Not set');
    console.log('SMTP Password:', emailAccount.smtpPassword ? 'Set (encrypted)' : 'Not set');
    console.log('Decrypted Password:', decryptedPassword || 'Not available');
    console.log('IMAP Host:', emailAccount.imapHost || 'Not set');
    console.log('IMAP Port:', emailAccount.imapPort || 'Not set');
    console.log('IMAP Username:', emailAccount.imapUsername || 'Not set');
    console.log('IMAP Password:', emailAccount.imapPassword ? 'Set (encrypted)' : 'Not set');
    console.log('IMAP Enabled:', emailAccount.imapEnabled);
  } catch (error) {
    console.error('Error checking email account:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkEmailAccount();
