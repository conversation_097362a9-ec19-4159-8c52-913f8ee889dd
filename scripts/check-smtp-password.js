const { PrismaClient } = require('@prisma/client');
const CryptoJS = require('crypto-js');
const dotenv = require('dotenv');

dotenv.config();

const prisma = new PrismaClient();

async function checkSmtpPassword() {
  try {
    // Get the email account
    const emailAccount = await prisma.emailAccount.findUnique({
      where: {
        id: 'cm97csvag00068uhn79oo631o'
      }
    });

    if (!emailAccount) {
      console.log('Email account not found');
      return;
    }

    console.log('Email Account Details:');
    console.log('ID:', emailAccount.id);
    console.log('Email:', emailAccount.email);
    console.log('SMTP Host:', emailAccount.smtpHost);
    console.log('SMTP Port:', emailAccount.smtpPort);
    console.log('SMTP Username:', emailAccount.smtpUsername);
    
    // Check if password exists
    if (!emailAccount.smtpPassword) {
      console.log('SMTP Password: Not set');
      return;
    }

    console.log('SMTP Password (encrypted):', emailAccount.smtpPassword);
    console.log('Encryption Key Length:', process.env.ENCRYPTION_KEY?.length || 0);
    
    // Try to decrypt the password
    try {
      const bytes = CryptoJS.AES.decrypt(emailAccount.smtpPassword, process.env.ENCRYPTION_KEY);
      const decrypted = bytes.toString(CryptoJS.enc.Utf8);
      
      if (decrypted) {
        console.log('Decrypted Password:', decrypted);
      } else {
        console.log('Failed to decrypt password (empty result)');
      }
    } catch (error) {
      console.error('Error decrypting password:', error);
    }
    
  } catch (error) {
    console.error('Error checking SMTP password:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSmtpPassword();
