const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    // Get the first organization
    const organization = await prisma.organization.findFirst();
    
    if (!organization) {
      console.error('No organization found. Please create an organization first.');
      return;
    }

    // Get the first user
    const user = await prisma.user.findFirst();
    
    if (!user) {
      console.error('No user found. Please create a user first.');
      return;
    }

    console.log('Creating notification...');
    
    // Create a sample notification
    const notification = await prisma.notification.create({
      data: {
        title: 'Welcome to Avian Email',
        message: 'Thank you for signing up! Get started by adding your first email account.',
        type: 'info',
        organizationId: organization.id,
        userId: user.id,
      },
    });

    console.log('Notification created:', notification);
  } catch (error) {
    console.error('Error creating notification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
