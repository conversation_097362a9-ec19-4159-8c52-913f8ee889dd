const { PrismaClient } = require('@prisma/client');
const CryptoJS = require('crypto-js');
const dotenv = require('dotenv');

dotenv.config();

const prisma = new PrismaClient();

function encryptPassword(password) {
  return CryptoJS.AES.encrypt(password, process.env.ENCRYPTION_KEY).toString();
}

async function updateEmailAccount() {
  try {
    // Update the email account with the SMTP credentials from the .env file
    const updatedEmailAccount = await prisma.emailAccount.update({
      where: {
        id: 'cm93giqfb000110tqvrh0yzzg'
      },
      data: {
        smtpHost: process.env.SMTP_HOST,
        smtpPort: parseInt(process.env.SMTP_PORT),
        smtpUsername: process.env.SMTP_USER,
        smtpPassword: encryptPassword(process.env.SMTP_PASSWORD),
        status: 'active'
      }
    });

    console.log('Email account updated successfully:');
    console.log('ID:', updatedEmailAccount.id);
    console.log('Email:', updatedEmailAccount.email);
    console.log('SMTP Host:', updatedEmailAccount.smtpHost);
    console.log('SMTP Port:', updatedEmailAccount.smtpPort);
    console.log('SMTP Username:', updatedEmailAccount.smtpUsername);
    console.log('SMTP Password: Set (encrypted)');
    console.log('Status:', updatedEmailAccount.status);
  } catch (error) {
    console.error('Error updating email account:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateEmailAccount();
