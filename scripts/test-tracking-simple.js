/**
 * Simplified test script for the email tracking system
 *
 * This script tests the tracking functionality without creating database records
 *
 * Usage:
 * node scripts/test-tracking-simple.js
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';

// Sample IDs for testing (these don't need to exist in the database)
const sampleCampaignId = 'test-campaign-id';
const sampleLeadId = 'test-lead-id';

async function main() {
  console.log('Starting simplified tracking system test...');
  console.log(`Using base URL: ${baseUrl}`);

  try {
    // Test open tracking
    console.log('\nTesting open tracking...');
    const openUrl = `${baseUrl}/api/tracking/pixel?cid=${sampleCampaignId}&lid=${sampleLeadId}`;
    console.log(`Open tracking URL: ${openUrl}`);
    try {
      const openResponse = await axios.get(openUrl);
      console.log(`Open tracking response status: ${openResponse.status}`);
      console.log(`Open tracking response type: ${openResponse.headers['content-type']}`);
      console.log('Open tracking test completed!');
    } catch (error) {
      console.error('Error testing open tracking:', error.message);
    }

    // Test link click tracking
    console.log('\nTesting link click tracking...');
    const clickUrl = `${baseUrl}/api/tracking/link?cid=${sampleCampaignId}&lid=${sampleLeadId}&url=${encodeURIComponent('https://example.com')}`;
    console.log(`Click tracking URL: ${clickUrl}`);
    try {
      const clickResponse = await axios.get(clickUrl, { maxRedirects: 0 }).catch(error => {
        if (error.response && error.response.status === 302) {
          return { status: 302, headers: error.response.headers };
        }
        throw error;
      });
      console.log(`Click tracking response status: ${clickResponse.status}`);
      console.log(`Redirect location: ${clickResponse.headers.location}`);
      console.log('Click tracking test completed!');
    } catch (error) {
      console.error('Error testing click tracking:', error.message);
    }

    // Test unsubscribe tracking
    console.log('\nTesting unsubscribe tracking...');
    const unsubscribeUrl = `${baseUrl}/api/tracking/unsubscribe?cid=${sampleCampaignId}&lid=${sampleLeadId}`;
    console.log(`Unsubscribe URL: ${unsubscribeUrl}`);
    console.log('Skipping actual unsubscribe request to avoid modifying data.');
    console.log('To test manually, visit the URL in a browser.');

    // Test bounce tracking
    console.log('\nTesting bounce tracking...');
    const bounceUrl = `${baseUrl}/api/tracking/bounce?campaignId=${sampleCampaignId}&leadId=${sampleLeadId}&bounceType=soft&bounceReason=Test%20bounce`;
    console.log(`Bounce tracking URL: ${bounceUrl}`);
    console.log('Skipping actual bounce request to avoid modifying data.');
    console.log('To test manually, make a GET request to the URL.');

    // Test reply tracking
    console.log('\nTesting reply tracking...');
    const replyUrl = `${baseUrl}/api/tracking/reply`;
    console.log(`Reply tracking URL: ${replyUrl}`);
    console.log('To test reply tracking, send an email to a reply tracking address.');
    console.log('Example reply tracking address: <EMAIL>');

    // Generate a sample email with tracking
    console.log('\nGenerating sample email with tracking...');
    const sampleHtml = `
      <html>
        <body>
          <h1>Test Email</h1>
          <p>This is a test email with tracking.</p>
          <p><a href="https://example.com">Click here</a> to visit our website.</p>
          <p>Thank you!</p>
        </body>
      </html>
    `;

    // Save the sample HTML to a file
    const sampleHtmlPath = path.join(__dirname, 'sample-email.html');
    fs.writeFileSync(sampleHtmlPath, sampleHtml);
    console.log(`Sample email saved to: ${sampleHtmlPath}`);

    // Create a sample tracking URL for manual testing
    const trackingPixelUrl = `${baseUrl}/api/tracking/pixel?cid=${sampleCampaignId}&lid=${sampleLeadId}`;
    const trackingLinkUrl = `${baseUrl}/api/tracking/link?cid=${sampleCampaignId}&lid=${sampleLeadId}&url=${encodeURIComponent('https://example.com')}`;

    // Create a sample email with tracking
    const emailWithTracking = `
      <html>
        <body>
          <h1>Test Email with Tracking</h1>
          <p>This is a test email with tracking.</p>
          <p><a href="${trackingLinkUrl}">Click here</a> to visit our website.</p>
          <p>Thank you!</p>
          <img src="${trackingPixelUrl}" width="1" height="1" alt="" style="display:none !important;" />
        </body>
      </html>
    `;

    // Save the email with tracking to a file
    const emailWithTrackingPath = path.join(__dirname, 'sample-email-with-tracking.html');
    fs.writeFileSync(emailWithTrackingPath, emailWithTracking);
    console.log(`Sample email with tracking saved to: ${emailWithTrackingPath}`);

    console.log('\nSimplified tracking system test completed!');
    console.log('You can open the sample email files in a browser to test the tracking functionality.');
  } catch (error) {
    console.error('Error during tracking system test:', error);
  }
}

main()
  .catch(e => {
    console.error(e);
    process.exit(1);
  });
