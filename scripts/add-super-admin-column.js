const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addSuperAdminColumn() {
  try {
    console.log('🔧 Adding isAdmin column to User table...');
    
    // Add the isAdmin column if it doesn't exist
    await prisma.$executeRaw`
      ALTER TABLE "User" 
      ADD COLUMN IF NOT EXISTS "isAdmin" BOOLEAN DEFAULT false;
    `;
    
    console.log('✅ Successfully added isAdmin column');
    
    // Check if any users exist
    const userCount = await prisma.user.count();
    console.log(`📊 Found ${userCount} existing users`);
    
    if (userCount === 0) {
      console.log('ℹ️  No existing users found. You can create the first super admin through the dashboard.');
    } else {
      console.log('ℹ️  Existing users found. Use the admin dashboard to create your first super admin.');
    }
    
  } catch (error) {
    console.error('❌ Error adding super admin column:', error.message);
    
    if (error.message.includes('already exists')) {
      console.log('✅ Column already exists, continuing...');
    } else {
      throw error;
    }
  } finally {
    await prisma.$disconnect();
  }
}

addSuperAdminColumn()
  .then(() => {
    console.log('🎉 Super admin setup completed!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Refresh your admin dashboard at http://localhost:3001');
    console.log('2. Use admin secret: super-secret-admin-key-change-in-production-2024');
    console.log('3. Create your first super admin account');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Setup failed:', error);
    process.exit(1);
  });
