/**
 * <PERSON><PERSON><PERSON> to create the ProcessingQueue table directly in the database
 * This avoids having to reset the entire database
 */
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createProcessingQueueTable() {
  try {
    console.log('Creating ProcessingQueue table...');
    
    // Check if the table already exists
    const tableExists = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public'
        AND table_name = 'ProcessingQueue'
      );
    `;
    
    if (tableExists[0].exists) {
      console.log('ProcessingQueue table already exists. Skipping creation.');
      return;
    }
    
    // Create the table using raw SQL
    await prisma.$executeRaw`
      CREATE TABLE "ProcessingQueue" (
        "id" TEXT NOT NULL,
        "type" TEXT NOT NULL,
        "status" TEXT NOT NULL DEFAULT 'pending',
        "payload" JSONB NOT NULL,
        "priority" INTEGER NOT NULL DEFAULT 0,
        "error" TEXT,
        "retryCount" INTEGER NOT NULL DEFAULT 0,
        "maxRetries" INTEGER NOT NULL DEFAULT 3,
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) NOT NULL,
        "processedAt" TIMESTAMP(3),
        "completedAt" TIMESTAMP(3),

        CONSTRAINT "ProcessingQueue_pkey" PRIMARY KEY ("id")
      );
    `;
    
    // Create the indexes
    await prisma.$executeRaw`
      CREATE INDEX "ProcessingQueue_type_status_idx" ON "ProcessingQueue"("type", "status");
    `;
    
    await prisma.$executeRaw`
      CREATE INDEX "ProcessingQueue_status_priority_createdAt_idx" ON "ProcessingQueue"("status", "priority", "createdAt");
    `;
    
    await prisma.$executeRaw`
      CREATE INDEX "ProcessingQueue_createdAt_idx" ON "ProcessingQueue"("createdAt");
    `;
    
    console.log('ProcessingQueue table created successfully!');
  } catch (error) {
    console.error('Error creating ProcessingQueue table:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
createProcessingQueueTable()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
