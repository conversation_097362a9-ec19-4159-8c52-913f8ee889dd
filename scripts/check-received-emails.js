const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking received emails...');
    
    // Get all email accounts
    const emailAccounts = await prisma.emailAccount.findMany({
      select: { id: true, email: true },
    });
    
    console.log(`Found ${emailAccounts.length} email accounts`);
    
    for (const account of emailAccounts) {
      console.log(`\nChecking account: ${account.email}`);
      
      // Get received emails for this account
      const receivedEmails = await prisma.receivedEmail.findMany({
        where: { emailAccountId: account.id },
        select: {
          id: true,
          from: true,
          subject: true,
          headers: true,
          receivedAt: true
        },
        orderBy: { receivedAt: 'desc' },
        take: 10
      });
      
      console.log(`  Total received emails: ${receivedEmails.length}`);
      
      // Check for sent emails (emails from the same account)
      const sentEmails = receivedEmails.filter(email => {
        const fromEmail = email.from.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
        const isSentByHeader = email.headers && 
                              typeof email.headers === 'object' && 
                              email.headers['X-Email-Type'] === 'sent';
        const isSentByFrom = fromEmail && fromEmail[0].toLowerCase() === account.email.toLowerCase();
        
        return isSentByHeader || isSentByFrom;
      });
      
      if (sentEmails.length > 0) {
        console.log(`  ⚠️  Found ${sentEmails.length} sent emails that should be cleaned up:`);
        sentEmails.forEach(email => {
          console.log(`    - ${email.subject} (from: ${email.from})`);
        });
      } else {
        console.log(`  ✅ No sent emails found in received emails`);
      }
      
      // Show recent emails
      if (receivedEmails.length > 0) {
        console.log(`  Recent emails:`);
        receivedEmails.slice(0, 3).forEach(email => {
          console.log(`    - ${email.subject} (from: ${email.from})`);
        });
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
