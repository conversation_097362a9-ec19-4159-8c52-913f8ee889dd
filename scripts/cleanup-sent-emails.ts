#!/usr/bin/env ts-node

import { ImapService } from '../src/services/imap.service';
import { logger } from '../src/lib/logger';

async function main() {
  try {
    console.log('Starting cleanup of sent emails...');
    await ImapService.cleanupSentEmails();
    console.log('Cleanup completed successfully!');
  } catch (error) {
    console.error('Error running cleanup:', error);
    process.exit(1);
  }
}

main();
