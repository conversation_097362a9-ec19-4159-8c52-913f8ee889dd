const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanupSentEmails() {
  try {
    console.log('Starting cleanup of sent emails from received emails table');

    // Get all email accounts
    const emailAccounts = await prisma.emailAccount.findMany({
      select: { id: true, email: true },
    });

    let totalCleaned = 0;

    for (const account of emailAccounts) {
      console.log(`Processing account: ${account.email}`);
      
      // Find received emails that are actually sent emails
      const sentEmails = await prisma.receivedEmail.findMany({
        where: {
          emailAccountId: account.id,
          OR: [
            // Emails with X-Email-Type: sent header
            {
              headers: {
                path: ['X-Email-Type'],
                equals: 'sent'
              }
            },
            // Emails where from address matches the account email
            {
              from: {
                contains: account.email,
                mode: 'insensitive'
              }
            }
          ]
        }
      });

      if (sentEmails.length > 0) {
        console.log(`Found ${sentEmails.length} sent emails to clean up for account ${account.email}`);

        // Delete these emails
        await prisma.receivedEmail.deleteMany({
          where: {
            id: {
              in: sentEmails.map(email => email.id)
            }
          }
        });

        totalCleaned += sentEmails.length;
        console.log(`Cleaned up ${sentEmails.length} emails for ${account.email}`);
      } else {
        console.log(`No sent emails found for ${account.email}`);
      }
    }

    console.log(`Cleanup completed. Removed ${totalCleaned} sent emails from received emails table`);
  } catch (error) {
    console.error('Error cleaning up sent emails:', error);
  }
}

async function main() {
  try {
    await cleanupSentEmails();
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
