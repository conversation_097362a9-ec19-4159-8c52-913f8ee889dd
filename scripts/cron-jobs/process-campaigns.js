// Script to process active campaigns
// This script is executed by <PERSON><PERSON>'s cron job service

require('dotenv').config();
const axios = require('axios');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function processCampaigns() {
  console.log('Starting campaign processing cron job');
  console.log('Timestamp:', new Date().toISOString());

  try {
    // Option 1: Call the API endpoint with authentication
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const apiUrl = `${baseUrl}/api/cron/process-campaigns`;

    console.log('Calling API endpoint:', apiUrl);

    const response = await axios.post(apiUrl, {}, {
      headers: {
        'x-cron-secret': process.env.CRON_SECRET,
        'x-api-key': process.env.CRON_API_KEY
      }
    });

    console.log('API response status:', response.status);
    console.log('API response data:', response.data);
  } catch (apiError) {
    console.error('Error calling API endpoint:', apiError.message);

    // Option 2: Direct database access as fallback
    console.log('Falling back to direct database access');

    try {
      // Find all active campaigns (both standard and sequence)
      const activeCampaigns = await prisma.campaign.findMany({
        where: {
          status: 'active',
        },
        select: {
          id: true,
          name: true,
          type: true
        }
      });

      const standardCampaigns = activeCampaigns.filter(c => c.type === 'standard');
      const sequenceCampaigns = activeCampaigns.filter(c => c.type === 'sequence');

      console.log(`Found ${activeCampaigns.length} active campaigns (${standardCampaigns.length} standard, ${sequenceCampaigns.length} sequence)`);

      // Process each standard campaign by calling the API for each one
      for (const campaign of standardCampaigns) {
        try {
          console.log(`Processing campaign: ${campaign.id} - ${campaign.name}`);

          const campaignUrl = `${baseUrl}/api/campaigns/process-now`;
          await axios.post(campaignUrl, { campaignId: campaign.id }, {
            headers: {
              'x-cron-secret': process.env.CRON_SECRET,
              'x-api-key': process.env.CRON_API_KEY
            }
          });

          console.log(`Successfully processed campaign: ${campaign.id}`);
        } catch (campaignError) {
          console.error(`Error processing campaign ${campaign.id}:`, campaignError.message);
        }

        // Add a small delay between campaigns to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // Process sequence campaigns
      if (sequenceCampaigns.length > 0) {
        try {
          console.log(`Processing ${sequenceCampaigns.length} sequence campaigns...`);

          const sequenceUrl = `${baseUrl}/api/campaigns/sequence/process`;
          await axios.post(sequenceUrl, {}, {
            headers: {
              'x-cron-secret': process.env.CRON_SECRET,
              'x-api-key': process.env.CRON_API_KEY
            }
          });

          console.log(`Successfully processed sequence campaigns`);
        } catch (sequenceError) {
          console.error(`Error processing sequence campaigns:`, sequenceError.message);
        }
      }
    } catch (dbError) {
      console.error('Error accessing database:', dbError.message);
    }
  } finally {
    await prisma.$disconnect();
  }

  console.log('Campaign processing cron job completed');
}

// Run the function
processCampaigns()
  .catch(error => {
    console.error('Fatal error in process-campaigns script:', error);
    process.exit(1);
  });
