// <PERSON>ript to process AI agent tasks
// This script is executed by <PERSON><PERSON>'s cron job service or can be run manually

require('dotenv').config();
const axios = require('axios');

async function processAgents() {
  console.log('Starting agent processing cron job');
  console.log('Timestamp:', new Date().toISOString());

  try {
    // Call the API endpoint with authentication
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const apiUrl = `${baseUrl}/api/cron/process-agents`;

    console.log('Calling API endpoint:', apiUrl);

    const response = await axios.post(apiUrl, {}, {
      headers: {
        'x-cron-secret': process.env.CRON_SECRET,
        'x-api-key': process.env.CRON_API_KEY
      }
    });

    console.log('API response status:', response.status);
    console.log('API response data:', response.data);
  } catch (error) {
    console.error('Error processing agents:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }

  console.log('Agent processing cron job completed');
}

// Execute the function
processAgents();
