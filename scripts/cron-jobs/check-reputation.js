// Script to check email account reputation
// This script is executed by <PERSON><PERSON>'s cron job service

require('dotenv').config();
const axios = require('axios');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkReputation() {
  console.log('Starting reputation check cron job');
  console.log('Timestamp:', new Date().toISOString());
  
  try {
    // Option 1: Call the API endpoint with authentication
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const apiUrl = `${baseUrl}/api/cron/reputation`;
    
    console.log('Calling API endpoint:', apiUrl);
    
    const response = await axios.post(apiUrl, {}, {
      headers: {
        'x-cron-secret': process.env.CRON_SECRET,
        'x-api-key': process.env.CRON_API_KEY
      }
    });
    
    console.log('API response status:', response.status);
    console.log('API response data:', response.data);
  } catch (apiError) {
    console.error('Error calling API endpoint:', apiError.message);
    
    // Option 2: Direct database access as fallback
    console.log('Falling back to direct database access');
    
    try {
      // Find all active email accounts
      const emailAccounts = await prisma.emailAccount.findMany({
        where: {
          status: {
            in: ['active', 'verified']
          }
        },
        select: {
          id: true,
          email: true
        }
      });
      
      console.log(`Found ${emailAccounts.length} active email accounts`);
      
      // Check reputation for each account by calling the API
      for (const account of emailAccounts) {
        try {
          console.log(`Checking reputation for account: ${account.email}`);
          
          const reputationUrl = `${baseUrl}/api/email-accounts/${account.id}/reputation/check`;
          await axios.post(reputationUrl, {}, {
            headers: {
              'x-cron-secret': process.env.CRON_SECRET,
              'x-api-key': process.env.CRON_API_KEY
            }
          });
          
          console.log(`Successfully checked reputation for: ${account.email}`);
        } catch (accountError) {
          console.error(`Error checking reputation for ${account.email}:`, accountError.message);
        }
        
        // Add a small delay between accounts to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (dbError) {
      console.error('Error accessing database:', dbError.message);
    }
  } finally {
    await prisma.$disconnect();
  }
  
  console.log('Reputation check cron job completed');
}

// Run the function
checkReputation()
  .catch(error => {
    console.error('Fatal error in check-reputation script:', error);
    process.exit(1);
  });
