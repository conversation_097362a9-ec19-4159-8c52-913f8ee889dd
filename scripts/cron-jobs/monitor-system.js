// Script to monitor system health
// This script is executed by <PERSON><PERSON>'s cron job service

require('dotenv').config();
const axios = require('axios');

async function monitorSystem() {
  console.log('Starting system monitoring cron job');
  console.log('Timestamp:', new Date().toISOString());
  
  try {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const apiUrl = `${baseUrl}/api/cron/monitor`;
    
    console.log('Calling API endpoint:', apiUrl);
    
    const response = await axios.post(apiUrl, {}, {
      headers: {
        'x-cron-secret': process.env.CRON_SECRET,
        'x-api-key': process.env.CRON_API_KEY
      }
    });
    
    console.log('API response status:', response.status);
    console.log('System health status:', response.data.health.status);
    
    // Check for any critical metrics
    const metrics = response.data.metrics;
    if (metrics) {
      console.log('System metrics:', JSON.stringify(metrics, null, 2));
      
      // Log any concerning metrics
      if (metrics.cpuUsage > 80) {
        console.warn('High CPU usage detected:', metrics.cpuUsage);
      }
      
      if (metrics.memoryUsage > 80) {
        console.warn('High memory usage detected:', metrics.memoryUsage);
      }
      
      if (metrics.diskUsage > 80) {
        console.warn('High disk usage detected:', metrics.diskUsage);
      }
    }
  } catch (error) {
    console.error('Error monitoring system:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
  
  console.log('System monitoring cron job completed');
}

// Run the function
monitorSystem()
  .catch(error => {
    console.error('Fatal error in monitor-system script:', error);
    process.exit(1);
  });
