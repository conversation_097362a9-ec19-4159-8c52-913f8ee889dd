// Script to sync emails from IMAP accounts
// This script is executed by <PERSON><PERSON>'s cron job service

require('dotenv').config();
const axios = require('axios');

async function syncEmails() {
  console.log('Starting email sync cron job');
  console.log('Timestamp:', new Date().toISOString());
  
  try {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const apiUrl = `${baseUrl}/api/cron/sync-emails`;
    
    console.log('Calling API endpoint:', apiUrl);
    
    const response = await axios.post(apiUrl, {}, {
      headers: {
        'x-cron-secret': process.env.CRON_SECRET,
        'x-api-key': process.env.CRON_API_KEY
      }
    });
    
    console.log('API response status:', response.status);
    console.log('API response data:', response.data);
  } catch (error) {
    console.error('Error syncing emails:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
  
  console.log('Email sync cron job completed');
}

// Run the function
syncEmails()
  .catch(error => {
    console.error('Fatal error in sync-emails script:', error);
    process.exit(1);
  });
