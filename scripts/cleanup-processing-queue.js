/**
 * <PERSON><PERSON>t to clean up old processed events from the ProcessingQueue table
 * This script can be run as a scheduled job to prevent the table from growing too large
 */
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Default retention period is 7 days
const RETENTION_DAYS = parseInt(process.env.QUEUE_RETENTION_DAYS || '7');

async function cleanupProcessingQueue() {
  try {
    console.log(`Starting ProcessingQueue cleanup (retention: ${RETENTION_DAYS} days)`);
    
    // Calculate the cutoff date
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - RETENTION_DAYS);
    
    console.log(`Deleting processed events older than ${cutoffDate.toISOString()}`);
    
    // Delete completed and error events older than the cutoff date
    const result = await prisma.processingQueue.deleteMany({
      where: {
        status: {
          in: ['completed', 'error']
        },
        completedAt: {
          lt: cutoffDate
        }
      }
    });
    
    console.log(`Deleted ${result.count} old events from the ProcessingQueue table`);
    
    return result.count;
  } catch (error) {
    console.error('Error cleaning up ProcessingQueue:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// If this file is run directly (not imported), run the cleanup
if (require.main === module) {
  cleanupProcessingQueue()
    .then(count => {
      console.log(`Cleanup completed successfully. Deleted ${count} events.`);
      process.exit(0);
    })
    .catch(error => {
      console.error('Cleanup failed:', error);
      process.exit(1);
    });
} else {
  // Export the function for use in other modules
  module.exports = {
    cleanupProcessingQueue
  };
}
