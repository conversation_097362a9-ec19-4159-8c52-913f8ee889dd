const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  // Get the first organization
  const organization = await prisma.organization.findFirst();

  if (!organization) {
    console.error('No organization found. Please create an organization first.');
    return;
  }

  // Get the first user
  const user = await prisma.user.findFirst();

  if (!user) {
    console.error('No user found. Please create a user first.');
    return;
  }

  // Create sample notifications
  const notifications = [
    {
      title: 'Welcome to Avian Email',
      message: 'Thank you for signing up! Get started by adding your first email account.',
      type: 'info',
      organizationId: organization.id,
      userId: user.id,
    },
    {
      title: 'Email Campaign Completed',
      message: 'Your recent campaign "Welcome Series" has been delivered to all recipients.',
      type: 'success',
      organizationId: organization.id,
      userId: user.id,
    },
    {
      title: 'High Bounce Rate Detected',
      message: 'Your campaign "Product Launch" has a bounce rate of 8%, which is above the recommended threshold.',
      type: 'warning',
      organizationId: organization.id,
      userId: user.id,
    },
    {
      title: 'Email Account Authentication Failed',
      message: 'We could not connect to your Gmail account. Please check your credentials and try again.',
      type: 'error',
      organizationId: organization.id,
      userId: user.id,
    },
    {
      title: 'New Feature Available',
      message: 'We\'ve added a new A/B testing feature. Try it out with your next campaign!',
      type: 'info',
      organizationId: organization.id,
      userId: user.id,
    },
  ];

  for (const notification of notifications) {
    await prisma.notification.create({
      data: notification,
    });
  }

  console.log('Sample notifications created successfully!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
