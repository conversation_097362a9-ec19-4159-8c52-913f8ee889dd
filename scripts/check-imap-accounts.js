const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking IMAP enabled accounts...');
    
    // Get all email accounts with IMAP enabled
    const imapAccounts = await prisma.emailAccount.findMany({
      where: { imapEnabled: true },
      select: {
        id: true,
        email: true,
        imapEnabled: true,
        imapHost: true,
        imapPort: true,
        lastImapSync: true
      }
    });
    
    console.log(`Found ${imapAccounts.length} IMAP enabled accounts:`);
    
    imapAccounts.forEach(account => {
      console.log(`- ${account.email} (${account.imapHost}:${account.imapPort})`);
      console.log(`  Last sync: ${account.lastImapSync || 'Never'}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
