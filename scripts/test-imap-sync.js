const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Simple logger for testing
const logger = {
  info: (message, data) => console.log(`[INFO] ${message}`, data || ''),
  error: (message, data) => console.error(`[ERROR] ${message}`, data || ''),
  warn: (message, data) => console.warn(`[WARN] ${message}`, data || '')
};

// Mock the IMAP service processEmail method to test filtering
async function testEmailFiltering() {
  try {
    console.log('Testing email filtering logic...');
    
    // Get the email account
    const emailAccount = await prisma.emailAccount.findFirst({
      where: { imapEnabled: true }
    });
    
    if (!emailAccount) {
      console.log('No IMAP enabled accounts found');
      return;
    }
    
    console.log(`Testing with account: ${emailAccount.email}`);
    
    // Test cases
    const testEmails = [
      {
        name: 'Incoming email from external sender',
        from: '<EMAIL>',
        to: emailAccount.email,
        subject: 'Test incoming email',
        headers: {},
        shouldBeProcessed: true
      },
      {
        name: 'Sent email from same account',
        from: emailAccount.email,
        to: '<EMAIL>',
        subject: 'Test sent email',
        headers: {},
        shouldBeProcessed: false
      },
      {
        name: 'Sent email with X-Email-Type header',
        from: '<EMAIL>',
        to: emailAccount.email,
        subject: 'Test sent email with header',
        headers: { 'X-Email-Type': 'sent' },
        shouldBeProcessed: false
      },
      {
        name: 'Reply to campaign email',
        from: '<EMAIL>',
        to: emailAccount.email,
        subject: 'Re: Campaign email',
        headers: {},
        inReplyTo: '<<EMAIL>>',
        shouldBeProcessed: true
      }
    ];
    
    // Helper function to extract email address
    function extractEmailAddress(str) {
      const matches = str.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
      return matches ? matches[0] : null;
    }
    
    // Test each email
    for (const testEmail of testEmails) {
      console.log(`\nTesting: ${testEmail.name}`);
      
      // Extract the sender's email address
      const fromEmail = extractEmailAddress(testEmail.from);
      
      // Check if this is a sent email by looking at headers
      const isSentEmail = testEmail.headers && 
                         typeof testEmail.headers === 'object' && 
                         testEmail.headers['X-Email-Type'] === 'sent';
      
      // Check if email should be skipped
      const shouldSkip = isSentEmail || (fromEmail && fromEmail.toLowerCase() === emailAccount.email.toLowerCase());
      const shouldBeProcessed = !shouldSkip;
      
      console.log(`  From: ${testEmail.from}`);
      console.log(`  Headers: ${JSON.stringify(testEmail.headers)}`);
      console.log(`  Is sent email: ${isSentEmail}`);
      console.log(`  Should be processed: ${shouldBeProcessed}`);
      console.log(`  Expected: ${testEmail.shouldBeProcessed}`);
      console.log(`  Result: ${shouldBeProcessed === testEmail.shouldBeProcessed ? '✅ PASS' : '❌ FAIL'}`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testEmailFiltering();
