require('dotenv').config();
const nodemailer = require('nodemailer');

async function testEmailSending() {
  console.log('Starting email sending test...');

  // Log SMTP settings
  console.log('SMTP Settings:');
  console.log('- Host:', process.env.SMTP_HOST || 'NOT SET');
  console.log('- Port:', process.env.SMTP_PORT || 'NOT SET');
  console.log('- User:', process.env.SMTP_USER ? 'SET' : 'NOT SET');
  console.log('- Password:', process.env.SMTP_PASSWORD ? 'SET' : 'NOT SET');
  console.log('- From:', process.env.SMTP_FROM || 'NOT SET');

  // Get the recipient email from command line arguments
  const to = process.argv[2];
  if (!to) {
    console.error('Please provide a recipient email address');
    console.log('Usage: node test-email-sending.js <EMAIL>');
    process.exit(1);
  }

  try {
    // Create transporter
    console.log('Creating transporter...');
    const smtpPassword = process.env.SMTP_PASSWORD || '';
    const transportOptions = {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '465'),
      secure: process.env.SMTP_PORT === '465',
      auth: {
        user: process.env.SMTP_USER,
        pass: smtpPassword.replace(/^["'](.+)["']$/, '$1'),
      },
      tls: {
        rejectUnauthorized: false
      },
      debug: true,
      logger: true,
      connectionTimeout: 30000,
      greetingTimeout: 30000,
      socketTimeout: 45000
    };

    const transporter = nodemailer.createTransport(transportOptions);
    console.log('Transporter created successfully');

    // Verify connection
    console.log('Verifying SMTP connection...');
    try {
      const verificationResult = await transporter.verify();
      console.log('SMTP connection verified successfully:', verificationResult);
    } catch (verifyError) {
      console.error('SMTP connection verification failed:', verifyError);
      console.log('Continuing despite verification failure...');
    }

    // Create a test verification URL
    const testToken = require('crypto').randomBytes(32).toString('hex');
    const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify?token=${testToken}`;
    console.log(`Test verification URL: ${verificationUrl}`);

    // Send test email using the same template as the verification email
    console.log(`Sending test verification email to ${to}...`);
    const info = await transporter.sendMail({
      from: process.env.SMTP_FROM || '"Avian Email" <<EMAIL>>',
      to,
      subject: 'Verify your email address',
      text: `Please verify your email by clicking: ${verificationUrl}`,
      html: `
        <h1>Email Verification</h1>
        <p>Please verify your email by clicking the link below:</p>
        <a href="${verificationUrl}" style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 15px;">Verify Email</a>
        <p style="margin-top: 20px;">If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p>${verificationUrl}</p>
        <p>This link will expire in 24 hours.</p>
      `,
      headers: {
        'X-Priority': '1',
        'X-MSMail-Priority': 'High',
        'Importance': 'High'
      }
    });

    console.log('Email sent successfully:');
    console.log('- Message ID:', info.messageId);
    console.log('- Response:', info.response);
    console.log('- Accepted:', info.accepted);
    console.log('- Rejected:', info.rejected);

  } catch (error) {
    console.error('Error sending email:', error);
  }
}

testEmailSending().catch(console.error);
