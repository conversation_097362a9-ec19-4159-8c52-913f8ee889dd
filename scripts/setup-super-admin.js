#!/usr/bin/env node

/**
 * Super Admin Setup CLI Tool
 * 
 * This script helps you set up the super admin system securely.
 * It creates the first super admin user and initializes the package system.
 */

const readline = require('readline');
const bcrypt = require('bcryptjs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

function hiddenQuestion(prompt) {
  return new Promise((resolve) => {
    process.stdout.write(prompt);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let password = '';
    
    process.stdin.on('data', function(char) {
      char = char + '';
      
      switch(char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdout.write('\n');
          resolve(password);
          break;
        case '\u0003':
          process.exit();
          break;
        case '\u007f': // backspace
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          password += char;
          process.stdout.write('*');
          break;
      }
    });
  });
}

async function main() {
  try {
    colorLog('cyan', '\n🔐 SUPER ADMIN SETUP TOOL');
    colorLog('cyan', '========================\n');
    
    colorLog('yellow', '⚠️  SECURITY WARNING:');
    colorLog('yellow', 'This tool creates a super admin user with full system access.');
    colorLog('yellow', 'Only run this on a secure environment and keep credentials safe.\n');
    
    // Check if we should continue
    const shouldContinue = await question('Do you want to continue? (yes/no): ');
    if (shouldContinue.toLowerCase() !== 'yes') {
      colorLog('red', 'Setup cancelled.');
      process.exit(0);
    }
    
    console.log('\n');
    colorLog('blue', '📝 SUPER ADMIN DETAILS');
    colorLog('blue', '======================\n');
    
    // Get admin details
    const email = await question('Enter super admin email: ');
    if (!email || !email.includes('@')) {
      colorLog('red', 'Invalid email address.');
      process.exit(1);
    }
    
    const name = await question('Enter super admin name (optional): ') || 'Super Admin';
    
    const password = await hiddenQuestion('Enter super admin password: ');
    if (!password || password.length < 8) {
      colorLog('red', 'Password must be at least 8 characters long.');
      process.exit(1);
    }
    
    const confirmPassword = await hiddenQuestion('Confirm password: ');
    if (password !== confirmPassword) {
      colorLog('red', 'Passwords do not match.');
      process.exit(1);
    }
    
    console.log('\n');
    colorLog('green', '✅ Admin details collected successfully!\n');
    
    // Show setup instructions
    colorLog('magenta', '🚀 SETUP INSTRUCTIONS');
    colorLog('magenta', '====================\n');
    
    colorLog('bright', '1. Database Migration:');
    console.log('   Run the following command to update your database:');
    colorLog('cyan', '   npx prisma db push\n');
    
    colorLog('bright', '2. Environment Variables:');
    console.log('   Add these to your .env file:');
    colorLog('cyan', '   SUPER_ADMIN_SECRET=your-super-secret-key-change-this');
    colorLog('cyan', '   SUPER_ADMIN_JWT_SECRET=your-jwt-secret-change-this');
    colorLog('cyan', '   SUPER_ADMIN_ALLOWED_IPS=127.0.0.1,your.server.ip (optional)\n');
    
    colorLog('bright', '3. API Endpoints:');
    console.log('   Your super admin APIs will be available at:');
    colorLog('cyan', '   POST /api/super-admin/auth (Authentication)');
    colorLog('cyan', '   GET  /api/super-admin/analytics (Business Analytics)');
    colorLog('cyan', '   *    /api/super-admin/packages (Package Management)');
    colorLog('cyan', '   POST /api/super-admin/initialize (System Setup)\n');
    
    colorLog('bright', '4. First Login:');
    console.log('   Use these credentials for your first login:');
    colorLog('green', `   Email: ${email}`);
    colorLog('green', `   Password: [HIDDEN]`);
    console.log('   Name: ' + name + '\n');
    
    colorLog('bright', '5. Security Notes:');
    colorLog('yellow', '   • Change the SUPER_ADMIN_SECRET in production');
    colorLog('yellow', '   • Use HTTPS in production');
    colorLog('yellow', '   • Consider IP whitelisting');
    colorLog('yellow', '   • Store credentials securely');
    colorLog('yellow', '   • Monitor admin access logs\n');
    
    // Generate SQL for manual insertion (if needed)
    const hashedPassword = await bcrypt.hash(password, 12);
    const userId = 'admin_' + Date.now();
    
    colorLog('bright', '6. Manual Database Setup (if needed):');
    console.log('   If automatic setup fails, run this SQL:');
    colorLog('cyan', `   INSERT INTO "User" (id, email, password, name, "isAdmin", "emailVerified", "createdAt", "updatedAt")`);
    colorLog('cyan', `   VALUES ('${userId}', '${email}', '${hashedPassword}', '${name}', true, NOW(), NOW(), NOW());\n`);
    
    colorLog('bright', '7. Testing the Setup:');
    console.log('   Test your super admin login:');
    colorLog('cyan', '   curl -X POST http://localhost:3002/api/super-admin/auth \\');
    colorLog('cyan', '     -H "Content-Type: application/json" \\');
    colorLog('cyan', `     -d '{"action":"login","email":"${email}","password":"[YOUR_PASSWORD]"}'\n`);
    
    colorLog('green', '🎉 Setup instructions generated successfully!');
    colorLog('green', 'Follow the steps above to complete your super admin setup.\n');
    
    // Ask if they want to see the package system setup
    const setupPackages = await question('Would you like to see package system setup instructions? (yes/no): ');
    if (setupPackages.toLowerCase() === 'yes') {
      console.log('\n');
      colorLog('magenta', '📦 PACKAGE SYSTEM SETUP');
      colorLog('magenta', '======================\n');
      
      colorLog('bright', '1. Initialize Package System:');
      console.log('   After logging in as super admin, call:');
      colorLog('cyan', '   POST /api/super-admin/initialize');
      colorLog('cyan', '   {"action": "full-setup"}\n');
      
      colorLog('bright', '2. Default Packages Created:');
      colorLog('green', '   • Free Package: $0/month, 100 emails/day, no AI features');
      colorLog('green', '   • Pro Package: $29.99/month, 1000 emails/day, basic AI features');
      colorLog('green', '   • Enterprise Package: $99.99/month, 10000 emails/day, all AI features\n');
      
      colorLog('bright', '3. Package Enforcement:');
      colorLog('yellow', '   • All organizations MUST have a package to send emails');
      colorLog('yellow', '   • Free package is assigned automatically to existing organizations');
      colorLog('yellow', '   • AI features are disabled for free package users\n');
      
      colorLog('bright', '4. Stripe Integration:');
      console.log('   Packages automatically create Stripe products for paid subscriptions.');
      console.log('   Make sure your STRIPE_SECRET_KEY is configured.\n');
    }
    
    colorLog('bright', '🔒 SECURITY REMINDER:');
    colorLog('red', 'Keep your super admin credentials secure and change default secrets!');
    
  } catch (error) {
    colorLog('red', `\nError: ${error.message}`);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  colorLog('yellow', '\n\nSetup cancelled by user.');
  process.exit(0);
});

main();
